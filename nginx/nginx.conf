server {
        server_name tajapi.therapynimbus.com;

        client_body_buffer_size 200K;
        client_header_buffer_size 2k;
        client_max_body_size 100M;
        large_client_header_buffers 8 16k;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "Upgrade";
        proxy_set_header Host $host;
        client_body_timeout 60s;
        client_header_timeout 60s;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        location = /favicon.ico { access_log off; log_not_found off; }

        location / {
            proxy_pass http://localhost:8139;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            client_max_body_size 100M;
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        location /static/ { 
           autoindex on;
           alias /var/www/taj/static/;
        }


    location /media/ {
        autoindex on;
        alias /var/www/taj/uploads/;

}


}
