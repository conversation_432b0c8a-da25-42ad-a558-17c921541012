# Django Configuration
TARGET=taj
SECRET_KEY="dummy_secret_key_replace_with_your_own_secure_key"
ENV=0
DJANGO_ALLOWED_HOSTS=example.com
CORS_ALLOWED_ORIGINS=https://app1.example.com,https://app2.example.com
CORS_ORIGIN_WHITELIST=http://localhost:3000,https://app1.example.com,https://app2.example.com
UI_BASE_URL=https://app.example.com
BASE_URL=http://0.0.0.0:8000
CSRF_TRUSTED_ORIGINS=https://app1.example.com,https://app2.example.com,http://localhost:8000

# Database Configuration
DATABASE_NAME=dbname
DATABASE_USER=dbuser
DATABASE_PASSWORD=dbpassword
DATABASE_HOST=db
DATABASE_PORT=5432
POSTGRES_USER=dbuser
POSTGRES_PASSWORD=dbpassword
POSTGRES_DB=dbname

# Email Configuration
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=email_password

# Content Types
FILE_CONTENT_TYPE=16
IMAGE_CONTENT_TYPE=15
TEXT_CONTENT_TYPE=13
VIDEO_CONTENT_TYPE=12

# Social Authentication
SOCIAL_AUTH_ALLOWED_REDIRECT_URIS=http://localhost:3000
GOOGLE_CLIENT_ID=your_google_client_id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Admin User
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin_password

# API Service
API_PORT=8000

SENTRY_DSN=https://<EMAIL>/12345

CELERY_BROKER_URL=redis://redis:6379/0

EMAIL_ACTIVATION_HOST=<EMAIL>
EMAIL_ACTIVATION_PASSWORD=email_password
EMAIL_ICOMING_SERVER=imap.example.com
EMAIL_IMAP_PORT=587
EMAIL_OUTGOING_SERVER=smtp.example.com
EMAIL_SMTP_PORT=587
EMAIL_COUPON_HOST=<EMAIL>
EMAIL_COUPON_PASSWORD=email_password
EMAIL_NO_REPLY_HOST=<EMAIL>
EMAIL_NO_REPLY_PASSWORD=email_password

MOYASSAR_PUBLISHABLE_KEY=pk_test_dummy_publishable_key
MOYASSAR_TEST_KEY=pk_test_dummy_test_key
MOYASSAR_SECRET_KEY=sk_test_dummy_secret_key
MOYASSAR_SECRET_TEST_KEY=sk_test_dummy_secret_test_key
PAYMENT_CALLBACK_URL=http://localhost:3000/payment/callback

MAILGUN_API_KEY=dummy_mailgun_api_key
MAILGUN_SMTP_PASSWORD=dummy_smtp_password
MAILGUN_SENDER_DOMAIN=sandbox.mailgun.org
DEFAULT_FROM_EMAIL=<EMAIL>
SERVER_EMAIL=<EMAIL>

# TWILIO
TWILIO_SID=dummy_twilio_sid
TWILIO_MESSAGING_SID=dummy_messaging_sid
TWILIO_AUTH_TOKEN=dummy_auth_token
TWILIO_TEST_SID=dummy_test_sid
TWLIO_TEST_AUTH_TOKEN=dummy_test_auth_token
TWILIO_NUMBER=+12345678901
TWILIO_VERIFY_SERVICE_SID=dummy_verify_service_sid

# VONAGE
VONAGE_API_KEY=dummy_api_key
VONAGE_API_SECRET=dummy_api_secret
VONAGE_FROM=example

# VPS
BACKUPS_PATH=/path/to/backups
FRONTEND_URL=http://localhost:3000
RESET_PASSWORD_URL=http://localhost:3000/auth/reset-password?token=
SITE_NAME=example

# RATE LIMITER
RATE_LIMITER_ENABLED=False
RATE_LIMITER_DEFAULT_RATE=5
RATE_LIMITER_DEFAULT_PERIOD=1

# JURY ASSIGNMENT LIMIT
JURY_ASSIGNMENT_LIMIT=20

# Zoho Mail Settings (used as fallback)
EMAIL_SMTP_SERVER=smtp.zoho.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=test_username
EMAIL_PASSWORD=test_password
EMAIL_USE_TLS=True
EMAIL_SENDER=<EMAIL>
EMAIL_RECIPIENT=<EMAIL>