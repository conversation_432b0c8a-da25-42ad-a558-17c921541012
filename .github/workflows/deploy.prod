name: Deploy Production

on:
  push:
    branches: [master]
  workflow_dispatch:

env:
  PYTHON_VERSION: "3.10"
  TARGET: "taj"

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    environment: production

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Deploy using SSH
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST_PROD }}
          username: ${{ secrets.USERNAME_PROD }}
          key: ${{ secrets.PRIVATE_KEY_PROD }}
          port: 22
          script: |
            cd /var/www/taj-api
            git fetch origin master
            git reset --hard origin/master || { echo 'Git reset failed'; exit 1; }
            make build-prod

      - name: Verify deployment
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST_PROD }}
          username: ${{ secrets.USERNAME_PROD }}
          key: ${{ secrets.PRIVATE_KEY_PROD }}
          port: 22
          script: |
            cd /var/www/taj-api
            docker ps | grep taj
            echo "Production deployment completed successfully!"
