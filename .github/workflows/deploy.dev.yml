name: Django CI/CD Pipeline - Development

on:
  pull_request:
    branches: [develop]
  workflow_dispatch:
    inputs:
      deploy_only:
        description: "Skip tests and deploy only"
        type: boolean
        default: false

env:
  PYTHON_VERSION: "3.10"
  POSTGRES_USER: postgres
  POSTGRES_PASSWORD: postgres
  POSTGRES_DB: test_db
  POSTGRES_HOST: localhost
  POSTGRES_PORT: 5432
  DATABASE_URL: postgres://postgres:postgres@localhost:5432/test_db
  REDIS_URL: redis://localhost:6379/0

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    concurrency:
      group: deploy-dev
      cancel-in-progress: false

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Set up SSH key
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.PRIVATE_KEY }}

      - name: Add host to known_hosts
        run: |
          mkdir -p ~/.ssh
          ssh-keyscan -H ${{ secrets.HOST }} >> ~/.ssh/known_hosts

      - name: Deploy to development server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.PRIVATE_KEY }}
          port: 22
          script: |
            set -e
            echo "Starting deployment at $(date)"

            cd /var/www/taj-api

            echo "Creating database backup..."
            ./scripts/backup-db.sh || echo "Database backup failed, but continuing deployment"

            echo "Pulling latest code..."
            git fetch origin develop
            git reset --hard origin/develop

            echo "Building and deploying..."
            make build-prod

            echo "Deployment completed successfully at $(date)"
