import re
import uuid


class SlugUtil:
    """
    A utility to convert a string into a slug.
    """

    @staticmethod
    def slugify(value, allow_unicode=False, unique=False):
        """
        Convert a string into a slug.
        """
        value = str(value).lower()
        if allow_unicode:
            value = re.sub(r"[^\w\s-]", "", value)
        else:
            value = (
                re.sub(r"[^\w\s-]", "", value).encode("ascii", "ignore").decode("utf-8")
            )
        if unique:
            value = f"{value}-{uuid.uuid4().hex[:8]}"
        value = re.sub(r"[-\s]+", "-", value).strip("-")
        return value
