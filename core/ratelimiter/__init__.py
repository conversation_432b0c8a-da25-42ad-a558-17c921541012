import time
from functools import wraps
from django.core.cache import cache
from django.http import JsonResponse
from rest_framework import status
from django.conf import settings

RATE_LIMITER_ENABLED = settings.RATE_LIMITER_ENABLED
RATE_LIMITER_DEFAULT_RATE = settings.RATE_LIMITER_DEFAULT_RATE
RATE_LIMITER_DEFAULT_PERIOD = settings.RATE_LIMITER_DEFAULT_PERIOD


def get_client_ip(request, *args, **kwargs):
    """Get client IP address from request"""
    if hasattr(request, "request"):
        request = request.request
    x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
    if x_forwarded_for:
        ip = x_forwarded_for.split(",")[0]
    else:
        ip = request.META.get("REMOTE_ADDR")
    return ip


def dynamic_rate_limit(key_func=get_client_ip, default_rate=5, default_period=1):
    """
    Rate limiting decorator that can be applied to views.

    Args:
        key_func: Function to generate cache key. De<PERSON>ult uses client IP.
        default_rate: Number of allowed requests per period. Default is 5.
        default_period: Time period in seconds. Default is 1 second.
    """

    default_rate = int(default_rate)
    default_period = int(default_period)

    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if not getattr(settings, "RATE_LIMITER_ENABLED", True):
                return view_func(request, *args, **kwargs)

            key = key_func(request, *args, **kwargs)
            cache_key = f"ratelimit:{key}:{view_func.__name__}"

            timestamps = cache.get(cache_key, [])
            if not isinstance(timestamps, list):
                timestamps = []

            now = time.time()

            timestamps = [
                ts
                for ts in timestamps
                if isinstance(ts, (int, float)) and ts > now - default_period
            ]

            if len(timestamps) >= default_rate:
                return JsonResponse(
                    {"detail": "Rate limit exceeded"},
                    status=status.HTTP_429_TOO_MANY_REQUESTS,
                )

            timestamps.append(now)
            cache.set(cache_key, timestamps, default_period)

            return view_func(request, *args, **kwargs)

        return _wrapped_view

    return decorator
