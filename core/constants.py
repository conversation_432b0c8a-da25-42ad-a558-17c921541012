from datetime import <PERSON><PERSON><PERSON>

ROLE_CHOICES = [
    ("admin", "Admin"),
    ("club_manager", "Club Manager"),
    ("member", "Member"),
]

SUBMISSION_CATEGORIES = {
    "WEB": "web",
    "MOBILE": "mobile",
    "AI": "ai",
    "DATA": "data",
    "IOT": "iot",
    "OTHER": "other",
}

SUBMISSION_STATUSES = {
    "DRAFT": "draft",
    "SUBMITTED": "submitted",
    "UNDER_REVIEW": "under_review",
    "ACCEPTED": "accepted",
    "REJECTED": "rejected",
    "PHASE_2_INVITED": "phase_2_invited",
    "PHASE_2_SUBMITTED": "phase_2_submitted",
    "PHASE_2_UNDER_REVIEW": "phase_2_under_review",
    "INCUBATED": "incubated",
}

TEAM_MEMBER_STATUSES = {
    "INVITED": "invited",
    "ACCEPTED": "accepted",
    "DECLINED": "declined",
}

SAMPLE_PRODUCT_FEATURES = [
    "User authentication and authorization",
    "Real-time data synchronization",
    "Mobile-responsive design",
    "Advanced analytics dashboard",
    "API integration capabilities",
    "Automated reporting system",
    "Multi-language support",
    "File upload and management",
    "Payment gateway integration",
    "Social media integration",
]

SAMPLE_BUSINESS_METRICS = {
    "monthly_active_users": {"min": 1000, "max": 100000},
    "revenue_growth": {"min": 10, "max": 200},
    "customer_acquisition_cost": {"min": 5, "max": 100},
    "lifetime_value": {"min": 100, "max": 1000},
    "churn_rate": {"min": 1, "max": 10},
}

SAMPLE_FINANCIAL_DETAILS = {
    "startup_costs": {"min": 50000, "max": 500000},
    "monthly_expenses": {"min": 5000, "max": 50000},
    "revenue_streams": [
        "Subscription fees",
        "In-app purchases",
        "Advertisement",
        "Professional services",
        "Data monetization",
    ],
}

SAMPLE_DOMAINS = [
    "tech.io",
    "startup.com",
    "innovation.net",
    "digital.co",
    "solutions.dev",
]

SAMPLE_COMPANIES = [
    "TechVision",
    "InnovateLabs",
    "FutureWorks",
    "SmartSolutions",
    "DataDrive",
    "CloudPeak",
    "DevForge",
    "AIVentures",
    "MobileFirst",
    "WebPioneers",
]

SAMPLE_TITLES = [
    "AI-Powered Healthcare Assistant",
    "Smart City Management Platform",
    "Blockchain Supply Chain Solution",
    "IoT Home Automation System",
    "Machine Learning Financial Advisor",
    "Sustainable Energy Tracker",
    "EdTech Learning Platform",
    "AR Shopping Experience",
    "Digital Identity Manager",
    "Cloud-Based Project Management Tool",
]

REVIEW_TIME_RANGES = {"MIN": timedelta(minutes=15), "MAX": timedelta(hours=2)}

SCORE_RANGES = {"MIN": 60.0, "MAX": 100.0}

SUBMISSION_COUNTS = {"MIN_PER_USER": 1, "MAX_PER_USER": 3}

TEAM_SIZES = {"MIN": 2, "MAX": 5}

REVIEW_COUNTS = {"MIN_PER_JURY": 5, "MAX_PER_JURY": 20}

LOGIN_COUNTS = {"MIN": 1, "MAX": 10}

ACTIVITY_PERIODS = {"DAYS_AGO": 30}

JURY_GROUP_SIZES = {"MIN": 3, "MAX": 7}

FEEDBACK_TEMPLATES = [
    "Strong technical implementation with room for UX improvements.",
    "Innovative solution but needs better market validation.",
    "Well-documented code and clear architecture.",
    "Good potential but requires more scalability considerations.",
    "Excellent presentation of business model.",
    "Creative approach to solving the problem.",
    "Needs more focus on security aspects.",
    "Great team collaboration evident in the project.",
    "Strong market research but technical execution needs work.",
    "Impressive use of cutting-edge technologies.",
]

MEETING_NOTES_TEMPLATES = [
    "Team demonstrated strong technical knowledge.",
    "Project shows significant market potential.",
    "Identified key areas for improvement.",
    "Discussed scaling strategies.",
    "Reviewed technical architecture.",
    "Explored potential partnerships.",
    "Analyzed competition and market positioning.",
    "Evaluated team capabilities and resources.",
    "Discussed funding requirements and strategy.",
    "Reviewed development roadmap.",
]
