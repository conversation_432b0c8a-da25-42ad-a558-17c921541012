import logging
import traceback
import smtplib
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.text import MIMEText
from django.conf import settings
from anymail.message import AnymailMessage
from django.core.mail import send_mail
from celery import shared_task

logger = logging.getLogger(__name__)


ZOHO_EMAIL_SMTP_SERVER = getattr(settings, "EMAIL_SMTP_SERVER", "smtp.zoho.com")
ZOHO_EMAIL_SMTP_PORT = getattr(settings, "EMAIL_SMTP_PORT", 587)
ZOHO_EMAIL_USERNAME = getattr(settings, "EMAIL_USERNAME", "<EMAIL>")
ZOHO_EMAIL_PASSWORD = getattr(settings, "EMAIL_PASSWORD", "cv5ZsKsMnysh")
ZOHO_EMAIL_USE_TLS = getattr(settings, "EMAIL_USE_TLS", True)
ZOHO_EMAIL_SENDER = getattr(settings, "<PERSON><PERSON><PERSON>_SENDER", "<EMAIL>")
ZOHO_EMAIL_RECIPIENT = getattr(settings, "EMAIL_RECIPIENT", "<EMAIL>")


class EmailBackend:
    def send_email(
        self, subject, text_content, from_email, to_email, html_content=None
    ):
        raise NotImplementedError("Subclasses must implement this method.")


class GmailSMTPBackend(EmailBackend):
    def __init__(self, fail_silently=False, **kwargs):
        self.fail_silently = fail_silently
        self.smtp_server = settings.EMAIL_ICOMING_SERVER
        self.smtp_port = settings.EMAIL_IMAP_PORT
        self.username = kwargs.get("username") or settings.EMAIL_ACTIVATION_HOST
        self.password = kwargs.get("password") or settings.EMAIL_ACTIVATION_PASSWORD
        self.validate_settings()

    def validate_settings(self):
        if not all([self.smtp_server, self.smtp_port, self.username, self.password]):
            logger.error("Gmail SMTP settings are not properly configured.")
            raise ValueError("Gmail SMTP settings are not properly configured.")

    def send_messages(self, email_messages):
        """
        Send one or more EmailMessage objects and return the number of email
        messages sent.
        """
        if not email_messages:
            return 0

        sent = 0
        for message in email_messages:
            try:
                self.send_email(
                    subject=message.subject,
                    text_content=message.body,
                    from_email=message.from_email,
                    to_email=message.to,
                    html_content=(
                        message.alternatives[0][0] if message.alternatives else None
                    ),
                )
                sent += 1
            except Exception as e:
                if not self.fail_silently:
                    raise
                logger.error(f"Failed to send email: {e}")
        return sent

    def send_email(
        self, subject, text_content, from_email, to_email, html_content=None
    ):
        try:
            if isinstance(to_email, str):
                to_email = [to_email]

            msg = MIMEMultipart("alternative")
            msg["Subject"] = str(subject)
            msg["From"] = str(from_email)
            msg["To"] = ", ".join(map(str, to_email))

            text_content = text_content or ""
            html_content = html_content or ""

            part1 = MIMEText(text_content, "plain", "utf-8")
            msg.attach(part1)

            if html_content:
                part2 = MIMEText(html_content, "html", "utf-8")
                msg.attach(part2)

            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.username, self.password)
                server.sendmail(from_email, to_email, msg.as_string())
                logger.info("Email sent successfully via Gmail SMTP.")
        except Exception as e:
            traceback.print_exc()
            logger.error(f"Failed to send email via Gmail SMTP: {e}")
            raise


class MailgunBackend(EmailBackend):
    def __init__(self, fail_silently=False, **kwargs):
        self.fail_silently = fail_silently
        self.api_key = settings.ANYMAIL["MAILGUN_API_KEY"]
        self.domain = settings.ANYMAIL["MAILGUN_SENDER_DOMAIN"]
        self.validate_settings()

    def validate_settings(self):
        required_settings = [self.api_key, self.domain]
        if not all(required_settings):
            logger.error("Mailgun settings are not properly configured.")
            raise ValueError("Mailgun settings are not properly configured.")

    def send_messages(self, email_messages):
        if not email_messages:
            return 0

        sent = 0
        for message in email_messages:
            try:
                self.send_email(
                    subject=message.subject,
                    text_content=message.body,
                    from_email=message.from_email,
                    to_email=message.to,
                    html_content=(
                        message.alternatives[0][0] if message.alternatives else None
                    ),
                )
                sent += 1
            except Exception as e:
                if not self.fail_silently:
                    raise
                logger.error(f"Failed to send email: {e}")
        return sent

    def send_email(
        self, subject, text_content, from_email, to_email, html_content=None
    ):
        try:
            if isinstance(to_email, str):
                to_email = [to_email]

            message = AnymailMessage(
                subject=subject,
                body=text_content,
                from_email=from_email,
                to=to_email,
            )

            if html_content:
                message.attach_alternative(html_content, "text/html")

            message.send()
            logger.info("Email sent successfully via Mailgun.")
        except Exception as e:
            logger.error(f"Failed to send email via Mailgun: {e}")
            raise


class ZohoMailBackend(EmailBackend):
    def __init__(self, fail_silently=False, **kwargs):
        self.fail_silently = fail_silently
        self.smtp_server = kwargs.get("smtp_server") or ZOHO_EMAIL_SMTP_SERVER
        self.smtp_port = kwargs.get("smtp_port") or ZOHO_EMAIL_SMTP_PORT
        self.username = kwargs.get("username") or ZOHO_EMAIL_USERNAME
        self.password = kwargs.get("password") or ZOHO_EMAIL_PASSWORD
        self.use_tls = kwargs.get("use_tls") or ZOHO_EMAIL_USE_TLS
        self.sender = kwargs.get("sender") or ZOHO_EMAIL_SENDER
        self.validate_settings()

    def validate_settings(self):
        if not all([self.smtp_server, self.smtp_port, self.username, self.password]):
            logger.error("Zoho Mail SMTP settings are not properly configured.")
            raise ValueError("Zoho Mail SMTP settings are not properly configured.")

    def send_messages(self, email_messages):
        """
        Send one or more EmailMessage objects and return the number of email
        messages sent.
        """
        if not email_messages:
            return 0

        sent = 0
        for message in email_messages:
            try:
                self.send_email(
                    subject=message.subject,
                    text_content=message.body,
                    from_email=message.from_email,
                    to_email=message.to,
                    html_content=(
                        message.alternatives[0][0] if message.alternatives else None
                    ),
                )
                sent += 1
            except Exception as e:
                if not self.fail_silently:
                    raise
                logger.error(f"Failed to send email via Zoho: {e}")
        return sent

    def send_email(
        self, subject, text_content, from_email, to_email, html_content=None
    ):
        try:
            if isinstance(to_email, str):
                to_email = [to_email]

            msg = MIMEMultipart("alternative")
            msg["Subject"] = str(subject)
            msg["From"] = str(from_email) if from_email else self.sender
            msg["To"] = ", ".join(map(str, to_email))

            text_content = text_content or ""
            html_content = html_content or ""

            part1 = MIMEText(text_content, "plain", "utf-8")
            msg.attach(part1)

            if html_content:
                part2 = MIMEText(html_content, "html", "utf-8")
                msg.attach(part2)

            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                if self.use_tls:
                    server.starttls()
                server.login(self.username, self.password)
                server.sendmail(
                    from_email if from_email else self.sender, to_email, msg.as_string()
                )
                logger.info("Email sent successfully via Zoho Mail SMTP.")
        except Exception as e:
            logger.error(f"Failed to send email via Zoho Mail SMTP: {e}")
            if not self.fail_silently:
                traceback.print_exc()
                raise


def get_email_backend():
    try:
        return GmailSMTPBackend()
    except Exception as e:
        logger.error(f"GmailSMTPBackend failed, falling back to MailgunBackend: {e}")
        try:
            return MailgunBackend()
        except Exception as ex:
            logger.error(
                f"MailgunBackend failed, falling back to ZohoMailBackend: {ex}"
            )
            try:
                return ZohoMailBackend()
            except Exception as zoho_ex:
                logger.error(
                    f"ZohoMailBackend also failed, falling back to Django's send_mail: {zoho_ex}"
                )
                return None


@shared_task
def send_activation_email(subject, text_content, to_email, html_content=None):
    backend = get_email_backend()
    if backend:
        try:
            from_email = settings.EMAIL_ACTIVATION_HOST
            backend.send_email(
                subject=subject,
                text_content=text_content,
                from_email=from_email,
                to_email=to_email,
                html_content=html_content,
            )
            return
        except Exception as e:
            logger.error(f"Primary email backend failed: {e}")

    try:
        send_mail(
            subject=subject,
            message=text_content,
            from_email=settings.EMAIL_ACTIVATION_HOST,
            recipient_list=[to_email] if isinstance(to_email, str) else to_email,
            html_message=html_content,
        )
        logger.info("Email sent successfully using Django's send_mail fallback.")
    except Exception as e:
        logger.error(f"Fallback send_mail failed: {e}")
        raise
