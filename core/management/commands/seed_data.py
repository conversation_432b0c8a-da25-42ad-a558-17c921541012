import random 
import datetime 
import uuid 
from django .core .management .base import BaseCommand 
from django .utils import timezone 
from django .db import transaction 
from django .contrib .auth import get_user_model 
from dateutil .relativedelta import relativedelta 

from apps .clubs .models import ClubType ,Club ,ClubInvitation 
from apps .domains .models import Domain 
from apps .okrs .models import SixWeekPeriod ,WeeklyProgress 
from apps .missions .models import (
Goal ,
WeeklyMission ,
DailyGoal ,
DailyGoalProgress ,
WeeklySuccessRating ,
)

from .constants import (
DEFAULT_PASSWORD ,
MANAGER_EMAIL ,
MEMBER_EMAIL ,
ADMIN_EMAIL ,
NUM_CLUB_TYPES ,
NUM_CLUBS ,
NUM_DOMAINS ,
NUM_MISSIONS_PER_USER ,
NUM_DAILY_GOALS_PER_MISSION ,
NUM_MEMBERS ,
CLUB_TYPES ,
CLUB_NAMES ,
DOMAINS ,
MISSION_TITLES ,
DAILY_GOAL_TITLES ,
ALL_ARABIC_NAMES ,
<PERSON>LE_ARABIC_NAMES ,
FEMALE_ARABIC_NAMES ,
MANAGER_TITLES ,
REALISTIC_USERNAMES ,
)

User =get_user_model ()


class Command (BaseCommand ):
    help ="Seed database with initial data for development and testing"

    def add_arguments (self ,parser ):
        parser .add_argument (
        "--flush",
        action ="store_true",
        help ="Flush existing data before seeding",
        )
        parser .add_argument (
        "--historical",
        action ="store_true",
        help ="Create historical data for charts and stats",
        )

    def handle (self ,*args ,**options ):
        if options ["flush"]:
            self .flush_data ()

        self .stdout .write (self .style .SUCCESS ("Starting data seeding..."))

        with transaction .atomic ():

            domains =self .create_domains ()

            admin_user =self .create_admin_user ()

            club_types =self .create_club_types (admin_user )

            managers =self .create_managers ()

            clubs =self .create_clubs (managers ,club_types )

            member_users =self .create_member_users ()

            self .add_members_to_clubs (clubs ,member_users )
            self .create_club_invitations (clubs ,member_users )

            all_users =managers +member_users 

            for user in all_users :
                self .create_missions_and_goals (user ,domains )

            main_member =next (
            (user for user in member_users if user .email ==MEMBER_EMAIL ),None 
            )
            if main_member :
                self .create_complete_member_missions (main_member ,domains )

            if options .get ("historical"):
                self .stdout .write (
                self .style .SUCCESS ("Creating historical data for charts...")
                )
                self .create_historical_missions_data (all_users ,domains )

        self .stdout .write (self .style .SUCCESS ("Data seeding completed successfully!"))

    def flush_data (self ):
        """Flush existing data"""
        self .stdout .write (self .style .WARNING ("Flushing existing data..."))

        DailyGoalProgress .objects .all ().delete ()
        DailyGoal .objects .all ().delete ()
        WeeklyMission .objects .all ().delete ()
        Goal .objects .all ().delete ()
        WeeklyProgress .objects .all ().delete ()
        SixWeekPeriod .objects .all ().delete ()

        ClubInvitation .objects .all ().delete ()
        Club .objects .all ().delete ()
        ClubType .objects .all ().delete ()

        Domain .objects .all ().delete ()

        self .stdout .write (self .style .SUCCESS ("Data flushed successfully!"))

    def create_domains (self ):
        """Create domains if they don't exist"""
        self .stdout .write ("Creating domains...")
        domains =[]

        for i ,domain_data in enumerate (DOMAINS ):
            domain ,created =Domain .objects .get_or_create (
            name =domain_data ["name"],
            defaults ={
            "category":domain_data ["category"],
            "description":domain_data ["description"],
            "order":i ,
            },
            )
            domains .append (domain )

            if created :
                self .stdout .write (f"  Created domain: {domain.name}")

        return domains 

    def create_admin_user (self ):
        """Create admin user if it doesn't exist"""
        self .stdout .write ("Creating admin user...")

        admin ,created =User .objects .get_or_create (
        email =ADMIN_EMAIL ,
        defaults ={
        "username":"admin",
        "fullname":"إدارة النظام الرئيسية",
        "is_staff":True ,
        "is_superuser":True ,
        "is_active":True ,
        "is_email_verified":True ,
        "role":"admin",
        },
        )

        if created :
            admin .set_password (DEFAULT_PASSWORD )
            admin .save ()
            self .stdout .write (
            self .style .SUCCESS (f"  Created admin user: {admin.email}")
            )
        else :
            self .stdout .write (f"  Admin user already exists: {admin.email}")

        return admin 

    def create_member_users (self ):
        """Create member users if they don't exist"""
        self .stdout .write ("Creating member users...")
        members =[]


        main_member ,created =User .objects .get_or_create (
        email =MEMBER_EMAIL ,
        defaults ={
        "username":"main_member",
        "fullname":"محمد أحمد الرئيسي",
        "is_active":True ,
        "is_email_verified":True ,
        "role":"member",
        },
        )

        if created :
            main_member .set_password (DEFAULT_PASSWORD )
            main_member .save ()
            self .stdout .write (
            self .style .SUCCESS (f"  Created main member user: {main_member.email}")
            )
        else :
            self .stdout .write (f"  Main member user already exists: {main_member.email}")

        members .append (main_member )


        used_names ={main_member .fullname }
        used_usernames ={main_member .username }

        for i in range (1 ,NUM_MEMBERS ):

            available_names =[name for name in ALL_ARABIC_NAMES if name not in used_names ]
            if not available_names :

                fullname =f"عضو النادي {i}"
            else :
                fullname =random .choice (available_names )
                used_names .add (fullname )


            available_usernames =[username for username in REALISTIC_USERNAMES if username not in used_usernames ]
            if not available_usernames :

                username =f"member{i}"
            else :
                username =random .choice (available_usernames )
                used_usernames .add (username )

            email =f"member{i}@member.com"

            member ,created =User .objects .get_or_create (
            email =email ,
            defaults ={
            "username":username ,
            "fullname":fullname ,
            "is_active":True ,
            "is_email_verified":True ,
            "role":"member",
            },
            )

            if created :
                member .set_password (DEFAULT_PASSWORD )
                member .save ()
                self .stdout .write (
                self .style .SUCCESS (f"  Created member user: {member.email} ({fullname})")
                )
            else :
                self .stdout .write (f"  Member user already exists: {member.email}")

            members .append (member )

        return members 

    def create_club_types (self ,creator ):
        """Create club types"""
        self .stdout .write ("Creating club types...")
        club_types =[]

        for i in range (min (NUM_CLUB_TYPES ,len (CLUB_TYPES ))):
            club_type ,created =ClubType .objects .get_or_create (
            name =CLUB_TYPES [i ],
            defaults ={
            "created_by":creator ,
            "visibility":ClubType .VISIBILITY_PUBLIC ,
            },
            )
            club_types .append (club_type )

            if created :
                self .stdout .write (f"  Created club type: {club_type.name}")

        return club_types 

    def create_managers (self ):
        """Create managers (one for each club we want)"""
        self .stdout .write ("Creating managers...")
        managers =[]


        used_names =set ()
        used_usernames =set ()

        for i in range (min (NUM_CLUBS ,len (CLUB_NAMES ))):

            available_names =[name for name in ALL_ARABIC_NAMES if name not in used_names ]
            if not available_names :

                fullname =f"مدير النادي {i}"
            else :
                fullname =random .choice (available_names )
                used_names .add (fullname )


            available_usernames =[username for username in REALISTIC_USERNAMES if username not in used_usernames ]
            if not available_usernames :

                username =f"manager{i}"
            else :
                username =random .choice (available_usernames )
                used_usernames .add (username )


            title =random .choice (MANAGER_TITLES )
            professional_name =f"{title} {fullname}"

            email =f"manager{i}@manager.com"
            manager ,created =User .objects .get_or_create (
            email =email ,
            defaults ={
            "username":username ,
            "fullname":professional_name ,
            "is_active":True ,
            "is_email_verified":True ,
            "role":"club_manager",
            },
            )

            if created :
                manager .set_password (DEFAULT_PASSWORD )
                manager .save ()
                self .stdout .write (
                self .style .SUCCESS (f"  Created manager user: {manager.email} ({professional_name})")
                )
            else :
                self .stdout .write (f"  Manager user already exists: {manager.email}")

            managers .append (manager )

        return managers 

    def create_clubs (self ,managers ,club_types ):
        """Create clubs"""
        self .stdout .write ("Creating clubs...")
        clubs =[]

        for i in range (min (NUM_CLUBS ,len (CLUB_NAMES ))):
            club_name =CLUB_NAMES [i ]
            club_type =random .choice (club_types )

            club ,created =Club .objects .get_or_create (
            name =club_name ,
            defaults ={
            "type":club_type ,
            "manager":managers [i ],
            "privacy":random .choice ([Club .PRIVACY_CLOSED ,Club .PRIVACY_OPEN ]),
            "join_permissions":random .choice (
            [Club .JOIN_PERMISSIONS_OPEN ,Club .JOIN_PERMISSIONS_INVITE ]
            ),
            },
            )

            if created :
                club .members .add (managers [i ])
                self .stdout .write (f"  Created club: {club.name}")
            else :
                self .stdout .write (f"  Club already exists: {club.name}")

            clubs .append (club )

        return clubs 

    def add_members_to_clubs (self ,clubs ,members ):
        """Add members to clubs"""
        self .stdout .write ("Adding members to clubs...")

        for club in clubs :
            num_members =random .randint (1 ,len (members ))
            selected_members =random .sample (members ,num_members )

            for member in selected_members :
                if member not in club .members .all ():
                    club .members .add (member )
                    self .stdout .write (f"  Added {member.username} to {club.name}")

    def create_club_invitations (self ,clubs ,members ):
        """Create club invitations"""
        self .stdout .write ("Creating club invitations...")

        for club in clubs :
            non_members =[m for m in members if m not in club .members .all ()]

            if non_members :
                num_invites =random .randint (0 ,len (non_members ))
                invited_members =random .sample (non_members ,num_invites )

                for member in invited_members :
                    _ ,created =ClubInvitation .objects .get_or_create (
                    club =club ,
                    email =member .email ,
                    defaults ={
                    "user":member ,
                    "sender":club .manager ,
                    "status":ClubInvitation .STATUS_PENDING ,
                    },
                    )

                    if created :
                        self .stdout .write (
                        f"  Created invitation for {member.username} to {club.name}"
                        )

    def create_missions_and_goals (self ,user ,domains ):
        """Create missions and goals for a user"""
        self .stdout .write (f"Creating missions and goals for {user.username}...")

        start_date =timezone .now ().date ()
        end_date =start_date +datetime .timedelta (days =42 )

        period ,created =SixWeekPeriod .objects .get_or_create (
        user =user ,
        title =f"{user.username}'s Six Week Challenge",
        defaults ={
        "description":"A six-week period to achieve personal goals",
        "start_date":start_date ,
        "end_date":end_date ,
        "status":SixWeekPeriod .STATUS_IN_PROGRESS ,
        "is_public":True ,
        },
        )

        if created :
            self .stdout .write (f"  Created six-week period: {period.title}")

            for week in range (1 ,7 ):
                WeeklyProgress .objects .create (
                six_week_period =period ,
                week_number =week ,
                reflection ="Initial reflection for the week",
                challenges ="Challenges to overcome",
                learnings ="Things I've learned",
                next_week_plan ="Plan for next week",
                )

            goals =[]
            for i in range (NUM_MISSIONS_PER_USER ):
                goal =Goal .objects .create (
                user =user ,
                six_week_period =period ,
                title =f"Goal {i+1}: {random.choice(MISSION_TITLES)}",
                priority =random .choice ([Goal .PRIORITY_HIGH ,Goal .PRIORITY_LOW ]),
                )
                goals .append (goal )
                self .stdout .write (f"  Created goal: {goal.title}")

            for week in range (1 ,7 ):
                domain =random .choice (domains )
                practice_days =random .sample (range (1 ,8 ),random .randint (3 ,5 ))

                daily_reminder =random .choice ([True ,False ])
                reminder_time =(
                datetime .time (hour =9 ,minute =0 )if daily_reminder else None 
                )

                mission =WeeklyMission .objects .create (
                user =user ,
                six_week_period =period ,
                domain =domain ,
                week_number =week ,
                practice_days =practice_days ,
                daily_reminder =daily_reminder ,
                reminder_time =reminder_time ,
                )

                mission_goals =random .sample (goals ,random .randint (1 ,len (goals )))
                mission .goals .add (*mission_goals )

                self .stdout .write (f"  Created weekly mission for week {week}")

                used_titles =set ()

                for j in range (NUM_DAILY_GOALS_PER_MISSION ):
                    while True :
                        title =(
                        f"{random.choice(DAILY_GOAL_TITLES)} {j+1} (Week {week})"
                        )
                        if title not in used_titles :
                            used_titles .add (title )
                            break 

                    daily_goal =DailyGoal .objects .create (
                    weekly_mission =mission ,
                    title =title ,
                    priority =random .choice (
                    [DailyGoal .PRIORITY_HIGH ,DailyGoal .PRIORITY_LOW ]
                    ),
                    )

                    for day_number in practice_days :
                        progress =DailyGoalProgress .objects .create (
                        daily_goal =daily_goal ,
                        day_number =day_number ,
                        completed =random .choice ([True ,False ]),
                        )

                        if progress .completed :
                            progress .completion_date =(
                            timezone .now ()
                            -datetime .timedelta (days =random .randint (1 ,7 ))
                            )
                            progress .save ()

                    if daily_goal .is_completed ():
                        daily_goal .completion_date =(
                        timezone .now ()
                        -datetime .timedelta (days =random .randint (1 ,7 ))
                        )
                        daily_goal .save ()

                    self .stdout .write (f"    Created daily goal: {daily_goal.title}")
        else :
            self .stdout .write (f"  Six-week period already exists for {user.username}")

    def create_complete_member_missions (self ,user ,domains ):
        """Create a complete and realistic set of 6 weekly missions for the member user"""
        self .stdout .write (
        f"Creating comprehensive missions and goals for member {user.username}..."
        )

        start_date =timezone .now ().date ()
        end_date =start_date +datetime .timedelta (days =42 )


        user_display_name =getattr (user ,'fullname',None )or user .username 

        period =SixWeekPeriod .objects .create (
        user =user ,
        title =f"رحلة النمو لمدة 6 أسابيع - {user_display_name}",
        description ="فترة منظمة لمدة ستة أسابيع لتحقيق أهداف النمو الشخصي",
        start_date =start_date ,
        end_date =end_date ,
        status =SixWeekPeriod .STATUS_IN_PROGRESS ,
        is_public =True ,
        )

        self .stdout .write (f"  Created six-week period: {period.title}")

        for week in range (1 ,7 ):
            WeeklyProgress .objects .create (
            six_week_period =period ,
            week_number =week ,
            reflection =f"تأمل الأسبوع {week} حول التقدم والتحديات",
            challenges =f"العقبات التي واجهتها خلال الأسبوع {week}",
            learnings =f"الرؤى الرئيسية المكتسبة في الأسبوع {week}",
            next_week_plan =f"خطة التحسين للأسبوع {week+1 if week < 6 else 1}",
            )

        main_goals =[
        {"title":"تطوير عادة القراءة اليومية","priority":Goal .PRIORITY_HIGH },
        {"title":"تحسين اللياقة البدنية","priority":Goal .PRIORITY_HIGH },
        {"title":"إتقان مهارات مهنية جديدة","priority":Goal .PRIORITY_HIGH },
        {"title":"تعلم لغة جديدة","priority":Goal .PRIORITY_LOW },
        {"title":"ممارسة تأمل اليقظة","priority":Goal .PRIORITY_LOW },
        {
        "title":"بناء روتين كتابة ثابت",
        "priority":Goal .PRIORITY_LOW ,
        },
        ]

        goals =[]
        for i ,goal_data in enumerate (main_goals ):
            goal =Goal .objects .create (
            user =user ,
            six_week_period =period ,
            title =goal_data ["title"],
            priority =goal_data ["priority"],
            )
            goals .append (goal )
            self .stdout .write (f"  Created goal: {goal.title}")

        domain_mapping ={}
        available_domains =list (domains )
        for i in range (min (len (main_goals ),len (available_domains ))):
            domain_mapping [main_goals [i ]["title"]]=available_domains [i ]

        for week in range (1 ,7 ):
            primary_goals =random .sample (goals ,min (3 ,len (goals )))

            primary_goal =primary_goals [0 ]
            domain =domain_mapping .get (primary_goal .title ,random .choice (domains ))

            base_practice_days =[1 ,2 ,4 ,5 ]
            if week >3 :
                base_practice_days .extend ([6 ,7 ])
            practice_days =base_practice_days 

            mission =WeeklyMission .objects .create (
            user =user ,
            six_week_period =period ,
            domain =domain ,
            week_number =week ,
            practice_days =practice_days ,
            daily_reminder =True ,
            reminder_time =datetime .time (hour =8 ,minute =0 ),
            )

            mission .goals .add (*primary_goals )

            if week <=3 :
                mission .mark_completed ()
                if week <=2 :
                    WeeklySuccessRating .objects .create (
                    weekly_mission =mission ,
                    rating =7 +week ,
                    )

            self .stdout .write (f"  Created weekly mission for week {week}")

            daily_goal_templates =[
            {
            "base_title":"التدرب لمدة 30 دقيقة",
            "priority":DailyGoal .PRIORITY_HIGH ,
            },
            {
            "base_title":"إكمال التمارين اليومية",
            "priority":DailyGoal .PRIORITY_HIGH ,
            },
            {
            "base_title":"مراجعة التقدم والتأمل",
            "priority":DailyGoal .PRIORITY_LOW ,
            },
            ]

            completion_rate =min (0.95 ,0.6 +(week /10 ))

            for j ,template in enumerate (daily_goal_templates ):
                daily_goal =DailyGoal .objects .create (
                weekly_mission =mission ,
                title =f"{template['base_title']} - الأسبوع {week}",
                priority =template ["priority"],
                )

                for day_number in practice_days :
                    day_factor =1 -(day_number /10 )
                    completed =random .random ()<(completion_rate +day_factor )

                    if week <=3 :
                        completed =random .random ()<(0.7 +(week /10 ))
                        completion_date =None 
                        if completed :
                            day_offset =(week -1 )*7 +day_number -1 
                            completion_date =start_date +datetime .timedelta (
                            days =-30 +day_offset 
                            )
                    else :
                        completion_date =None 

                    progress =DailyGoalProgress .objects .create (
                    daily_goal =daily_goal ,
                    day_number =day_number ,
                    completed =completed ,
                    completion_date =completion_date ,
                    )

                self .stdout .write (f"    Created daily goal: {daily_goal.title}")

    def create_historical_missions_data (self ,users ,domains ):
        """Create historical missions and goals data across multiple months"""
        self .stdout .write ("Creating historical missions data across previous months...")

        current_date =timezone .now ()
        start_month =timezone .datetime (2024 ,6 ,1 ,tzinfo =timezone .utc )

        if current_date .date ()<start_month .date ():
            start_date =current_date -relativedelta (months =12 )
            end_date =current_date 
        else :
            start_date =start_month 
            end_date =timezone .datetime (2025 ,5 ,31 ,tzinfo =timezone .utc )

        month_ranges =[]
        current_month =start_date 
        while current_month <=end_date :
            month_end =current_month +relativedelta (months =1 )-relativedelta (days =1 )
            month_ranges .append ((current_month ,month_end ))
            current_month +=relativedelta (months =1 )

        for user in users :
            self .stdout .write (f"  Creating historical data for user {user.username}")

            for domain in domains :
                base_completion_rate =random .uniform (0.3 ,0.9 )

                for i ,(month_start ,month_end )in enumerate (month_ranges ):
                    period_start =month_start 
                    period_end =period_start +datetime .timedelta (days =42 )

                    period_title =(
                    f"{user.username}'s Challenge - {month_start.strftime('%B %Y')}"
                    )

                    existing_period =SixWeekPeriod .objects .filter (
                    user =user ,title =period_title 
                    ).first ()

                    if existing_period :
                        period =existing_period 
                        self .stdout .write (f"    Using existing period: {period.title}")
                    else :
                        period =SixWeekPeriod .objects .create (
                        user =user ,
                        title =period_title ,
                        description =f"Goals for {month_start.strftime('%B %Y')}",
                        start_date =period_start .date (),
                        end_date =period_end .date (),
                        status =SixWeekPeriod .STATUS_IN_PROGRESS ,
                        is_public =True ,
                        created =period_start ,
                        )
                        self .stdout .write (f"    Created period: {period.title}")

                    month_progress_factor =i /len (month_ranges )
                    completion_rate =min (
                    0.95 ,base_completion_rate +(month_progress_factor *0.4 )
                    )

                    goals =[]

                    existing_goal_titles =set (
                    Goal .objects .filter (six_week_period =period ).values_list (
                    "title",flat =True 
                    )
                    )

                    for j in range (random .randint (3 ,6 )):
                        while True :
                            goal_title =f"Goal {j+1}: {random.choice(MISSION_TITLES)} - {month_start.strftime('%b')} {uuid.uuid4().hex[:4]}"
                            if goal_title not in existing_goal_titles :
                                break 

                        goal =Goal .objects .create (
                        user =user ,
                        six_week_period =period ,
                        title =goal_title ,
                        priority =random .choice (
                        [Goal .PRIORITY_HIGH ,Goal .PRIORITY_LOW ]
                        ),
                        created =month_start 
                        +datetime .timedelta (days =random .randint (0 ,5 )),
                        )
                        goals .append (goal )
                        existing_goal_titles .add (goal_title )

                    for week in range (1 ,7 ):
                        if random .random ()<0.1 :
                            continue 

                        week_start =month_start +datetime .timedelta (
                        days =(week -1 )*7 
                        )
                        practice_days =random .sample (range (1 ,8 ),random .randint (3 ,5 ))

                        mission =WeeklyMission .objects .create (
                        user =user ,
                        six_week_period =period ,
                        domain =domain ,
                        week_number =week ,
                        practice_days =practice_days ,
                        daily_reminder =random .choice ([True ,False ]),
                        reminder_time =datetime .time (hour =9 ,minute =0 ),
                        created =week_start ,
                        )

                        mission_goals =random .sample (
                        goals ,min (random .randint (1 ,3 ),len (goals ))
                        )
                        mission .goals .add (*mission_goals )

                        should_complete =random .random ()<completion_rate 
                        if should_complete :
                            mission .completion_date =week_start +datetime .timedelta (
                            days =random .randint (3 ,6 )
                            )
                            mission .save ()

                        for k in range (random .randint (2 ,4 )):
                            daily_goal_title =f"{random.choice(DAILY_GOAL_TITLES)} {k+1} ({month_start.strftime('%B')} Wk{week} - {uuid.uuid4().hex[:4]})"

                            daily_goal =DailyGoal .objects .create (
                            weekly_mission =mission ,
                            title =daily_goal_title ,
                            priority =random .choice (
                            [DailyGoal .PRIORITY_HIGH ,DailyGoal .PRIORITY_LOW ]
                            ),
                            created =week_start ,
                            )

                            goal_completion_rate =min (
                            0.98 ,completion_rate +random .uniform (0.05 ,0.15 )
                            )

                            for day_number in practice_days :
                                completed =random .random ()<goal_completion_rate 
                                progress =DailyGoalProgress .objects .create (
                                daily_goal =daily_goal ,
                                day_number =day_number ,
                                completed =completed ,
                                )

                                if completed :
                                    completion_day =week_start +datetime .timedelta (
                                    days =day_number -1 
                                    )
                                    progress .completion_date =completion_day 
                                    progress .save ()

                            if daily_goal .is_completed ():
                                daily_goal .completion_date =(
                                week_start +datetime .timedelta (days =6 )
                                )
                                daily_goal .save ()

                        self .stdout .write (
                        f"    Created period for {month_start.strftime('%B %Y')} with {completion_rate:.0%} completion rate"
                        )
