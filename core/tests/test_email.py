from django.test import TestCase, override_settings
from unittest.mock import patch, MagicMock
from smtplib import SMTPEx<PERSON>

from core.email_backends import (
    GmailSMTPBackend,
    MailgunBackend,
    ZohoMailBackend,
    get_email_backend,
)


class EmailBackendTests(TestCase):
    """Test the email backend classes and fallback mechanism."""

    def test_gmail_backend_initialization(self):
        """Test that GmailSMTPBackend can be initialized."""
        with patch("core.email_backends.settings") as mock_settings:
            mock_settings.EMAIL_ICOMING_SERVER = "smtp.gmail.com"
            mock_settings.EMAIL_IMAP_PORT = 587
            mock_settings.EMAIL_ACTIVATION_HOST = "<EMAIL>"
            mock_settings.EMAIL_ACTIVATION_PASSWORD = "password"

            backend = GmailSMTPBackend()
            self.assertEqual(backend.smtp_server, "smtp.gmail.com")
            self.assertEqual(backend.smtp_port, 587)
            self.assertEqual(backend.username, "<EMAIL>")
            self.assertEqual(backend.password, "password")

    def test_mailgun_backend_initialization(self):
        """Test that MailgunBackend can be initialized."""
        with patch("core.email_backends.settings") as mock_settings:
            mock_settings.ANYMAIL = {
                "MAILGUN_API_KEY": "test-key",
                "MAILGUN_SENDER_DOMAIN": "example.com",
            }

            backend = MailgunBackend()
            self.assertEqual(backend.api_key, "test-key")
            self.assertEqual(backend.domain, "example.com")

    def test_zoho_backend_initialization(self):
        """Test that ZohoMailBackend can be initialized."""
        with patch(
            "core.email_backends.ZOHO_EMAIL_SMTP_SERVER", "smtp.zoho.com"
        ), patch("core.email_backends.ZOHO_EMAIL_SMTP_PORT", 587), patch(
            "core.email_backends.ZOHO_EMAIL_USERNAME", "<EMAIL>"
        ), patch(
            "core.email_backends.ZOHO_EMAIL_PASSWORD", "password"
        ), patch(
            "core.email_backends.ZOHO_EMAIL_USE_TLS", True
        ), patch(
            "core.email_backends.ZOHO_EMAIL_SENDER", "<EMAIL>"
        ):
            backend = ZohoMailBackend()
            self.assertEqual(backend.smtp_server, "smtp.zoho.com")
            self.assertEqual(backend.smtp_port, 587)
            self.assertEqual(backend.username, "<EMAIL>")
            self.assertEqual(backend.password, "password")
            self.assertEqual(backend.use_tls, True)
            self.assertEqual(backend.sender, "<EMAIL>")

    def test_fallback_chain(self):
        """Test the fallback chain when backends fail."""

        with patch(
            "core.email_backends.GmailSMTPBackend.__init__", side_effect=ValueError
        ), patch(
            "core.email_backends.MailgunBackend.__init__", side_effect=ValueError
        ), patch(
            "core.email_backends.ZohoMailBackend.__init__", side_effect=ValueError
        ), patch(
            "core.email_backends.logger"
        ) as mock_logger:
            backend = get_email_backend()
            self.assertIsNone(backend)

            mock_logger.error.assert_any_call(
                "GmailSMTPBackend failed, falling back to MailgunBackend: "
            )
            mock_logger.error.assert_any_call(
                "MailgunBackend failed, falling back to ZohoMailBackend: "
            )
            mock_logger.error.assert_any_call(
                "ZohoMailBackend also failed, falling back to Django's send_mail: "
            )

    def test_zoho_backend_sends_email(self):
        """Test that ZohoMailBackend can send emails."""
        with patch("smtplib.SMTP") as mock_smtp:
            mock_server = MagicMock()
            mock_smtp.return_value.__enter__.return_value = mock_server

            backend = ZohoMailBackend(
                smtp_server="smtp.zoho.com",
                smtp_port=587,
                username="<EMAIL>",
                password="password",
                use_tls=True,
                sender="<EMAIL>",
            )

            backend.send_email(
                subject="Test Subject",
                text_content="Test Content",
                from_email="<EMAIL>",
                to_email="<EMAIL>",
                html_content="<p>Test HTML Content</p>",
            )

            mock_server.starttls.assert_called_once()
            mock_server.login.assert_called_once_with("<EMAIL>", "password")
            mock_server.sendmail.assert_called_once()

            args = mock_server.sendmail.call_args[0]
            self.assertEqual(args[0], "<EMAIL>")
            self.assertEqual(args[1], ["<EMAIL>"])

            self.assertIn("Subject: Test Subject", args[2])
            self.assertIn("From: <EMAIL>", args[2])
            self.assertIn("To: <EMAIL>", args[2])

            self.assertIn("Content-Type: text/plain", args[2])
            self.assertIn("Content-Type: text/html", args[2])

            self.assertIn("VGVzdCBDb250ZW50", args[2])

            self.assertIn("PHA+VGVzdCBIVE1MIENvbnRlbnQ8L3A+", args[2])

    def test_fallback_to_zoho(self):
        """Test that the system falls back to Zoho when other backends fail."""

        with patch(
            "core.email_backends.GmailSMTPBackend.__init__", side_effect=ValueError
        ), patch(
            "core.email_backends.MailgunBackend.__init__", side_effect=ValueError
        ), patch.object(
            ZohoMailBackend, "send_email"
        ) as mock_send_email, patch(
            "core.email_backends.logger"
        ) as mock_logger:
            with patch.object(ZohoMailBackend, "__init__", return_value=None):
                backend = get_email_backend()
                self.assertIsInstance(backend, ZohoMailBackend)

                mock_logger.error.assert_any_call(
                    "GmailSMTPBackend failed, falling back to MailgunBackend: "
                )
                mock_logger.error.assert_any_call(
                    "MailgunBackend failed, falling back to ZohoMailBackend: "
                )
