import os
import sys
import django
from datetime import timedelta


os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.development")
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    django.setup()
except Exception as e:
    print(f"Error setting up Django: {e}")
    sys.exit(1)

from django.utils import timezone
from django.contrib.auth import get_user_model
from django.db import transaction
from apps.domains.models import Domain
from apps.okrs.models import SixWeekPeriod
from apps.missions.models import (
    Goal,
    WeeklyMission,
    DailyGoal,
    DailyGoalProgress,
    WeeklySuccessRating,
)

User = get_user_model()


def create_complete_member_missions(user):
    """Create a complete and realistic set of 6 weekly missions for the member user"""
    print(f"Creating comprehensive missions and goals for member {user.username}...")

    domains = []
    domain_data = [
        {
            "name": "Reading",
            "category": "Personal Development",
            "description": "Reading and literature",
        },
        {
            "name": "Physical Fitness",
            "category": "Health",
            "description": "Exercise and physical well-being",
        },
        {
            "name": "Professional Skills",
            "category": "Career",
            "description": "Job-related skills development",
        },
        {
            "name": "Language Learning",
            "category": "Education",
            "description": "Learning new languages",
        },
        {
            "name": "Mindfulness",
            "category": "Mental Health",
            "description": "Meditation and mindfulness practices",
        },
        {
            "name": "Writing",
            "category": "Creative",
            "description": "Writing and creative expression",
        },
    ]

    for i, data in enumerate(domain_data):
        domain, created = Domain.objects.get_or_create(
            name=data["name"],
            defaults={
                "category": data["category"],
                "description": data["description"],
                "order": i,
            },
        )
        domains.append(domain)
        print(f"Domain: {domain.name} ({'created' if created else 'already exists'})")

    start_date = timezone.now().date()
    end_date = start_date + timedelta(days=42)

    with transaction.atomic():
        period = SixWeekPeriod.objects.create(
            user=user,
            title=f"{user.username}'s 6-Week Growth Journey",
            description="A structured six-week period to achieve personal growth goals",
            start_date=start_date,
            end_date=end_date,
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
            is_public=True,
        )

        print(f"Created six-week period: {period.title}")

        main_goals = [
            {"title": "Develop daily reading habit", "priority": Goal.PRIORITY_HIGH},
            {"title": "Improve physical fitness", "priority": Goal.PRIORITY_HIGH},
            {"title": "Master new professional skills", "priority": Goal.PRIORITY_HIGH},
            {"title": "Learn a new language", "priority": Goal.PRIORITY_LOW},
            {"title": "Practice mindfulness meditation", "priority": Goal.PRIORITY_LOW},
            {
                "title": "Build consistent writing routine",
                "priority": Goal.PRIORITY_LOW,
            },
        ]

        goals = []
        for i, goal_data in enumerate(main_goals):
            goal = Goal.objects.create(
                user=user,
                six_week_period=period,
                title=goal_data["title"],
                priority=goal_data["priority"],
            )
            goals.append(goal)
            print(f"Created goal: {goal.title}")

        domain_mapping = {}
        available_domains = list(domains)
        for i in range(min(len(main_goals), len(available_domains))):
            domain_mapping[main_goals[i]["title"]] = available_domains[i]

        for week in range(1, 7):

            primary_goals = goals[:3]

            primary_goal = primary_goals[0]
            domain = domain_mapping.get(primary_goal.title, domains[0])

            base_practice_days = [1, 2, 4, 5]
            if week > 3:
                base_practice_days.extend([6, 7])
            practice_days = base_practice_days

            mission = WeeklyMission.objects.create(
                user=user,
                six_week_period=period,
                domain=domain,
                week_number=week,
                practice_days=practice_days,
                daily_reminder=True,
                reminder_time=timezone.datetime.now().time(),
            )

            mission.goals.add(*primary_goals)

            if week <= 3:
                mission.mark_completed()
                if week <= 2:

                    WeeklySuccessRating.objects.create(
                        weekly_mission=mission,
                        rating=7 + week,
                        notes=f"Week {week} went well, making good progress",
                    )

            print(f"Created weekly mission for week {week}")

            daily_goal_templates = [
                {
                    "base_title": "Practice for 30 minutes",
                    "priority": DailyGoal.PRIORITY_HIGH,
                },
                {
                    "base_title": "Complete daily exercises",
                    "priority": DailyGoal.PRIORITY_HIGH,
                },
                {
                    "base_title": "Review progress and reflect",
                    "priority": DailyGoal.PRIORITY_LOW,
                },
            ]

            completion_rate = min(0.95, 0.6 + (week / 10))

            for j, template in enumerate(daily_goal_templates):
                daily_goal = DailyGoal.objects.create(
                    weekly_mission=mission,
                    title=f"{template['base_title']} - Week {week}",
                    priority=template["priority"],
                )

                for day_number in practice_days:

                    if week <= 3:
                        completed = True if day_number < 4 else False
                        completion_date = None
                        if completed:
                            day_offset = (week - 1) * 7 + day_number - 1
                            completion_date = start_date - timedelta(
                                days=30 - day_offset
                            )
                    else:
                        completed = False
                        completion_date = None

                    progress = DailyGoalProgress.objects.create(
                        daily_goal=daily_goal,
                        day_number=day_number,
                        completed=completed,
                        completion_date=completion_date,
                    )

                print(f"Created daily goal: {daily_goal.title}")

    print(f"Successfully created all missions and goals for user {user.username}")
    return period


def print_mission_stats():
    """Print statistics about created missions"""
    try:
        total_missions = WeeklyMission.objects.count()
        total_goals = Goal.objects.count()
        total_daily_goals = DailyGoal.objects.count()
        total_progress = DailyGoalProgress.objects.count()

        print("\n--- Mission Statistics ---")
        print(f"Total WeeklyMissions: {total_missions}")
        print(f"Total Goals: {total_goals}")
        print(f"Total DailyGoals: {total_daily_goals}")
        print(f"Total DailyGoalProgress entries: {total_progress}")

        completed_missions = WeeklyMission.objects.filter(
            completion_date__isnull=False
        ).count()
        print(f"Completed missions: {completed_missions}")

        rated_missions = (
            WeeklyMission.objects.filter(success_ratings__isnull=False)
            .distinct()
            .count()
        )
        print(f"Missions with success ratings: {rated_missions}")

        if User.objects.filter(username="testuser").exists():
            user = User.objects.get(username="testuser")
            missions = WeeklyMission.objects.filter(user=user).order_by("week_number")

            print(f"\nMissions for {user.username}:")
            for mission in missions:
                daily_goals = DailyGoal.objects.filter(weekly_mission=mission).count()
                progress_entries = DailyGoalProgress.objects.filter(
                    daily_goal__weekly_mission=mission
                ).count()
                completed_progress = DailyGoalProgress.objects.filter(
                    daily_goal__weekly_mission=mission, completed=True
                ).count()

                print(
                    f"Week {mission.week_number}: {mission.domain.name} - "
                    + f"{'Completed' if mission.completion_date else 'In Progress'} - "
                    + f"Daily Goals: {daily_goals}, Progress Entries: {progress_entries}, "
                    + f"Completed Entries: {completed_progress}"
                )

    except Exception as e:
        print(f"Error getting mission stats: {e}")


if __name__ == "__main__":
    try:

        username = "testuser"
        email = "<EMAIL>"

        user, created = User.objects.get_or_create(
            username=username,
            defaults={
                "email": email,
                "fullname": "Test User",
                "is_active": True,
                "is_email_verified": True,
                "role": "member",
            },
        )

        if created:
            user.set_password("testpassword")
            user.save()
            print(f"Created test user: {username}")
        else:
            print(f"Using existing user: {username}")

        period = create_complete_member_missions(user)

        print_mission_stats()

        print("\nSuccess! Test completed.")

    except Exception as e:
        print(f"Error running test: {e}")
