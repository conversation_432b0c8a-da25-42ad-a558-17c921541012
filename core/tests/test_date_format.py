import os
import django
from django.urls import resolve, reverse


os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.development")
django.setup()

from apps.stats.filters import TimeSeriesFilter
from django.utils import timezone

print("Testing TimeSeriesFilter date formats:")


months_filter = TimeSeriesFilter({"format": "months"})
periods, labels = months_filter.generate_time_periods()
print("Months format labels:", labels)


weeks_filter = TimeSeriesFilter({"format": "weeks"})
periods, labels = weeks_filter.generate_time_periods()
print("Weeks format labels:", labels)


days_filter = TimeSeriesFilter({"format": "days"})
periods, labels = days_filter.generate_time_periods()
print("Days format labels:", labels)


try:
    url = reverse("stats:charts-users")
    print("\nURL for users chart:", url)
    resolver = resolve(url)
    print("View function:", resolver.func.__name__)
    print("Namespace:", resolver.namespace)
    print("URL name:", resolver.url_name)
except Exception as e:
    print("\nError resolving URL:", str(e))
