from django.test import TestCase
from rest_framework.exceptions import ValidationError, AuthenticationFailed, NotFound
from rest_framework.test import APIRequestFactory
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from core.exceptions import custom_exception_handler


class TestView(APIView):
    def get(self, request, *args, **kwargs):
        raise ValidationError(
            {"non_field_errors": ["A mission for week 1 already exists in this period"]}
        )


class FieldErrorTestView(APIView):
    def get(self, request, *args, **kwargs):
        raise ValidationError(
            {"practice_days": ["Practice days must be integers between 1 and 7"]}
        )


class ListErrorTestView(APIView):
    def get(self, request, *args, **kwargs):
        raise ValidationError(["Invalid input data"])


class StringErrorTestView(APIView):
    def get(self, request, *args, **kwargs):
        raise ValidationError("Simple error message")


class NotFoundTestView(APIView):
    def get(self, request, *args, **kwargs):
        raise NotFound("Resource not found")


class AuthFailedTestView(APIView):
    def get(self, request, *args, **kwargs):
        raise AuthenticationFailed("Authentication credentials were not provided")


class CustomExceptionHandlerTests(TestCase):
    def setUp(self):
        self.factory = APIRequestFactory()

    def test_non_field_errors_flattening(self):
        """Test that non_field_errors are flattened to a single error message."""
        view = TestView.as_view()
        request = self.factory.get("/")
        response = view(request)
        response.data = custom_exception_handler(
            ValidationError(
                {
                    "non_field_errors": [
                        "A mission for week 1 already exists in this period"
                    ]
                }
            ),
            {"view": view, "request": request},
        ).data

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data,
            {"error": "A mission for week 1 already exists in this period"},
        )
        self.assertNotIn("non_field_errors", response.data)

    def test_field_errors_flattening(self):
        """Test that field errors are flattened to a single error message."""
        view = FieldErrorTestView.as_view()
        request = self.factory.get("/")
        response = view(request)
        response.data = custom_exception_handler(
            ValidationError(
                {"practice_days": ["Practice days must be integers between 1 and 7"]}
            ),
            {"view": view, "request": request},
        ).data

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data, {"error": "Practice days must be integers between 1 and 7"}
        )
        self.assertNotIn("practice_days", response.data)

    def test_list_errors_flattening(self):
        """Test that list errors are flattened to a single error message."""
        view = ListErrorTestView.as_view()
        request = self.factory.get("/")
        response = view(request)
        response.data = custom_exception_handler(
            ValidationError(["Invalid input data"]), {"view": view, "request": request}
        ).data

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data, {"error": "Invalid input data"})

    def test_string_error_handling(self):
        """Test that string errors are properly formatted."""
        view = StringErrorTestView.as_view()
        request = self.factory.get("/")
        response = view(request)
        response.data = custom_exception_handler(
            ValidationError("Simple error message"), {"view": view, "request": request}
        ).data

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data, {"error": "Simple error message"})

    def test_not_found_handling(self):
        """Test that NotFound exceptions are properly formatted."""
        view = NotFoundTestView.as_view()
        request = self.factory.get("/")
        response = view(request)
        response.data = custom_exception_handler(
            NotFound("Resource not found"), {"view": view, "request": request}
        ).data

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data, {"error": "Resource not found"})

    def test_authentication_failed_handling(self):
        """Test that AuthenticationFailed exceptions are properly formatted."""
        view = AuthFailedTestView.as_view()
        request = self.factory.get("/")
        response = view(request)
        response.data = custom_exception_handler(
            AuthenticationFailed("Authentication credentials were not provided"),
            {"view": view, "request": request},
        ).data

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertEqual(
            response.data, {"error": "Authentication credentials were not provided"}
        )

    def test_mission_duplicate_week_error(self):
        """Test the specific case of a duplicate mission week error."""
        view = TestView.as_view()
        request = self.factory.get("/")
        response = view(request)
        response.data = custom_exception_handler(
            ValidationError(
                {
                    "non_field_errors": [
                        "A mission for week 1 already exists in this period"
                    ]
                }
            ),
            {"view": view, "request": request},
        ).data

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data,
            {"error": "A mission for week 1 already exists in this period"},
        )
