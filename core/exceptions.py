from rest_framework.exceptions import (
    NotFound,
    AuthenticationFailed,
    ValidationError,
)
from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status
from rest_framework.authentication import (
    TokenAuthentication as BaseTokenAuthentication,
)


class CustomTokenAuthentication(BaseTokenAuthentication):
    def authenticate(self, request):
        result = super().authenticate(request)
        if not result:
            raise AuthenticationFailed(
                {"error": "Authentication credentials were not provided."}
            )
        return result


def get_object_or_404(model, **kwargs):
    message = kwargs.pop("message", None)
    try:
        return model.objects.get(**kwargs)
    except model.DoesNotExist:
        raise NotFound("Object not found." if not message else message)


def custom_exception_handler(exc, context):
    response = exception_handler(exc, context)
    if isinstance(exc, AuthenticationFailed):
        custom_response_data = {"error": exc.detail}
        return Response(custom_response_data, status=status.HTTP_401_UNAUTHORIZED)

    if isinstance(exc, ValidationError):
        if isinstance(exc.detail, dict):
            if "non_field_errors" in exc.detail:
                error_msg = exc.detail["non_field_errors"][0]

            else:
                for field, errors in exc.detail.items():
                    if isinstance(errors, list) and errors:
                        error_msg = errors[0]
                        break
                else:
                    error_msg = str(exc.detail)

        elif isinstance(exc.detail, list) and exc.detail:
            error_msg = exc.detail[0]

        else:
            error_msg = str(exc.detail)

        custom_response_data = {"error": error_msg}
        return Response(custom_response_data, status=status.HTTP_400_BAD_REQUEST)

    if isinstance(exc, NotFound):
        custom_response_data = {"error": exc.detail}
        return Response(custom_response_data, status=status.HTTP_404_NOT_FOUND)

    if response is not None:
        if isinstance(response.data, dict):
            if "detail" in response.data:
                custom_response_data = {"error": response.data["detail"]}
                response.data = custom_response_data

            elif any(key.endswith("_errors") for key in response.data.keys()):
                for key, errors in response.data.items():
                    if key.endswith("_errors") and errors:
                        custom_response_data = {"error": errors[0]}
                        response.data = custom_response_data
                        break

    return response
