from urllib.parse import parse_qs
import jwt
from channels.auth import AuthMiddlewareStack
from channels.middleware import BaseMiddleware
from django.conf import settings
from django.contrib.auth.models import AnonymousUser
from asgiref.sync import sync_to_async
from apps.accounts.user.models import User


@sync_to_async
def get_user_by_id(user_id):
    try:
        return User.objects.get(id=user_id)
    except User.DoesNotExist:
        return None


class JWTAuthMiddleware(BaseMiddleware):
    async def __call__(self, scope, receive, send):

        query_string = parse_qs(scope["query_string"].decode())
        token = query_string.get("token", [None])[0]

        if token:
            try:

                payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])

                user = await get_user_by_id(payload["user_id"])
                scope["user"] = user if user else AnonymousUser()
            except (jwt.ExpiredSignatureError, jwt.InvalidTokenError):
                scope["user"] = AnonymousUser()
        else:
            scope["user"] = AnonymousUser()

        return await super().__call__(scope, receive, send)


def JWTAuthMiddlewareStack(inner):
    return JWTAuthMiddleware(AuthMiddlewareStack(inner))
