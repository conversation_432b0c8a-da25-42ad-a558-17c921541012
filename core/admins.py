from django.contrib import admin
from django.http import HttpRequest
from django.contrib.auth.models import User


class StaffAdminModel(admin.ModelAdmin):
    def has_add_permission(self, request: HttpRequest) -> bool:
        user: User = request.user if request.user.is_authenticated else None
        return (
            super().has_add_permission(request)
            if user is None
            else (user.is_staff and user.is_authenticated)
        )

    def has_change_permission(self, request: HttpRequest, obj=None) -> bool:
        user: User = request.user if request.user.is_authenticated else None
        return (
            super().has_change_permission(request, obj)
            if user is None
            else (user.is_staff and user.is_authenticated)
        )

    def has_delete_permission(self, request: HttpRequest, obj=None) -> bool:
        user: User = request.user if request.user.is_authenticated else None
        return (
            super().has_delete_permission(request, obj)
            if user is None
            else (user.is_staff and user.is_authenticated)
        )

    def has_module_permission(self, request: HttpRequest) -> bool:
        user: User = request.user if request.user.is_authenticated else None
        return (
            super().has_module_permission(request)
            if user is None
            else (user.is_staff and user.is_authenticated)
        )

    def has_view_permission(self, request: HttpRequest, obj=None) -> bool:
        user: User = request.user if request.user.is_authenticated else None
        return (
            super().has_view_permission(request, obj)
            if user is None
            else (user.is_staff and user.is_authenticated)
        )

    class Meta:
        abstract = True
        managed = False
        app_label = "core"
