from rest_framework.pagination import (
    LimitOffsetPagination,
    PageNumberPagination,
)
from rest_framework.response import Response
from rest_framework.exceptions import ValidationError
from rest_framework import status


class MetaLimitOffsetPagination(LimitOffsetPagination):
    default_limit = 5
    limit_query_param = "limit"
    max_limit = 1000
    offset_query_param = "offset"
    page_size_query_param = "page_size"

    def get_paginated_response(self, data):
        current_offset = self.offset
        current_limit = self.get_limit(self.request)

        page_size = int(self.request.query_params.get("page_size", current_limit))
        current_limit = min(max(0, page_size), self.max_limit)

        total_count = self.count

        current_page_number = (
            (current_offset // current_limit + 1) if current_limit > 0 else 1
        )
        total_pages = (
            (total_count // current_limit)
            + (1 if total_count % current_limit != 0 else 0)
            if current_limit > 0
            else 1
        )
        next_page_number = (
            current_page_number + 1
            if current_offset + current_limit < total_count
            else None
        )
        previous_page_number = current_page_number - 1 if current_offset > 0 else None

        return Response(
            {
                "meta": {
                    "next": self.get_next_link(),
                    "previous": self.get_previous_link(),
                    "next_page_number": next_page_number,
                    "previous_page_number": previous_page_number,
                    "limit": current_limit,
                    "offset": current_offset,
                    "count": total_count,
                    "total_pages": total_pages,
                    "current_page_number": current_page_number,
                },
                "results": data,
            }
        )


class MetaPageNumberPagination(PageNumberPagination):
    page_size = 5
    page_query_param = "page"
    page_size_query_param = "limit"
    max_page_size = 100

    def get_page_size(self, request):
        try:
            page_size = int(
                request.query_params.get(self.page_size_query_param, self.page_size)
            )
            if page_size <= 0:
                raise ValidationError(
                    {"limit": "Page size must be a positive integer."}
                )
            if page_size > self.max_page_size:
                raise ValidationError(
                    {"limit": f"Page size cannot be greater than {self.max_page_size}"}
                )
            return page_size
        except (TypeError, ValueError):
            raise ValidationError({"limit": "Invalid page size value."})

    def get_paginated_response(self, data):
        return Response(
            {
                "meta": {
                    "count": self.page.paginator.count,
                    "current_page_number": self.page.number,
                    "limit": self.get_page_size(self.request),
                    "next": self.get_next_link(),
                    "previous": self.get_previous_link(),
                    "next_page_number": (
                        self.page.next_page_number() if self.page.has_next() else None
                    ),
                    "previous_page_number": (
                        self.page.previous_page_number()
                        if self.page.has_previous()
                        else None
                    ),
                    "total_pages": self.page.paginator.num_pages,
                },
                "results": data,
            }
        )
