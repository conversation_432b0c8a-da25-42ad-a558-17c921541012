from django.db import models
from django.utils import timezone
import uuid

from .managers import (
    AbstractManager,
    AbstractSlugManager,
    AbstractTimestampManager,
)
from core.utilities.slugify import SlugUtil


class AbstarctModelTimeStamp(models.Model):
    created = models.DateTimeField(default=timezone.now)
    updated = models.DateTimeField(auto_now=True)

    objects = AbstractTimestampManager()

    class Meta:
        abstract = True


class AbstractModel(AbstarctModelTimeStamp):
    """
    An abstract model with a id field.
    """

    id = models.UUIDField(
        db_index=True,
        unique=True,
        default=uuid.uuid4,
        editable=False,
        primary_key=True,
    )

    objects = AbstractManager()

    class Meta:
        abstract = True


class AbstractAutoIncrementModel(AbstractModel):
    """
    Abstract model that adds an auto-incrementing _id field.
    This field will increment independently for each table that inherits from this model.
    """

    _id = models.IntegerField(
        db_index=True,
        unique=True,
        editable=False,
        null=True,
    )

    class Meta:
        abstract = True

    def save(self, *args, **kwargs):
        if self._id is None:
            max_id = self.__class__.objects.all().aggregate(models.Max("_id"))[
                "_id__max"
            ]
            self._id = (max_id or 0) + 1
        super().save(*args, **kwargs)


class AbstractSlugModel(AbstractAutoIncrementModel):
    """
    Abstract model with a slug field.
    Auto generate and save a slug based on the 'name' field.
    """

    name = models.CharField(max_length=250)
    slug = models.SlugField(
        max_length=200,
        unique=True,
        db_index=True,
        allow_unicode=True,
        blank=True,
    )

    objects = AbstractSlugManager()

    class Meta:
        abstract = True

    def _generate_slug(self):
        """
        Generate a slug based on the 'name' field.
        """
        value = self.name
        self.slug = SlugUtil.slugify(value, allow_unicode=True, unique=True)

    def save(self, *args, **kwargs):
        self._generate_slug()
        super().save(*args, **kwargs)
