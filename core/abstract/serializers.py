from rest_framework import serializers


class AbstractSerializer(serializers.ModelSerializer):
    id = serializers.UUIDField(read_only=True, format="hex")
    created = serializers.DateTimeField(read_only=True)
    updated = serializers.DateTimeField(read_only=True)

    class Meta:
        abstract = True
        fields = [
            "id",
            "created",
            "updated",
        ]
        read_only_fields = [
            "id",
            "created",
            "updated",
        ]

    def to_representation(self, instance):
        rep = super().to_representation(instance)
        rep["id"] = instance.id
        rep["created"] = instance.created
        rep["updated"] = instance.updated
        return rep
