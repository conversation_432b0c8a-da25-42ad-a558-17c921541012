from functools import wraps
import logging
import traceback
from django_redis.exceptions import ConnectionInterrupted
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse
from rest_framework import status
from rest_framework.exceptions import (
    APIException,
    ValidationError,
    PermissionDenied,
    NotFound,
    MethodNotAllowed,
    NotAcceptable,
    NotAuthenticated,
    UnsupportedMediaType,
    Throttled,
)
from django.db.utils import IntegrityError


logger = logging.getLogger(__name__)


def api_error_handler(view_func):
    """
    Base decorator for handling exceptions in API views.
    """

    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        try:
            return view_func(request, *args, **kwargs)
        except ValidationError as e:
            return JsonResponse({"error": e.detail}, status=status.HTTP_400_BAD_REQUEST)
        except PermissionDenied as e:
            return JsonResponse({"error": str(e)}, status=status.HTTP_403_FORBIDDEN)
        except NotFound as e:
            return JsonResponse({"error": str(e)}, status=status.HTTP_404_NOT_FOUND)
        except MethodNotAllowed as e:
            return JsonResponse(
                {"error": str(e)}, status=status.HTTP_405_METHOD_NOT_ALLOWED
            )
        except NotAcceptable as e:
            return JsonResponse(
                {"error": str(e)}, status=status.HTTP_406_NOT_ACCEPTABLE
            )
        except NotAuthenticated as e:
            return JsonResponse({"error": str(e)}, status=status.HTTP_401_UNAUTHORIZED)
        except UnsupportedMediaType as e:
            return JsonResponse(
                {"error": str(e)},
                status=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
            )
        except Throttled as e:
            return JsonResponse(
                {"error": str(e)}, status=status.HTTP_429_TOO_MANY_REQUESTS
            )

        except AssertionError as e:
            return JsonResponse(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        except APIException as e:
            return JsonResponse({"error": e.detail}, status=e.status_code)
        except ConnectionInterrupted as e:
            return JsonResponse(
                {"error": _("Redis connection interrupted")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        except IntegrityError as e:
            error_message = str(e)
            logger.error(f"IntegrityError: {error_message}")

            if (
                "_user_user_phone_number_key" in error_message
                or "UNIQUE constraint failed: _user_user.phone_number" in error_message
            ):
                return JsonResponse(
                    {
                        "error": {
                            "phone_number": [
                                _("User with this phone number already exists.")
                            ]
                        }
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            elif (
                "_user_user_email_key" in error_message
                or "UNIQUE constraint failed: _user_user.email" in error_message
            ):
                return JsonResponse(
                    {"error": {"email": [_("User with this email already exists.")]}},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            elif (
                "_user_user_username_key" in error_message
                or "UNIQUE constraint failed: _user_user.username" in error_message
            ):
                return JsonResponse(
                    {
                        "error": {
                            "username": [_("User with this username already exists.")]
                        }
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            logger.error(traceback.format_exc())

            return JsonResponse(
                {"error": _("Database integrity error. Please check your input data.")},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            logger.error(traceback.format_exc())
            logger.exception(e)
            return JsonResponse(
                {"error": _("Internal Server Error")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    return _wrapped_view
