{% load static %}

<!DOCTYPE html>
<html>

    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{% block title %}{% endblock %}</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 0;
                background-color: #f9f9f9;
            }

            .container {
                background-color: #ffffff;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
                margin: 20px;
            }

            .header {
                background-color: #f5f7fa;
                padding: 20px;
                text-align: center;
                border-bottom: 1px solid #eaeaea;
            }

            .logo {
                max-width: 180px;
                height: auto;
            }

            .content {
                padding: 30px;
            }

            h1,
            h2,
            h3 {
                color: #2c3e50;
                margin-top: 0;
            }

            .button-container {
                margin: 25px 0;
                text-align: center;
            }

            .button {
                display: inline-block;
                padding: 12px 24px;
                background-color: #3498db;
                color: white !important;
                text-decoration: none;
                border-radius: 4px;
                font-weight: bold;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                transition: background-color 0.2s;
            }

            .button:hover {
                background-color: #2980b9;
            }

            blockquote {
                margin: 20px 0;
                padding: 15px 20px;
                border-left: 4px solid #3498db;
                background-color: #f8f9fa;
                font-style: italic;
            }

            .question-box {
                background-color: #f8f9fa;
                border-left: 4px solid #3498db;
                padding: 15px 20px;
                margin: 20px 0;
            }

            .footer {
                margin-top: 30px;
                padding: 20px;
                background-color: #f5f7fa;
                border-top: 1px solid #eaeaea;
                font-size: 0.9em;
                color: #7f8c8d;
                text-align: center;
            }

            .details-box {
                background-color: #f8f9fa;
                border-radius: 4px;
                padding: 15px 20px;
                margin: 20px 0;
            }

            @media only screen and (max-width: 600px) {
                .container {
                    margin: 10px;
                }

                .content {
                    padding: 20px;
                }
            }
        </style>
    </head>

    <body>
        <div class="container">
            <div class="header">
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 518 554"
                    width="518" height="554" preserveAspectRatio="xMidYMid meet"
                    style="width: 100%; height: 100%; transform: translate3d(0px, 0px, 0px); content-visibility: visible;">
                    <defs>
                        <clipPath id="__lottie_element_22">
                            <rect width="518" height="554" x="0" y="0"></rect>
                        </clipPath>
                    </defs>
                    <g clip-path="url(#__lottie_element_22)">
                        <g transform="matrix(1,0,0,1,140.7689971923828,111.0999984741211)" opacity="1"
                            style="display: block;">
                            <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                                <path fill="#46ECFF" fill-opacity="1"
                                    d=" M26.59000015258789,-78.14600372314453 C26.59000015258789,-78.14600372314453 84.13600158691406,-111.0999984741211 84.13600158691406,-111.0999984741211 C84.13600158691406,-111.0999984741211 106.3219985961914,56.393001556396484 106.3219985961914,56.393001556396484 C106.3489990234375,56.66299819946289 111.2770004272461,88.26699829101562 82.58499908447266,104.64900207519531 C75.1259994506836,108.94000244140625 67.23100280761719,111.0999984741211 59.064998626708984,111.0999984741211 C41.42499923706055,111.0999984741211 28.523000717163086,100.87100219726562 28.523000717163086,100.87100219726562 C28.523000717163086,100.87100219726562 -106.66000366210938,-1.8760000467300415 -106.66000366210938,-1.8760000467300415 C-106.66000366210938,-1.8760000467300415 -49.112998962402344,-34.82899856567383 -49.112998962402344,-34.82899856567383 C-49.112998962402344,-34.82899856567383 54.928001403808594,57.22999954223633 54.928001403808594,57.22999954223633 C54.928001403808594,57.22999954223633 26.59000015258789,-78.14600372314453 26.59000015258789,-78.14600372314453z">
                                </path>
                            </g>
                        </g>
                        <g transform="matrix(1,0,0,1,93.84700012207031,276.98699951171875)" opacity="1"
                            style="display: block;">
                            <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                                <path fill="#00B7C8" fill-opacity="1"
                                    d=" M63.57600021362305,-44.505001068115234 C63.82099914550781,-44.39699935913086 93.84600067138672,-32.65700149536133 93.84600067138672,0 C93.87300109863281,32.52199935913086 63.792999267578125,44.42399978637695 63.49399948120117,44.53200149536133 C63.49399948120117,44.53200149536133 -93.84700012207031,109.2239990234375 -93.84700012207031,109.2239990234375 C-93.84700012207031,109.2239990234375 -93.84700012207031,43.34400177001953 -93.84700012207031,43.34400177001953 C-93.84700012207031,43.34400177001953 -91.99500274658203,42.75 -91.99500274658203,42.75 C-91.99500274658203,42.75 38.558998107910156,0 38.558998107910156,0 C38.558998107910156,0 -93.84700012207031,-43.34400177001953 -93.84700012207031,-43.34400177001953 C-93.84700012207031,-43.34400177001953 -93.84700012207031,-109.2239990234375 -93.84700012207031,-109.2239990234375 C-93.84700012207031,-109.2239990234375 63.57600021362305,-44.505001068115234 63.57600021362305,-44.505001068115234z">
                                </path>
                            </g>
                        </g>
                        <g transform="matrix(1,0,0,1,140.7259979248047,442.87701416015625)" opacity="1"
                            style="display: block;">
                            <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                                <path fill="#00F6F6" fill-opacity="1"
                                    d=" M28.6200008392334,-100.9010009765625 C29.62700080871582,-101.71099853515625 54.34400177001953,-120.87300109863281 82.62699890136719,-104.65299987792969 C82.62699890136719,-104.65299987792969 82.5999984741211,-104.6259994506836 82.5999984741211,-104.6259994506836 C111.15499877929688,-88.2979965209961 106.36399841308594,-56.612998962402344 106.30999755859375,-56.28900146484375 C106.30999755859375,-56.28900146484375 84.1520004272461,111.1500015258789 84.1520004272461,111.1500015258789 C84.1520004272461,111.1500015258789 26.604999542236328,78.1969985961914 26.604999542236328,78.1969985961914 C26.604999542236328,78.1969985961914 27.013999938964844,76.30699920654297 27.013999938964844,76.30699920654297 C27.013999938964844,76.30699920654297 54.970001220703125,-57.18000030517578 54.970001220703125,-57.18000030517578 C54.970001220703125,-57.18000030517578 -49.097999572753906,34.85200119018555 -49.097999572753906,34.85200119018555 C-49.097999572753906,34.85200119018555 -106.64399719238281,1.8990000486373901 -106.64399719238281,1.8990000486373901 C-106.64399719238281,1.8990000486373901 28.6200008392334,-100.9010009765625 28.6200008392334,-100.9010009765625z">
                                </path>
                            </g>
                        </g>
                        <g transform="matrix(1,0,0,1,377.2200012207031,442.8609924316406)" opacity="1"
                            style="display: block;">
                            <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                                <path fill="#00C4B7" fill-opacity="1"
                                    d=" M-106.30999755859375,-56.380001068115234 C-106.33699798583984,-56.650001525878906 -111.23699951171875,-88.25399780273438 -82.572998046875,-104.63600158691406 C-82.572998046875,-104.63600158691406 -82.5999984741211,-104.69000244140625 -82.5999984741211,-104.69000244140625 C-54.04499816894531,-121.0719985961914 -28.783000946044922,-101.10099792480469 -28.538000106811523,-100.88500213623047 C-28.538000106811523,-100.88500213623047 106.64399719238281,1.8609999418258667 106.64399719238281,1.8609999418258667 C106.64399719238281,1.8609999418258667 49.097999572753906,34.814998626708984 49.097999572753906,34.814998626708984 C49.097999572753906,34.814998626708984 47.654998779296875,33.54600143432617 47.654998779296875,33.54600143432617 C47.654998779296875,33.54600143432617 -54.970001220703125,-57.189998626708984 -54.970001220703125,-57.189998626708984 C-54.970001220703125,-57.189998626708984 -26.604999542236328,78.18599700927734 -26.604999542236328,78.18599700927734 C-26.604999542236328,78.18599700927734 -84.1520004272461,111.13899993896484 -84.1520004272461,111.13899993896484 C-84.1520004272461,111.13899993896484 -106.30999755859375,-56.380001068115234 -106.30999755859375,-56.380001068115234z">
                                </path>
                            </g>
                        </g>
                        <g transform="matrix(1,0,0,1,424.15399169921875,276.98699951171875)" opacity="1"
                            style="display: block;">
                            <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                                <path fill="#00F8EE" fill-opacity="1"
                                    d=" M-63.49399948120117,-44.53200149536133 C-63.49399948120117,-44.53200149536133 93.84700012207031,-109.2239990234375 93.84700012207031,-109.2239990234375 C93.84700012207031,-109.2239990234375 93.84700012207031,-43.34400177001953 93.84700012207031,-43.34400177001953 C93.84700012207031,-43.34400177001953 -38.558998107910156,0 -38.558998107910156,0 C-38.558998107910156,0 93.84700012207031,43.34400177001953 93.84700012207031,43.34400177001953 C93.84700012207031,43.34400177001953 93.84700012207031,109.2239990234375 93.84700012207031,109.2239990234375 C93.84700012207031,109.2239990234375 -63.57600021362305,44.505001068115234 -63.57600021362305,44.505001068115234 C-63.82099914550781,44.39699935913086 -93.87300109863281,32.65700149536133 -93.84600067138672,0 C-93.87300109863281,-32.68299865722656 -63.792999267578125,-44.42399978637695 -63.49399948120117,-44.53200149536133z">
                                </path>
                            </g>
                        </g>
                        <g transform="matrix(1,0,0,1,377.24700927734375,111.11299896240234)" opacity="1"
                            style="display: block;">
                            <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                                <path fill="#0097B5" fill-opacity="1"
                                    d=" M-59.08100128173828,111.11299896240234 C-67.24700164794922,111.11299896240234 -75.14099884033203,108.927001953125 -82.5999984741211,104.66300201416016 C-111.15499877929688,88.33499908447266 -106.36399841308594,56.650001525878906 -106.30999755859375,56.32600021362305 C-106.30999755859375,56.32600021362305 -84.1520004272461,-111.11299896240234 -84.1520004272461,-111.11299896240234 C-84.1520004272461,-111.11299896240234 -26.604999542236328,-78.18699645996094 -26.604999542236328,-78.18699645996094 C-26.604999542236328,-78.18699645996094 -54.970001220703125,57.18899917602539 -54.970001220703125,57.18899917602539 C-54.970001220703125,57.18899917602539 49.097999572753906,-34.84299850463867 49.097999572753906,-34.84299850463867 C49.097999572753906,-34.84299850463867 106.64399719238281,-1.8890000581741333 106.64399719238281,-1.8890000581741333 C106.64399719238281,-1.8890000581741333 -28.593000411987305,100.91100311279297 -28.593000411987305,100.91100311279297 C-28.674999237060547,100.99199676513672 -41.46900177001953,111.11299896240234 -59.08100128173828,111.11299896240234z">
                                </path>
                            </g>
                        </g>
                    </g>
                </svg>
            </div>

            <div class="content">
                {% block content %}{% endblock %}
            </div>

            <div class="footer">
                <p>
                    This is an automated message from {{ site_name|default:'taj' }}<br>
                    Please do not reply to this email.
                </p>
                <p>
                    &copy; {% now "Y" %} {{ site_name|default:'taj' }}. All rights reserved.
                </p>
            </div>
        </div>
    </body>

</html>