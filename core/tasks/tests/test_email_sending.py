from django.test import TestCase, override_settings
from django.conf import settings
from unittest.mock import patch, MagicMock
import pytest
from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import Mail
import socket
from python_http_client.exceptions import UnauthorizedError

from core.tasks.emails import send_sendgrid_email, send_email_task


class TestSendGridConfiguration(TestCase):
    """Test suite for SendGrid configuration and email sending functionality."""

    def setUp(self):
        self.test_email = "<EMAIL>"
        self.api_key = settings.SENDGRID_API_KEY
        self.from_email = settings.DEFAULT_FROM_EMAIL

    def test_sendgrid_api_key_is_set(self):
        """Test that SendGrid API key is properly configured."""
        self.assertIsNotNone(settings.SENDGRID_API_KEY)
        self.assertTrue(len(settings.SENDGRID_API_KEY) > 0)
        self.assertTrue(settings.SENDGRID_API_KEY.startswith("SG."))

    def test_sendgrid_api_key_validity(self):
        """Test that the configured SendGrid API key is valid."""
        sg = SendGridAPIClient(self.api_key)
        message = Mail(
            from_email=self.from_email,
            to_emails=self.test_email,
            subject="API Key Test",
            plain_text_content="Testing SendGrid API key validity",
        )

        try:
            response = sg.client.mail.send.post(request_body=message.get())
            self.assertIn(response.status_code, [200, 201, 202])
        except UnauthorizedError:
            self.fail("SendGrid API key is invalid or unauthorized")
        except Exception as e:
            self.fail(f"Unexpected error testing SendGrid API key: {str(e)}")

    @patch("core.tasks.emails.SendGridAPIClient")
    def test_sendgrid_api_fallback_to_smtp(self, mock_sendgrid):
        """Test that email sending falls back to SMTP when SendGrid API fails."""

        mock_sendgrid.return_value.send.side_effect = Exception("API Error")

        with self.settings(
            EMAIL_BACKEND="django.core.mail.backends.locmem.EmailBackend"
        ):
            try:
                send_sendgrid_email(
                    to_emails=self.test_email,
                    subject="Test Fallback",
                    text_content="Testing SMTP fallback",
                    from_email=self.from_email,
                )

                from django.core import mail

                self.assertEqual(len(mail.outbox), 1)
                self.assertEqual(mail.outbox[0].subject, "Test Fallback")
            except Exception as e:
                self.fail(f"SMTP fallback failed: {str(e)}")

    @patch("core.tasks.emails.send_sendgrid_email")
    def test_email_task_retries(self, mock_send):
        """Test that the email task properly handles retries on network errors."""

        mock_send.side_effect = socket.gaierror("Network error")

        with pytest.raises(Exception):
            send_email_task.delay(
                to_emails=self.test_email,
                subject="Test Retries",
                text_content="Testing retry mechanism",
            )

        self.assertEqual(mock_send.call_count, 4)

    def test_html_email_sending(self):
        """Test sending HTML emails with text fallback."""
        html_content = "<html><body><h1>Test</h1><p>HTML Content</p></body></html>"
        text_content = "Test Plain Text Content"

        try:
            response = send_sendgrid_email(
                to_emails=self.test_email,
                subject="Test HTML Email",
                text_content=text_content,
                html_content=html_content,
                from_email=self.from_email,
            )
            self.assertIn(response, [200, 201, 202])
        except Exception as e:
            self.fail(f"Failed to send HTML email: {str(e)}")

    @override_settings(SENDGRID_API_KEY="invalid_key")
    def test_invalid_api_key_handling(self):
        """Test handling of invalid SendGrid API key."""
        with self.assertLogs("core.tasks.emails", level="WARNING") as logs:
            try:
                send_sendgrid_email(
                    to_emails=self.test_email,
                    subject="Test Invalid Key",
                    text_content="Testing invalid API key handling",
                )
            except Exception:
                pass

            self.assertTrue(
                any(
                    "SendGrid API failed, falling back to SMTP" in log
                    for log in logs.output
                )
            )

    def test_multiple_recipients(self):
        """Test sending email to multiple recipients."""
        recipients = ["<EMAIL>", "<EMAIL>"]

        try:
            response = send_sendgrid_email(
                to_emails=recipients,
                subject="Test Multiple Recipients",
                text_content="Testing multiple recipients",
                from_email=self.from_email,
            )
            self.assertIn(response, [200, 201, 202])
        except Exception as e:
            self.fail(f"Failed to send to multiple recipients: {str(e)}")
