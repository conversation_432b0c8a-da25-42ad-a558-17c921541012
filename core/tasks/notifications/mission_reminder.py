from celery import shared_task
from django.conf import settings
from django.template.loader import render_to_string
from core.tasks.emails import send_email_with_html
from datetime import datetime, timedelta, date
from apps.missions.models import WeeklyMission
import logging
from django.utils import timezone

logger = logging.getLogger(__name__)


@shared_task
def send_mission_reminder_notification(assignment_id):
    """Send notification to user about daily mission."""
    try:
        mission = WeeklyMission.objects.select_related("six_week_period", "domain").get(
            id=assignment_id
        )

        context = {
            "mission": mission,
            "six_week_period": mission.six_week_period,
            "domain": mission.domain,
            "frontend_url": settings.FRONTEND_URL,
        }

        html_content = render_to_string(
            "submissions/emails/missions/mission_reminder.html", context
        )
        text_content = render_to_string(
            "submissions/emails/missions/mission_reminder.txt", context
        )
        subject = f"Daily Mission Reminder: {mission.id}"

        send_email_with_html.delay(
            subject=subject,
            text_content=text_content,
            html_content=html_content,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to_email=mission.user.email,
        )

        logger.info(f"Mission reminder notification sent to {mission.user.email}")

    except Exception as e:
        logger.error(
            f"Failed to send mission reminder notification: {str(e)}", exc_info=True
        )
