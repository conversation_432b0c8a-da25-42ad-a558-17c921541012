from django.conf import settings
from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import Mail, Content, To, ReplyTo
import logging

logger = logging.getLogger(__name__)


def send_email(
    to_email,
    subject,
    text_content=None,
    html_content=None,
    from_email=None,
    reply_to=None,
):
    """
    Send an email using SendGrid's API

    Args:
        to_email: String or list of recipient email addresses
        subject: Email subject
        text_content: Plain text content (optional if html_content is provided)
        html_content: HTML content (optional)
        from_email: Sender email address (defaults to settings.DEFAULT_FROM_EMAIL)
        reply_to: Reply-to email address (optional)

    Returns:
        int: Status code from SendGrid API (202 indicates success)
    """

    if isinstance(to_email, str):
        to_emails = [to_email]
    else:
        to_emails = to_email

    if text_content is not None:
        text_content = str(text_content)
    if html_content is not None:
        html_content = str(html_content)
    if subject is not None:
        subject = str(subject)

    message = Mail(
        from_email=from_email or settings.DEFAULT_FROM_EMAIL,
        to_emails=[To(email) for email in to_emails],
        subject=subject,
    )

    if text_content:
        message.add_content(Content("text/plain", text_content))
    if html_content:
        message.add_content(Content("text/html", html_content))

    if reply_to:
        message.reply_to = ReplyTo(reply_to)

    try:
        sg = SendGridAPIClient(settings.SENDGRID_API_KEY)
        response = sg.send(message)

        if response.status_code not in [200, 201, 202]:
            logger.error(
                f"Failed to send email via SendGrid. Status code: {response.status_code}"
            )
            raise Exception(f"SendGrid API returned status code {response.status_code}")

        logger.info(
            f"Email sent successfully to {to_emails}. Status code: {response.status_code}"
        )
        return response.status_code
    except Exception as e:
        logger.error(f"SendGrid API error: {str(e)}")
        raise
