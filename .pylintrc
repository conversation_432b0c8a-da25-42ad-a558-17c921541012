[MASTER]
ignore=env
init-hook="import sys; from pathlib import Path; sys.path.append(str(next(Path.cwd().glob('./env/lib/*/site-packages'))))"

[MESSAGES CONTROL]
disable=C0111,C0103,C0303,W0311,W0611,E1101,C0326,C0330,C0301,R0903,R0913,W0621,W0622,W0703,R0914,R0912,R0915,R0904,R0801,W0511,C0413,C0412,C0411,W0401,W0614,W0613,W0612,W0603,W0602,W0601,R0902,R0911,R0201,R0401,R0901,R0921,R0922,R0923,E0401

[FORMAT]
max-line-length=120

[TYPECHECK]
ignored-modules=rest_framework,drf_spectacular,django_filters,rest_framework_simplejwt,djoser,social_django,drf_social_oauth2,oauth2_provider,health_check,anymail,django_celery_beat,django_q,rest_framework
ignored-classes=rest_framework.viewsets.ModelViewSet,rest_framework.serializers.ModelSerializer,rest_framework

[BASIC]
good-names=i,j,k,ex,Run,_,pk,id,up

[tool.ruff]
line-length = 99
select = ["F", "E", "W", "Q", "I001"]

exclude = ["migrations"]
ignore = ["F401"]

[tool.pytest.ini_options]
DJANGO_SETTINGS_MODULE = "config.settings.base"

exclude = [
    "**/migrations/*.py"
]
enable_error_code = [
    "truthy-bool",
    "truthy-iterable",
    "redundant-expr",
    "unused-awaitable",
    "ignore-without-code",
    "possibly-undefined",
    "redundant-self",
]
allow_redefinition = false
check_untyped_defs = true
disallow_untyped_decorators = true
disallow_any_explicit = true
disallow_any_generics = true
disallow_untyped_calls = true
disallow_incomplete_defs = true
explicit_package_bases = true
ignore_errors = false
ignore_missing_imports = true
implicit_reexport = false
local_partial_types = true
strict_equality = true
strict_optional = true
show_error_codes = true
no_implicit_optional = true
warn_unused_ignores = true
warn_redundant_casts = true
warn_unused_configs = true
warn_unreachable = true
warn_no_return = true

[tool.django-stubs]
django_settings_module = "config.settings.base"
strict_settings = false