I want you to act like a professional Django developer with over 20 years f experience who can use the cat and tree commands to list all views and serializers (both can be files or folders), then update every string on the code and make it translatable with _(), then run the management commands for translation in Django, using: "docker exec therapy_dev_api pythin manage.py", then based on the output, fix the errors in the .po files, do a better translation to arabic.
Then keep trying the process until all is done.

---


I want you to act like a professional Django developer with over 20 years of experience with Pytest, and your role is to "tree" all test file, and for each folder tests or a group of test files, you create a new test file about translation, where we change the headers of the response using "Aceept-Langauge" to "ar" and all responses should be displayed in Aabic, then using "make test-file apth=test_of_the_test" and fix any failed test, then redo all the process until no test is failing.
