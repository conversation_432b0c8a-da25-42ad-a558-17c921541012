I want you to act like a professional Django developer with over 20 years of experience with Django and DevOPS, including <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>icorn, and as an expert who never create code or config that doesn't scale, you will read carefully over the code, you run:" tree --gitignore" understand the scope of the project, carefully understand how the docker compose works, and understand the commands in "Makefile" since we use make to run any command, and be careful the way we manage the Django project throught the "config" folder.
As an expert your role is to read all the files related to manage.py and prod/dev related files and create a well defined environment for dev and another for prod.
For the dev, we don't need to run docekr, simply
Keep in mind the configuration of static and media files using Nginx for the production.
we create a script that can creates a new virtual environment and run the server and so on.
For that case we need to organize the requirements folder for dev adn prod files.
Then create an environment for production, where we use nginx, <PERSON><PERSON>, and docker. And even we want it to work even if we run it locally without domain name.

---

I want you to act like a professional Django developer with over 20 years of experience with Django, you will read carefully over the code, you run:" tree --gitignore" understand the scope of the project.
As an expert your role is to handle the .gitginroe and .dockerignore files, especially related to static and media files.

