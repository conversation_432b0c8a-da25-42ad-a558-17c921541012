<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Meeting Invitation</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
        }
        .meeting-details {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Meeting Invitation</h2>
        <p>Hello {% if attendee_name %}{{ attendee_name }}{% else %}{{ attendee.fullname|default:attendee.username }}{% endif %},</p>
        <p>You have been invited to a meeting for the club "{{ meeting.club.name }}" by {{ meeting.organizer.fullname|default:meeting.organizer.username }}.</p>

        {% if is_new_user %}
        <p><strong>Note:</strong> An account has been created for you to access this meeting. You can set your password by clicking the "View Meeting" button below and following the instructions.</p>
        {% endif %}

        <div class="meeting-details">
            <h3>{{ meeting.title }}</h3>
            <p><strong>Description:</strong> {{ meeting.description|default:"No description provided" }}</p>
            <p><strong>Date:</strong> {{ meeting.start_time|date:"F j, Y" }}</p>
            <p><strong>Time:</strong> {{ meeting.start_time|time:"g:i A" }} - {{ meeting.end_time|time:"g:i A" }}</p>
            {% if meeting.is_jitsi_meeting and meeting.jitsi_embed_link %}
            <p><strong>Meeting Link:</strong> <a href="{{ meeting.meeting_link }}">Join Meeting</a></p>
            {% endif %}
        </div>

        <p>Click the button below to view the meeting details:</p>
        <a href="{{ frontend_url }}/clubs/{{ meeting.club.id }}/meetings/{{ meeting.id }}" class="button">View Meeting</a>

        <p>Best regards,<br>The Team</p>
    </div>
</body>
</html>
