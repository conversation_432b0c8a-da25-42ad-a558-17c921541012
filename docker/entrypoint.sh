#!/bin/bash

if [ "$DATABASE" = "postgres" ]; then
  echo "Waiting for postgres..."

  while ! nc -z $DB_HOST $DATABASE_PORT; do
    sleep 0.1
  done

  echo "PostgreSQL started"
fi

echo "Collect static files"
python manage.py collectstatic --noinput

echo "Apply database migrations"
echo "Migrating and seeding the database..."
python manage.py makemigrations
python manage.py migrate
python manage.py makemessages -l ar
python manage.py compilemessages

echo "Checking if countries need to be seeded..."
COUNTRIES_COUNT=$(python manage.py shell -c "from apps.countries.models import Country; print(Country.objects.count())")
if [ "$COUNTRIES_COUNT" -eq "0" ]; then
  echo "Seeding countries data..."
  python manage.py seed-countries
else
  echo "Countries data already exists, skipping seeding."
fi

python manage.py seed_domains &
python manage.py seed_club_types &
wait

python manage.py shell -c """
from apps.accounts.user.models import User; 
admin = User.objects.filter(email='$ADMIN_EMAIL').first()
if not admin:
    admin = User.objects.create_superuser(is_email_verified=True, username="admin", email='$ADMIN_EMAIL', password='$ADMIN_PASSWORD')  
    admin.set_password('$ADMIN_PASSWORD')
    admin.save()
else:
    admin.set_password('$ADMIN_PASSWORD')
    admin.is_email_verified = True
    admin.username = 'admin'
    admin.save()
"""

echo "Starting server"
python manage.py runserver 0.0.0.0:$API_PORT
