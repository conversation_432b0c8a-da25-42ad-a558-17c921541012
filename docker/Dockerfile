FROM python:3.10-bullseye

ENV PYTHONFAULTHANDLER=1 \
  PYTHONHASHSEED=random \
  PYTHONUNBUFFERED=1 \
  TZ=UTC

WORKDIR /app

RUN apt-get update && apt-get install -y \
  libpq-dev \
  gettext \
  zlib1g-dev \
  libjpeg-dev \
  python3-dev \
  build-essential \
  gdal-bin \
  netcat-traditional \
  && rm -rf /var/lib/apt/lists/*

# Install wheel properly before any other packages
RUN pip install --upgrade pip setuptools wheel

# Copy requirements and install dependencies
COPY requirements.txt .
# Install the problematic packages first with wheel support
RUN pip install --no-cache-dir wheel django-celery-beat django-pdb googletrans starkbank-ecdsa django_q2
# Then install the rest of the requirements
RUN pip install --no-cache-dir -r requirements.txt

# Copy the application
COPY . .

# Ensure entrypoint scripts are executable at build time
# This ensures they have executable permissions even when mounted as volumes
RUN chmod +x /app/docker/entrypoint.sh
RUN chmod +x /app/docker/entrypoint-beat.sh
RUN chmod +x /app/docker/ensure-permissions.sh
RUN chmod +x /app/scripts/*

# Use the ensure-permissions script as an entrypoint
ENTRYPOINT ["/app/docker/ensure-permissions.sh"]
CMD ["sh", "/app/docker/entrypoint.sh"]
