import os
from decouple import config

from .emails import *
from .base import *

DEBUG = False
ALLOWED_HOSTS = (os.environ.get("DJANGO_ALLOWED_HOSTS") or "").split(",")
CORS_ALLOWED_ORIGINS = (os.environ.get("CORS_ALLOWED_ORIGINS") or "").split(",")
CORS_ALLOW_ALL_ORIGINS = False
CORS_ORIGIN_WHITELIST = (os.environ.get("CORS_ORIGIN_WHITELIST") or "").split(",")
CORS_ALLOWED_ORIGIN_REGEXES = [
    r"^https://\w+\.localhost$",
    r"^http://\w+\.localhost$",
    r"^http://\w+\.therapynimbus.com$",
    r"^http://taj.therapynimbus.com$",
    r"^http://\w+\.taj.com$",
    r"^http://quiz.taj.com$",
    r"^http://localhost:3000$",
]
CORS_ALLOW_METHODS = (
    "DELETE",
    "GET",
    "OPTIONS",
    "PATCH",
    "POST",
    "PUT",
)
CSRF_TRUSTED_ORIGINS = (os.environ.get("CSRF_TRUSTED_ORIGINS") or "").split(",")

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": os.getenv("DATABASE_NAME"),
        "USER": os.getenv("DATABASE_USER"),
        "PASSWORD": os.getenv("DATABASE_PASSWORD"),
        "HOST": os.getenv("DATABASE_HOST"),
        "PORT": 5432,
    }
}


LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "handlers": {
        "file": {
            "level": "DEBUG",
            "class": "logging.FileHandler",
            "filename": "/tmp/debug.log",
        },
    },
    "loggers": {
        "django": {
            "handlers": ["file"],
            "level": "DEBUG",
            "propagate": True,
        },
    },
}
