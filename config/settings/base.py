import os
from dotenv import load_dotenv
from pathlib import Path
from datetime import timedelta
from django.utils.translation import gettext_lazy as _
import string
from decouple import config
from celery.schedules import crontab
import sys

from .logging import *


if hasattr(sys, "set_int_max_str_digits"):
    sys.set_int_max_str_digits(10000)

BASE_DIR = Path(__file__).resolve().parent.parent.parent

load_dotenv()


SECRET_KEY = os.environ.get("SECRET_KEY")

ALLOWED_HEADERS = os.environ.get("ALLOWED_HEADERS", default="*")

LOCAL_APPS = [
    "apps.accounts.auth",
    "apps.files",
    "apps.notifications",
    "apps.accounts.user",
    "apps.accounts.profile",
    "core",
    "apps.countries",
    "apps.internationalization",
    "apps.domains",
    "apps.okrs",
    "apps.missions",
    "apps.google_calendar",
    "apps.clubs",
    "apps.clubs.demands",
    "apps.clubs.meetings",
    "apps.stats",
]
DJANGO_APPS = [
    "jazzmin",
    "daphne",
    "django.contrib.auth",
    "django.contrib.admin",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
]

THIRD_PARTY_APPS = [
    "cachalot",
    "rest_framework",
    "rest_framework_simplejwt",
    "rest_framework.authtoken",
    "corsheaders",
    "rest_framework_simplejwt.token_blacklist",
    "drf_spectacular",
    "drf_spectacular_sidecar",
    "djoser",
    "rosetta",
    "parler",
    "django_extensions",
    "django_celery_results",
    "phonenumber_field",
    "django_countries",
    "taggit",
    "oauth2_provider",
    "social_django",
    "drf_social_oauth2",
    "health_check",
    "health_check.db",
    "health_check.cache",
    "health_check.storage",
    "health_check.contrib.migrations",
    "health_check.contrib.celery",
    "health_check.contrib.celery_ping",
    "health_check.contrib.redis",
    "anymail",
    "import_export",
    "django_celery_beat",
    "django_q",
]
INSTALLED_APPS = DJANGO_APPS + THIRD_PARTY_APPS + LOCAL_APPS


TEMPLATE_CONTEXT_PROCESSORS = (
    "social_django.context_processors.backends",
    "social_django.context_processors.login_redirect",
    "django.core.context_processors.i18n",
)


SPECTACULAR_SETTINGS = {
    "TITLE": "taj-api API",
    "DESCRIPTION": "taj API API",
    "VERSION": "1.0.0",
    "SERVE_INCLUDE_SCHEMA": False,
    "SWAGGER_UI_DIST": "SIDECAR",
    "SWAGGER_UI_FAVICON_HREF": "SIDECAR",
    "REDOC_DIST": "SIDECAR",
    "COMPONENT_SPLIT_REQUEST": True,
    "OAUTH2_FLOWS": ["password"],
    "OAUTH2_SCOPES": {
        "read": "Read scope",
        "write": "Write scope",
    },
}


DJOSER = {
    "LOGIN_FIELD": "email",
    "SOCIAL_AUTH_TOKEN_STRATEGY": "apps.auth.strategy.TokenStrategy",
    "SOCIAL_AUTH_ALLOWED_ REDIRECT_URIS": os.environ.get(
        "SOCIAL_AUTH_ALLOWED_REDIRECT_URIS", default="*"
    ).split(","),
}
SOCIAL_AUTH_JSONFIELD_ENABLED = True
SOCIAL_AUTH_REDIRECT_IS_HTTPS = False
SOCIAL_AUTH_GOOGLE_OAUTH2_KEY = os.environ.get("GOOGLE_CLIENT_ID")
SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET = os.environ.get("GOOGLE_CLIENT_SECRET")
SOCIAL_AUTH_GOOGLE_OAUTH2_SCOPE = [
    "https://www.googleapis.com/auth/userinfo.email",
    "https://www.googleapis.com/auth/userinfo.profile",
    "https://www.googleapis.com/auth/calendar.events",
]
SOCIAL_AUTH_GOOGLE_OAUTH2_EXTRA_DATA = ["username"]
GOOGLE_OAUTH2_REDIRECT_URI = os.environ.get(
    "GOOGLE_OAUTH2_REDIRECT_URI", "localhost:3000"
)
SOCIAL_AUTH_PIPELINE = (
    "social_core.pipeline.social_auth.social_details",
    "social_core.pipeline.social_auth.social_uid",
    "social_core.pipeline.social_auth.auth_allowed",
    "social_core.pipeline.social_auth.social_user",
    "social_core.pipeline.social_auth.associate_user",
    "social_core.pipeline.social_auth.load_extra_data",
    "social_core.pipeline.user.user_details",
)


MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.locale.LocaleMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "social_django.middleware.SocialAuthExceptionMiddleware",
]

ROOT_URLCONF = "config.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR / "templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
                "social_django.context_processors.backends",
                "social_django.context_processors.login_redirect",
            ],
        },
    },
]


ASGI_APPLICATION = "config.asgi.application"

REDIS_PORT = 6379
REDIS_HOST = "redis"

CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.pubsub.RedisPubSubChannelLayer",
        "CONFIG": {
            "hosts": [f"redis://{REDIS_HOST}:{REDIS_PORT}/1"],
        },
    }
}


DAPHNE_TIMEOUT = 180
DAPHNE_MAX_REQUEST_SIZE = 1024 * 1024 * 25
DAPHNE_WEBSOCKET_TIMEOUT = 120

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(minutes=300),
}

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"
AUTH_USER_MODEL = "_user.User"


AUTHENTICATION_BACKENDS = (
    "social_core.backends.google.GoogleOAuth2",
    "drf_social_oauth2.backends.DjangoOAuth2",
    "django.contrib.auth.backends.ModelBackend",
)

REST_FRAMEWORK = {
    "EXCEPTION_HANDLER": "core.exceptions.custom_exception_handler",
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework_simplejwt.authentication.JWTAuthentication",
        "rest_framework.authentication.BasicAuthentication",
    ),
    "DEFAULT_FILTER_BACKENDS": ["django_filters.rest_framework.DjangoFilterBackend"],
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.LimitOffsetPagination",
    "TEST_REQUEST_DEFAULT_FORMAT": "json",
    "PAGE_SIZE": 10,
    "DEFAULT_PARSER_CLASSES": [
        "rest_framework.parsers.JSONParser",
        "rest_framework.parsers.MultiPartParser",
    ],
    "DEFAULT_RENDERER_CLASSES": [
        "rest_framework.renderers.JSONRenderer",
        "rest_framework.renderers.BrowsableAPIRenderer",
    ],
    "DEFAULT_SCHEMA_CLASS": "drf_spectacular.openapi.AutoSchema",
}


MEDIA_URL = "/media/"
MEDIA_ROOT = "/var/www/taj/uploads"
STATIC_ROOT = "/var/www/taj/static"
STATIC_URL = "/static/"


DEFAULT_AVATAR_URL = "https://avatars.dicebear.com/api/identicon/.svg"


CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://redis:6379/0",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        },
    }
}


OVERRIDE_FILE_UPLOAD_NAME = True

SWAGGER_SETTINGS = {
    "SECURITY_DEFINITIONS": {
        "bearer": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header",
            "description": 'JWT Authorization header using the Bearer scheme. Example: "Authorization: Bearer {token}"',
        }
    },
    "FORM_ENCTYPE": "multipart/form-data",
}
REST_AUTH = {
    "USE_JWT": True,
    "JWT_AUTH_COOKIE": "jwt-auth",
}
LOGIN_REDIRECT_URL = "/admin"

LANGUAGES = [
    ("en", _("English")),
    ("ar", _("Arabic")),
]
MODELTRANSLATION_LANGUAGES = ("en", "ar")
LANGUAGE_CODE = "ar"
USE_I18N = True
USE_TZ = True
LOCALE_PATHS = [BASE_DIR / "locale"]
ROSETTA_MESSAGES_PER_PAGE = 100
ROSETTA_ENABLE_TRANSLATION_SUGGESTIONS = True
PARLER_DEFAULT_LANGUAGE_CODE = "ar"
PARLER_LANGUAGES = {
    None: (
        {"code": "ar"},
        {"code": "en"},
    ),
    "default": {
        "fallbacks": ["ar", "en"],
    },
}
TIME_ZONE = "UTC"
CELERY_BROKER_URL = os.environ.get("CELERY_BROKER_URL")
CELERY_ACCEPT_CONTENT = {"application/json"}
CELERY_RESULT_SERIALIZER = "json"
CELERY_TASK_SERIALIZER = "json"
CELERY_TIMEZONE = "UTC"
CELERY_RESULT_BACKEND = "django-db"

CELERY_BEAT_SCHEDULER = "django_celery_beat.schedulers:DatabaseScheduler"
CELERY_BEAT_SCHEDULE = {}
CELERY_IMPORTS = ("core.tasks.notifications.mission_reminder",)


LOGIN_URL = "https://taj.therapynimbus.com/admin/dashboard/login"
OTP_EXPIRATION_MINS = 10

CODE_LENGTH = 6
CODE_CHARS = string.ascii_letters + string.digits
SEGMENTED_CODES = False
SEGMENT_LENGTH = 4
SEGMENT_SEPARATOR = "-"


Q_CLUSTER = {
    "name": "taj",
    "workers": 8,
    "recycle": 500,
    "timeout": 60,
    "compress": True,
    "cpu_affinity": 1,
    "save_limit": 250,
    "queue_limit": 500,
    "label": "Django Q",
    "redis": {
        "host": "redis",
        "port": 6379,
        "db": 0,
    },
}

try:
    TWILIO_ACCOUNT_SID = config("TWILIO_SID", default="AC**********")
    TWILIO_MESSAGING_SID = config("TWILIO_MESSAGING_SID", default="MG**********")
    TWILIO_AUTH_TOKEN = config("TWILIO_AUTH_TOKEN", default="**********")
    TWILIO_NUMBER = config("TWILIO_NUMBER", default="+**********")
    TWLIO_TEST_AUTH_TOKEN = config("TWLIO_TEST_AUTH_TOKEN", default="**********")
    TWILIO_TEST_SID = config("TWILIO_TEST_SID", default="AC**********")
    TWILIO_VERIFY_SERVICE_SID = config(
        "TWILIO_VERIFY_SERVICE_SID", default="VA**********"
    )
except:
    pass
VONAGE_API_KEY = config("VONAGE_API_KEY", default="**********")
VONAGE_API_SECRET = config("VONAGE_API_SECRET", default="**********")
VONAGE_FROM = config("VONAGE_FROM", default="**********")


EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
EMAIL_HOST = "smtp.sendgrid.net"
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = "apikey"
EMAIL_HOST_PASSWORD = os.environ.get("SENDGRID_API_KEY")
SENDGRID_API_KEY = os.environ.get("SENDGRID_API_KEY")


DEFAULT_FROM_EMAIL = os.environ.get("DEFAULT_FROM_EMAIL", "<EMAIL>")
EMAIL_ACTIVATION_HOST = os.environ.get("EMAIL_ACTIVATION_HOST", "<EMAIL>")


RATE_LIMITER_ENABLED = os.environ.get("RATE_LIMITER_ENABLED", default=True)
RATE_LIMITER_DEFAULT_RATE = os.environ.get("RATE_LIMITER_DEFAULT_RATE", default=5)
RATE_LIMITER_DEFAULT_PERIOD = os.environ.get("RATE_LIMITER_DEFAULT_PERIOD", default=1)
FRONTEND_URL = os.environ.get("FRONTEND_URL")
JURY_FRONTEND_URL = os.environ.get("JURY_FRONTEND_URL", FRONTEND_URL)
CANDIDATE_FRONTEND_URL = os.environ.get("CANDIDATE_FRONTEND_URL", FRONTEND_URL)
ADMIN_FRONTEND_URL = os.environ.get("ADMIN_FRONTEND_URL", FRONTEND_URL)
SITE_NAME = os.environ.get("SITE_NAME")
JURY_ASSIGNMENT_LIMIT = os.environ.get("JURY_ASSIGNMENT_LIMIT", default=20)

SITE_NAME = os.environ.get("SITE_NAME", "taj")
SITE_URL = os.environ.get("SITE_URL", "https://taj.com")


GEOIP_PATH = os.path.join(BASE_DIR, "geoip")


VISITOR_TRACKING = {
    "TRACK_API_REQUESTS": True,
    "TRACK_ADMIN_REQUESTS": False,
    "TRACK_STATIC_REQUESTS": False,
    "TRACK_MEDIA_REQUESTS": False,
}


ASYNC_REQUEST_TIMEOUT = 60
REQUEST_TIMEOUT = 60
CHANNEL_EXPIRY_SECONDS = 60


CLUB_MANAGER_CAN_CREATE_X_CLUBS = os.environ.get(
    "CLUB_MANAGER_CAN_CREATE_X_CLUBS", default=1
)


JITSI_DOMAIN = os.environ.get("JITSI_DOMAIN", "https://jitsidev.ntaj.me")
JITSI_JWT_APP_ID = os.environ.get("JITSI_JWT_APP_ID", "jitsi-prod")
JITSI_JWT_APP_SECRET = os.environ.get(
    "JITSI_JWT_APP_SECRET", "kk.asdmqwWQe43623Q12#!24ASfsAwqdqwdwqd"
)
JITSI_JWT_AUDIENCE = os.environ.get("JITSI_JWT_AUDIENCE", "jitsi")
JITSI_JWT_SUBJECT = os.environ.get("JITSI_JWT_SUBJECT", JITSI_JWT_APP_ID)
JITSI_JWT_EXPIRY = int(os.environ.get("JITSI_JWT_EXPIRY", 3600))
JITSI_ENABLE_RECORDING = os.environ.get("JITSI_ENABLE_RECORDING", "1") == "1"


CACHE_TTL = int(os.getenv("CACHE_TTL", 60 * 5))


JITSI_DOMAIN = os.environ.get("JITSI_DOMAIN", "https://jitsidev.ntaj.me")
JITSI_JWT_APP_ID = os.environ.get("JITSI_JWT_APP_ID", "jitsi-prod")
JITSI_JWT_APP_SECRET = os.environ.get(
    "JITSI_JWT_APP_SECRET", "kk.asdmqwWQe43623Q12#!24ASfsAwqdqwdwqd"
)
JITSI_JWT_AUDIENCE = os.environ.get("JITSI_JWT_AUDIENCE", "jitsi")
JITSI_JWT_SUBJECT = os.environ.get("JITSI_JWT_SUBJECT", JITSI_JWT_APP_ID)
JITSI_JWT_EXPIRY = int(os.environ.get("JITSI_JWT_EXPIRY", 3600))
JITSI_ENABLE_RECORDING = os.environ.get("JITSI_ENABLE_RECORDING", "1") == "1"


MEMBER_BASE_UI_URL = os.environ.get("MEMBER_BASE_UI_URL", "http://localhost:4189")
ADMIN_BASE_UI_URL = os.environ.get("ADMIN_BASE_UI_URL", "http://localhost:4150")
MANAGER_BASE_UI_URL = os.environ.get("MANAGER_BASE_UI_URL", "http://localhost:4190")
MEMBER_RESET_PASSWORD_URL = os.environ.get(
    "MEMBER_RESET_PASSWORD_URL", "/auth/reset-password?token="
)

WEEKDAY_START_FROM = os.environ.get("WEEKDAY_START_FROM", "sunday")