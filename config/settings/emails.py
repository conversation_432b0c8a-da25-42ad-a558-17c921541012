from django.core.mail.backends.smtp import EmailBackend
from decouple import config
import os

try:
    ANYMAIL = {
        "MAILGUN_API_KEY": config("MAILGUN_API_KEY"),
        "MAILGUN_SENDER_DOMAIN": config("MAILGUN_SENDER_DOMAIN"),
    }
except:
    pass


EMAIL_HOST_USER = os.environ.get("EMAIL_NO_REPLY_HOST", "<EMAIL>")
EMAIL_HOST = os.environ.get("EMAIL_OUTGOING_SERVER", "mail.taj.com")
EMAIL_PORT = int(os.environ.get("EMAIL_PORT", 587))
EMAIL_USE_TLS = os.environ.get("EMAIL_USE_TLS", "True") == "True"
EMAIL_HOST_PASSWORD = os.environ.get("EMAIL_NO_REPLY_PASSWORD")
EMAIL_ACTIVATION_HOST = os.environ.get(
    "EMAIL_ACTIVATION_HOST", "<EMAIL>"
)
EMAIL_ACTIVATION_PASSWORD = os.environ.get("EMAIL_ACTIVATION_PASSWORD")


EMAIL_ACTIVATION_BACKEND = EmailBackend(
    host="mail.taj.com",
    port=465,
    username=EMAIL_ACTIVATION_HOST,
    password=EMAIL_ACTIVATION_PASSWORD,
    use_tls=False,
    use_ssl=True,
)


EMAIL_NO_REPLY_BACKEND = EmailBackend(
    host="mail.taj.com",
    port=465,
    username=EMAIL_HOST_USER,
    password=EMAIL_HOST_PASSWORD,
    use_tls=False,
    use_ssl=True,
)


EMAIL_SMTP_SERVER = os.environ.get("EMAIL_SMTP_SERVER", "smtp.zoho.com")
EMAIL_SMTP_PORT = int(os.environ.get("EMAIL_SMTP_PORT", 587))
EMAIL_USERNAME = os.environ.get("EMAIL_USERNAME", "<EMAIL>")
EMAIL_PASSWORD = os.environ.get("EMAIL_PASSWORD", "cv5ZsKsMnysh")
EMAIL_USE_TLS = os.environ.get("EMAIL_USE_TLS", "True") == "True"
EMAIL_SENDER = os.environ.get("EMAIL_SENDER", "<EMAIL>")
EMAIL_RECIPIENT = os.environ.get("EMAIL_RECIPIENT", "<EMAIL>")


ZOHO_MAIL_BACKEND = EmailBackend(
    host=EMAIL_SMTP_SERVER,
    port=EMAIL_SMTP_PORT,
    username=EMAIL_USERNAME,
    password=EMAIL_PASSWORD,
    use_tls=EMAIL_USE_TLS,
    use_ssl=False,
)
