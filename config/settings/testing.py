from .base import *
from .emails import *
from decouple import config

DEBUG = True
ALLOWED_HOSTS = ["*"]
CORS_ALLOW_ALL_ORIGINS = True


TESTING = True


DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": ":memory:",
    }
}

LANGUAGE_CODE = "ar"
state = "testing"

CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.core.RedisChannelLayer",
        "CONFIG": {
            "hosts": ["redis://redis:6379/0"],
            "capacity": 1500,
            "expiry": 10,
        },
    },
}

CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.dummy.DummyCache",
    }
}

CACHE_TTL = 60 * 5


PASSWORD_HASHERS = [
    "django.contrib.auth.hashers.MD5PasswordHasher",
]

CELERY_TASK_ALWAYS_EAGER = True
CELERY_TASK_EAGER_PROPAGATES = True
BROKER_BACKEND = "memory"


LOGGING = {
    "version": 1,
    "disable_existing_loggers": True,
}


TEST_RUNNER = "django.test.runner.DiscoverRunner"


EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"
EMAIL_HOST = "smtp.gmail.com"
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = "<EMAIL>"
EMAIL_HOST_PASSWORD = "nvzvdwdwdwdrtrfzmw"
DEFAULT_FROM_EMAIL = "<EMAIL>"
EMAIL_ACTIVATION_HOST = "<EMAIL>"
SENDGRID_API_KEY = "mock-api-key"


Q_CLUSTER = {
    "name": "DjangORM",
    "workers": 1,
    "timeout": 90,
    "retry": 120,
    "cpu_affinity": 1,
    "label": "Django Q",
    "django_redis": "default",
    "orm": "default",
    "sync": True,
}
