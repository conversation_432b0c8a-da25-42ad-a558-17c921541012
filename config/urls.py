from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

from drf_spectacular.views import (
    SpectacularAPIView,
    SpectacularRedocView,
    SpectacularSwaggerView,
)

urlpatterns = [
    path(
        "admin/",
        include(
            [
                path("dashboard/", admin.site.urls),
                path("translation/", include("rosetta.urls")),
                path("server-status/", include("health_check.urls")),
            ]
        ),
    ),
    path(
        "dev/",
        include(
            [
                path(
                    "download-schema/",
                    SpectacularAPIView.as_view(),
                    name="schema",
                ),
                path(
                    "docs/",
                    SpectacularSwaggerView.as_view(url_name="schema"),
                    name="swagger-ui",
                ),
                path(
                    "redoc/",
                    SpectacularRedocView.as_view(url_name="schema"),
                    name="redoc",
                ),
            ]
        ),
    ),
    path(
        "api/v1/",
        include(
            [
                path("users/", include("apps.accounts.user.urls")),
                path("auth/", include("djoser.social.urls")),
                path("domains/", include("apps.domains.urls")),
                path("okrs/", include("apps.okrs.urls")),
                path("notifications/", include("apps.notifications.urls")),
                path("missions/", include("apps.missions.urls")),
                path(
                    "social-auth/",
                    include("drf_social_oauth2.urls", namespace="drf"),
                ),
                path("auth/", include("apps.accounts.auth.urls")),
                path("files/", include("apps.files.urls")),
                path("profile/", include("apps.accounts.profile.urls")),
                path("countries/", include("apps.countries.urls")),
                path("", include("social_django.urls", namespace="social")),
                path("google-calendar/", include("apps.google_calendar.urls")),
                path("clubs/", include("apps.clubs.urls", namespace="clubs")),
                path("stats/", include("apps.stats.urls", namespace="stats")),
                path(
                    "club-demands/",
                    include("apps.clubs.demands.urls", namespace="club_demands"),
                ),
            ]
        ),
    ),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns.append(path("__debug__/", include("debug_toolbar.urls")))
