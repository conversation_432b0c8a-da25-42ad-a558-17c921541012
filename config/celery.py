from django.apps import apps
import os
from celery import Celery
from django.conf import settings
from celery.signals import task_failure, task_retry, task_success, task_revoked
import logging

logger = logging.getLogger(__name__)

DEV = os.environ.get("ENV", "False") == "1"
os.environ.setdefault(
    "DJANGO_SETTINGS_MODULE",
    "config.settings.production" if not DEV else "config.settings.development",
)

app = Celery("config")


app.conf.update(
    task_time_limit=300,
    task_soft_time_limit=60,
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    task_publish_retry=True,
    task_publish_retry_policy={
        "max_retries": 3,
        "interval_start": 0,
        "interval_step": 0.2,
        "interval_max": 0.2,
    },
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    worker_max_memory_per_child=50000,
    result_expires=3600,
    enable_utc=True,
    timezone=settings.TIME_ZONE,
)

app.config_from_object("django.conf:settings", namespace="CELERY")


app.autodiscover_tasks(lambda: [n.name for n in apps.get_app_configs()])


@task_failure.connect
def handle_task_failure(
    sender=None,
    task_id=None,
    exception=None,
    args=None,
    kwargs=None,
    traceback=None,
    einfo=None,
    **other,
):
    """Log task failures with detailed information"""
    logger.error(
        f"Task {sender.name} with id {task_id} failed: {exception}\nArgs: {args}\nKwargs: {kwargs}",
        exc_info=(type(exception), exception, traceback),
    )


@task_retry.connect
def handle_task_retry(sender=None, request=None, reason=None, einfo=None, **kwargs):
    """Log task retries"""
    logger.warning(
        f"Task {sender.name} is being retried: {reason}\n"
        f"Request: {request}\nAttempt: {request.retries + 1}"
    )


@task_success.connect
def handle_task_success(sender=None, **kwargs):
    """Log successful task completion"""
    logger.info(f"Task {sender.name} completed successfully")


@task_revoked.connect
def handle_task_revoked(
    sender=None, request=None, terminated=None, signum=None, expired=None, **kwargs
):
    """Log task revocations"""
    reason = "expired" if expired else "terminated" if terminated else "revoked"
    logger.warning(f"Task {sender.name} was {reason}")


@app.on_after_configure.connect
def setup_error_handlers(sender, **kwargs):
    """Setup global error handlers for the Celery app"""
    from celery.signals import celeryd_after_setup

    @celeryd_after_setup.connect
    def configure_worker(sender, instance, **kwargs):
        logger.info(f"Celery worker {sender} is ready")
