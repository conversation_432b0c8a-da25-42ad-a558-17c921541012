import os
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from apps.notifications.routing import websocket_urlpatterns
from channels.security.websocket import AllowedHostsOriginValidator
from channels.exceptions import RequestTimeout, RequestAborted
import logging

from core.middlewares.channels import JWTAuthMiddlewareStack

logger = logging.getLogger(__name__)

DEV = os.environ.get("ENV", "False") == "1"

os.environ.setdefault(
    "DJANGO_SETTINGS_MODULE",
    "config.settings.production" if not DEV else "config.settings.development",
)


ASGI_TIMEOUT = 60
ASGI_MAX_REQUEST_SIZE = 1024 * 1024 * 25


class TimeoutMiddleware:
    """Middleware to handle timeouts gracefully"""

    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        try:
            await self.app(scope, receive, send)
        except RequestTimeout:
            logger.warning(f"Request timeout for {scope.get('path', 'unknown path')}")
            await send(
                {
                    "type": "http.response.start",
                    "status": 504,
                    "headers": [
                        (b"content-type", b"application/json"),
                    ],
                }
            )
            await send(
                {
                    "type": "http.response.body",
                    "body": b'{"error": "Request timeout"}',
                }
            )
        except RequestAborted:
            logger.warning(f"Request aborted for {scope.get('path', 'unknown path')}")
            await send(
                {
                    "type": "http.response.start",
                    "status": 499,
                    "headers": [
                        (b"content-type", b"application/json"),
                    ],
                }
            )
            await send(
                {
                    "type": "http.response.body",
                    "body": b'{"error": "Client closed request"}',
                }
            )
        except Exception as e:
            logger.error(f"ASGI error: {str(e)}")
            await send(
                {
                    "type": "http.response.start",
                    "status": 500,
                    "headers": [
                        (b"content-type", b"application/json"),
                    ],
                }
            )
            await send(
                {
                    "type": "http.response.body",
                    "body": b'{"error": "Internal server error"}',
                }
            )


application = TimeoutMiddleware(
    ProtocolTypeRouter(
        {
            "http": get_asgi_application(),
            "websocket": AllowedHostsOriginValidator(
                JWTAuthMiddlewareStack(URLRouter(websocket_urlpatterns))
            ),
        }
    )
)
