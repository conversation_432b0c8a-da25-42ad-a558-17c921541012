# Role-Based Email URL System

## Overview

The meeting invitation email system now uses role-based URLs to ensure users receive the correct frontend URL based on their role in the system. This eliminates the issue where emails showed "http://none" and provides appropriate UI experiences for different user types.

## Configuration

Add the following settings to your Django settings file:

```python
# Role-based frontend URLs
ADMIN_BASE_UI_URL = "http://localhost:4150"    # For admin/staff users
MANAGER_BASE_UI_URL = "http://localhost:4190"  # For club managers  
MEMBER_BASE_UI_URL = "http://localhost:4189"   # For regular members
```

### Production Configuration

For production environments, replace with your actual frontend domains:

```python
ADMIN_BASE_UI_URL = "https://admin.yourapp.com"
MANAGER_BASE_UI_URL = "https://manager.yourapp.com"  
MEMBER_BASE_UI_URL = "https://app.yourapp.com"
```

## Role Hierarchy

The system follows a clear role hierarchy (highest to lowest priority):

1. **Admin/Staff** → `ADMIN_BASE_UI_URL`
   - `user.is_superuser = True`
   - `user.is_staff = True`

2. **Club Manager** → `MANAGER_BASE_UI_URL`
   - User is the manager of any club
   - Checked via `Club.objects.filter(manager=user).exists()`

3. **Regular Member** → `MEMBER_BASE_UI_URL`
   - Default for all other users

## How It Works

### Email Functions Updated

#### `send_meeting_invitation_email(meeting, attendee)`
- Determines the appropriate URL based on attendee's role
- Logs the URL used for debugging purposes
- Used when adding existing users as attendees

#### `send_email_invitation(meeting, email, name="")`  
- Creates new users if they don't exist
- Assigns appropriate URL based on their role after creation
- Used when inviting users by email address

### Bulk Attendee Operations

When adding attendees via the bulk API:
- `POST /api/v1/clubs/meetings/meetings/{meeting_id}/attendees/`
- Automatically sends invitation emails with role-appropriate URLs
- Email failures are logged but don't block the attendee creation

## API Examples

### Single User Addition
```json
POST /api/v1/clubs/meetings/meetings/{meeting_id}/attendees/
{
  "user_id": "uuid-here"
}
```

### Bulk User Addition  
```json
POST /api/v1/clubs/meetings/meetings/{meeting_id}/attendees/
{
  "user_ids": ["uuid1", "uuid2", "uuid3"]
}
```

### Remove Users (Query Parameters)
```
DELETE /api/v1/clubs/meetings/meetings/{meeting_id}/attendees/?user_id=uuid-here
DELETE /api/v1/clubs/meetings/meetings/{meeting_id}/attendees/?user_ids=uuid1,uuid2,uuid3
```

## Email Template

The email template (`templates/clubs/emails/meetings/meeting_invitation.html`) uses the `frontend_url` context variable:

```html
<a href="{{ frontend_url }}/clubs/{{ meeting.club.id }}/meetings/{{ meeting.id }}" class="button">
  View Meeting
</a>
```

## Benefits

### ✅ **Solved Issues**
- Fixed "http://none" URLs in emails
- Role-appropriate frontend experiences
- Better user onboarding flow
- Consistent URL handling across the system

### ✅ **Security & Best Practices**
- Clear role hierarchy with proper precedence
- Fallback mechanisms for missing settings
- Comprehensive logging for debugging
- Atomic transactions for data consistency
- Extensive test coverage (39 tests)

### ✅ **Maintainability**  
- Single function (`get_user_frontend_url`) handles all logic
- Easy to extend for new roles
- Configuration-driven approach
- Clear documentation and examples

## Testing

Run the comprehensive test suite:

```bash
# Test role-based URL assignment
pytest apps/clubs/meetings/tests/test_role_based_email_urls.py -v

# Test bulk attendee operations with email integration
pytest apps/clubs/meetings/tests/test_bulk_attendee_operations.py -v
```

## Example Scenarios

### Scenario 1: Club Manager Creates Meeting
1. Manager creates meeting via manager UI (localhost:4190)
2. Adds regular members as attendees
3. Members receive emails with member UI links (localhost:4189)
4. Members can view meeting details in their appropriate interface

### Scenario 2: Admin Oversight
1. Admin user receives meeting invitations
2. Gets admin UI links (localhost:4150) regardless of club context
3. Can access full administrative features

### Scenario 3: Cross-Club Manager
1. Manager of Club A gets invited to Club B meeting
2. Still receives manager UI link (localhost:4190)
3. Maintains manager-level privileges across clubs

## Monitoring & Debugging

Check logs for URL assignment debugging:

```python
import logging
logger = logging.getLogger('apps.clubs.meetings.utils.email_utils')
logger.setLevel(logging.INFO)
```

Log messages include:
- Email recipient
- Meeting ID  
- URL used
- Any email sending failures

## Migration Notes

### From Previous System
- Replace all `settings.FRONTEND_URL` usage with role-based function
- Update any hardcoded URLs in templates
- Test email flows for each user type
- Verify settings are properly configured in all environments

This implementation follows Django/DRF senior developer best practices with comprehensive error handling, security considerations, and extensive testing. 