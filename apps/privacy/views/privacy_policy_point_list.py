from rest_framework import generics
from drf_spectacular.utils import extend_schema

from apps.privacy.models import PrivacyPolicyPoint
from apps.privacy.serializers.privacy_policy_point import PrivacyPolicyPointSerializer
from core.abstract.paginations import MetaPageNumberPagination


@extend_schema(tags=["privacy"])
class PrivacyPolicyPointListView(generics.ListAPIView):
    queryset = PrivacyPolicyPoint.objects.all()
    pagination_class = MetaPageNumberPagination
    serializer_class = PrivacyPolicyPointSerializer
