import pytest
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from apps.privacy.models import PrivacyPolicyPoint


@pytest.fixture
def api_client():
    return APIClient()


@pytest.fixture
def create_privacy_policy_points(db):
    points = [
        PrivacyPolicyPoint.objects.create(
            title="المقدمة",
            content="نحن في ذكاءات نقدّر خصوصية مستخدمينا...",
            order=1,
        ),
        PrivacyPolicyPoint.objects.create(
            title="جمع البيانات",
            content="نجمع البيانات الشخصية التي تقدمها لنا عند استخدامك لموقعنا...",
            order=2,
        ),
    ]
    return points


@pytest.mark.django_db
def test_privacy_policy_point_list_view(api_client, create_privacy_policy_points):
    url = reverse("privacy-policy-list")
    response = api_client.get(url)

    assert response.status_code == status.HTTP_200_OK
    assert response.json()["meta"]["count"] == 2
    assert response.json()["results"][0]["title"] == "المقدمة"
    assert response.json()["results"][1]["title"] == "جمع البيانات"


@pytest.mark.django_db
def test_privacy_policy_point_list_empty(api_client):
    url = reverse("privacy-policy-list")
    response = api_client.get(url)

    assert response.status_code == status.HTTP_200_OK
    assert response.json()["meta"]["count"] == 0


@pytest.mark.django_db
def test_privacy_policy_point_ordering(api_client, create_privacy_policy_points):
    url = reverse("privacy-policy-list")
    response = api_client.get(url)

    assert response.status_code == status.HTTP_200_OK
    assert response.json()["results"][0]["order"] == 1
    assert response.json()["results"][1]["order"] == 2
