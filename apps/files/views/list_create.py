from rest_framework import viewsets
from rest_framework import permissions
from drf_spectacular.utils import extend_schema, extend_schema_view
from django.utils.translation import gettext as _
from core.abstract.paginations import MetaPageNumberPagination
from core.decorators.error_handler import api_error_handler
from apps.files.models import File
from apps.files.serializers.create_file import CreateFileSerializer
from apps.files.serializers.file import FileSerializer


@extend_schema_view(
    list=extend_schema(
        tags=["files"],
        summary="List all files",
        description="Returns a list of all files uploaded by the authenticated user.",
    ),
    retrieve=extend_schema(
        tags=["files"],
        summary="Retrieve a specific file",
        description="Returns details of a specific file. Users can only access files they uploaded.",
    ),
    create=extend_schema(
        tags=["files"],
        summary="Upload a new file",
        description="Uploads a new file to the server. Only authenticated users can upload files.",
    ),
    update=extend_schema(
        tags=["files"],
        summary="Update a file",
        description="Updates a file. Only the owner of the file can perform this action.",
    ),
    partial_update=extend_schema(
        tags=["files"],
        summary="Partially update a file",
        description="Partially updates a file. Only the owner of the file can perform this action.",
    ),
    destroy=extend_schema(
        tags=["files"],
        summary="Delete a file",
        description="Deletes a file. Only the owner of the file can perform this action.",
    ),
)
class FileListCreateView(viewsets.ModelViewSet):
    queryset = File.objects.all()
    serializer_class = CreateFileSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = MetaPageNumberPagination

    @extend_schema(
        tags=["Files"],
        request=CreateFileSerializer,
        responses={201: FileSerializer, 400: _("Bad Request")},
    )
    @api_error_handler
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @extend_schema(
        tags=["Files"],
        responses={200: FileSerializer},
    )
    @api_error_handler
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    def get_serializer_context(self):
        return {"request": self.request}

    def get_serializer_class(self):
        if self.action == "create":
            return CreateFileSerializer
        else:
            return FileSerializer
