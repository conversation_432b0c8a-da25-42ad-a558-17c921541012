import uuid
from rest_framework import serializers
from rest_framework.exceptions import NotAcceptable
from django.utils.translation import gettext as _

from apps.files.models import File
from apps.files.utils import guess_file_type_by_extension


class FileSerializer(serializers.ModelSerializer):
    file = serializers.FileField(required=True)

    class Meta:
        model = File
        fields = ["id", "file", "name", "created", "updated"]

    def to_representation(self, instance):
        rep = super().to_representation(instance)
        rep["type"] = guess_file_type_by_extension(instance.file.name)
        return rep
