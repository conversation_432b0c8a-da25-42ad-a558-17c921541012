import uuid
from rest_framework import serializers
from rest_framework.exceptions import NotAcceptable
from django.utils.translation import gettext as _

from apps.files.models import File
from apps.files.utils import guess_file_type_by_extension


class CreateFileSerializer(serializers.ModelSerializer):
    file = serializers.FileField(required=True)

    class Meta:
        model = File
        fields = ["file"]

    def create(self, validated_data):
        file = validated_data["file"]
        file_extension = file.name.split(".")[-1]
        if file_extension not in [
            "pdf",
            "docx",
            "doc",
            "xls",
            "xlsx",
            "csv",
            "txt",
            "png",
            "jpg",
            "jpeg",
            "mp4",
            "mp3",
        ]:
            raise NotAcceptable(
                detail=_(
                    "Only pdf, docx, doc, xls, xlsx, csv, txt, png, jpg, jpeg, mp4, mp3 files are allowed"
                )
            )
        file_name = f"{uuid.uuid4()}.{file_extension}"
        return File.objects.create(file=file, name=file_name)

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["id"] = instance.id
        representation["type"] = guess_file_type_by_extension(instance.file.name)
        return representation
