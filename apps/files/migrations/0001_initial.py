# Generated by Django 4.2.4 on 2024-07-17 18:52

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="File",
            fields=[
                ("created", models.DateTimeField(auto_now_add=True)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("name", models.CharField(max_length=200, unique=True)),
                ("file", models.FileField(max_length=1000, upload_to="files")),
            ],
            options={
                "verbose_name_plural": "files",
                "ordering": ["name"],
            },
        ),
    ]
