# Generated by Django 4.2.9 on 2025-03-16 15:17

from django.db import migrations, models
import django.utils.timezone
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Domain",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=100, unique=True, verbose_name="Name"),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("language", "Language Learning"),
                            ("education", "Education"),
                            ("productivity", "Productivity"),
                            ("health", "Health & Fitness"),
                            ("career", "Career Development"),
                            ("finance", "Personal Finance"),
                            ("creativity", "Creativity"),
                            ("social", "Social Skills"),
                            ("technology", "Technology"),
                            ("personal", "Personal Growth"),
                        ],
                        max_length=20,
                        verbose_name="Category",
                    ),
                ),
                (
                    "icon",
                    models.CharField(blank=True, max_length=50, verbose_name="Icon"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("order", models.PositiveIntegerField(default=0, verbose_name="Order")),
            ],
            options={
                "verbose_name": "Domain",
                "verbose_name_plural": "Domains",
                "ordering": ["order", "name"],
            },
        ),
    ]
