from rest_framework import serializers
from drf_spectacular.utils import extend_schema_serializer, OpenApiExample
from django.utils.translation import gettext_lazy as _
from apps.domains.models import Domain


@extend_schema_serializer(
    examples=[
        OpenApiExample(
            "Domain Example",
            value={
                "id": 1,
                "name": "Language Learning",
                "description": "Improve your language skills",
                "category": "language",
                "icon": "http://example.com/media/domains/icons/language.png",
                "is_active": True,
                "order": 1,
            },
            response_only=True,
        ),
    ],
)
class DomainSerializer(serializers.ModelSerializer):
    icon = serializers.SerializerMethodField()
    name = serializers.SerializerMethodField()
    description = serializers.SerializerMethodField()

    class Meta:
        model = Domain
        fields = [
            "id",
            "name",
            "description",
            "category",
            "icon",
            "is_active",
            "order",
            "created",
            "updated",
        ]
        read_only_fields = ["id", "created", "updated"]

    def get_icon(self, obj):
        request = self.context.get("request")
        if request and obj.icon:
            return request.build_absolute_uri(obj.icon_url)
        return obj.icon_url

    def get_name(self, obj):
        return _(obj.name)

    def get_description(self, obj):
        return _(obj.description)
