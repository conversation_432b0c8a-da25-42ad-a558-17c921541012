from django.shortcuts import render
from django.core.cache import cache
from django.conf import settings
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from drf_spectacular.utils import extend_schema, extend_schema_view, OpenApiParameter
from drf_spectacular.types import OpenApiTypes
from rest_framework.response import Response

from core.abstract.paginations import MetaPageNumberPagination
from apps.domains.models import Domain
from apps.domains.serializers.domain import DomainSerializer
from apps.domains.filters import DomainFilter


@extend_schema_view(
    list=extend_schema(
        tags=["domains"],
        summary="List all domains",
        description="Returns a list of all active domains.",
    ),
    retrieve=extend_schema(
        tags=["domains"],
        summary="Retrieve a specific domain",
        description="Returns details of a specific domain.",
    ),
)
class DomainViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Domain.objects.filter(is_active=True)
    serializer_class = DomainSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]
    lookup_field = "id"
    pagination_class = MetaPageNumberPagination
    filterset_class = DomainFilter

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["request"] = self.request
        return context

    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="category",
                type=OpenApiTypes.STR,
                description="Filter domains by category",
                required=False,
            ),
            OpenApiParameter(
                name="name",
                type=OpenApiTypes.STR,
                description="Filter domains by name (case-insensitive contains)",
                required=False,
            ),
            OpenApiParameter(
                name="is_active",
                type=OpenApiTypes.BOOL,
                description="Filter domains by active status",
                required=False,
            ),
        ],
        tags=["Domains"],
    )
    def list(self, request, *args, **kwargs):
        category = request.query_params.get("category", None)
        category_str = str(category) if category is not None else "all"
        cache_key = f"domain_list_{category_str}"
        cached_data = cache.get(cache_key)

        if cached_data is None:
            response = super().list(request, *args, **kwargs)
            cache.set(cache_key, response.data, timeout=settings.CACHE_TTL)
            return response

        return Response(cached_data)

    @extend_schema(tags=["Domains"])
    def retrieve(self, request, *args, **kwargs):
        cache_key = f"domain_detail_{str(kwargs['id'])}"
        cached_data = cache.get(cache_key)

        if cached_data is None:
            response = super().retrieve(request, *args, **kwargs)
            cache.set(cache_key, response.data, timeout=settings.CACHE_TTL)
            return response

        return Response(cached_data)
