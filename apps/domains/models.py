from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.cache import cache
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from core.abstract.models import AbstractModel
from django.conf import settings


class Domain(AbstractModel):
    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_("Name"),
    )
    description = models.TextField(
        blank=True,
        verbose_name=_("Description"),
    )
    category = models.CharField(
        max_length=20,
        choices=[
            ("language", _("Language Learning")),
            ("education", _("Education")),
            ("productivity", _("Productivity")),
            ("health", _("Health & Fitness")),
            ("career", _("Career Development")),
            ("finance", _("Personal Finance")),
            ("creativity", _("Creativity")),
            ("social", _("Social Skills")),
            ("technology", _("Technology")),
            ("personal", _("Personal Growth")),
        ],
        verbose_name=_("Category"),
    )
    icon = models.ImageField(
        upload_to="domains/icons/",
        blank=True,
        verbose_name=_("Icon"),
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Is Active"),
    )
    order = models.PositiveIntegerField(
        default=0,
        verbose_name=_("Order"),
    )

    class Meta:
        verbose_name = _("Domain")
        verbose_name_plural = _("Domains")
        ordering = ["order", "name"]

    def __str__(self):
        return self.name

    @property
    def icon_url(self):
        if self.icon:
            return f"/media/{self.icon}"
        return None


@receiver([post_save, post_delete], sender=Domain)
def clear_domain_cache(sender, instance, **kwargs):
    cache.clear()
