from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from apps.domains.models import Domain


class DomainPaginationTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.url = reverse("domain-list")
        for i in range(15):
            Domain.objects.create(
                name=f"Domain {i}",
                category="language",
            )

    def test_default_pagination(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["meta"]["count"], 15)
        self.assertEqual(len(response.data["results"]), 5)

    def test_custom_page_size(self):
        response = self.client.get(f"{self.url}?limit=10")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["meta"]["count"], 15)
        self.assertEqual(len(response.data["results"]), 10)

    def test_invalid_page_size(self):
        response = self.client.get(f"{self.url}?limit=-1")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_page_out_of_range(self):
        response = self.client.get(f"{self.url}?page=999")
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_pagination_with_category_filter(self):
        Domain.objects.create(
            name="Health Domain",
            description="Health Description",
            category="health",
            icon="health-icon",
        )
        response = self.client.get(f"{self.url}?category=health")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["results"]), 1)
