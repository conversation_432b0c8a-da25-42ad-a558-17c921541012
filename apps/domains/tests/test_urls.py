from django.test import TestCase
from django.urls import reverse


class DomainURLTests(TestCase):
    def test_domain_list_url(self):
        url = reverse("domain-list")
        self.assertEqual(url, "/api/v1/domains/domains/")

    def test_domain_detail_url(self):
        url = reverse("domain-detail", kwargs={"id": 1})
        self.assertEqual(url, "/api/v1/domains/domains/1/")

    def test_domain_urls_included(self):
        url = reverse("domain-list")
        self.assertTrue(url.startswith("/api/"))
