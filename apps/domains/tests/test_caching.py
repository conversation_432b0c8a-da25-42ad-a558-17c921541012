from django.test import TestCase
from django.urls import reverse
from django.core.cache import cache
from rest_framework.test import APIClient
from rest_framework import status
from apps.domains.models import Domain
import unittest


class DomainCachingTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.domain = Domain.objects.create(
            name="Test Domain",
            description="Test Description",
            category="language",
            icon="test-icon",
        )
        cache.clear()

    @unittest.skip("Caching is disabled in test environment")
    def test_list_cache_hit(self):
        response = self.client.get(reverse("domain-list"))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(cache.get("domain_list_all"))

    @unittest.skip("Caching is disabled in test environment")
    def test_detail_cache_hit(self):
        url = reverse("domain-detail", kwargs={"id": self.domain.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(cache.get(f"domain_detail_{self.domain.id}"))

    @unittest.skip("Caching is disabled in test environment")
    def test_cache_invalidation(self):
        response = self.client.get(reverse("domain-list"))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(cache.get("domain_list_all"))

        self.domain.name = "Updated Domain"
        self.domain.save()

        self.assertIsNone(cache.get("domain_list_all"))
        self.assertIsNone(cache.get(f"domain_detail_{self.domain.id}"))

    @unittest.skip("Caching is disabled in test environment")
    def test_cache_timeout(self):
        response = self.client.get(reverse("domain-list"))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(cache.get("domain_list_all"))

        Domain.objects.create(
            name="Another Domain",
            description="Another Description",
            category="education",
            icon="another-icon",
        )

        response = self.client.get(reverse("domain-list"))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["meta"]["count"], 2)

    @unittest.skip("Caching is disabled in test environment")
    def test_category_filter_cache(self):
        response = self.client.get(reverse("domain-list"), {"category": "language"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)

        cached_data = cache.get("domain_list_language")
        self.assertIsNotNone(cached_data)
        self.assertEqual(len(cached_data["results"]), 1)

        Domain.objects.create(
            name="Another Language Domain",
            description="Another Description",
            category="language",
            icon="another-icon",
        )
        cache.clear()

        response = self.client.get(reverse("domain-list"), {"category": "language"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 2)
