from django.test import TestCase
from django.core.files.storage import default_storage
from django.conf import settings
import os
from apps.domains.models import Domain
from apps.domains.seeders import seed_domains


class DomainIconTests(TestCase):
    def setUp(self):
        seed_domains()

    def test_icon_directory_exists(self):
        icon_dir = os.path.join(settings.MEDIA_ROOT, "domains", "icons")
        self.assertTrue(os.path.exists(icon_dir), "Icon directory does not exist")

    def test_icon_files_exist(self):
        domains = Domain.objects.all()
        for domain in domains:
            icon_path = domain.icon.name
            self.assertTrue(
                default_storage.exists(icon_path),
                f"Icon file {icon_path} does not exist",
            )

    def test_icon_paths_are_valid(self):
        domains = Domain.objects.all()
        for domain in domains:
            icon_path = domain.icon.name
            self.assertTrue(
                icon_path.startswith("domains/icons/"),
                f"Icon path {icon_path} does not start with 'domains/icons/'",
            )

    def test_icon_urls_are_valid(self):
        domains = Domain.objects.all()
        for domain in domains:
            icon_url = domain.icon_url
            if icon_url is not None:
                self.assertTrue(
                    icon_url.startswith("/media/"),
                    f"Icon URL {icon_url} does not start with '/media/'",
                )
                self.assertTrue(
                    icon_url.endswith(domain.icon.name),
                    f"Icon URL {icon_url} does not end with the correct filename",
                )

    def test_icon_files_are_copied_correctly(self):
        domains = Domain.objects.all()
        for domain in domains:
            icon_path = domain.icon.name
            source_path = f"domains/{os.path.basename(icon_path)}"

            if default_storage.exists(source_path):
                with default_storage.open(source_path, "rb") as source_file:
                    source_content = source_file.read()
                with default_storage.open(icon_path, "rb") as dest_file:
                    dest_content = dest_file.read()
                self.assertEqual(
                    source_content,
                    dest_content,
                    f"Icon file {icon_path} was not copied correctly",
                )
