from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from apps.domains.models import Domain


class DomainAdminTests(TestCase):
    def setUp(self):
        self.client = Client()
        self.admin_user = get_user_model().objects.create_superuser(
            username="admin",
            email="<EMAIL>",
            password="admin123",
        )
        self.client.force_login(self.admin_user)
        self.domain = Domain.objects.create(
            name="Test Domain",
            description="Test Description",
            category="language",
            icon="test-icon",
        )

    def test_domain_list_view(self):
        url = reverse("admin:domains_domain_changelist")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test Domain")

    def test_domain_detail_view(self):
        url = reverse("admin:domains_domain_change", args=[self.domain.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test Domain")
        self.assertContains(response, "Test Description")

    def test_domain_add_view(self):
        url = reverse("admin:domains_domain_add")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_domain_delete_view(self):
        url = reverse("admin:domains_domain_delete", args=[self.domain.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_domain_search(self):
        url = reverse("admin:domains_domain_changelist")
        response = self.client.get(url, {"q": "Test"})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test Domain")

    def test_domain_filter(self):
        url = reverse("admin:domains_domain_changelist")
        response = self.client.get(url, {"category": "language"})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test Domain")

    def test_domain_ordering(self):
        Domain.objects.create(
            name="Second Domain",
            category="education",
            order=2,
        )
        Domain.objects.create(
            name="Third Domain",
            category="productivity",
            order=1,
        )
        url = reverse("admin:domains_domain_changelist")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
