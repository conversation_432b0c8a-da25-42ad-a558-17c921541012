from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.core.cache import cache
from apps.domains.models import Domain


class DomainViewTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.url = reverse("domain-list")
        self.domain = Domain.objects.create(
            name="Test Domain",
            description="Test Description",
            category="language",
            icon="test-icon",
        )
        cache.clear()

    def test_list_domains(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["meta"]["count"], 1)

    def test_list_domains_caching(self):
        response1 = self.client.get(self.url)
        self.assertEqual(response1.status_code, status.HTTP_200_OK)

        Domain.objects.create(
            name="New Domain",
            category="education",
        )

        response2 = self.client.get(self.url)
        self.assertEqual(response2.status_code, status.HTTP_200_OK)
        self.assertEqual(response2.data["meta"]["count"], 2)

    def test_retrieve_domain_caching(self):
        url = reverse("domain-detail", kwargs={"id": self.domain.id})

        response1 = self.client.get(url)
        self.assertEqual(response1.status_code, status.HTTP_200_OK)

        self.domain.name = "Updated Name"
        self.domain.save()

        response2 = self.client.get(url)
        self.assertEqual(response2.status_code, status.HTTP_200_OK)
        self.assertEqual(response2.data["name"], "Updated Name")

    def test_list_domains_by_category(self):
        Domain.objects.create(
            name="Education Domain",
            category="education",
        )
        Domain.objects.create(
            name="Productivity Domain",
            category="productivity",
        )

        response = self.client.get(f"{self.url}?category=education")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["meta"]["count"], 1)

    def test_inactive_domains_not_shown(self):
        Domain.objects.create(
            name="Inactive Domain",
            category="education",
            is_active=False,
        )

        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["meta"]["count"], 1)
