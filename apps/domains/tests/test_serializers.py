from django.test import TestCase
from apps.domains.models import Domain
from apps.domains.serializers.domain import DomainSerializer


class DomainSerializerTests(TestCase):
    def setUp(self):
        self.domain = Domain.objects.create(
            name="Test Domain",
            description="Test Description",
            category="language",
            icon="test-icon",
            is_active=True,
            order=1,
        )
        self.serializer = DomainSerializer(instance=self.domain)

    def test_contains_expected_fields(self):
        data = self.serializer.data
        expected_fields = [
            "id",
            "name",
            "description",
            "category",
            "icon",
            "is_active",
            "order",
            "created",
            "updated",
        ]
        self.assertEqual(set(data.keys()), set(expected_fields))

    def test_read_only_fields(self):
        data = {
            "id": 999,
            "name": "New Name",
            "created": "2024-01-01T00:00:00Z",
            "updated": "2024-01-01T00:00:00Z",
        }
        serializer = DomainSerializer(self.domain, data=data, partial=True)
        self.assertTrue(serializer.is_valid())
        domain = serializer.save()
        self.assertEqual(domain.id, self.domain.id)
        self.assertEqual(domain.name, "Test Domain")
        self.assertEqual(domain.created, self.domain.created)
        self.assertEqual(domain.updated, self.domain.updated)
