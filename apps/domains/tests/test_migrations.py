from django.test import TestCase
from apps.domains.models import Domain
from apps.domains.seeders import seed_domains


class InitialDomainsMigrationTests(TestCase):
    def setUp(self):
        seed_domains()

    def test_initial_domains_created(self):
        domains = Domain.objects.all()
        self.assertEqual(domains.count(), 10)

    def test_domain_categories(self):
        categories = set(Domain.objects.values_list("category", flat=True))
        expected_categories = {
            "language",
            "education",
            "productivity",
            "health",
            "career",
            "finance",
            "creativity",
            "social",
            "personal",
        }
        self.assertEqual(categories, expected_categories)

    def test_domain_ordering(self):
        domains = Domain.objects.order_by("order")
        self.assertEqual(domains[0].name, "Language Learning")
        self.assertEqual(domains[1].name, "Education")
        self.assertEqual(domains[2].name, "Productivity")

    def test_domain_descriptions(self):
        domains = Domain.objects.all()
        for domain in domains:
            self.assertTrue(domain.description)

    def test_domain_icons(self):
        domains = Domain.objects.all()
        for domain in domains:
            self.assertTrue(domain.icon)
