from django.test import TestCase
from django.core.files.storage import default_storage
from django.utils.translation import activate, get_language
from django.conf import settings
import os
from apps.domains.models import Domain
from apps.domains.seeders import seed_domains


class DomainSeederTests(TestCase):
    def setUp(self):
        self.current_language = get_language()
        activate("en")
        seed_domains()

    def tearDown(self):
        activate(self.current_language)

    def test_domains_count(self):
        self.assertEqual(Domain.objects.count(), 10)

    def test_domain_data(self):
        expected_domains = [
            ("Language Learning", "language", "learning-new-language.png", 1),
            ("Education", "education", "school&education.png", 2),
            ("Productivity", "productivity", "productivity.png", 3),
            ("Health & Fitness", "health", "health&fitness.png", 4),
            ("Career Development", "career", "learning&improvements.png", 5),
            ("Personal Finance", "finance", "financial-habits.png", 6),
            ("Creativity", "creativity", "Creativity&hobbies.png", 7),
            ("Social Skills", "social", "self-care.png", 8),
            ("Health Habits", "health", "following-a-diet.png", 9),
            ("Other", "personal", "something-else.png", 10),
        ]

        for name, category, icon, order in expected_domains:
            domain = Domain.objects.get(name=name)
            self.assertEqual(domain.category, category)
            self.assertEqual(domain.icon.name, f"domains/icons/{icon}")
            self.assertEqual(domain.order, order)
            self.assertTrue(domain.is_active)
            self.assertTrue(domain.description)

    def test_icon_files_exist_and_are_valid(self):
        """Test that all domain icons exist and have content."""
        domains = Domain.objects.all()
        for domain in domains:

            self.assertTrue(
                default_storage.exists(domain.icon.name),
                f"Icon file {domain.icon.name} does not exist",
            )

            icon_path = domain.icon.path
            self.assertTrue(
                os.path.exists(icon_path),
                f"Icon file {icon_path} does not exist on disk",
            )
            self.assertGreater(
                os.path.getsize(icon_path),
                0,
                f"Icon file {domain.icon.name} exists but is empty (0 bytes)",
            )

    def test_icon_url(self):
        domain = Domain.objects.get(name="Language Learning")
        expected_url = "/media/domains/icons/learning-new-language.png"
        self.assertEqual(domain.icon_url, expected_url)

    def test_unique_orders(self):
        orders = Domain.objects.values_list("order", flat=True)
        self.assertEqual(len(set(orders)), 10)
        self.assertEqual(min(orders), 1)
        self.assertEqual(max(orders), 10)

    def test_reseed_domains(self):
        initial_count = Domain.objects.count()
        initial_domains = list(
            Domain.objects.values("name", "description", "category", "icon", "order")
        )

        domain = Domain.objects.get(name="Language Learning")
        domain.description = "Modified description"
        domain.save()

        seed_domains()

        self.assertEqual(Domain.objects.count(), initial_count)

        for initial_data in initial_domains:
            domain = Domain.objects.get(name=initial_data["name"])
            self.assertEqual(domain.description, initial_data["description"])
            self.assertEqual(domain.category, initial_data["category"])
            self.assertEqual(domain.icon.name, initial_data["icon"])
            self.assertEqual(domain.order, initial_data["order"])

    def test_domain_categories_distribution(self):
        category_counts = {
            "language": 1,
            "education": 1,
            "productivity": 1,
            "health": 2,
            "career": 1,
            "finance": 1,
            "creativity": 1,
            "social": 1,
            "personal": 1,
        }

        for category, expected_count in category_counts.items():
            actual_count = Domain.objects.filter(category=category).count()
            self.assertEqual(
                actual_count,
                expected_count,
                f"Expected {expected_count} domains with category '{category}', but found {actual_count}",
            )

    def test_translation_strings(self):
        activate("es")
        domain = Domain.objects.get(category="language")
        self.assertEqual(str(domain.name), "Language Learning")
        self.assertTrue(domain.description)

    def test_delete_and_recreate_domains_without_icons(self):
        """Test that domains without icons are deleted and recreated with valid icons."""

        from apps.domains.seeders import DEFAULT_ICON

        test_domain = Domain.objects.create(
            name="Test Domain Without Icon",
            description="Test description",
            category="personal",
            icon="domains/icons/non_existent_icon.png",
            order=11,
        )

        domain_id = test_domain.id

        if default_storage.exists(test_domain.icon.name):
            default_storage.delete(test_domain.icon.name)

        seed_domains()

        with self.assertRaises(Domain.DoesNotExist):
            Domain.objects.get(id=domain_id)

        recreated_domain = Domain.objects.get(name="Test Domain Without Icon")

        self.assertTrue(
            DEFAULT_ICON.split(".")[0] in recreated_domain.icon.name,
            f"Expected icon name to contain '{DEFAULT_ICON.split('.')[0]}' but got '{recreated_domain.icon.name}'",
        )

        self.assertTrue(
            default_storage.exists(recreated_domain.icon.name),
            f"Icon file {recreated_domain.icon.name} does not exist",
        )
        self.assertGreater(
            os.path.getsize(recreated_domain.icon.path),
            0,
            f"Icon file {recreated_domain.icon.name} exists but is empty",
        )

        recreated_domain.delete()
