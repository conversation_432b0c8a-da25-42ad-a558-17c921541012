from django.test import TestCase
from django.core.exceptions import ValidationError
from apps.domains.models import Domain


class DomainModelTests(TestCase):
    def test_domain_creation(self):
        domain = Domain.objects.create(
            name="Test Domain",
            description="Test Description",
            category="language",
            icon="test-icon",
        )
        self.assertEqual(domain.name, "Test Domain")
        self.assertEqual(domain.description, "Test Description")
        self.assertEqual(domain.category, "language")
        self.assertEqual(domain.icon, "test-icon")
        self.assertTrue(domain.is_active)
        self.assertEqual(domain.order, 0)

    def test_domain_str(self):
        domain = Domain.objects.create(
            name="Test Domain",
            description="Test Description",
            category="language",
            icon="test-icon",
        )
        self.assertEqual(str(domain), "Test Domain")

    def test_domain_ordering(self):
        Domain.objects.create(
            name="Domain 1",
            description="Description 1",
            category="language",
            icon="icon1",
            order=2,
        )
        Domain.objects.create(
            name="Domain 2",
            description="Description 2",
            category="education",
            icon="icon2",
            order=1,
        )
        Domain.objects.create(
            name="Domain 3",
            description="Description 3",
            category="productivity",
            icon="icon3",
            order=3,
        )

        domains = Domain.objects.all()
        self.assertEqual(domains[0].name, "Domain 2")
        self.assertEqual(domains[1].name, "Domain 1")
        self.assertEqual(domains[2].name, "Domain 3")

    def test_domain_unique_name(self):
        Domain.objects.create(
            name="Test Domain",
            description="Test Description",
            category="language",
            icon="test-icon",
        )
        with self.assertRaises(Exception):
            Domain.objects.create(
                name="Test Domain",
                description="Another Description",
                category="education",
                icon="another-icon",
            )

    def test_domain_category_choices(self):
        valid_categories = [
            "language",
            "education",
            "productivity",
            "health",
            "career",
            "finance",
            "creativity",
            "social",
            "technology",
            "personal",
        ]
        for category in valid_categories:
            domain = Domain.objects.create(
                name=f"Test Domain {category}",
                description="Test Description",
                category=category,
                icon="test-icon",
            )
            domain.full_clean()

        domain = Domain.objects.create(
            name="Invalid Category Domain",
            description="Test Description",
            category="invalid_category",
            icon="test-icon",
        )
        with self.assertRaises(ValidationError):
            domain.full_clean()
