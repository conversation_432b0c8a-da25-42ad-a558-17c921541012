from django.db import transaction
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from django.conf import settings
import os
import shutil
import logging
from .models import Domain

logger = logging.getLogger(__name__)


DEFAULT_ICON = "something-else.png"


def seed_domains():
    domains = [
        {
            "name": "Language Learning",
            "description": "Learn new languages and improve your communication skills",
            "category": "language",
            "icon": "learning-new-language.png",
            "order": 1,
        },
        {
            "name": "Education",
            "description": "Enhance your academic knowledge and skills",
            "category": "education",
            "icon": "school&education.png",
            "order": 2,
        },
        {
            "name": "Productivity",
            "description": "Boost your efficiency and time management",
            "category": "productivity",
            "icon": "productivity.png",
            "order": 3,
        },
        {
            "name": "Health & Fitness",
            "description": "Improve your physical health and fitness",
            "category": "health",
            "icon": "health&fitness.png",
            "order": 4,
        },
        {
            "name": "Career Development",
            "description": "Advance your professional growth and skills",
            "category": "career",
            "icon": "learning&improvements.png",
            "order": 5,
        },
        {
            "name": "Personal Finance",
            "description": "Manage your money and financial goals",
            "category": "finance",
            "icon": "financial-habits.png",
            "order": 6,
        },
        {
            "name": "Creativity",
            "description": "Express yourself through art and creative pursuits",
            "category": "creativity",
            "icon": "Creativity&hobbies.png",
            "order": 7,
        },
        {
            "name": "Social Skills",
            "description": "Build better relationships and communication",
            "category": "social",
            "icon": "self-care.png",
            "order": 8,
        },
        {
            "name": "Health Habits",
            "description": "Develop and maintain healthy lifestyle habits",
            "category": "health",
            "icon": "following-a-diet.png",
            "order": 9,
        },
        {
            "name": "Other",
            "description": "Explore other areas of personal development",
            "category": "personal",
            "icon": "something-else.png",
            "order": 10,
        },
    ]

    with transaction.atomic():

        media_domains_path = os.path.join(settings.MEDIA_ROOT, "domains", "icons")
        os.makedirs(media_domains_path, exist_ok=True)

        default_static_icon_path = os.path.join(
            settings.BASE_DIR, "apps", "domains", "static", "domains", DEFAULT_ICON
        )

        if (
            not os.path.exists(default_static_icon_path)
            or os.path.getsize(default_static_icon_path) == 0
        ):
            raise ValueError(
                f"Default icon '{DEFAULT_ICON}' is missing or empty. Cannot proceed with seeding."
            )

        default_media_icon_path = os.path.join(media_domains_path, DEFAULT_ICON)
        shutil.copy2(default_static_icon_path, default_media_icon_path)

        for domain_data in domains:
            icon_name = domain_data["icon"]
            static_icon_path = os.path.join(
                settings.BASE_DIR, "apps", "domains", "static", "domains", icon_name
            )
            media_icon_path = os.path.join(media_domains_path, icon_name)
            relative_icon_path = f"domains/icons/{icon_name}"

            icon_is_valid = (
                os.path.exists(static_icon_path)
                and os.path.getsize(static_icon_path) > 0
            )

            if icon_is_valid:

                shutil.copy2(static_icon_path, media_icon_path)
                logger.info(
                    f"Using icon '{icon_name}' for domain '{domain_data['name']}'"
                )
            else:

                logger.warning(
                    f"Icon '{icon_name}' for domain '{domain_data['name']}' is missing or empty. "
                    f"Using default icon '{DEFAULT_ICON}' instead."
                )
                relative_icon_path = f"domains/icons/{DEFAULT_ICON}"

            domain, _ = Domain.objects.update_or_create(
                name=domain_data["name"],
                defaults={
                    "description": domain_data["description"],
                    "category": domain_data["category"],
                    "icon": relative_icon_path,
                    "order": domain_data["order"],
                },
            )

        domains_to_delete = []

        for domain in Domain.objects.all():

            if not domain.icon or not default_storage.exists(domain.icon.name):
                logger.warning(
                    f"Domain '{domain.name}' has missing icon. Will delete and recreate it."
                )
                domains_to_delete.append(domain.id)

        domains_to_recreate = []
        for domain_id in domains_to_delete:
            domain = Domain.objects.get(id=domain_id)

            matching_domain = None
            for domain_data in domains:
                if domain_data["name"] == domain.name:
                    matching_domain = domain_data
                    break

            if matching_domain:

                domains_to_recreate.append(
                    {
                        "name": domain.name,
                        "description": domain.description,
                        "category": domain.category,
                        "icon": matching_domain["icon"],
                        "order": domain.order,
                        "is_active": domain.is_active,
                    }
                )
            else:

                domains_to_recreate.append(
                    {
                        "name": domain.name,
                        "description": domain.description,
                        "category": domain.category,
                        "icon": DEFAULT_ICON,
                        "order": domain.order,
                        "is_active": domain.is_active,
                    }
                )

        if domains_to_delete:
            logger.info(f"Deleting {len(domains_to_delete)} domains without icons")
            Domain.objects.filter(id__in=domains_to_delete).delete()

        for domain_data in domains_to_recreate:
            icon_name = domain_data["icon"]
            static_icon_path = os.path.join(
                settings.BASE_DIR, "apps", "domains", "static", "domains", icon_name
            )
            media_domains_path = os.path.join(settings.MEDIA_ROOT, "domains", "icons")
            media_icon_path = os.path.join(media_domains_path, icon_name)
            relative_icon_path = f"domains/icons/{icon_name}"

            icon_is_valid = (
                os.path.exists(static_icon_path)
                and os.path.getsize(static_icon_path) > 0
            )

            if icon_is_valid:

                shutil.copy2(static_icon_path, media_icon_path)
                logger.info(
                    f"Using icon '{icon_name}' for recreated domain '{domain_data['name']}'"
                )
            else:

                logger.warning(
                    f"Icon '{icon_name}' for domain '{domain_data['name']}' is missing or empty. "
                    f"Using default icon '{DEFAULT_ICON}' instead."
                )
                icon_name = DEFAULT_ICON
                static_icon_path = os.path.join(
                    settings.BASE_DIR, "apps", "domains", "static", "domains", icon_name
                )
                media_icon_path = os.path.join(media_domains_path, icon_name)
                relative_icon_path = f"domains/icons/{icon_name}"
                shutil.copy2(static_icon_path, media_icon_path)

            Domain.objects.create(
                name=domain_data["name"],
                description=domain_data["description"],
                category=domain_data["category"],
                icon=relative_icon_path,
                order=domain_data["order"],
                is_active=domain_data.get("is_active", True),
            )
            logger.info(
                f"Recreated domain '{domain_data['name']}' with icon '{icon_name}'"
            )

        for domain in Domain.objects.all():
            if not domain.icon or not default_storage.exists(domain.icon.name):
                logger.warning(
                    f"Domain '{domain.name}' still has missing icon after recreation. Using default icon."
                )

                default_icon_path = os.path.join(
                    settings.BASE_DIR,
                    "apps",
                    "domains",
                    "static",
                    "domains",
                    DEFAULT_ICON,
                )
                with open(default_icon_path, "rb") as f:
                    domain.icon.save(DEFAULT_ICON, ContentFile(f.read()), save=True)
