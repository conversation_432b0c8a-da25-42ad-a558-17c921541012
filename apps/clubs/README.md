# Club Management System

This module implements a Club management system allowing users to create and join clubs, manage members, track member progress, and view club statistics.

## Overview

The Club system is designed to help users organize into groups and track collective progress on their goals. Key features include:

1. **Club Creation and Management**: Create clubs with specific types and privacy settings.
2. **Member Management**: Invite members to clubs, accept/reject invitations, and remove members.
3. **Progress Tracking**: Calculate progress percentages for club members based on their goal completion.
4. **Activity Monitoring**: Track how active members are in updating their goals and progress.
5. **Club Statistics**: View comprehensive statistics about the club, including overall progress, active members, and individual member stats.

## Models

### ClubType

Defines different types of clubs:
- Name: Type name (e.g., "Fitness", "Professional Development", "Hobby")

### Club

The main club model with the following attributes:
- Name: Club name
- Icon: Club logo/image
- Type: Reference to ClubType
- Manager: The user who created/manages the club
- Members: Users who are members of the club
- Privacy: Public or Private
- Join Permissions: Open or Invite-only

### ClubInvitation

Manages invitations to join clubs:
- User: The invited user
- Sender: The user who sent the invitation
- Club: The club the user is invited to
- Email: Email address for users not yet registered
- Status: Pending, Accepted, or Rejected

## Statistics Functionality

The Club model includes methods to calculate various statistics:

### Member Progress

- `get_member_progress(member)`: Calculates progress percentage for an individual member
- `get_all_members_progress()`: Calculates progress for all members in the club

### Member Activity

- `get_member_activity(member, days=30)`: Calculates activity level for a specific member
- `get_all_members_activity(days=30)`: Calculates activity for all members in the club

### Comprehensive Statistics

- `get_club_stats(activity_days=30)`: Provides a comprehensive overview of club statistics

## API Endpoints

### Club Management

- `GET /api/v1/clubs/`: List all clubs
- `POST /api/v1/clubs/create/`: Create a new club
- `GET /api/v1/clubs/{id}/`: Retrieve a specific club
- `PUT /api/v1/clubs/{id}/update/`: Update a club
- `PATCH /api/v1/clubs/{id}/partial-update/`: Partially update a club
- `DELETE /api/v1/clubs/{id}/delete/`: Delete a club

### Club Statistics

- `GET /api/v1/clubs/{id}/stats/`: Get comprehensive club statistics

Parameters:
- `activity_days` (optional): Number of days to calculate activity for (default: 30)

Response includes:
- `total_members`: Total members in the club
- `overall_progress`: Overall club progress percentage
- `overall_activity`: Overall club activity percentage
- `active_members_percentage`: Percentage of members with activity > 0
- `member_stats`: Dictionary of detailed member statistics by member_id

### Member Management

- `POST /api/v1/clubs/{id}/invite/`: Invite users to a club
- `POST /api/v1/clubs/{id}/join/`: Join a club
- `POST /api/v1/clubs/{club_id}/remove-member/`: Remove a member from a club
- `GET /api/v1/clubs/{club_id}/members/`: List all club members
- `POST /api/v1/clubs/{invitation_id}/accept-join-request/`: Accept a join request
- `POST /api/v1/clubs/{invitation_id}/reject-join-request/`: Reject a join request
- `GET /api/v1/clubs/my-invitations/`: List invitations for the current user
- `GET /api/v1/clubs/managed-invitations/`: List invitations for clubs managed by the current user

## Permissions

- Club managers can view statistics for their clubs
- Only authenticated users can access club endpoints
- Users can only view clubs they have access to
- Only club managers can invite/remove members

## Usage Examples

### Retrieving Club Statistics

```python
import requests

# Get club statistics
response = requests.get(
    "https://example.com/api/v1/clubs/123e4567-e89b-12d3-a456-426614174000/stats/",
    headers={"Authorization": "Bearer YOUR_TOKEN"}
)

# Get club statistics for the last 14 days
response = requests.get(
    "https://example.com/api/v1/clubs/123e4567-e89b-12d3-a456-426614174000/stats/?activity_days=14",
    headers={"Authorization": "Bearer YOUR_TOKEN"}
)
```

## Implementation Details

The club statistics functionality calculates:

1. **Progress Percentage**: Based on completed daily goals vs. total daily goals
2. **Activity Percentage**: Based on goal updates in the specified time period
3. **Active Members**: Members who have made at least one update in the specified time period

Progress and activity metrics are calculated both at the individual member level and aggregated for the entire club. 