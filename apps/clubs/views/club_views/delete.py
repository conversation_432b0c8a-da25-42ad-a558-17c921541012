from rest_framework import generics, permissions
from drf_spectacular.utils import extend_schema
from apps.clubs.models import Club
from apps.clubs.views.club_views.update import ManagerPermission


@extend_schema(
    tags=["Clubs"],
    summary="Delete a club",
    description="Deletes a club. Only the owner or an admin can perform this action.",
)
class ClubDeleteAPIView(generics.DestroyAPIView):
    queryset = Club.objects.all()
    permission_classes = [permissions.IsAuthenticated, ManagerPermission]
