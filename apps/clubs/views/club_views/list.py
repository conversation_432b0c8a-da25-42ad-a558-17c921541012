from rest_framework import generics, permissions
from drf_spectacular.utils import extend_schema
from apps.clubs.models import Club
from apps.clubs.serializers.club_serializers.list import ClubListSerializer
from core.abstract.paginations import MetaPageNumberPagination
from rest_framework import filters
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import OpenApiResponse, OpenApiParameter
from apps.clubs.filters.club_filters import ClubFilter
from django.db import models


class ClubListAPIView(generics.ListAPIView):
    serializer_class = ClubListSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = MetaPageNumberPagination
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_class = ClubFilter
    search_fields = ["name", "type__name"]
    ordering_fields = ["name", "created"]
    ordering = ["-created"]

    def get_queryset(self):
        """
        Override to implement role-based access control:
        - <PERSON><PERSON> can see all clubs
        - Club managers can only see clubs they manage
        - Normal users can see clubs they're a member of or clubs with PRIVACY_OPEN
        """
        user = self.request.user

        if user.is_staff or user.role == "admin":
            return Club.objects.all()

        if user.role == "club_manager":
            return Club.objects.filter(manager=user)

        return Club.objects.filter(
            models.Q(members=user) | models.Q(privacy=Club.PRIVACY_OPEN)
        ).distinct()

    @extend_schema(
        tags=["Clubs"],
        summary="List all clubs",
        description="Returns a list of clubs based on user's role. Admins can see all clubs, managers can see clubs they manage, and users can see clubs they're members of or public clubs.",
        responses={
            200: OpenApiResponse(description="Successfully retrieved clubs"),
            400: OpenApiResponse(description="Invalid request"),
            403: OpenApiResponse(description="No valid invitation found"),
            404: OpenApiResponse(description="Club not found"),
        },
        parameters=[
            OpenApiParameter(
                name="limit",
                type=int,
                required=False,
                description="Limit the number of clubs returned. Provide an integer.",
            ),
            OpenApiParameter(
                name="page",
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                description="A page number within the paginated result set.",
                required=False,
            ),
            OpenApiParameter(
                name="name",
                type=str,
                required=False,
                description="Filter clubs by name (case-insensitive partial match).",
            ),
            OpenApiParameter(
                name="privacy",
                type=str,
                required=False,
                description="Filter clubs by privacy setting.",
            ),
            OpenApiParameter(
                name="join_permissions",
                type=str,
                required=False,
                description="Filter clubs by join permission setting.",
            ),
            OpenApiParameter(
                name="type",
                type=str,
                required=False,
                description="Filter clubs by type ID (UUID).",
            ),
            OpenApiParameter(
                name="manager",
                type=str,
                required=False,
                description="Filter clubs by manager ID (UUID).",
            ),
            OpenApiParameter(
                name="member",
                type=str,
                required=False,
                description="Filter clubs by member ID (UUID).",
            ),
            OpenApiParameter(
                name="created_after",
                type=str,
                required=False,
                description="Filter clubs created after specified date (YYYY-MM-DD).",
            ),
            OpenApiParameter(
                name="created_before",
                type=str,
                required=False,
                description="Filter clubs created before specified date (YYYY-MM-DD).",
            ),
            OpenApiParameter(
                name="search",
                type=str,
                required=False,
                description="Search clubs by name or type name.",
            ),
            OpenApiParameter(
                name="ordering",
                type=str,
                required=False,
                description="Order results by field (prefix with '-' for descending). Options: name, created",
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def get_serializer_context(self):
        context = super().get_serializer_context()
        return context
