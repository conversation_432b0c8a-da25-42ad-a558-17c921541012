import logging
from django.conf import settings
from django.contrib.auth import get_user_model
from rest_framework import generics, permissions, status
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>, MultiPartParser, FormParser
from django.utils.translation import gettext as _
from django.template.loader import render_to_string
from drf_spectacular.utils import extend_schema, OpenApiResponse
from django.contrib.auth.tokens import (
    PasswordResetTokenGenerator,
    default_token_generator,
)
from rest_framework.exceptions import PermissionDenied
from django.utils.http import urlsafe_base64_encode
from django.utils.encoding import force_bytes

from apps.clubs.utils import generate_invitation_token
from apps.clubs.models import Club, ClubInvitation
from apps.accounts.user.models import UserToken
from apps.clubs.serializers.club_serializers.create import ClubCreateSerializer
from apps.clubs.serializers.club_serializers.detail import ClubDetailSerializer
from core.tasks.emails import send_email_with_html

logger = logging.getLogger(__name__)
User = get_user_model()


@extend_schema(
    tags=["Clubs"],
    summary="Create a new club",
    description="""Creates a new club. Only authenticated users can create clubs.
    
Note: Users are limited to creating a maximum number of clubs as defined by the system setting. 
The current limit is set in CLUB_MANAGER_CAN_CREATE_X_CLUBS configuration.

Example request body:
```json
{
  "name": "My Club",
  "type": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "privacy": "private",
  "join_permissions": "open",
  "members": [
    {"email": "<EMAIL>", "name": "John Doe"},
    {"email": "<EMAIL>"}
  ]
}
```

The `name` field in the members array is optional and will be used when sending invitations.
You can also use a simple list of email strings for backward compatibility: `"members": ["<EMAIL>"]`
""",
    responses={
        201: OpenApiResponse(description="Club created successfully"),
        400: OpenApiResponse(description="Invalid request data"),
        401: OpenApiResponse(
            description="Authentication credentials were not provided"
        ),
        403: OpenApiResponse(
            description="You have reached your limit of clubs and cannot create more"
        ),
    },
)
class ClubCreateAPIView(generics.CreateAPIView):
    queryset = Club.objects.all()
    serializer_class = ClubCreateSerializer
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [JSONParser, MultiPartParser, FormParser]

    def perform_create(self, serializer):
        request = self.request
        club = serializer.save(manager=request.user)

        members_data = serializer.validated_data.get("members", [])
        self._handle_members(club, members_data, request)

        return club

    def _handle_members(self, club, members_data, request):
        if not members_data:
            return

        member_url = getattr(settings, "MEMBER_BASE_UI_URL", "http://localhost:4189")

        for member in members_data:

            if not isinstance(member, dict):
                try:
                    if isinstance(member, User):
                        user = member
                    else:
                        user = User.objects.get(id=member)

                    club.members.add(user)
                    logger.info(f"User added to club: {user.email}")
                except User.DoesNotExist:
                    logger.warning(f"User with ID {member} not found")
                continue

            if "email" in member:
                email = member["email"]
                name = member.get("name", "")

                try:
                    user = User.objects.get(email=email)
                    user_exists = True
                except User.DoesNotExist:

                    try:
                        user = User.objects.create_user(
                            email=email, username=email.split("@")[0]
                        )
                        token = PasswordResetTokenGenerator().make_token(user)
                        UserToken.objects.get_or_create(user=user, token=token)
                        user_exists = False
                    except Exception as e:
                        logger.error(f"Error creating user {email}: {str(e)}")
                        continue

                invitation_token = generate_invitation_token(user)

                if not user_exists:
                    token = default_token_generator.make_token(user)
                    uid = urlsafe_base64_encode(force_bytes(user.pk))
                    member_base_url = getattr(
                        settings, "MEMBER_BASE_UI_URL", "http://localhost:4189"
                    )
                    reset_url = (
                        f"{member_base_url}/auth/reset-password?uid={uid}&token={token}"
                    )

                    context = {
                        "club": club,
                        "inviter": request.user,
                        "invited_user": user,
                        "include_reset_link": True,
                        "reset_password_url": reset_url,
                        "role": "member",
                        "role_display": "Member",
                        "frontend_url": member_base_url,
                        "invitation_token": invitation_token,
                        "fullname": name,
                    }

                    subject = f"Welcome to {club}!"
                    logger.info(
                        f"New user created and invited: {email}, using reset URL: {reset_url}"
                    )
                else:
                    context = {
                        "club": club,
                        "inviter": request.user,
                        "invited_user": user,
                        "frontend_url": member_url,
                        "invitation_token": invitation_token,
                        "fullname": name,
                    }
                    subject = _("Invitation to join {club_name}").format(
                        club_name=club.name
                    )
                    logger.info(
                        f"Existing user invited: {email}, using URL: {member_url}"
                    )

                html_content = render_to_string(
                    "clubs/emails/invites/invite.html", context
                )
                text_content = render_to_string(
                    "clubs/emails/invites/invite.txt", context
                )

                send_email_with_html(
                    subject=subject,
                    text_content=text_content,
                    html_content=html_content,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    to_email=email,
                )

                ClubInvitation.objects.create(user=user, club=club, fullname=name)

                club.members.add(user)

    def create(self, request, *args, **kwargs):
        user_clubs_count = Club.objects.filter(manager=request.user).count()
        max_clubs_allowed = int(settings.CLUB_MANAGER_CAN_CREATE_X_CLUBS)

        if user_clubs_count >= max_clubs_allowed:
            raise PermissionDenied(
                _(
                    "You have reached your limit of {max_clubs} clubs. You cannot create more clubs."
                ).format(max_clubs=max_clubs_allowed)
            )

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        club = self.perform_create(serializer)

        detail_serializer = ClubDetailSerializer(club, context={"request": request})
        headers = self.get_success_headers(serializer.data)
        return Response(
            detail_serializer.data, status=status.HTTP_201_CREATED, headers=headers
        )
