from rest_framework import generics, permissions, response, status
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Part<PERSON>ars<PERSON>, <PERSON><PERSON>ars<PERSON>
from drf_spectacular.utils import extend_schema
from django.utils.translation import gettext as _
from apps.clubs.models import Club
from apps.clubs.serializers.club_serializers.update import ClubUpdateSerializer
from apps.clubs.serializers.club_serializers.detail import ClubDetailSerializer


class ManagerPermission(permissions.BasePermission):
    def has_object_permission(self, request, view, obj):
        return obj.manager == request.user


@extend_schema(
    tags=["Clubs"],
    summary="Update a club",
    description="Updates a club's details. Only the owner or an admin can perform this action.",
)
class ClubUpdateAPIView(generics.UpdateAPIView):
    queryset = Club.objects.all()
    serializer_class = ClubUpdateSerializer
    permission_classes = [permissions.IsAuthenticated, ManagerPermission]
    parser_classes = [JSONPars<PERSON>, MultiPartParser, FormParser]

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop("partial", False)
        instance = self.get_object()

        if not partial and not request.data:
            return response.Response(
                {"error": _("At least one field must be provided for update")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.get_serializer(instance, data=request.data, partial=partial)

        try:
            serializer.is_valid(raise_exception=True)
        except Exception as e:
            if not partial and request.data:
                serializer = self.get_serializer(
                    instance, data=request.data, partial=True
                )
                serializer.is_valid(raise_exception=True)
            else:
                raise e

        self.perform_update(serializer)

        detail_serializer = ClubDetailSerializer(instance, context={"request": request})
        return response.Response(detail_serializer.data)

    def get_serializer_context(self):
        context = super().get_serializer_context()
        return context
