import csv
from django.http import HttpResponse
from rest_framework import generics, permissions
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiResponse
from drf_spectacular.types import OpenApiTypes
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from django.db import models

from apps.clubs.models import Club
from apps.clubs.serializers.club_serializers.list import ClubListSerializer
from apps.clubs.filters.club_filters import ClubFilter


class ClubExportAPIView(generics.ListAPIView):
    """
    API endpoint to export clubs data as CSV.
    Uses the same queryset and filtering logic as ClubListAPIView.
    """

    serializer_class = ClubListSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_class = ClubFilter
    search_fields = ["name", "type__name"]
    ordering_fields = ["name", "created"]
    ordering = ["-created"]
    pagination_class = None

    def get_queryset(self):
        """
        Override to implement role-based access control:
        - <PERSON><PERSON> can see all clubs
        - Club managers can only see clubs they manage
        - Normal users can see clubs they're a member of or clubs with PRIVACY_OPEN
        """
        user = self.request.user

        if user.is_staff or user.role == "admin":
            return Club.objects.all()

        if user.role == "club_manager":
            return Club.objects.filter(manager=user)

        return Club.objects.filter(
            models.Q(members=user) | models.Q(privacy=Club.PRIVACY_OPEN)
        ).distinct()

    @extend_schema(
        tags=["Clubs"],
        summary="Export clubs as CSV",
        description="Exports clubs as CSV with the same filtering capabilities as the list endpoint. Admins can export all clubs, managers can export clubs they manage, and users can export clubs they're members of or public clubs.",
        responses={
            200: OpenApiResponse(description="CSV export of clubs"),
            400: OpenApiResponse(description="Invalid request"),
            403: OpenApiResponse(description="Permission denied"),
        },
        parameters=[
            OpenApiParameter(
                name="name",
                type=str,
                required=False,
                description="Filter clubs by name (case-insensitive partial match).",
            ),
            OpenApiParameter(
                name="privacy",
                type=str,
                required=False,
                description="Filter clubs by privacy setting.",
            ),
            OpenApiParameter(
                name="join_permissions",
                type=str,
                required=False,
                description="Filter clubs by join permission setting.",
            ),
            OpenApiParameter(
                name="type",
                type=str,
                required=False,
                description="Filter clubs by type ID (UUID).",
            ),
            OpenApiParameter(
                name="manager",
                type=str,
                required=False,
                description="Filter clubs by manager ID (UUID).",
            ),
            OpenApiParameter(
                name="member",
                type=str,
                required=False,
                description="Filter clubs by member ID (UUID).",
            ),
            OpenApiParameter(
                name="created_after",
                type=str,
                required=False,
                description="Filter clubs created after specified date (YYYY-MM-DD).",
            ),
            OpenApiParameter(
                name="created_before",
                type=str,
                required=False,
                description="Filter clubs created before specified date (YYYY-MM-DD).",
            ),
            OpenApiParameter(
                name="search",
                type=str,
                required=False,
                description="Search clubs by name or type name.",
            ),
            OpenApiParameter(
                name="ordering",
                type=str,
                required=False,
                description="Order results by field (prefix with '-' for descending). Options: name, created",
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())

        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = 'attachment; filename="clubs_export.csv"'

        writer = csv.writer(response)

        writer.writerow(
            [
                "ID",
                "Name",
                "Type",
                "Manager",
                "Privacy",
                "Join Permissions",
                "Member Count",
                "Created Date",
            ]
        )

        for club in queryset:
            writer.writerow(
                [
                    str(club.id),
                    club.name,
                    club.type.name if club.type else "",
                    (
                        f"{club.manager.fullname} ({club.manager.email})"
                        if club.manager
                        else ""
                    ),
                    club.get_privacy_display(),
                    club.get_join_permissions_display(),
                    club.members.count(),
                    club.created.strftime("%Y-%m-%d %H:%M:%S"),
                ]
            )

        return response
