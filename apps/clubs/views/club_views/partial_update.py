from rest_framework import generics, permissions
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>PartPars<PERSON>, <PERSON>Pars<PERSON>
from drf_spectacular.utils import extend_schema
from apps.clubs.models import Club
from apps.clubs.serializers.club_serializers.update import ClubUpdateSerializer
from apps.clubs.views.club_views.update import ManagerPermission, ClubUpdateAPIView


@extend_schema(
    tags=["Clubs"],
    summary="Partially update a club",
    description="Partially updates a club's details. Only the owner or an admin can perform this action.",
)
class ClubPartialUpdateAPIView(ClubUpdateAPIView):
    queryset = Club.objects.all()
    serializer_class = ClubUpdateSerializer
    permission_classes = [permissions.IsAuthenticated, ManagerPermission]
    http_method_names = ["patch"]
    parser_classes = [JSONParser, MultiPartParser, FormParser]
