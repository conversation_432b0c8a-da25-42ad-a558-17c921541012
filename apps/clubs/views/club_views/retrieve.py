from rest_framework import generics, permissions
from drf_spectacular.utils import extend_schema
from apps.clubs.models import Club
from apps.clubs.serializers.club_serializers.detail import ClubDetailSerializer


@extend_schema(
    tags=["Clubs"],
    summary="Retrieve a specific club",
    description="Returns details of a specific club.",
)
class ClubRetrieveAPIView(generics.RetrieveAPIView):
    queryset = Club.objects.all()
    serializer_class = ClubDetailSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        return context
