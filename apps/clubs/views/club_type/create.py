from rest_framework import generics, permissions
from drf_spectacular.utils import extend_schema
from apps.clubs.models import ClubType
from apps.clubs.serializers.club_type import ClubTypeSerializer


@extend_schema(
    tags=["Club Types"],
    summary="Create a new club type",
    description="Creates a new club type. Only authenticated users can create club types.",
)
class ClubTypeCreateAPIView(generics.CreateAPIView):
    queryset = ClubType.objects.all()
    serializer_class = ClubTypeSerializer
    permission_classes = [permissions.IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
