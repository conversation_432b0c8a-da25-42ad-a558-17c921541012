from rest_framework import generics, permissions
from drf_spectacular.utils import extend_schema
from apps.clubs.models import ClubType
from apps.clubs.serializers.club_type import ClubTypeSerializer
from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import OpenApiResponse, OpenApiParameter
from rest_framework import filters
from core.abstract.paginations import MetaPageNumberPagination
from django.db.models import Q


class ClubTypeListAPIView(generics.ListAPIView):
    serializer_class = ClubTypeSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = MetaPageNumberPagination
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    ordering_fields = ["name"]

    def get_queryset(self):
        if self.request.user.is_superuser:
            return ClubType.objects.all()
        else:
            return ClubType.objects.filter(
                Q(visibility=ClubType.VISIBILITY_PUBLIC)
                | Q(created_by=self.request.user)
            )

    @extend_schema(
        tags=["Club Types"],
        summary="List all club types",
        description="Returns a list of club types. Regular users can only see public club types and ones they created. Super admins can see all club types.",
        responses={
            200: OpenApiResponse(description="Successfully retrieved club types"),
            400: OpenApiResponse(description="Invalid request"),
            403: OpenApiResponse(description="No valid invitation found"),
            404: OpenApiResponse(description="Club not found"),
        },
        parameters=[
            OpenApiParameter(
                name="limit",
                type=int,
                required=False,
                description="Limit the number of club types returned. Provide an integer.",
            ),
            OpenApiParameter(
                name="page",
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                description="A page number within the paginated result set.",
                required=False,
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
