from rest_framework import generics, permissions
from drf_spectacular.utils import extend_schema
from apps.clubs.models import ClubType
from apps.clubs.serializers.club_type import ClubTypeSerializer
from django.db.models import Q
from django.shortcuts import get_object_or_404


@extend_schema(
    tags=["Club Types"],
    summary="Update a club type",
    description="Updates a club type's details. Regular users can only update club types they created. Super admins can update any club type.",
    methods=["PUT"],
)
@extend_schema(
    tags=["Club Types"],
    summary="Partially update a club type",
    description="Partially updates a club type's details. Regular users can only update club types they created. Super admins can update any club type.",
    methods=["PATCH"],
)
class ClubTypeUpdateAPIView(generics.UpdateAPIView):
    serializer_class = ClubTypeSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        if self.request.user.is_superuser:
            return ClubType.objects.all()
        else:
            return ClubType.objects.filter(created_by=self.request.user)

    def get_object(self):
        queryset = self.get_queryset()
        obj = get_object_or_404(queryset, pk=self.kwargs["pk"])
        self.check_object_permissions(self.request, obj)
        return obj
