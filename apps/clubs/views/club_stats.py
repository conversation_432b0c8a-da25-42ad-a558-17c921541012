from rest_framework import generics, permissions
from rest_framework.exceptions import NotFound
from drf_spectacular.utils import extend_schema, OpenApiParameter
from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import OpenApiResponse

from apps.clubs.models import Club
from apps.clubs.serializers.club_stats import ClubStatsSerializer
from apps.clubs.views.club_views.update import ManagerPermission


@extend_schema(
    tags=["Club Statistics"],
    summary="Get club statistics",
    description="Returns statistics for a specific club including progress and activity metrics for all members.",
    responses={
        200: OpenApiResponse(description="Successfully retrieved club statistics"),
        403: OpenApiResponse(description="Permission denied"),
        404: OpenApiResponse(description="Club not found"),
    },
    parameters=[
        OpenApiParameter(
            name="activity_days",
            type=OpenApiTypes.INT,
            location=OpenApiParameter.QUERY,
            description="Number of days to calculate activity for (default: 30)",
            required=False,
        ),
    ],
)
class ClubStatsAPIView(generics.RetrieveAPIView):
    """
    API view to retrieve statistics for a specific club
    """

    serializer_class = ClubStatsSerializer
    permission_classes = [permissions.IsAuthenticated, ManagerPermission]
    queryset = Club.objects.all()

    def get_serializer_context(self):
        """
        Add activity_days to the serializer context
        """
        context = super().get_serializer_context()
        activity_days = self.request.query_params.get("activity_days", 30)

        try:
            activity_days = int(activity_days)
            if activity_days <= 0:
                activity_days = 30
        except (ValueError, TypeError):
            activity_days = 30

        context["activity_days"] = activity_days
        return context
