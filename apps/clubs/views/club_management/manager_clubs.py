from rest_framework import generics, permissions
from rest_framework.exceptions import NotFound, PermissionDenied
from django.contrib.auth import get_user_model
from django.utils.translation import gettext as _
from drf_spectacular.utils import extend_schema
from apps.clubs.models import Club
from apps.clubs.serializers.club_serializers.list import ClubListSerializer
from core.abstract.paginations import MetaPageNumberPagination
from rest_framework import filters
from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import OpenApiResponse, OpenApiParameter

User = get_user_model()


class IsAdminUser(permissions.BasePermission):
    """
    Permission check for admin users.
    """

    def has_permission(self, request, view):
        return request.user and request.user.is_staff


@extend_schema(
    tags=["Clubs"],
    summary="List clubs by manager",
    description="Returns a list of clubs managed by a specific user. Only admin users can access this endpoint.",
    responses={
        200: OpenApiResponse(description="Successfully retrieved clubs"),
        400: OpenApiResponse(description="Invalid request"),
        401: OpenApiResponse(
            description="Authentication credentials were not provided"
        ),
        403: OpenApiResponse(description="Not authorized to view these clubs"),
        404: OpenApiResponse(description="Manager not found"),
    },
    parameters=[
        OpenApiParameter(
            name="limit",
            type=int,
            required=False,
            description="Limit the number of clubs returned. Provide an integer.",
        ),
        OpenApiParameter(
            name="page",
            type=OpenApiTypes.INT,
            location=OpenApiParameter.QUERY,
            description="A page number within the paginated result set.",
            required=False,
        ),
        OpenApiParameter(
            name="search",
            type=OpenApiTypes.STR,
            location=OpenApiParameter.QUERY,
            description="Search clubs by name.",
            required=False,
        ),
        OpenApiParameter(
            name="ordering",
            type=OpenApiTypes.STR,
            location=OpenApiParameter.QUERY,
            description="Order results by field (e.g., 'name' or '-name' for descending order).",
            required=False,
        ),
    ],
)
class ManagerClubsAPIView(generics.ListAPIView):
    """
    API view to list clubs managed by a specific user.
    Only admin users can access this endpoint.
    """

    serializer_class = ClubListSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdminUser]
    pagination_class = MetaPageNumberPagination
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["name"]
    ordering_fields = ["name", "created", "updated"]

    def get_queryset(self):
        """
        Return clubs managed by the specified user.
        """
        manager_id = self.kwargs.get("manager_id")

        try:
            manager = User.objects.get(id=manager_id)
        except User.DoesNotExist:
            raise NotFound(_("Manager not found"))

        return Club.objects.filter(manager=manager)
