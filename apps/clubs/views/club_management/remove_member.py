from rest_framework import permissions, status
from rest_framework.views import APIView
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.utils.translation import gettext as _
from drf_spectacular.utils import extend_schema, OpenApiResponse

from apps.clubs.models import Club, ClubInvitation
from apps.clubs.serializers.remove_member import RemoveMemberSerializer


@extend_schema(
    tags=["Clubs"],
    summary="Remove a member from a club",
    description="Remove a member from a club",
    request=RemoveMemberSerializer,
    responses={
        200: OpenApiResponse(description="Successfully removed the member"),
        400: OpenApiResponse(description="Invalid request"),
        403: OpenApiResponse(description="Only the manager can remove members"),
        404: OpenApiResponse(description="Club not found"),
    },
)
class RemoveMemberClubAPIView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, club_id=None):
        """Remove a member from a club."""
        serializer = RemoveMemberSerializer(
            data=request.data, context={"request": request, "club_id": club_id}
        )
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        club = Club.objects.get(pk=club_id)
        member = club.members.get(pk=serializer.validated_data["member_id"])
        club.members.remove(member)
        club.save()

        return Response(
            {"message": _(f"Successfully removed {member.username} from {club.name}")},
            status=status.HTTP_200_OK,
        )
