from rest_framework import generics, permissions
from drf_spectacular.utils import extend_schema
from apps.clubs.models import Club
from apps.clubs.serializers.club_serializers.list import ClubListSerializer
from core.abstract.paginations import MetaPageNumberPagination
from rest_framework import filters
from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import OpenApiResponse, OpenApiParameter


@extend_schema(
    tags=["Clubs"],
    summary="List my clubs",
    description="Returns a list of clubs managed by the authenticated user.",
    responses={
        200: OpenApiResponse(description="Successfully retrieved clubs"),
        400: OpenApiResponse(description="Invalid request"),
        401: OpenApiResponse(
            description="Authentication credentials were not provided"
        ),
    },
    parameters=[
        OpenApiParameter(
            name="limit",
            type=int,
            required=False,
            description="Limit the number of clubs returned. Provide an integer.",
        ),
        OpenApiParameter(
            name="page",
            type=OpenApiTypes.INT,
            location=OpenApiParameter.QUERY,
            description="A page number within the paginated result set.",
            required=False,
        ),
        OpenApiParameter(
            name="search",
            type=OpenApiTypes.STR,
            location=OpenApiParameter.QUERY,
            description="Search clubs by name.",
            required=False,
        ),
        OpenApiParameter(
            name="ordering",
            type=OpenApiTypes.STR,
            location=OpenApiParameter.QUERY,
            description="Order results by field (e.g., 'name' or '-name' for descending order).",
            required=False,
        ),
    ],
)
class MyClubsAPIView(generics.ListAPIView):
    """
    API view to list clubs managed by the authenticated user.
    """

    serializer_class = ClubListSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = MetaPageNumberPagination
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["name"]
    ordering_fields = ["name", "created", "updated"]

    def get_queryset(self):
        """
        Return clubs managed by the authenticated user.
        """
        return Club.objects.filter(manager=self.request.user)
