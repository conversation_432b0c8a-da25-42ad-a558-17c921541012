from rest_framework import generics, permissions, status
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema, OpenApiResponse
from django.db.models import Q
from apps.clubs.models import Club
from apps.clubs.serializers.my_club import MyClubSerializer


@extend_schema(
    tags=["Clubs"],
    summary="Get my most recent club",
    description="Returns the most recent club where the authenticated user is either a manager or a member.",
    responses={
        200: OpenApiResponse(
            description="Successfully retrieved the most recent club",
            response=MyClubSerializer,
        ),
        204: OpenApiResponse(description="No club found"),
        401: OpenApiResponse(
            description="Authentication credentials were not provided"
        ),
    },
)
class MyClubAPIView(generics.RetrieveAPIView):
    """
    API view to retrieve the most recent club for the authenticated user.

    This endpoint returns the most recent club where the user is either
    a manager or a member. If the user belongs to multiple clubs, the most
    recently created one is returned.
    """

    serializer_class = MyClubSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        """
        Return the most recent club for the authenticated user.

        This method queries for clubs where the user is either a manager or a member,
        and returns the most recently created one.
        """
        user = self.request.user

        clubs = (
            Club.objects.filter(Q(manager=user) | Q(members=user))
            .distinct()
            .order_by("-created")
        )

        if clubs.exists():
            return clubs.first()

        return None

    def retrieve(self, request, *args, **kwargs):
        """
        Override retrieve to handle the case when no club is found.
        """
        instance = self.get_object()

        if instance is None:
            return Response(
                {"detail": "You don't belong to any club."},
                status=status.HTTP_204_NO_CONTENT,
            )

        serializer = self.get_serializer(instance)
        return Response(serializer.data)
