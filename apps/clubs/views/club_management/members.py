from rest_framework import generics
from rest_framework.exceptions import PermissionDenied
from django.http import Http404
from rest_framework import status
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema, OpenApiResponse, OpenApiParameter
from apps.clubs.models import Club, ClubInvitation
from apps.clubs.serializers.members import ClubMembersSerializer
from apps.accounts.user.models import User
from django.utils.translation import gettext as _
from core.abstract.paginations import MetaPageNumberPagination
from apps.accounts.user.filters import UserFilter
from drf_spectacular.types import OpenApiTypes
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters


class ClubMembersAPIView(generics.ListAPIView):
    """
    API view to retrieve a list of members of a specific club with optional filters.
    """

    pagination_class = MetaPageNumberPagination
    serializer_class = ClubMembersSerializer
    filter_backends = (DjangoFilterBackend, filters.OrderingFilter)
    filterset_fields = ["fullname"]
    ordering_fields = ["fullname", "id"]

    def get_queryset(self):
        """
        Override to filter club members based on query parameters.

        This implementation ensures consistency between ClubMembersAPIView and MyClubsAPIView:
        - For invitation_status=accepted or when no status is specified, it gets members directly
          from the club's members ManyToMany relationship. This matches how members are displayed
          in the MyClubsAPIView through ClubDetailSerializer.
        - For invitation_status=pending or invitation_status=rejected, it fetches users through
          the invitation model.

        This ensures that when comparing members between the two views for the same club,
        the data will be consistent.
        """
        user = self.request.user
        club_id = self.kwargs.get("club_id")

        try:
            self.club = Club.objects.get(id=club_id)
        except Club.DoesNotExist:
            raise Http404(_("Club not found"))

        if self.club.manager != user:
            raise PermissionDenied(
                _("You are not authorized to view this club's members")
            )

        invitation_status = self.request.query_params.get("invitation_status", None)

        if (
            invitation_status == ClubInvitation.STATUS_ACCEPTED
            or invitation_status is None
        ):

            members = self.club.members.all()
        else:

            club_invitations = ClubInvitation.objects.filter(
                club=self.club, status=invitation_status
            )
            members = User.objects.filter(
                id__in=club_invitations.values_list("user_id", flat=True)
            )

        fullname = self.request.query_params.get("fullname", None)
        if fullname:
            members = members.filter(fullname__icontains=fullname)

        return members

    @extend_schema(
        tags=["Clubs"],
        summary="Get club members",
        description="Get all members of a specific club with optional filtering and pagination",
        responses={
            200: OpenApiResponse(description="Successfully retrieved club members"),
            400: OpenApiResponse(description="Invalid request"),
            403: OpenApiResponse(description="No valid invitation found"),
            404: OpenApiResponse(description="Club not found"),
        },
        parameters=[
            OpenApiParameter(
                name="fullname",
                type=str,
                required=False,
                description="Filter members by fullname (case-insensitive).",
            ),
            OpenApiParameter(
                name="limit",
                type=int,
                required=False,
                description="Limit the number of members returned. Provide an integer.",
            ),
            OpenApiParameter(
                name="invitation_status",
                type=str,
                required=False,
                description="Filter members by invitation status (pending, accepted, rejected).",
            ),
            OpenApiParameter(
                name="page",
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                description="A page number within the paginated result set.",
                required=False,
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def get_serializer_context(self):
        """
        Override to include the club in the serializer context.
        """
        context = super().get_serializer_context()
        context["club"] = self.club
        return context
