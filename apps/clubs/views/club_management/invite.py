from apps.clubs.utils import generate_invitation_token
from core.tasks.emails import send_email_with_html
from apps.clubs.models import Club, ClubInvitation
from apps.clubs.serializers.invite import ClubInviteSerializer
from apps.clubs.serializers.club_serializers.detail import UserSerializer
from django.utils.translation import gettext as _
from drf_spectacular.utils import extend_schema
from rest_framework.response import Response
from rest_framework.views import APIView
from django.template.loader import render_to_string
from django.contrib.auth import get_user_model
from django.conf import settings
from django.db import transaction
import logging
from typing import List, Dict, Any, Union
from django.contrib.auth.tokens import PasswordResetTokenGenerator
from rest_framework import permissions, status

from apps.accounts.user.models import UserToken

logger = logging.getLogger(__name__)
User = get_user_model()


@extend_schema(
    tags=["Clubs"],
    summary="Invite members to a club",
    description="Allows the club manager to invite new members by email and optional fullname. Users will receive invitations and must accept them to join the club.",
    responses={
        200: {"description": "Invitations sent successfully."},
        400: {"description": "Invalid data provided or validation errors."},
        403: {"description": "Permission denied."},
        404: {"description": "Club not found."},
    },
)
class InviteMembersAPIView(APIView):
    serializer_class = ClubInviteSerializer
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk=None):
        """
        Invite members to the specified club by email and optional fullname.

        This endpoint:
        - Validates that the user is the club manager
        - Prevents duplicate invitations
        - Prevents inviting existing members
        - Creates user accounts for non-existing users
        - Sends invitation emails with appropriate context
        - Creates ClubInvitation records in pending status
        - Does NOT automatically add users to the club (they must accept)
        """
        try:
            club = Club.objects.get(pk=pk)
        except Club.DoesNotExist:
            return Response(
                {"detail": _("Club not found")}, status=status.HTTP_404_NOT_FOUND
            )

        if request.user != club.manager:
            return Response(
                {"error": _("Only club managers can send invitations")},
                status=status.HTTP_403_FORBIDDEN,
            )

        serializer = ClubInviteSerializer(
            data=request.data, club=club, current_user=request.user
        )

        if not serializer.is_valid():
            return Response(
                {"errors": serializer.errors}, status=status.HTTP_400_BAD_REQUEST
            )

        validated_data = serializer.validated_data

        members_data = []
        emails = validated_data.get("emails", [])
        for email in emails:
            members_data.append({"email": email.lower(), "name": ""})

        invitations = validated_data.get("invitations", [])
        for invitation in invitations:
            members_data.append(
                {
                    "email": invitation["email"].lower(),
                    "name": invitation.get("fullname", ""),
                }
            )

        unique_members = {}
        for member in members_data:
            email = member["email"]
            if email not in unique_members:
                unique_members[email] = member
        members_data = list(unique_members.values())

        invited_users = []
        invitation_results = []
        member_url = getattr(settings, "MEMBER_BASE_UI_URL", "http://localhost:4189")

        try:
            with transaction.atomic():
                for member in members_data:
                    email = member["email"]
                    fullname = member.get("name", "")

                    try:

                        user = User.objects.get(email=email)
                        user_exists = True
                        logger.info(f"Found existing user for email: {email}")
                    except User.DoesNotExist:

                        try:
                            username = self._generate_unique_username(email)
                            user = User.objects.create_user(
                                email=email,
                                username=username,
                                fullname=fullname if fullname else email.split("@")[0],
                            )

                            token = PasswordResetTokenGenerator().make_token(user)
                            UserToken.objects.get_or_create(user=user, token=token)
                            user_exists = False
                            logger.info(f"Created new user for email: {email}")
                        except Exception as e:
                            logger.error(f"Error creating user {email}: {str(e)}")
                            invitation_results.append(
                                {
                                    "email": email,
                                    "status": "failed",
                                    "error": f"Failed to create user: {str(e)}",
                                }
                            )
                            continue

                    invitation_token = generate_invitation_token(user)

                    try:
                        invitation = ClubInvitation.objects.create(
                            user=user,
                            club=club,
                            email=email,
                            fullname=fullname,
                            sender=request.user,
                            status=ClubInvitation.STATUS_PENDING,
                        )
                        logger.info(f"Created invitation record for {email}")
                    except Exception as e:
                        logger.error(f"Error creating invitation for {email}: {str(e)}")
                        invitation_results.append(
                            {
                                "email": email,
                                "status": "failed",
                                "error": f"Failed to create invitation: {str(e)}",
                            }
                        )
                        continue

                    context = {
                        "club": club,
                        "inviter": request.user,
                        "invited_user": user,
                        "frontend_url": member_url,
                        "invitation_token": invitation_token,
                        "fullname": fullname,
                        "invitation_id": invitation.id,
                    }

                    if not user_exists:
                        reset_path = "/auth/reset-password?token="
                        reset_password_url = f"{member_url}{reset_path}{token}"
                        context["include_reset_link"] = True
                        context["reset_password_url"] = reset_password_url
                        subject = _(
                            "Welcome to {club_name}! You've been invited to join."
                        ).format(club_name=club.name)
                    else:
                        context["include_reset_link"] = False
                        subject = _("Invitation to join {club_name}").format(
                            club_name=club.name
                        )

                    try:
                        html_content = render_to_string(
                            "clubs/emails/invites/invite.html", context
                        )
                        text_content = render_to_string(
                            "clubs/emails/invites/invite.txt", context
                        )

                        send_email_with_html(
                            subject=subject,
                            text_content=text_content,
                            html_content=html_content,
                            from_email=settings.DEFAULT_FROM_EMAIL,
                            to_email=email,
                        )

                        invited_users.append(user)
                        invitation_results.append(
                            {
                                "email": email,
                                "status": "sent",
                                "user_existed": user_exists,
                                "invitation_id": str(invitation.id),
                            }
                        )

                        logger.info(f"Invitation sent successfully to {email}")

                    except Exception as e:
                        logger.error(f"Error sending email to {email}: {str(e)}")

                        invitation.delete()
                        invitation_results.append(
                            {
                                "email": email,
                                "status": "failed",
                                "error": f"Failed to send email: {str(e)}",
                            }
                        )

        except Exception as e:
            logger.error(f"Transaction failed during invitation process: {str(e)}")
            return Response(
                {"error": _("Failed to process invitations. Please try again.")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        successful_invitations = [
            r for r in invitation_results if r["status"] == "sent"
        ]
        failed_invitations = [r for r in invitation_results if r["status"] == "failed"]

        response_data = {
            "message": _(
                "Processed {total} invitations. {success} sent successfully, {failed} failed."
            ).format(
                total=len(invitation_results),
                success=len(successful_invitations),
                failed=len(failed_invitations),
            ),
            "invited_members": UserSerializer(invited_users, many=True).data,
            "invitation_summary": {
                "total_processed": len(invitation_results),
                "successful": len(successful_invitations),
                "failed": len(failed_invitations),
                "details": invitation_results,
            },
        }

        if failed_invitations and not successful_invitations:
            return Response(response_data, status=status.HTTP_400_BAD_REQUEST)
        elif failed_invitations:
            return Response(response_data, status=status.HTTP_207_MULTI_STATUS)
        else:
            return Response(response_data, status=status.HTTP_200_OK)

    def _generate_unique_username(self, email: str) -> str:
        """Generate a unique username from email."""
        base_username = email.split("@")[0]
        username = base_username
        counter = 1

        while User.objects.filter(username=username).exists():
            username = f"{base_username}{counter}"
            counter += 1

        return username
