from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from apps.clubs.models import ClubInvitation
from django.utils.translation import gettext as _
from django.shortcuts import get_object_or_404
from rest_framework import permissions
from drf_spectacular.utils import extend_schema, OpenApiResponse


class RejectInvitationView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        tags=["Clubs"],
        summary="Reject an invitation",
        description="Reject an invitation",
        responses={
            200: OpenApiResponse(description="Successfully rejected the invitation"),
            400: OpenApiResponse(description="Invitation is not pending"),
            403: OpenApiResponse(
                description="You are not authorized to reject this invitation"
            ),
        },
    )
    def post(self, request, invitation_id):
        invitation = get_object_or_404(ClubInvitation, id=invitation_id)
        user = request.user
        authorized = False

        if invitation.status != ClubInvitation.STATUS_PENDING:
            return Response(
                {"error": _("Invitation is not pending")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if invitation.user == user and invitation.sender != user:
            authorized = True
        if invitation.club.manager == user and invitation.sender != user:
            authorized = True

        if not authorized:
            return Response(
                {"error": _("You are not authorized to reject this invitation")},
                status=status.HTTP_403_FORBIDDEN,
            )

        invitation.status = ClubInvitation.STATUS_REJECTED
        invitation.save()
        return Response(
            {"message": _("Invitation rejected")}, status=status.HTTP_200_OK
        )
