from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from apps.clubs.models import Club
from apps.clubs.models import ClubInvitation
from drf_spectacular.utils import extend_schema, OpenApiResponse, OpenApiParameter
from django.shortcuts import get_object_or_404
from rest_framework import permissions
from django.utils.translation import gettext as _
from django.template.loader import render_to_string
from django.conf import settings
from core.tasks.emails import send_email_with_html


@extend_schema(
    tags=["Clubs"],
    summary="Accept a club join request",
    description="Accept a club join request. Both the club manager and the invited user can accept the invitation.",
    responses={
        200: OpenApiResponse(description="Successfully accepted the club join request"),
        403: OpenApiResponse(
            description="You are not authorized to accept this club join request"
        ),
        404: OpenApiResponse(description="Club join request not found"),
    },
)
class AcceptClubJoinRequestAPIView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, invitation_id=None):
        invitation = get_object_or_404(ClubInvitation, pk=invitation_id)

        is_authorized = (invitation.club.manager == request.user) or (
            invitation.user == request.user
        )

        if not is_authorized:
            return Response(
                {
                    "error": _(
                        "Only the club manager or the invited user can accept this invitation"
                    )
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        if invitation.status == ClubInvitation.STATUS_PENDING:
            invitation.status = ClubInvitation.STATUS_ACCEPTED
            invitation.save()

            if invitation.user and invitation.user not in invitation.club.members.all():
                invitation.club.members.add(invitation.user)
                invitation.club.save()

            email_recipient = (
                invitation.user.email if invitation.user else invitation.email
            )
            if request.user == invitation.user:
                email_recipient = invitation.club.manager.email
                context = {
                    "club": invitation.club,
                    "user": invitation.user,
                }
                html_content = render_to_string(
                    "clubs/emails/join_request/self_join_request_accepted.html", context
                )
                text_content = render_to_string(
                    "clubs/emails/join_request/self_join_request_accepted.txt", context
                )
                subject = _("User accepted club invitation")
            else:
                context = {
                    "club": invitation.club,
                    "user": invitation.user,
                }
                html_content = render_to_string(
                    "clubs/emails/join_request/join_request_accepted.html", context
                )
                text_content = render_to_string(
                    "clubs/emails/join_request/join_request_accepted.txt", context
                )
                subject = _("Club join request accepted")

            send_email_with_html(
                subject=subject,
                text_content=text_content,
                html_content=html_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to_email=email_recipient,
            )
            return Response(
                {"message": _("Club join request accepted")}, status=status.HTTP_200_OK
            )
        else:
            return Response(
                {"error": _("Club join request not found")},
                status=status.HTTP_404_NOT_FOUND,
            )
