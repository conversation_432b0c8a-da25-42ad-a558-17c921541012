from rest_framework import generics
from apps.clubs.models import ClubInvitation
from apps.clubs.serializers.club_invitation import ClubInvitationSerializer
from core.abstract.paginations import MetaPageNumberPagination
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from drf_spectacular.utils import (
    extend_schema,
    OpenApiResponse,
    OpenApiParameter,
    OpenApiTypes,
)
from django.db.models import Q
from rest_framework.permissions import IsAuthenticated


class MyInvitationsAPIView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = ClubInvitationSerializer
    queryset = ClubInvitation.objects.all()
    pagination_class = MetaPageNumberPagination
    filter_backends = (DjangoFilterBackend, filters.OrderingFilter)
    filterset_fields = ["status"]
    ordering_fields = ["status"]

    def get_queryset(self):
        user = self.request.user
        return ClubInvitation.objects.filter(Q(user=user) | Q(email=user.email))

    @extend_schema(
        tags=["Clubs"],
        summary="List club invitations",
        description="List all invitations for a specific club",
        responses={
            "200": OpenApiResponse(
                description="Successfully retrieved club invitations"
            ),
            "400": OpenApiResponse(description="Invalid request"),
            "403": OpenApiResponse(description="No valid invitation found"),
            "404": OpenApiResponse(description="Club not found"),
        },
        parameters=[
            OpenApiParameter(
                name="status",
                type=str,
                required=False,
                description="Filter invitations by status (pending, accepted, rejected).",
            ),
            OpenApiParameter(
                name="limit",
                type=int,
                required=False,
                description="Limit the number of invitations returned. Provide an integer.",
            ),
            OpenApiParameter(
                name="page",
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                description="A page number within the paginated result set.",
                required=False,
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
