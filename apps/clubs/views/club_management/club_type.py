from rest_framework import viewsets, permissions
from drf_spectacular.utils import extend_schema, extend_schema_view
from apps.clubs.models import ClubType
from apps.clubs.serializers.club_type import ClubTypeSerializer
from django.db.models import Q


@extend_schema_view(
    list=extend_schema(
        tags=["Club Types"],
        summary="List all club types",
        description="Returns a list of club types. Regular users can only see public club types and ones they created. Super admins can see all club types.",
    ),
    retrieve=extend_schema(
        tags=["Club Types"],
        summary="Retrieve a specific club type",
        description="Returns details of a specific club type.",
    ),
    create=extend_schema(
        tags=["Club Types"],
        summary="Create a new club type",
        description="Creates a new club type. Only authenticated users can create club types.",
    ),
    update=extend_schema(
        tags=["Club Types"],
        summary="Update a club type",
        description="Updates a club type's details. Only authenticated users can update club types.",
    ),
    partial_update=extend_schema(
        tags=["Club Types"],
        summary="Partially update a club type",
        description="Partially updates a club type's details. Only authenticated users can update club types.",
    ),
    destroy=extend_schema(
        tags=["Club Types"],
        summary="Delete a club type",
        description="Deletes a club type. Only authenticated users can delete club types.",
    ),
)
class ClubTypeViewSet(viewsets.ModelViewSet):
    serializer_class = ClubTypeSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        if self.request.user.is_superuser:
            return ClubType.objects.all()
        else:
            return ClubType.objects.filter(
                Q(visibility=ClubType.VISIBILITY_PUBLIC)
                | Q(created_by=self.request.user)
            )

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
