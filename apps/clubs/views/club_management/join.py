from rest_framework import permissions, status
from rest_framework.views import APIView
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.utils.translation import gettext as _
from drf_spectacular.utils import extend_schema, OpenApiResponse
from django.db.models import Q

from django.template.loader import render_to_string
from django.conf import settings
from core.tasks.emails import send_email_with_html

from apps.clubs.models import Club, ClubInvitation


@extend_schema(
    tags=["Clubs"],
    summary="Join a club",
    description="Accept a club invitation and join as a member or request to join a club",
    responses={
        200: OpenApiResponse(
            description="Successfully joined the club or sent a join request"
        ),
        400: OpenApiResponse(description="Invalid request"),
        403: OpenApiResponse(description="No valid invitation found"),
        404: OpenApiResponse(description="Club not found"),
    },
)
class JoinClubAPIView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk=None):
        """Accept club invitation and join as a member."""
        club = get_object_or_404(Club, pk=pk)

        try:
            invitation = ClubInvitation.objects.get(
                Q(user=request.user) | Q(email=request.user.email), club=club
            )
            if invitation.sender != club.manager:
                return Response(
                    {"error": _("Waiting for approval")},
                    status=status.HTTP_403_FORBIDDEN,
                )

            if request.user in club.members.all():
                return Response(
                    {
                        "message": _("You are already a member of {club_name}").format(
                            club_name=club.name
                        )
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                invitation.status = ClubInvitation.STATUS_ACCEPTED
                invitation.save()
                club.members.add(request.user)
                club.save()

            return Response(
                {
                    "message": _("Successfully joined {club_name}").format(
                        club_name=club.name
                    )
                },
                status=status.HTTP_200_OK,
            )

        except ClubInvitation.DoesNotExist:
            if club.privacy == Club.PRIVACY_CLOSED:
                return Response(
                    {"error": _("This club is private")},
                    status=status.HTTP_403_FORBIDDEN,
                )

            if request.user in club.members.all():
                return Response(
                    {
                        "error": _("You are already a member of {club_name}").format(
                            club_name=club.name
                        )
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )

            ClubInvitation.objects.create(
                user=request.user,
                email=request.user.email,
                fullname=request.user.fullname,
                club=club,
                status=ClubInvitation.STATUS_PENDING,
            )
            context = {
                "club": club,
                "user": request.user,
            }
            html_content = render_to_string(
                "clubs/emails/join_request/join_request_sent.html", context
            )
            text_content = render_to_string(
                "clubs/emails/join_request/join_request_sent.txt", context
            )
            subject = _("Club joining request")

            send_email_with_html(
                subject=subject,
                text_content=text_content,
                html_content=html_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to_email=club.manager.email,
            )
            return Response(
                {
                    "message": _("Join request sent to {club_name}").format(
                        club_name=club.name
                    )
                },
                status=status.HTTP_200_OK,
            )
