from email.policy import default
from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from core.abstract.models import AbstractModel
from apps.clubs.models import ClubType


class ClubDemand(AbstractModel):
    STATUS_PENDING = "pending"
    STATUS_ACCEPTED = "accepted"
    STATUS_REJECTED = "rejected"
    STATUS_CHOICES = [
        (STATUS_ACCEPTED, "Accepted"),
        (STATUS_REJECTED, "Rejected"),
        (STATUS_PENDING, "Pending"),
    ]

    club_name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_("Club Demand Name"),
    )
    icon = models.ImageField(
        upload_to="clubs/icons/",
        blank=True,
        verbose_name=_("Club Demand Type"),
    )
    type = models.ForeignKey(
        ClubType,
        on_delete=models.CASCADE,
        related_name="club_demand_type",
    )
    manager = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="club_demand_manager",
    )
    status = models.CharField(
        max_length=10,
        choices=STATUS_CHOICES,
        default=STATUS_PENDING,
        verbose_name=_("Club Demand Status"),
    )

    class Meta:
        verbose_name = _("ClubDemand")
        verbose_name_plural = _("ClubDemands")
        ordering = ["club_name"]

    def __str__(self):
        return self.club_name

    @property
    def icon_url(self):
        if self.icon:
            return f"/media/{self.icon}"
        return None
