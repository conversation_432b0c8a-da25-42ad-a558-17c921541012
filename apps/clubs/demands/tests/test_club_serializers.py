from apps.clubs.demands.serializers.accept_demand import AcceptClubDemandSerializer
from rest_framework.test import APITestCase
from apps.clubs.demands.serializers.club_demand import ClubDemandSerializer
from apps.clubs.models import ClubType


class ClubDemandSerializerTestCase(APITestCase):
    def setUp(self):
        self.club_type = ClubType.objects.create(name="Test Club Type")

    def test_serializer_valid(self):
        data = {"club_name": "Valid Club Demand", "type": self.club_type.id}
        serializer = ClubDemandSerializer(data=data)
        self.assertTrue(serializer.is_valid())

    def test_serializer_invalid(self):
        data = {"club_name": "", "type": self.club_type.id}
        serializer = ClubDemandSerializer(data=data)
        self.assertFalse(serializer.is_valid())
