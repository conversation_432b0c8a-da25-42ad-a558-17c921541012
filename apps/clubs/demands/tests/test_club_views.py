from rest_framework .test import APITestCase ,APIClient 
from apps .clubs .demands .models import ClubDemand ,ClubType 
from django .contrib .auth .models import User 
from apps .clubs .models import Club ,ClubType 
from apps .clubs .demands .serializers .club_demand import ClubDemandSerializer 
from rest_framework .test import APIClient 
from apps .clubs .demands .models import ClubDemand 
from rest_framework .test import APITestCase 
from rest_framework import status 
from django .urls import reverse 
from django .contrib .auth import get_user_model 

User =get_user_model ()


class CreateClubDemandAPITestCase (APITestCase ):
    def setUp (self ):
        self .client =APIClient ()
        self .manager =User .objects .create_user (
        username ="manager",email ="<EMAIL>",password ="password123"
        )
        self .client .force_authenticate (user =self .manager )
        self .url =reverse ("club_demands:create-club-demand")

        self .club_type =ClubType .objects .create (name ="Test Club Type")

    def test_create_club_demand_success (self ):
        data ={"club_name":"New Club Demand","type":self .club_type .id }
        response =self .client .post (self .url ,data ,format ="json")
        self .assertEqual (response .status_code ,status .HTTP_201_CREATED )
        self .assertEqual (ClubDemand .objects .count (),1 )

    def test_create_club_demand_invalid (self ):
        data ={"club_name":"","type":self .club_type .id }
        response =self .client .post (self .url ,data ,format ="json")
        self .assertEqual (response .status_code ,status .HTTP_400_BAD_REQUEST )

    def test_create_club_demand_unauthorized (self ):
        self .client .force_authenticate (user =None )
        response =self .client .post (self .url ,format ="json")
        self .assertEqual (response .status_code ,status .HTTP_401_UNAUTHORIZED )


class AcceptClubDemandAPIViewTests (APITestCase ):
    def setUp (self ):
        self .admin_user =User .objects .create_user (
        username ="testuser",
        password ="testpass",
        role ="admin",
        email ="<EMAIL>",
        )
        self .client .force_authenticate (user =self .admin_user )
        self .club_type =ClubType .objects .create (name ="Test Club Type")
        self .club_demand =ClubDemand .objects .create (
        club_name ="Test Club1",
        status ="pending",
        type =self .club_type ,
        manager =self .admin_user ,
        )

    def test_accept_club_demand (self ):
        url =reverse (
        "club_demands:accept-reject-club-demand",args =[self .club_demand .id ]
        )
        response =self .client .post (url ,{"accept":True })
        self .club_demand .refresh_from_db ()
        self .assertEqual (response .status_code ,status .HTTP_200_OK )
        self .assertEqual (self .club_demand .status ,"accepted")

    def test_reject_club_demand (self ):
        url =reverse (
        "club_demands:accept-reject-club-demand",args =[self .club_demand .id ]
        )
        response =self .client .post (url ,{"accept":False })
        self .club_demand .refresh_from_db ()
        self .assertEqual (response .status_code ,status .HTTP_200_OK )
        self .assertEqual (self .club_demand .status ,"rejected")

    def test_invalid_data (self ):
        url =reverse (
        "club_demands:accept-reject-club-demand",args =[self .club_demand .id ]
        )
        response =self .client .post (url ,{"accept":"invalid"})
        self .assertEqual (response .status_code ,status .HTTP_400_BAD_REQUEST )

    def test_invalid_club_demand_id (self ):
        url =reverse (
        "club_demands:accept-reject-club-demand",
        args =["2b6a0959-c26f-4c14-9a71-d335f80b11fb"],
        )
        response =self .client .post (url ,{"accept":True })
        self .assertEqual (response .status_code ,status .HTTP_400_BAD_REQUEST )

    def test_user_role_not_admin (self ):
        self .user =User .objects .create_user (
        username ="notadmin",
        password ="testpass",
        role ="member",
        email ="<EMAIL>",
        )
        self .client .force_authenticate (user =self .user )
        url =reverse (
        "club_demands:accept-reject-club-demand",args =[self .club_demand .id ]
        )
        response =self .client .post (url ,{"accept":True })
        self .assertEqual (response .status_code ,status .HTTP_400_BAD_REQUEST )

    def test_club_demand_status_not_pending (self ):
        self .club_demand .status ="accepted"
        self .club_demand .save ()
        url =reverse (
        "club_demands:accept-reject-club-demand",args =[self .club_demand .id ]
        )
        response =self .client .post (url ,{"accept":True })
        self .assertEqual (response .status_code ,status .HTTP_400_BAD_REQUEST )

    def test_already_accepted_or_rejected (self ):
        self .club_demand .status ="accepted"
        self .club_demand .save ()
        url =reverse (
        "club_demands:accept-reject-club-demand",args =[self .club_demand .id ]
        )
        response =self .client .post (url ,{"accept":True })
        self .assertEqual (response .status_code ,status .HTTP_400_BAD_REQUEST )

    def test_unauthenticated_user (self ):
        self .client .logout ()
        url =reverse (
        "club_demands:accept-reject-club-demand",args =[self .club_demand .id ]
        )
        response =self .client .post (url ,{"accept":True })
        self .assertEqual (response .status_code ,status .HTTP_401_UNAUTHORIZED )


User =get_user_model ()


class ListClubDemandsAPITestCase (APITestCase ):
    def setUp (self ):
        self .client =APIClient ()

        self .client .defaults ["HTTP_ACCEPT_LANGUAGE"]="en"

        self .admin_user =User .objects .create_user (
        username ="adminuser",
        password ="adminpass",
        role ="admin",
        email ="<EMAIL>",
        )
        self .member_user =User .objects .create_user (
        username ="memberuser",
        password ="memberpass",
        role ="member",
        email ="<EMAIL>",
        )
        self .club_type =ClubType .objects .create (name ="Test Club Type")

        self .pending_club_demand =ClubDemand .objects .create (
        club_name ="Pending Club",
        status ="pending",
        type =self .club_type ,
        manager =self .admin_user ,
        )
        self .accepted_club_demand =ClubDemand .objects .create (
        club_name ="Accepted Club",
        status ="accepted",
        type =self .club_type ,
        manager =self .admin_user ,
        )
        self .rejected_club_demand =ClubDemand .objects .create (
        club_name ="Rejected Club",
        status ="rejected",
        type =self .club_type ,
        manager =self .admin_user ,
        )

        self .url =reverse ("club_demands:list-club-demands")

    def test_list_club_demands_as_admin (self ):
        self .client .force_authenticate (user =self .admin_user )
        response =self .client .get (self .url )
        self .assertEqual (response .status_code ,status .HTTP_200_OK )
        self .assertEqual (len (response .data ),1 )
        self .assertEqual (response .data [0 ]["club_name"],"Pending Club")

    def test_list_club_demands_as_non_admin (self ):
        self .client .force_authenticate (user =self .member_user )
        response =self .client .get (self .url )
        self .assertEqual (response .status_code ,status .HTTP_403_FORBIDDEN )

        error_message =str (response .data ["detail"])
        self .assertTrue (
        ("admin"in error_message .lower ())or ("مدير"in error_message ),
        f"Expected admin-related error message, got: {error_message}"
        )

    def test_list_club_demands_unauthenticated (self ):
        self .client .force_authenticate (user =None )
        response =self .client .get (self .url )
        self .assertEqual (response .status_code ,status .HTTP_401_UNAUTHORIZED )
