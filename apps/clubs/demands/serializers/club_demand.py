from rest_framework import serializers
from apps.clubs.demands.models import ClubDemand


class ClubDemandSerializer(serializers.ModelSerializer):
    manager = serializers.ReadOnlyField(source="manager.id")

    class Meta:
        model = ClubDemand
        fields = ["id", "club_name", "manager", "type", "icon"]

    def create(self, validated_data):
        manager = self.context.get("manager")
        validated_data["manager"] = manager
        return super().create(validated_data)
