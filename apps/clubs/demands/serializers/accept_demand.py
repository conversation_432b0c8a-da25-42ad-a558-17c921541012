from rest_framework import serializers
from django.utils.translation import gettext as _
from apps.clubs.demands.models import ClubDemand


class AcceptClubDemandSerializer(serializers.Serializer):
    accept = serializers.BooleanField()

    def validate(self, data):
        club_demand = ClubDemand.objects.filter(
            pk=self.context["club_demand_id"]
        ).first()
        if not club_demand:
            raise serializers.ValidationError(_("Club Demand not found."))

        if self.context["request"].user.role != "admin":
            raise serializers.ValidationError(
                _("Only admins can accept or reject club demands.")
            )

        if club_demand.status != "pending":
            raise serializers.ValidationError(
                _("Club Demand already accepted or rejected.")
            )

        return data
