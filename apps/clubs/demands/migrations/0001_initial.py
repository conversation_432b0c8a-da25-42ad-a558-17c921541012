# Generated by Django 4.2.9 on 2025-04-03 12:29

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("clubs", "0004_clubinvitation"),
    ]

    operations = [
        migrations.CreateModel(
            name="ClubDemand",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "club_name",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="Club Demand Name"
                    ),
                ),
                (
                    "icon",
                    models.ImageField(
                        blank=True,
                        upload_to="clubs/icons/",
                        verbose_name="Club Demand Type",
                    ),
                ),
                (
                    "manager",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="club_demand_manager",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="club_demand_type",
                        to="clubs.clubtype",
                    ),
                ),
            ],
            options={
                "verbose_name": "ClubDemand",
                "verbose_name_plural": "ClubDemands",
                "ordering": ["club_name"],
            },
        ),
    ]
