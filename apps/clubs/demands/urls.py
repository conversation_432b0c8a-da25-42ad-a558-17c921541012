from django.urls import path
from apps.clubs.demands.views.create import CreateClubDemandAPIView
from apps.clubs.demands.views.accept_demand import AcceptClubDemandAPIView
from apps.clubs.demands.views.list import ListClubDemandsAPIView

app_name = "club_demands"

urlpatterns = [
    path("create", CreateClubDemandAPIView.as_view(), name="create-club-demand"),
    path(
        "accept/<uuid:club_demand_id>",
        AcceptClubDemandAPIView.as_view(),
        name="accept-reject-club-demand",
    ),
    path("list", ListClubDemandsAPIView.as_view(), name="list-club-demands"),
]
