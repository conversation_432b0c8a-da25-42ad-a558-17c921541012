from rest_framework import permissions, status
from rest_framework.views import APIView
from rest_framework.response import Response
from django.utils.translation import gettext as _
from drf_spectacular.utils import extend_schema, OpenApiResponse

from apps.clubs.demands.models import ClubDemand
from apps.clubs.demands.serializers.accept_demand import AcceptClubDemandSerializer


@extend_schema(
    tags=["Club-Demands"],
    summary="Accept/Reject Club Demand",
    description="Accept or Reject Club Demand",
    request=AcceptClubDemandSerializer,
    responses={
        200: OpenApiResponse(
            description="Successfully accepted or rejected the club demand"
        ),
        400: OpenApiResponse(description="Invalid request"),
    },
)
class AcceptClubDemandAPIView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, club_demand_id=None):
        """Remove a member from a club."""
        accepted = request.data["accept"]
        serializer = AcceptClubDemandSerializer(
            data=request.data,
            context={"request": request, "club_demand_id": club_demand_id},
        )
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        club_demand = ClubDemand.objects.get(pk=club_demand_id)
        if accepted:
            club_demand.status = "accepted"
            club_demand.save()

            return Response(
                {
                    "message": _(
                        f"Successfully accepted club creating demand for {club_demand.club_name}"
                    )
                },
                status=status.HTTP_200_OK,
            )

        else:
            club_demand.status = "rejected"
            club_demand.save()

            return Response(
                {
                    "message": _(
                        f"Successfully rejected club creating demand for {club_demand.club_name}"
                    )
                },
                status=status.HTTP_200_OK,
            )
