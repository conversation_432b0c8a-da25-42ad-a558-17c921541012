from rest_framework import permissions, status, serializers
from rest_framework.views import APIView
from rest_framework.response import Response
from django.utils.translation import gettext as _
from drf_spectacular.utils import extend_schema, OpenApiResponse

from apps.clubs.demands.models import ClubDemand
from apps.clubs.demands.serializers.list import ListClubDemandSerializer


@extend_schema(
    tags=["Club-Demands"],
    summary="List Club Demands",
    description="Retrieve a list of pending club demands. Only admins can access this endpoint.",
    responses={
        200: ListClubDemandSerializer(many=True),
        403: OpenApiResponse(
            description="Forbidden - Only admins can list club demands"
        ),
    },
)
class ListClubDemandsAPIView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """List all club demands."""
        if request.user.role != "admin":
            return Response(
                {"detail": _("Only admins can list club demands.")},
                status=status.HTTP_403_FORBIDDEN,
            )

        club_demands = ClubDemand.objects.filter(status="pending")
        serializer = ListClubDemandSerializer(club_demands, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
