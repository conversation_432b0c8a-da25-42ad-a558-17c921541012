from rest_framework import permissions, status
from rest_framework.views import APIView
from rest_framework.response import Response
from django.utils.translation import gettext as _
from drf_spectacular.utils import extend_schema, OpenApiResponse
from apps.clubs.demands.serializers.club_demand import ClubDemandSerializer


@extend_schema(
    tags=["Club-Demands"],
    summary="Create a club demand",
    description="Create a new club demand",
    request=ClubDemandSerializer,
    responses={
        200: OpenApiResponse(description="Successfully created the club demand"),
        400: OpenApiResponse(description="Invalid request"),
    },
)
class CreateClubDemandAPIView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """Create a new club demand."""
        serializer = ClubDemandSerializer(
            data=request.data, context={"manager": request.user}
        )
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
