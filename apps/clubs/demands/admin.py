from django.contrib import admin
from apps.clubs.demands.models import ClubDemand


class ClubDemandAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "club_name",
        "manager",
        "type",
        "status",
        "created",
        "updated",
    ]
    search_fields = ["club_name"]
    list_filter = [
        "manager",
        "type",
        "status",
        "created",
        "updated",
    ]
    readonly_fields = ["created", "updated"]


admin.site.register(ClubDemand, ClubDemandAdmin)
