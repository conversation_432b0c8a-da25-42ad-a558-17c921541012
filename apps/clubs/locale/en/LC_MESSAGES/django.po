# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-03 17:02+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: apps/clubs/demands/models.py:22
msgid "Club Demand Name"
msgstr ""

#: apps/clubs/demands/models.py:27
msgid "Club Demand Type"
msgstr ""

#: apps/clubs/demands/models.py:43
msgid "Club Demand Status"
msgstr ""

#: apps/clubs/demands/models.py:47
msgid "ClubDemand"
msgstr ""

#: apps/clubs/demands/models.py:48
msgid "ClubDemands"
msgstr ""

#: apps/clubs/demands/serializers/accept_demand.py:14
msgid "Club Demand not found."
msgstr ""

#: apps/clubs/demands/serializers/accept_demand.py:18
msgid "Only admins can accept or reject club demands."
msgstr ""

#: apps/clubs/demands/serializers/accept_demand.py:23
msgid "Club Demand already accepted or rejected."
msgstr ""

#: apps/clubs/demands/views/accept_demand.py:44
#, python-brace-format
msgid "Successfully accepted club creating demand for {club_demand.club_name}"
msgstr ""

#: apps/clubs/demands/views/accept_demand.py:57
#, python-brace-format
msgid "Successfully rejected club creating demand for {club_demand.club_name}"
msgstr ""

#: apps/clubs/demands/views/list.py:29
msgid "Only admins can list club demands."
msgstr ""

#: apps/clubs/meetings/filters.py:32
msgid "User Role"
msgstr ""

#: apps/clubs/meetings/filters.py:35 apps/clubs/meetings/models.py:70
msgid "Organizer"
msgstr ""

#: apps/clubs/meetings/filters.py:36
msgid "Attendee"
msgstr ""

#: apps/clubs/meetings/filters.py:37
msgid "All"
msgstr ""

#: apps/clubs/meetings/models.py:30 apps/clubs/models.py:103
msgid "Club"
msgstr ""

#: apps/clubs/meetings/models.py:34
msgid "Title"
msgstr ""

#: apps/clubs/meetings/models.py:37
msgid "Description"
msgstr ""

#: apps/clubs/meetings/models.py:42
msgid "Start Time"
msgstr ""

#: apps/clubs/meetings/models.py:45
msgid "End Time"
msgstr ""

#: apps/clubs/meetings/models.py:48
msgid "Meeting Link"
msgstr ""

#: apps/clubs/meetings/models.py:56
msgid "Google Calendar Event ID"
msgstr ""

#: apps/clubs/meetings/models.py:59
msgid "Jitsi Room Name"
msgstr ""

#: apps/clubs/meetings/models.py:62
msgid "Jitsi Meeting ID"
msgstr ""

#: apps/clubs/meetings/models.py:64
msgid "Jitsi Embed Link"
msgstr ""

#: apps/clubs/meetings/models.py:65
msgid "Is Jitsi Meeting"
msgstr ""

#: apps/clubs/meetings/models.py:76
msgid "Attendees"
msgstr ""

#: apps/clubs/meetings/models.py:82 apps/clubs/meetings/models.py:157
msgid "Status"
msgstr ""

#: apps/clubs/meetings/models.py:86
msgid "ClubMeeting"
msgstr ""

#: apps/clubs/meetings/models.py:87
msgid "ClubMeetings"
msgstr ""

#: apps/clubs/meetings/models.py:101
msgid "Start time must be before end time"
msgstr ""

#: apps/clubs/meetings/models.py:104
msgid "Cannot schedule a meeting in the past"
msgstr ""

#: apps/clubs/meetings/models.py:145
msgid "Meeting"
msgstr ""

#: apps/clubs/meetings/models.py:151
msgid "User"
msgstr ""

#: apps/clubs/meetings/models.py:161
msgid "Is Notified"
msgstr ""

#: apps/clubs/meetings/models.py:165
msgid "MeetingAttendee"
msgstr ""

#: apps/clubs/meetings/models.py:166
msgid "MeetingAttendees"
msgstr ""

#: apps/clubs/meetings/serializers/meeting.py:108
#: apps/clubs/views/club_views/update.py:32
msgid "At least one field must be provided for update"
msgstr "At least one field must be provided for update"

#: apps/clubs/meetings/serializers/meeting.py:122
#: apps/clubs/meetings/serializers/meeting.py:171
msgid "End time must be after start time"
msgstr "End time must be after start time"

#: apps/clubs/meetings/serializers/meeting.py:130
msgid "Meeting must be at least 15 minutes long"
msgstr "Meeting must be at least 15 minutes long"

#: apps/clubs/meetings/serializers/meeting.py:147
msgid "Cannot set meeting start time in the past"
msgstr "Cannot set meeting start time in the past"

#: apps/clubs/meetings/serializers/meeting.py:202
msgid "Email attendees must be a list"
msgstr "Email attendees must be a list"

#: apps/clubs/meetings/serializers/meeting.py:206
msgid "Duplicate emails are not allowed"
msgstr "Duplicate emails are not allowed"

#: apps/clubs/meetings/serializers/meeting_attendee.py:56
#: apps/clubs/meetings/views/meeting_attendee.py:100
msgid "Either 'user_id' or 'user_ids' must be provided"
msgstr "Either 'user_id' or 'user_ids' must be provided"

#: apps/clubs/meetings/serializers/meeting_attendee.py:61
#: apps/clubs/meetings/views/meeting_attendee.py:97
msgid "Provide either 'user_id' or 'user_ids', not both"
msgstr "Provide either 'user_id' or 'user_ids', not both"

#: apps/clubs/meetings/serializers/meeting_attendee.py:94
#, python-brace-format
msgid "The following user IDs do not exist: {ids}"
msgstr "The following user IDs do not exist: {ids}"

#: apps/clubs/meetings/serializers/meeting_attendee.py:120
#, python-brace-format
msgid "The following users are not members of this club: {ids}"
msgstr "The following users are not members of this club: {ids}"

#: apps/clubs/meetings/signals.py:65
#, python-brace-format
msgid "New club meeting: {title}"
msgstr ""

#: apps/clubs/meetings/signals.py:69
#, python-brace-format
msgid "A new meeting has been scheduled for {club_name} on {meeting_time}"
msgstr ""

#: apps/clubs/meetings/signals.py:80
#, python-brace-format
msgid "Meeting cancelled: {title}"
msgstr ""

#: apps/clubs/meetings/signals.py:86
#, python-brace-format
msgid ""
"The meeting for {club_name} scheduled for {meeting_time} has been cancelled."
msgstr ""

#: apps/clubs/meetings/signals.py:100
#, python-brace-format
msgid "You've been added to a meeting: {title}"
msgstr ""

#: apps/clubs/meetings/signals.py:106
#, python-brace-format
msgid "You've been added to a meeting for {club_name} on {meeting_time}"
msgstr ""

#: apps/clubs/meetings/signals.py:116
#, python-brace-format
msgid "{user_name} accepted meeting invitation"
msgstr ""

#: apps/clubs/meetings/signals.py:122
#, python-brace-format
msgid "{user_name} will attend the meeting {title} on {meeting_time}"
msgstr ""

#: apps/clubs/meetings/signals.py:132
#, python-brace-format
msgid "{user_name} declined meeting invitation"
msgstr ""

#: apps/clubs/meetings/signals.py:138
#, python-brace-format
msgid "{user_name} cannot attend the meeting {title} on {meeting_time}"
msgstr ""

#: apps/clubs/meetings/utils/jitsi_utils.py:36
msgid ""
"JITSI_JWT_APP_ID and JITSI_JWT_APP_SECRET must be set in Django settings."
msgstr ""

#: apps/clubs/meetings/utils/jitsi_utils.py:112
#: apps/clubs/meetings/utils/jitsi_utils.py:135
msgid "JITSI_DOMAIN must be set in Django settings."
msgstr ""

#: apps/clubs/meetings/views/jitsi_react_view.py:108
#: apps/clubs/meetings/views/jitsi_token_view.py:81
msgid "You are not authorized to join this meeting"
msgstr ""

#: apps/clubs/meetings/views/jitsi_react_view.py:117
#: apps/clubs/meetings/views/jitsi_token_view.py:90
msgid "This meeting is not currently active"
msgstr ""

#: apps/clubs/meetings/views/jitsi_react_view.py:125
#: apps/clubs/meetings/views/jitsi_token_view.py:98
msgid "This meeting does not have a Jitsi meeting configured"
msgstr ""

#: apps/clubs/meetings/views/jitsi_react_view.py:240
msgid "Failed to generate meeting configuration"
msgstr ""

#: apps/clubs/meetings/views/jitsi_token_view.py:131
msgid "Failed to generate meeting token"
msgstr ""

#: apps/clubs/meetings/views/meeting.py:72
#: apps/clubs/meetings/views/meeting.py:247
msgid "You must be a member or manager of this club to view meetings"
msgstr ""

#: apps/clubs/meetings/views/meeting.py:134
msgid "Only club manager can create meetings"
msgstr ""

#: apps/clubs/meetings/views/meeting.py:284
#: apps/clubs/meetings/views/meeting.py:326
msgid "Only club manager or meeting organizer can update this meeting"
msgstr ""

#: apps/clubs/meetings/views/meeting.py:359
msgid "Only club manager or meeting organizer can delete this meeting"
msgstr ""

#: apps/clubs/meetings/views/meeting.py:395
msgid "Only club manager or meeting organizer can cancel this meeting"
msgstr ""

#: apps/clubs/meetings/views/meeting.py:406
msgid "Meeting is already cancelled or completed"
msgstr ""

#: apps/clubs/meetings/views/meeting.py:414
msgid "Meeting cancelled successfully"
msgstr ""

#: apps/clubs/meetings/views/meeting_attendee.py:65
msgid "Only the club manager or meeting organizer can manage attendees."
msgstr ""

#: apps/clubs/meetings/views/meeting_attendee.py:81
msgid "Invalid user_id format"
msgstr "Invalid user_id format"

#: apps/clubs/meetings/views/meeting_attendee.py:93
msgid "Invalid user_ids format"
msgstr "Invalid user_ids format"

#: apps/clubs/meetings/views/meeting_attendee.py:171
msgid "User is already an attendee"
msgstr "User is already an attendee"

#: apps/clubs/meetings/views/meeting_attendee.py:199
#: apps/clubs/meetings/views/meeting_attendee.py:321
msgid "User not found"
msgstr "User not found"

#: apps/clubs/meetings/views/meeting_attendee.py:295
msgid "Cannot remove the meeting organizer"
msgstr "Cannot remove the meeting organizer"

#: apps/clubs/meetings/views/meeting_attendee.py:303
msgid "Cannot remove the club manager"
msgstr "Cannot remove the club manager"

#: apps/clubs/meetings/views/meeting_attendee.py:315
msgid "User is not an attendee of this meeting"
msgstr "User is not an attendee of this meeting"

#: apps/clubs/models.py:31 apps/clubs/models.py:74
msgid "Name"
msgstr ""

#: apps/clubs/models.py:44
msgid "Visibility"
msgstr ""

#: apps/clubs/models.py:48
msgid "ClubType"
msgstr ""

#: apps/clubs/models.py:49
msgid "ClubTypes"
msgstr ""

#: apps/clubs/models.py:79
msgid "Icon"
msgstr ""

#: apps/clubs/models.py:104
msgid "Clubs"
msgstr ""

#: apps/clubs/models.py:349
msgid "Email"
msgstr ""

#: apps/clubs/models.py:355
msgid "Full Name"
msgstr ""

#: apps/clubs/models.py:366
msgid "ClubInvitation"
msgstr ""

#: apps/clubs/models.py:367
msgid "ClubInvitations"
msgstr ""

#: apps/clubs/serializers/club.py:46
msgid "Members list cannot be empty."
msgstr ""

#: apps/clubs/serializers/club_serializers/create.py:31
#: apps/clubs/serializers/club_serializers/update.py:27
#, fuzzy
#| msgid "Invalid user_id format"
msgid "Invalid JSON string for members"
msgstr "Invalid user_id format"

#: apps/clubs/serializers/invite.py:49
msgid ""
"At least one invitation method (emails or invitations) must be provided."
msgstr ""

#: apps/clubs/serializers/invite.py:69
msgid "You cannot invite yourself to the club."
msgstr ""

#: apps/clubs/serializers/invite.py:79
#, python-brace-format
msgid "The following users are already members of this club: {emails}"
msgstr ""

#: apps/clubs/serializers/invite.py:92 apps/clubs/serializers/invite.py:115
#: apps/clubs/serializers/invite.py:119
#, python-brace-format
msgid "The following users already have pending invitations: {emails}"
msgstr ""

#: apps/clubs/serializers/remove_member.py:12
msgid "Club not found."
msgstr ""

#: apps/clubs/serializers/remove_member.py:16
msgid "Member not found in the club."
msgstr ""

#: apps/clubs/serializers/remove_member.py:19
msgid "Only the manager can remove members."
msgstr ""

#: apps/clubs/views/club_management/accept_club_join_request.py:41
msgid "Only the club manager or the invited user can accept this invitation"
msgstr ""

#: apps/clubs/views/club_management/accept_club_join_request.py:70
msgid "User accepted club invitation"
msgstr ""

#: apps/clubs/views/club_management/accept_club_join_request.py:82
#: apps/clubs/views/club_management/accept_club_join_request.py:92
msgid "Club join request accepted"
msgstr ""

#: apps/clubs/views/club_management/accept_club_join_request.py:96
msgid "Club join request not found"
msgstr ""

#: apps/clubs/views/club_management/invite.py:57
#: apps/clubs/views/club_management/members.py:49
msgid "Club not found"
msgstr ""

#: apps/clubs/views/club_management/invite.py:62
msgid "Only club managers can send invitations"
msgstr ""

#: apps/clubs/views/club_management/invite.py:177
#, python-brace-format
msgid "Welcome to {club_name}! You've been invited to join."
msgstr ""

#: apps/clubs/views/club_management/invite.py:181
#: apps/clubs/views/club_views/create.py:159
#, python-brace-format
msgid "Invitation to join {club_name}"
msgstr ""

#: apps/clubs/views/club_management/invite.py:228
msgid "Failed to process invitations. Please try again."
msgstr ""

#: apps/clubs/views/club_management/invite.py:239
#, python-brace-format
msgid ""
"Processed {total} invitations. {success} sent successfully, {failed} failed."
msgstr ""

#: apps/clubs/views/club_management/join.py:42
msgid "Waiting for approval"
msgstr ""

#: apps/clubs/views/club_management/join.py:49
#: apps/clubs/views/club_management/join.py:80
#, python-brace-format
msgid "You are already a member of {club_name}"
msgstr ""

#: apps/clubs/views/club_management/join.py:63
#, python-brace-format
msgid "Successfully joined {club_name}"
msgstr ""

#: apps/clubs/views/club_management/join.py:73
msgid "This club is private"
msgstr ""

#: apps/clubs/views/club_management/join.py:104
msgid "Club joining request"
msgstr ""

#: apps/clubs/views/club_management/join.py:115
#, python-brace-format
msgid "Join request sent to {club_name}"
msgstr ""

#: apps/clubs/views/club_management/manager_clubs.py:90
msgid "Manager not found"
msgstr ""

#: apps/clubs/views/club_management/members.py:53
msgid "You are not authorized to view this club's members"
msgstr ""

#: apps/clubs/views/club_management/reject_invitation.py:33
msgid "Invitation is not pending"
msgstr ""

#: apps/clubs/views/club_management/reject_invitation.py:44
msgid "You are not authorized to reject this invitation"
msgstr ""

#: apps/clubs/views/club_management/reject_invitation.py:51
msgid "Invitation rejected"
msgstr ""

#: apps/clubs/views/club_management/remove_member.py:41
#, python-brace-format
msgid "Successfully removed {member.username} from {club.name}"
msgstr ""

#: apps/clubs/views/club_views/create.py:192
#, python-brace-format
msgid ""
"You have reached your limit of {max_clubs} clubs. You cannot create more "
"clubs."
msgstr ""
