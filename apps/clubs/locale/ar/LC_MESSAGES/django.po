# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Taj\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-03 17:02+0000\n"
"PO-Revision-Date: 2023-01-01 00:00+0000\n"
"Last-Translator: Taj Team\n"
"Language-Team: Arabic <<EMAIL>>\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 "
"&& n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#: apps/clubs/demands/models.py:22
msgid "Club Demand Name"
msgstr "اسم طلب النادي"

#: apps/clubs/demands/models.py:27
msgid "Club Demand Type"
msgstr "نوع طلب النادي"

#: apps/clubs/demands/models.py:43
msgid "Club Demand Status"
msgstr "حالة طلب النادي"

#: apps/clubs/demands/models.py:47
msgid "ClubDemand"
msgstr "طلب النادي"

#: apps/clubs/demands/models.py:48
msgid "ClubDemands"
msgstr "طلبات الأندية"

#: apps/clubs/demands/serializers/accept_demand.py:14
msgid "Club Demand not found."
msgstr "لم يتم العثور على النادي"

#: apps/clubs/demands/serializers/accept_demand.py:18
msgid "Only admins can accept or reject club demands."
msgstr "يمكن للمديرين فقط قبول أو رفض طلبات الأندية."

#: apps/clubs/demands/serializers/accept_demand.py:23
msgid "Club Demand already accepted or rejected."
msgstr "تم قبول أو رفض طلب النادي بالفعل."

#: apps/clubs/demands/views/accept_demand.py:44
#, python-brace-format
msgid "Successfully accepted club creating demand for {club_demand.club_name}"
msgstr "تم قبول طلب إنشاء النادي بنجاح لـ {club_demand.club_name}"

#: apps/clubs/demands/views/accept_demand.py:57
#, fuzzy, python-brace-format
#| msgid "Successfully joined {club_name}"
msgid "Successfully rejected club creating demand for {club_demand.club_name}"
msgstr "تم الانضمام بنجاح إلى {club_name}"

#: apps/clubs/demands/views/list.py:29
msgid "Only admins can list club demands."
msgstr "يمكن للمديرين فقط عرض طلبات الأندية."

#: apps/clubs/meetings/filters.py:32
msgid "User Role"
msgstr "دور المستخدم"

#: apps/clubs/meetings/filters.py:35 apps/clubs/meetings/models.py:70
msgid "Organizer"
msgstr "منظم"

#: apps/clubs/meetings/filters.py:36
msgid "Attendee"
msgstr "حاضر"

#: apps/clubs/meetings/filters.py:37
msgid "All"
msgstr "الكل"

#: apps/clubs/meetings/models.py:30 apps/clubs/models.py:103
msgid "Club"
msgstr "النادي"

#: apps/clubs/meetings/models.py:34
msgid "Title"
msgstr "العنوان"

#: apps/clubs/meetings/models.py:37
msgid "Description"
msgstr "الوصف"

#: apps/clubs/meetings/models.py:42
msgid "Start Time"
msgstr "وقت البداية"

#: apps/clubs/meetings/models.py:45
msgid "End Time"
msgstr "وقت النهاية"

#: apps/clubs/meetings/models.py:48
msgid "Meeting Link"
msgstr "رابط الاجتماع"

#: apps/clubs/meetings/models.py:56
msgid "Google Calendar Event ID"
msgstr "معرف حدث تقويم جوجل"

#: apps/clubs/meetings/models.py:59
msgid "Jitsi Room Name"
msgstr "اسم غرفة Jitsi"

#: apps/clubs/meetings/models.py:62
msgid "Jitsi Meeting ID"
msgstr "معرف اجتماع Jitsi"

#: apps/clubs/meetings/models.py:64
msgid "Jitsi Embed Link"
msgstr "رابط تضمين Jitsi"

#: apps/clubs/meetings/models.py:65
msgid "Is Jitsi Meeting"
msgstr "هو اجتماع Jitsi"

#: apps/clubs/meetings/models.py:76
msgid "Attendees"
msgstr "الحضور"

#: apps/clubs/meetings/models.py:82 apps/clubs/meetings/models.py:157
msgid "Status"
msgstr "الحالة"

#: apps/clubs/meetings/models.py:86
msgid "ClubMeeting"
msgstr "اجتماع النادي"

#: apps/clubs/meetings/models.py:87
msgid "ClubMeetings"
msgstr "اجتماعات النادي"

#: apps/clubs/meetings/models.py:101
msgid "Start time must be before end time"
msgstr "يجب أن يكون وقت البداية قبل وقت النهاية"

#: apps/clubs/meetings/models.py:104
msgid "Cannot schedule a meeting in the past"
msgstr "لا يمكن جدولة اجتماع في الماضي"

#: apps/clubs/meetings/models.py:145
msgid "Meeting"
msgstr "الاجتماع"

#: apps/clubs/meetings/models.py:151
msgid "User"
msgstr "المستخدم"

#: apps/clubs/meetings/models.py:161
msgid "Is Notified"
msgstr "تم إشعاره"

#: apps/clubs/meetings/models.py:165
msgid "MeetingAttendee"
msgstr "حاضر الاجتماع"

#: apps/clubs/meetings/models.py:166
msgid "MeetingAttendees"
msgstr "حضور الاجتماع"

#: apps/clubs/meetings/serializers/meeting.py:108
#: apps/clubs/views/club_views/update.py:32
msgid "At least one field must be provided for update"
msgstr "يجب توفير حقل واحد على الأقل للتحديث"

#: apps/clubs/meetings/serializers/meeting.py:122
#: apps/clubs/meetings/serializers/meeting.py:171
msgid "End time must be after start time"
msgstr "يجب أن يكون وقت النهاية بعد وقت البداية"

#: apps/clubs/meetings/serializers/meeting.py:130
msgid "Meeting must be at least 15 minutes long"
msgstr "يجب أن يكون الاجتماع 15 دقيقة على الأقل"

#: apps/clubs/meetings/serializers/meeting.py:147
msgid "Cannot set meeting start time in the past"
msgstr "لا يمكن تحديد وقت بداية الاجتماع في الماضي"

#: apps/clubs/meetings/serializers/meeting.py:202
msgid "Email attendees must be a list"
msgstr "يجب أن تكون قائمة الحضور بالبريد الإلكتروني قائمة"

#: apps/clubs/meetings/serializers/meeting.py:206
msgid "Duplicate emails are not allowed"
msgstr "البريد الإلكتروني المكرر غير مسموح"

#: apps/clubs/meetings/serializers/meeting_attendee.py:56
#: apps/clubs/meetings/views/meeting_attendee.py:100
msgid "Either 'user_id' or 'user_ids' must be provided"
msgstr "يجب توفير إما 'user_id' أو 'user_ids'"

#: apps/clubs/meetings/serializers/meeting_attendee.py:61
#: apps/clubs/meetings/views/meeting_attendee.py:97
msgid "Provide either 'user_id' or 'user_ids', not both"
msgstr "قدم إما 'user_id' أو 'user_ids'، وليس كلاهما"

#: apps/clubs/meetings/serializers/meeting_attendee.py:94
#, python-brace-format
msgid "The following user IDs do not exist: {ids}"
msgstr "معرفات المستخدمين التالية غير موجودة: {ids}"

#: apps/clubs/meetings/serializers/meeting_attendee.py:120
#, python-brace-format
msgid "The following users are not members of this club: {ids}"
msgstr "المستخدمون التاليون ليسوا أعضاء في هذا النادي: {ids}"

#: apps/clubs/meetings/signals.py:65
#, python-brace-format
msgid "New club meeting: {title}"
msgstr "اجتماع نادي جديد: {title}"

#: apps/clubs/meetings/signals.py:69
#, python-brace-format
msgid "A new meeting has been scheduled for {club_name} on {meeting_time}"
msgstr "تم جدولة اجتماع جديد لـ {club_name} في {meeting_time}"

#: apps/clubs/meetings/signals.py:80
#, python-brace-format
msgid "Meeting cancelled: {title}"
msgstr "تم إلغاء الاجتماع: {title}"

#: apps/clubs/meetings/signals.py:86
#, python-brace-format
msgid ""
"The meeting for {club_name} scheduled for {meeting_time} has been cancelled."
msgstr "تم إلغاء الاجتماع الخاص بـ {club_name} المجدول في {meeting_time}."

#: apps/clubs/meetings/signals.py:100
#, python-brace-format
msgid "You've been added to a meeting: {title}"
msgstr "تمت إضافتك إلى اجتماع: {title}"

#: apps/clubs/meetings/signals.py:106
#, python-brace-format
msgid "You've been added to a meeting for {club_name} on {meeting_time}"
msgstr "تمت إضافتك إلى اجتماع لـ {club_name} في {meeting_time}"

#: apps/clubs/meetings/signals.py:116
#, python-brace-format
msgid "{user_name} accepted meeting invitation"
msgstr "{user_name} قبل دعوة الاجتماع"

#: apps/clubs/meetings/signals.py:122
#, python-brace-format
msgid "{user_name} will attend the meeting {title} on {meeting_time}"
msgstr "{user_name} سيحضر الاجتماع {title} في {meeting_time}"

#: apps/clubs/meetings/signals.py:132
#, python-brace-format
msgid "{user_name} declined meeting invitation"
msgstr "{user_name} رفض دعوة الاجتماع"

#: apps/clubs/meetings/signals.py:138
#, python-brace-format
msgid "{user_name} cannot attend the meeting {title} on {meeting_time}"
msgstr "{user_name} لا يستطيع حضور الاجتماع {title} في {meeting_time}"

#: apps/clubs/meetings/utils/jitsi_utils.py:36
msgid ""
"JITSI_JWT_APP_ID and JITSI_JWT_APP_SECRET must be set in Django settings."
msgstr "يجب تعيين JITSI_JWT_APP_ID و JITSI_JWT_APP_SECRET في إعدادات Django."

#: apps/clubs/meetings/utils/jitsi_utils.py:112
#: apps/clubs/meetings/utils/jitsi_utils.py:135
msgid "JITSI_DOMAIN must be set in Django settings."
msgstr "يجب تعيين JITSI_DOMAIN في إعدادات Django."

#: apps/clubs/meetings/views/jitsi_react_view.py:108
#: apps/clubs/meetings/views/jitsi_token_view.py:81
#, fuzzy
#| msgid "You are not authorized to reject this invitation"
msgid "You are not authorized to join this meeting"
msgstr "أنت غير مخول لرفض هذه الدعوة"

#: apps/clubs/meetings/views/jitsi_react_view.py:117
#: apps/clubs/meetings/views/jitsi_token_view.py:90
msgid "This meeting is not currently active"
msgstr "هذا الاجتماع غير نشط حالياً"

#: apps/clubs/meetings/views/jitsi_react_view.py:125
#: apps/clubs/meetings/views/jitsi_token_view.py:98
msgid "This meeting does not have a Jitsi meeting configured"
msgstr "هذا الاجتماع ليس له إعداد اجتماع Jitsi"

#: apps/clubs/meetings/views/jitsi_react_view.py:240
msgid "Failed to generate meeting configuration"
msgstr "فشل في إنشاء إعداد الاجتماع"

#: apps/clubs/meetings/views/jitsi_token_view.py:131
msgid "Failed to generate meeting token"
msgstr "فشل في إنشاء رمز الاجتماع"

#: apps/clubs/meetings/views/meeting.py:72
#: apps/clubs/meetings/views/meeting.py:247
msgid "You must be a member or manager of this club to view meetings"
msgstr "يجب أن تكون عضواً أو مديراً في هذا النادي لعرض الاجتماعات"

#: apps/clubs/meetings/views/meeting.py:134
#, fuzzy
#| msgid "Only club manager can send invitations"
msgid "Only club manager can create meetings"
msgstr "يمكن لمدير النادي فقط إرسال الدعوات"

#: apps/clubs/meetings/views/meeting.py:284
#: apps/clubs/meetings/views/meeting.py:326
#, fuzzy
#| msgid "Only club manager can send invitations"
msgid "Only club manager or meeting organizer can update this meeting"
msgstr "يمكن لمدير النادي فقط إرسال الدعوات"

#: apps/clubs/meetings/views/meeting.py:359
#, fuzzy
#| msgid "Only club manager can send invitations"
msgid "Only club manager or meeting organizer can delete this meeting"
msgstr "يمكن لمدير النادي فقط إرسال الدعوات"

#: apps/clubs/meetings/views/meeting.py:395
#, fuzzy
#| msgid "Only club manager can send invitations"
msgid "Only club manager or meeting organizer can cancel this meeting"
msgstr "يمكن لمدير النادي فقط إرسال الدعوات"

#: apps/clubs/meetings/views/meeting.py:406
msgid "Meeting is already cancelled or completed"
msgstr "الاجتماع ملغى أو مكتمل بالفعل"

#: apps/clubs/meetings/views/meeting.py:414
msgid "Meeting cancelled successfully"
msgstr "تم إلغاء الاجتماع بنجاح"

#: apps/clubs/meetings/views/meeting_attendee.py:65
#, fuzzy
#| msgid "Only club manager can send invitations"
msgid "Only the club manager or meeting organizer can manage attendees."
msgstr "يمكن لمدير النادي فقط إرسال الدعوات"

#: apps/clubs/meetings/views/meeting_attendee.py:81
msgid "Invalid user_id format"
msgstr "تنسيق معرف المستخدم غير صالح"

#: apps/clubs/meetings/views/meeting_attendee.py:93
msgid "Invalid user_ids format"
msgstr "تنسيق معرفات المستخدمين غير صالح"

#: apps/clubs/meetings/views/meeting_attendee.py:171
msgid "User is already an attendee"
msgstr "المستخدم حاضر بالفعل"

#: apps/clubs/meetings/views/meeting_attendee.py:199
#: apps/clubs/meetings/views/meeting_attendee.py:321
msgid "User not found"
msgstr "لم يتم العثور على المستخدم"

#: apps/clubs/meetings/views/meeting_attendee.py:295
msgid "Cannot remove the meeting organizer"
msgstr "لا يمكن إزالة منظم الاجتماع"

#: apps/clubs/meetings/views/meeting_attendee.py:303
msgid "Cannot remove the club manager"
msgstr "لا يمكن إزالة مدير النادي"

#: apps/clubs/meetings/views/meeting_attendee.py:315
msgid "User is not an attendee of this meeting"
msgstr "المستخدم ليس من المشاركين في هذا الاجتماع"

#: apps/clubs/models.py:31 apps/clubs/models.py:74
msgid "Name"
msgstr "الاسم"

#: apps/clubs/models.py:44
msgid "Visibility"
msgstr "الرؤية"

#: apps/clubs/models.py:48
msgid "ClubType"
msgstr "نوع النادي"

#: apps/clubs/models.py:49
msgid "ClubTypes"
msgstr "أنواع الأندية"

#: apps/clubs/models.py:79
msgid "Icon"
msgstr "الأيقونة"

#: apps/clubs/models.py:104
msgid "Clubs"
msgstr "الأندية"

#: apps/clubs/models.py:349
msgid "Email"
msgstr "البريد الإلكتروني"

#: apps/clubs/models.py:355
msgid "Full Name"
msgstr "الاسم الكامل"

#: apps/clubs/models.py:366
msgid "ClubInvitation"
msgstr "دعوة النادي"

#: apps/clubs/models.py:367
msgid "ClubInvitations"
msgstr "دعوات النادي"

#: apps/clubs/serializers/club.py:46
msgid "Members list cannot be empty."
msgstr ""

#: apps/clubs/serializers/club_serializers/create.py:31
#: apps/clubs/serializers/club_serializers/update.py:27
#, fuzzy
#| msgid "Invalid user_id format"
msgid "Invalid JSON string for members"
msgstr "تنسيق معرف المستخدم غير صالح"

#: apps/clubs/serializers/invite.py:49
msgid ""
"At least one invitation method (emails or invitations) must be provided."
msgstr "يجب توفير طريقة دعوة واحدة على الأقل (إيميلات أو دعوات)."

#: apps/clubs/serializers/invite.py:69
msgid "You cannot invite yourself to the club."
msgstr "لا يمكنك دعوة نفسك إلى النادي."

#: apps/clubs/serializers/invite.py:79
#, fuzzy, python-brace-format
#| msgid "You are already a member of {club_name}"
msgid "The following users are already members of this club: {emails}"
msgstr "أنت بالفعل عضو في {club_name}"

#: apps/clubs/serializers/invite.py:92 apps/clubs/serializers/invite.py:115
#: apps/clubs/serializers/invite.py:119
#, python-brace-format
msgid "The following users already have pending invitations: {emails}"
msgstr "المستخدمون التاليون لديهم دعوات معلقة بالفعل: {emails}"

#: apps/clubs/serializers/remove_member.py:12
msgid "Club not found."
msgstr "لم يتم العثور على النادي"

#: apps/clubs/serializers/remove_member.py:16
msgid "Member not found in the club."
msgstr "لم يتم العثور على العضو في النادي."

#: apps/clubs/serializers/remove_member.py:19
msgid "Only the manager can remove members."
msgstr "يمكن للمدير فقط إزالة الأعضاء."

#: apps/clubs/views/club_management/accept_club_join_request.py:41
#, fuzzy
#| msgid "Only club manager can send invitations"
msgid "Only the club manager or the invited user can accept this invitation"
msgstr "يمكن لمدير النادي فقط إرسال الدعوات"

#: apps/clubs/views/club_management/accept_club_join_request.py:70
msgid "User accepted club invitation"
msgstr "قبل المستخدم دعوة النادي"

#: apps/clubs/views/club_management/accept_club_join_request.py:82
#: apps/clubs/views/club_management/accept_club_join_request.py:92
msgid "Club join request accepted"
msgstr "تم قبول طلب الانضمام إلى النادي"

#: apps/clubs/views/club_management/accept_club_join_request.py:96
#, fuzzy
#| msgid "Club join request accepted"
msgid "Club join request not found"
msgstr "تم قبول طلب الانضمام إلى النادي"

#: apps/clubs/views/club_management/invite.py:57
#: apps/clubs/views/club_management/members.py:49
msgid "Club not found"
msgstr "لم يتم العثور على النادي"

#: apps/clubs/views/club_management/invite.py:62
#, fuzzy
#| msgid "Only club manager can send invitations"
msgid "Only club managers can send invitations"
msgstr "يمكن لمدير النادي فقط إرسال الدعوات"

#: apps/clubs/views/club_management/invite.py:177
#, python-brace-format
msgid "Welcome to {club_name}! You've been invited to join."
msgstr "مرحباً بك في {club_name}! تمت دعوتك للانضمام."

#: apps/clubs/views/club_management/invite.py:181
#: apps/clubs/views/club_views/create.py:159
#, python-brace-format
msgid "Invitation to join {club_name}"
msgstr "دعوة للانضمام إلى {club_name}"

#: apps/clubs/views/club_management/invite.py:228
msgid "Failed to process invitations. Please try again."
msgstr "فشل في معالجة الدعوات. يرجى المحاولة مرة أخرى."

#: apps/clubs/views/club_management/invite.py:239
#, python-brace-format
msgid ""
"Processed {total} invitations. {success} sent successfully, {failed} failed."
msgstr "تم معالجة {total} دعوة. تم إرسال {success} بنجاح، فشل في {failed}."

#: apps/clubs/views/club_management/join.py:42
msgid "Waiting for approval"
msgstr "في انتظار الموافقة"

#: apps/clubs/views/club_management/join.py:49
#: apps/clubs/views/club_management/join.py:80
#, python-brace-format
msgid "You are already a member of {club_name}"
msgstr "أنت بالفعل عضو في {club_name}"

#: apps/clubs/views/club_management/join.py:63
#, python-brace-format
msgid "Successfully joined {club_name}"
msgstr "تم الانضمام بنجاح إلى {club_name}"

#: apps/clubs/views/club_management/join.py:73
msgid "This club is private"
msgstr "هذا النادي خاص"

#: apps/clubs/views/club_management/join.py:104
msgid "Club joining request"
msgstr "طلب الانضمام إلى النادي"

#: apps/clubs/views/club_management/join.py:115
#, python-brace-format
msgid "Join request sent to {club_name}"
msgstr "تم إرسال طلب الانضمام إلى {club_name}"

#: apps/clubs/views/club_management/manager_clubs.py:90
#, fuzzy
#| msgid "Invitations sent to {count} users"
msgid "Manager not found"
msgstr "تم إرسال الدعوات إلى {count} مستخدم"

#: apps/clubs/views/club_management/members.py:53
msgid "You are not authorized to view this club's members"
msgstr "أنت غير مخول بعرض أعضاء هذا النادي."

#: apps/clubs/views/club_management/reject_invitation.py:33
msgid "Invitation is not pending"
msgstr "الدعوة غير قيد الانتظار"

#: apps/clubs/views/club_management/reject_invitation.py:44
msgid "You are not authorized to reject this invitation"
msgstr "أنت غير مخول لرفض هذه الدعوة"

#: apps/clubs/views/club_management/reject_invitation.py:51
msgid "Invitation rejected"
msgstr "تم رفض الدعوة"

#: apps/clubs/views/club_management/remove_member.py:41
#, fuzzy, python-brace-format
#| msgid "Successfully joined {club_name}"
msgid "Successfully removed {member.username} from {club.name}"
msgstr "تم الانضمام بنجاح إلى {club_name}"

#: apps/clubs/views/club_views/create.py:192
#, python-brace-format
msgid ""
"You have reached your limit of {max_clubs} clubs. You cannot create more "
"clubs."
msgstr ""
"لقد وصلت إلى الحد الأقصى من {max_clubs} نادي. لا يمكنك إنشاء المزيد من "
"الأندية."

#, fuzzy
#~| msgid "Invitations sent to {count} users"
#~ msgid "User not found."
#~ msgstr "تم إرسال الدعوات إلى {count} مستخدم"

#~ msgid "User is not a member of this club."
#~ msgstr "المستخدم ليس عضواً في هذا النادي."

#~ msgid "User is already an attendee."
#~ msgstr "المستخدم حاضر بالفعل."

#~ msgid "Cannot remove the meeting organizer."
#~ msgstr "لا يمكن إزالة منظم الاجتماع."
