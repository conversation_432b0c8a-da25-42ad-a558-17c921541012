# Generated by Django 4.2.9 on 2025-03-24 12:48

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ClubType",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=100, unique=True, verbose_name="Name"),
                ),
            ],
            options={
                "verbose_name": "ClubType",
                "verbose_name_plural": "ClubTypes",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Club",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=100, unique=True, verbose_name="Name"),
                ),
                (
                    "icon",
                    models.ImageField(
                        blank=True, upload_to="clubs/icons/", verbose_name="Icon"
                    ),
                ),
                (
                    "privacy",
                    models.CharField(
                        choices=[("private", "Private"), ("public", "public")],
                        default="public",
                        max_length=7,
                    ),
                ),
                (
                    "join_permissions",
                    models.CharField(
                        choices=[("open", "Open"), ("invite-only", "Invite-only")],
                        default="open",
                        max_length=11,
                    ),
                ),
                (
                    "members",
                    models.ManyToManyField(
                        related_name="club_members", to=settings.AUTH_USER_MODEL
                    ),
                ),
                (
                    "type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="club_type",
                        to="clubs.clubtype",
                    ),
                ),
            ],
            options={
                "verbose_name": "Club",
                "verbose_name_plural": "Clubs",
                "ordering": ["name"],
            },
        ),
    ]
