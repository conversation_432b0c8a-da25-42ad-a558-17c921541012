# Generated by Django 4.2.9 on 2025-04-09 19:23

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("clubs", "0004_clubinvitation"),
    ]

    operations = [
        migrations.AddField(
            model_name="clubinvitation",
            name="email",
            field=models.EmailField(
                blank=True, max_length=254, null=True, verbose_name="Email"
            ),
        ),
        migrations.AddField(
            model_name="clubinvitation",
            name="fullname",
            field=models.CharField(
                blank=True, max_length=150, null=True, verbose_name="Full Name"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="clubinvitation",
            unique_together={("email", "club")},
        ),
    ]
