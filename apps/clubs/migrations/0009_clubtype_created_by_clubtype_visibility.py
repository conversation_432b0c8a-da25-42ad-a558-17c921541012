# Generated by Django 4.2.9 on 2025-04-22 10:59

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("clubs", "0008_clubinvitation_sender"),
    ]

    operations = [
        migrations.AddField(
            model_name="clubtype",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="created_club_types",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="clubtype",
            name="visibility",
            field=models.CharField(
                choices=[("private", "Private"), ("public", "Public")],
                default="private",
                max_length=10,
                verbose_name="Visibility",
            ),
        ),
    ]
