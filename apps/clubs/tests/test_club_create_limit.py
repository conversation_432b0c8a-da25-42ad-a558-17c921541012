import uuid 
from django .test import TestCase ,override_settings 
from django .urls import reverse 
from django .contrib .auth import get_user_model 
from rest_framework .test import APIClient 
from rest_framework import status 

from apps .clubs .models import Club ,ClubType 

User =get_user_model ()


class ClubCreateLimitTest (TestCase ):
    """
    Test suite for the club creation limit functionality.
    """

    def setUp (self ):
        """Set up test data."""
        self .client =APIClient ()

        self .client .defaults ["HTTP_ACCEPT_LANGUAGE"]="en"

        self .user =User .objects .create_user (
        email ="<EMAIL>",username ="testuser",password ="testpass123"
        )

        self .club_type =ClubType .objects .create (name ="Test Club Type")

        self .club_create_url =reverse ("clubs:club-create")

        self .club_data ={
        "name":f"Test Club {uuid.uuid4()}",
        "type":str (self .club_type .id ),
        "privacy":Club .PRIVACY_OPEN ,
        "join_permissions":Club .JOIN_PERMISSIONS_OPEN ,
        }

    @override_settings (CLUB_MANAGER_CAN_CREATE_X_CLUBS =2 )
    def test_club_creation_under_limit (self ):
        """Test that a user can create clubs when under their limit."""
        self .client .force_authenticate (user =self .user )

        response1 =self .client .post (
        self .club_create_url ,
        {**self .club_data ,"name":f"Test Club 1 {uuid.uuid4()}"},
        format ="json",
        )
        self .assertEqual (response1 .status_code ,status .HTTP_201_CREATED )

        response2 =self .client .post (
        self .club_create_url ,
        {**self .club_data ,"name":f"Test Club 2 {uuid.uuid4()}"},
        format ="json",
        )
        self .assertEqual (response2 .status_code ,status .HTTP_201_CREATED )

        self .assertEqual (Club .objects .filter (manager =self .user ).count (),2 )

    @override_settings (CLUB_MANAGER_CAN_CREATE_X_CLUBS =1 )
    def test_club_creation_at_limit (self ):
        """Test that a user cannot create more clubs when at their limit."""
        self .client .force_authenticate (user =self .user )

        response1 =self .client .post (
        self .club_create_url ,
        {**self .club_data ,"name":f"Test Club 1 {uuid.uuid4()}"},
        format ="json",
        )
        self .assertEqual (response1 .status_code ,status .HTTP_201_CREATED )

        response2 =self .client .post (
        self .club_create_url ,
        {**self .club_data ,"name":f"Test Club 2 {uuid.uuid4()}"},
        format ="json",
        )
        self .assertEqual (response2 .status_code ,status .HTTP_403_FORBIDDEN )


        error_message =str (response2 .data )
        self .assertTrue (
        ("limit"in error_message .lower ())or ("حد"in error_message ),
        f"Expected limit-related error message, got: {error_message}"
        )

        self .assertEqual (Club .objects .filter (manager =self .user ).count (),1 )

    @override_settings (CLUB_MANAGER_CAN_CREATE_X_CLUBS =0 )
    def test_club_creation_with_zero_limit (self ):
        """Test that a user cannot create clubs when their limit is zero."""
        self .client .force_authenticate (user =self .user )

        response =self .client .post (self .club_create_url ,self .club_data ,format ="json")
        self .assertEqual (response .status_code ,status .HTTP_403_FORBIDDEN )


        error_message =str (response .data )
        self .assertTrue (
        ("limit"in error_message .lower ())or ("حد"in error_message ),
        f"Expected limit-related error message, got: {error_message}"
        )

        self .assertEqual (Club .objects .filter (manager =self .user ).count (),0 )

    def test_club_creation_unauthenticated (self ):
        """Test that unauthenticated users cannot create clubs."""
        response =self .client .post (self .club_create_url ,self .club_data ,format ="json")
        self .assertEqual (response .status_code ,status .HTTP_401_UNAUTHORIZED )

    @override_settings (CLUB_MANAGER_CAN_CREATE_X_CLUBS =5 )
    def test_club_creation_limit_per_user (self ):
        """Test that the club creation limit applies individually to each user."""
        self .client .force_authenticate (user =self .user )

        response1 =self .client .post (
        self .club_create_url ,
        {**self .club_data ,"name":f"Test Club 1 {uuid.uuid4()}"},
        format ="json",
        )
        self .assertEqual (response1 .status_code ,status .HTTP_201_CREATED )

        second_user =User .objects .create_user (
        email ="<EMAIL>",
        username ="seconduser",
        password ="testpass123",
        )

        self .client .force_authenticate (user =second_user )

        response2 =self .client .post (
        self .club_create_url ,
        {**self .club_data ,"name":f"Second User Club {uuid.uuid4()}"},
        format ="json",
        )
        self .assertEqual (response2 .status_code ,status .HTTP_201_CREATED )

        self .assertEqual (Club .objects .filter (manager =self .user ).count (),1 )
        self .assertEqual (Club .objects .filter (manager =second_user ).count (),1 )

    @override_settings (CLUB_MANAGER_CAN_CREATE_X_CLUBS =1 )
    def test_club_creation_error_format (self ):
        """Test the error message format when attempting to create too many clubs."""
        self .client .force_authenticate (user =self .user )

        response1 =self .client .post (
        self .club_create_url ,
        {**self .club_data ,"name":f"Test Club 1 {uuid.uuid4()}"},
        format ="json",
        )
        self .assertEqual (response1 .status_code ,status .HTTP_201_CREATED )

        response2 =self .client .post (
        self .club_create_url ,
        {**self .club_data ,"name":f"Test Club 2 {uuid.uuid4()}"},
        format ="json",
        )
        self .assertEqual (response2 .status_code ,status .HTTP_403_FORBIDDEN )

        self .assertIn ("error",response2 .data )
        error_msg =str (response2 .data ["error"])

        self .assertTrue (
        ("limit"in error_msg .lower ())or ("حد"in error_msg ),
        f"Expected limit-related error message, got: {error_msg}"
        )
