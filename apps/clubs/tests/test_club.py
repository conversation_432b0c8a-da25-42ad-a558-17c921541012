from unittest.mock import patch
from django.test import TestCase, override_settings
from rest_framework.test import APIClient
from rest_framework import status
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.conf import settings
from django.core.files.uploadedfile import SimpleUploadedFile
import json

from apps.accounts.user.models import UserToken
from apps.clubs.models import Club, ClubType, ClubInvitation
import uuid
import os
import tempfile
from PIL import Image
from apps.clubs.serializers.club_serializers.detail import ClubDetailSerializer

User = get_user_model()


@override_settings(CLUB_MANAGER_CAN_CREATE_X_CLUBS=100)
class ClubViewSetTest(TestCase):
    def setUp(self):
        self.client = APIClient()

        Club.objects.all().delete()
        self.user = User.objects.create_user(
            email="<EMAIL>", password="password123", username="testuser"
        )
        self.other_user = User.objects.create_user(
            email="<EMAIL>", password="password123", username="otheruser"
        )
        self.client.force_authenticate(user=self.user)
        self.club_type = ClubType.objects.create(name="Test Type")
        self.list_url = reverse("clubs:club-list")
        self.create_url = reverse("clubs:club-create")

        self.club = Club.objects.create(
            name="Test Club",
            type=self.club_type,
            manager=self.user,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_OPEN,
        )
        self.detail_url = reverse("clubs:club-detail", args=[self.club.id])
        self.invite_url = reverse("clubs:invite-members", args=[self.club.id])

        self.temp_image = self._create_test_image()

    def _create_test_image(self):
        """Helper method to create a test image for testing uploads"""
        with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as f:
            image = Image.new("RGB", (100, 100), color="red")
            image.save(f, "PNG")
        return f.name

    def tearDown(self):
        if hasattr(self, "temp_image") and os.path.exists(self.temp_image):
            os.unlink(self.temp_image)

    def test_list_clubs(self):
        for club in Club.objects.exclude(id=self.club.id):
            club.delete()

        self.client.force_authenticate(user=self.user)

        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        response_data = response.json()

        print(f"Response data: {response_data}")
        print(f"Response data: {response_data}")

        if isinstance(response_data, list):
            our_clubs = [c for c in response_data if c.get("name") == self.club.name]
            self.assertTrue(len(our_clubs) > 0, "Our club should be in the response")
            self.assertEqual(our_clubs[0]["name"], self.club.name)
        elif isinstance(response_data, dict) and "error" in response_data:
            self.fail(f"API returned an error: {response_data['error']}")
        elif isinstance(response_data, dict) and "results" in response_data:
            our_clubs = [
                c for c in response_data["results"] if c.get("name") == self.club.name
            ]
            self.assertTrue(len(our_clubs) > 0, "Our club should be in the response")
            self.assertEqual(our_clubs[0]["name"], self.club.name)
        else:
            self.assertIn(self.club.name, str(response_data))

    def test_create_club_sets_authenticated_user_as_manager(self):
        unique_name = f"New Club {uuid.uuid4()}"

        self.club_type.refresh_from_db()

        Club.objects.filter(name=unique_name).delete()

        self.client.force_authenticate(user=self.user)

        data = {
            "name": unique_name,
            "type": str(self.club_type.id),
            "privacy": Club.PRIVACY_OPEN,
            "join_permissions": Club.JOIN_PERMISSIONS_OPEN,
            "members": [{"email": str(self.user.email)}],
        }

        response = self.client.post(self.create_url, data, format="json")

        if response.status_code != status.HTTP_201_CREATED:
            print(f"Response status: {response.status_code}")
            print(f"Response content: {response.content.decode()}")
            print(f"Data sent: {data}")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        created_club = Club.objects.get(name=unique_name)
        self.assertEqual(created_club.manager, self.user)

        response_data = response.json()

        self.assertIsInstance(response_data["manager"], dict)
        self.assertEqual(response_data["manager"]["id"], str(self.user.id))
        self.assertEqual(response_data["manager"]["username"], self.user.username)

    @patch("apps.clubs.views.club_views.create.send_email_with_html")
    def test_create_club_with_icon(self, mock_send_email):
        """Test creating a club with an icon image"""
        with open(self.temp_image, "rb") as icon_file:
            icon_content = icon_file.read()

        icon = SimpleUploadedFile(
            name="club_icon.png", content=icon_content, content_type="image/png"
        )

        data = {
            "name": "Icon Club",
            "type": str(self.club_type.id),
            "privacy": Club.PRIVACY_OPEN,
            "join_permissions": Club.JOIN_PERMISSIONS_OPEN,
            "icon": icon,
        }

        response = self.client.post(self.create_url, data, format="multipart")

        if response.status_code != status.HTTP_201_CREATED:
            print(f"Response status: {response.status_code}")
            print(f"Response content: {response.content.decode()}")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        created_club = Club.objects.get(name="Icon Club")

        self.assertTrue(created_club.icon)
        response_data = response.json()
        self.assertIsNotNone(response_data.get("icon_url"))

        self.assertTrue(
            response_data.get("icon_url", "").startswith(settings.SITE_URL)
            or response_data.get("icon_url", "").startswith("http")
        )

        print(f"Response data: {response.json()}")

        self.assertTrue("icon" in response.json() or "icon_url" in response.json())

    def test_update_club_with_icon(self):
        """Test updating a club with a new icon image"""
        self.client.force_authenticate(user=self.user)
        update_url = reverse("clubs:club-partial-update", args=[self.club.id])

        with open(self.temp_image, "rb") as icon_file:
            icon_content = icon_file.read()

        icon = SimpleUploadedFile(
            name="updated_icon.png", content=icon_content, content_type="image/png"
        )

        data = {
            "privacy": Club.PRIVACY_CLOSED,
            "icon": icon,
        }

        response = self.client.patch(update_url, data, format="multipart")

        if response.status_code != status.HTTP_200_OK:
            print(f"Response status: {response.status_code}")
            print(f"Response content: {response.content.decode()}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.club.refresh_from_db()

        self.assertTrue(self.club.icon)
        response_data = response.json()
        self.assertIsNotNone(response_data.get("icon_url"))

        self.assertTrue(
            response_data.get("icon_url", "").startswith(settings.SITE_URL)
            or response_data.get("icon_url", "").startswith("http")
        )

        self.assertEqual(self.club.privacy, Club.PRIVACY_CLOSED)

        print(f"Response data: {response.json()}")

    def test_invite_members_by_email(self):
        invite_user = User.objects.create_user(
            email="<EMAIL>", password="password123", username="inviteuser"
        )

        data = {"emails": [invite_user.email]}

        response = self.client.post(self.invite_url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertEqual(ClubInvitation.objects.count(), 1)
        invitation = ClubInvitation.objects.first()
        self.assertEqual(invitation.user, invite_user)
        self.assertEqual(invitation.club, self.club)

    def test_invite_nonexistent_email(self):
        data = {"emails": ["<EMAIL>"]}

        response = self.client.post(self.invite_url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        response_data = response.json()

        self.assertIn("invited_members", response_data)
        self.assertEqual(len(response_data["invited_members"]), 1)
        self.assertEqual(
            response_data["invited_members"][0]["email"], "<EMAIL>"
        )

        self.assertEqual(ClubInvitation.objects.count(), 1)
        invitation = ClubInvitation.objects.first()
        self.assertEqual(invitation.user.email, "<EMAIL>")

    def test_invite_multiple_emails(self):
        invite_user1 = User.objects.create_user(
            email="<EMAIL>", password="password123", username="invite1user"
        )
        invite_user2 = User.objects.create_user(
            email="<EMAIL>", password="password123", username="invite2user"
        )

        data = {
            "emails": [
                invite_user1.email,
                invite_user2.email,
                "<EMAIL>",
            ]
        }

        response = self.client.post(self.invite_url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertEqual(ClubInvitation.objects.count(), 3)

        self.assertIn("invited_members", response.json())
        self.assertEqual(len(response.json()["invited_members"]), 3)

        user_emails = [user["email"] for user in response.json()["invited_members"]]
        self.assertIn("<EMAIL>", user_emails)

    def test_only_manager_can_invite(self):
        self.client.force_authenticate(user=self.other_user)

        data = {"emails": ["<EMAIL>"]}

        response = self.client.post(self.invite_url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        self.assertEqual(ClubInvitation.objects.count(), 0)

    @patch("apps.clubs.views.club_views.create.send_email_with_html")
    def test_create_club_with_new_users(self, mock_send_email):
        new_email = "<EMAIL>"
        unique_name = f"New Club {uuid.uuid4()}"

        data = {
            "name": unique_name,
            "type": str(self.club_type.id),
            "privacy": Club.PRIVACY_OPEN,
            "join_permissions": Club.JOIN_PERMISSIONS_OPEN,
            "members": [{"email": new_email, "name": "New User"}],
        }

        response = self.client.post(self.create_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        new_user = User.objects.get(email=new_email)
        self.assertIsNotNone(new_user)

        self.assertTrue(UserToken.objects.filter(user=new_user).exists())

        invitation = ClubInvitation.objects.filter(user=new_user).first()
        self.assertIsNotNone(invitation)
        self.assertEqual(invitation.fullname, "New User")

        mock_send_email.assert_called()

    @patch("apps.clubs.views.club_views.create.send_email_with_html")
    def test_create_club_with_existing_users(self, mock_send_email):
        existing_email = self.other_user.email
        unique_name = f"New Club {uuid.uuid4()}"

        data = {
            "name": unique_name,
            "type": str(self.club_type.id),
            "privacy": Club.PRIVACY_OPEN,
            "join_permissions": Club.JOIN_PERMISSIONS_OPEN,
            "members": [{"email": existing_email}],
        }

        response = self.client.post(self.create_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        self.assertEqual(User.objects.filter(email=existing_email).count(), 1)

        self.assertTrue(ClubInvitation.objects.filter(user=self.other_user).exists())

        self.assertTrue(mock_send_email.called)

    @patch("apps.clubs.views.club_views.create.send_email_with_html")
    def test_create_club_with_mixed_users(self, mock_send_email):
        new_email = "<EMAIL>"
        existing_email = self.other_user.email
        unique_name = f"Mixed Club {uuid.uuid4()}"

        data = {
            "name": unique_name,
            "type": str(self.club_type.id),
            "privacy": Club.PRIVACY_OPEN,
            "join_permissions": Club.JOIN_PERMISSIONS_OPEN,
            "members": [
                {"email": new_email, "name": "New User 2"},
                {"email": existing_email, "name": "Existing User"},
            ],
        }

        response = self.client.post(self.create_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        new_user = User.objects.get(email=new_email)
        self.assertIsNotNone(new_user)

        new_user_invitation = ClubInvitation.objects.filter(user=new_user).first()
        self.assertIsNotNone(new_user_invitation)
        self.assertEqual(new_user_invitation.fullname, "New User 2")

        existing_user_invitation = ClubInvitation.objects.filter(
            user=self.other_user
        ).first()
        self.assertIsNotNone(existing_user_invitation)
        self.assertEqual(existing_user_invitation.fullname, "Existing User")

        self.assertEqual(mock_send_email.call_count, 2)

    @patch("apps.clubs.views.club_views.create.send_email_with_html")
    def test_members_are_added_to_club_on_creation(self, mock_send_email):
        """Test that members are correctly added to the club during creation"""

        unique_name = f"Members Club {uuid.uuid4()}"
        new_email = "<EMAIL>"
        unique_name = f"Members Club {uuid.uuid4()}"
        new_email = "<EMAIL>"

        data = {
            "name": unique_name,
            "type": str(self.club_type.id),
            "privacy": Club.PRIVACY_OPEN,
            "join_permissions": Club.JOIN_PERMISSIONS_OPEN,
            "members": [
                {"email": new_email, "name": "New Member"},
                {"email": self.other_user.email, "name": "Existing Member"},
            ],
        }

        response = self.client.post(self.create_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        created_club = Club.objects.get(name=unique_name)

        self.assertEqual(created_club.members.count(), 2)

        new_user = User.objects.get(email=new_email)
        self.assertIn(new_user, created_club.members.all())
        self.assertIn(self.other_user, created_club.members.all())

        response_data = response.json()
        self.assertIn("member_ids", response_data)
        self.assertEqual(len(response_data["member_ids"]), 2)

        member_ids = [str(m.id) for m in created_club.members.all()]
        for member_id in response_data["member_ids"]:
            self.assertIn(member_id, member_ids)

    def test_list_clubs_includes_member_ids(self):
        """Test that club list includes member_ids field with correct values"""

        self.club.members.add(self.user)
        self.club.members.add(self.other_user)
        self.club.save()

        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        club_in_response = None
        if isinstance(response.data, list):
            for club in response.data:
                if str(club.get("id")) == str(self.club.id):
                    club_in_response = club
                    break
        elif isinstance(response.data, dict) and "results" in response.data:
            for club in response.data["results"]:
                if str(club.get("id")) == str(self.club.id):
                    club_in_response = club
                    break

        self.assertIsNotNone(club_in_response, "Club should be in the response data")

        self.assertIn("member_ids", club_in_response)
        member_ids = [str(m.id) for m in self.club.members.all()]
        self.assertEqual(len(club_in_response["member_ids"]), len(member_ids))
        for member_id in club_in_response["member_ids"]:
            self.assertIn(str(member_id), member_ids)

    def test_create_club_returns_detail_serializer(self):
        """Test that creating a club returns data from ClubDetailSerializer"""
        unique_name = f"Detail Club {uuid.uuid4()}"

        data = {
            "name": unique_name,
            "type": str(self.club_type.id),
            "privacy": Club.PRIVACY_OPEN,
            "join_permissions": Club.JOIN_PERMISSIONS_OPEN,
        }

        response = self.client.post(self.create_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        response_data = response.json()

        self.assertIn("type", response_data)
        self.assertIsInstance(response_data["type"], dict)
        self.assertEqual(response_data["type"]["name"], self.club_type.name)

        self.assertIn("manager", response_data)
        self.assertIsInstance(response_data["manager"], dict)
        self.assertEqual(response_data["manager"]["username"], self.user.username)

        self.assertIn("members_count", response_data)
        self.assertIn("created_at", response_data)
        self.assertIn("updated_at", response_data)


class ClubCreationTest(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            email="<EMAIL>", password="password123", username="manager"
        )
        self.client.force_authenticate(user=self.user)
        self.club_type = ClubType.objects.create(name="Test Club Type")
        self.create_url = reverse("clubs:club-create")

        self.temp_image = self._create_test_image()

    def _create_test_image(self):
        """Helper method to create a test image for testing uploads"""
        with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as f:
            image = Image.new("RGB", (100, 100), color="blue")
            image.save(f, "PNG")
        return f.name

    def tearDown(self):
        if hasattr(self, "temp_image") and os.path.exists(self.temp_image):
            os.unlink(self.temp_image)

    @patch("apps.clubs.views.club_views.create.send_email_with_html")
    def test_create_club_with_member_emails(self, mock_send_email):
        data = {
            "name": "Test Club",
            "type": str(self.club_type.id),
            "privacy": Club.PRIVACY_OPEN,
            "join_permissions": Club.JOIN_PERMISSIONS_INVITE,
            "members": [
                {"email": "<EMAIL>"},
                {"email": "<EMAIL>"},
            ],
        }
        response = self.client.post(self.create_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        self.assertTrue(Club.objects.filter(name="Test Club").exists())

        self.assertEqual(ClubInvitation.objects.count(), 2)

        self.assertTrue(User.objects.filter(email="<EMAIL>").exists())
        self.assertTrue(User.objects.filter(email="<EMAIL>").exists())

        self.assertEqual(mock_send_email.call_count, 2)

    @patch("apps.clubs.views.club_views.create.send_email_with_html")
    def test_create_club_with_more_member_emails(self, mock_send_email):
        data = {
            "name": "More Emails Club",
            "type": str(self.club_type.id),
            "privacy": Club.PRIVACY_OPEN,
            "join_permissions": Club.JOIN_PERMISSIONS_INVITE,
            "members": [
                {"email": "<EMAIL>", "name": "Member Three"},
                {"email": "<EMAIL>", "name": "Member Four"},
            ],
        }
        response = self.client.post(self.create_url, data, format="json")

        if response.status_code != status.HTTP_201_CREATED:
            print(f"Response status: {response.status_code}")
            print(f"Response content: {response.content.decode()}")
            print(f"Data sent: {data}")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        self.assertTrue(Club.objects.filter(name="More Emails Club").exists())

        invitations = ClubInvitation.objects.all()
        self.assertEqual(invitations.count(), 2)

        member3 = User.objects.get(email="<EMAIL>")
        member3_invitation = ClubInvitation.objects.get(user=member3)
        self.assertEqual(member3_invitation.fullname, "Member Three")

        member4 = User.objects.get(email="<EMAIL>")
        member4_invitation = ClubInvitation.objects.get(user=member4)
        self.assertEqual(member4_invitation.fullname, "Member Four")

        self.assertEqual(mock_send_email.call_count, 2)

    @patch("apps.clubs.views.club_management.invite.send_email_with_html")
    @patch("apps.clubs.views.club_views.create.send_email_with_html")
    def test_create_club_with_icon_and_members(
        self, mock_create_email, mock_invite_email
    ):
        """Test creating a club with an icon and then adding members"""

        with open(self.temp_image, "rb") as icon_file:
            icon_content = icon_file.read()

        icon = SimpleUploadedFile(
            name="club_icon.png", content=icon_content, content_type="image/png"
        )

        club_data = {
            "name": "Icon and Members Club",
            "type": str(self.club_type.id),
            "privacy": Club.PRIVACY_OPEN,
            "join_permissions": Club.JOIN_PERMISSIONS_INVITE,
            "icon": icon,
        }

        response = self.client.post(self.create_url, club_data, format="multipart")

        if response.status_code != status.HTTP_201_CREATED:
            print(f"Response status: {response.status_code}")
            print(f"Response content: {response.content.decode()}")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        created_club = Club.objects.get(name="Icon and Members Club")

        self.assertTrue(created_club.icon)
        response_data = response.json()
        self.assertIsNotNone(response_data.get("icon_url"))

        self.assertTrue(
            response_data.get("icon_url", "").startswith(settings.SITE_URL)
            or response_data.get("icon_url", "").startswith("http")
        )

        User.objects.filter(
            email__in=["<EMAIL>", "<EMAIL>"]
        ).delete()
        user5 = User.objects.create_user(
            email="<EMAIL>", password="password123", username="member5"
        )
        user6 = User.objects.create_user(
            email="<EMAIL>", password="password123", username="member6"
        )

        invite_url = reverse("clubs:invite-members", args=[created_club.id])
        invite_data = {"emails": ["<EMAIL>", "<EMAIL>"]}

        invite_response = self.client.post(invite_url, invite_data, format="json")

        if invite_response.status_code != status.HTTP_200_OK:
            print(f"Invite response status: {invite_response.status_code}")
            print(f"Invite response content: {invite_response.content.decode()}")

        self.assertEqual(invite_response.status_code, status.HTTP_200_OK)

        self.assertEqual(ClubInvitation.objects.filter(club=created_club).count(), 2)

        created_club.members.add(user5, user6)
        created_club.refresh_from_db()

        self.assertEqual(created_club.members.count(), 2)
        for user in [user5, user6]:
            self.assertIn(user, created_club.members.all())

        self.assertEqual(mock_create_email.call_count, 0)
        self.assertEqual(mock_invite_email.call_count, 2)

    @patch("apps.clubs.views.club_views.create.send_email_with_html")
    def test_create_club_with_icon(self, mock_send_email):
        """Test creating a club with an icon image but without members"""
        with open(self.temp_image, "rb") as icon_file:
            icon_content = icon_file.read()

        icon = SimpleUploadedFile(
            name="club_icon_test.png", content=icon_content, content_type="image/png"
        )

        data = {
            "name": "Simple Icon Club",
            "type": str(self.club_type.id),
            "privacy": Club.PRIVACY_OPEN,
            "join_permissions": Club.JOIN_PERMISSIONS_OPEN,
            "icon": icon,
        }

        response = self.client.post(self.create_url, data, format="multipart")

        if response.status_code != status.HTTP_201_CREATED:
            print(f"Response status: {response.status_code}")
            print(f"Response content: {response.content.decode()}")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        created_club = Club.objects.get(name="Simple Icon Club")

        self.assertTrue(created_club.icon)
        response_data = response.json()
        self.assertIsNotNone(response_data.get("icon_url"))

        self.assertTrue(
            response_data.get("icon_url", "").startswith(settings.SITE_URL)
            or response_data.get("icon_url", "").startswith("http")
        )

        print(f"Response data: {response_data}")

        self.assertTrue("icon" in response_data or "icon_url" in response_data)


class ClubDetailSerializerTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            email="<EMAIL>", password="password123", username="testuser"
        )
        self.club_type = ClubType.objects.create(name="Test Type")
        self.club = Club.objects.create(
            name="Test Club",
            type=self.club_type,
            manager=self.user,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_OPEN,
        )
        self.member = User.objects.create_user(
            email="<EMAIL>", password="password123", username="memberuser"
        )
        self.club.members.add(self.member)

    def test_club_detail_serializer(self):
        """Test that ClubDetailSerializer returns all expected fields in the correct format"""
        serializer = ClubDetailSerializer(self.club)
        data = serializer.data

        self.assertEqual(data["name"], self.club.name)
        self.assertEqual(data["privacy"], self.club.privacy)
        self.assertEqual(data["join_permissions"], self.club.join_permissions)

        self.assertEqual(data["type"]["id"], str(self.club_type.id))
        self.assertEqual(data["type"]["name"], self.club_type.name)

        self.assertEqual(data["manager"]["id"], str(self.user.id))
        self.assertEqual(data["manager"]["username"], self.user.username)
        self.assertEqual(data["manager"]["email"], self.user.email)

        self.assertEqual(data["members_count"], 1)
        self.assertEqual(len(data["member_ids"]), 1)
        self.assertEqual(str(data["member_ids"][0]), str(self.member.id))

        self.assertEqual(len(data["members"]), 1)
        self.assertEqual(data["members"][0]["id"], str(self.member.id))
        self.assertEqual(data["members"][0]["username"], self.member.username)
        self.assertEqual(data["members"][0]["email"], self.member.email)

        self.assertIn("created_at", data)
        self.assertIn("updated_at", data)

        print("ClubDetailSerializer output:")
        for key, value in data.items():
            print(f"{key}: {value}")
