from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient

from apps.accounts.user.models import User
from apps.clubs.models import Club, ClubType, ClubInvitation


class ClubMembersConsistencyTestCase(TestCase):
    """Test case to verify consistency of members between ClubMembersAPIView and MyClubsAPIView"""

    def setUp(self):
        """Set up test data"""

        self.manager = User.objects.create_user(
            username="manager_user",
            email="<EMAIL>",
            password="testpassword",
            fullname="Manager User",
        )

        self.club_type = ClubType.objects.create(
            name="Test Club Type",
            created_by=self.manager,
            visibility=ClubType.VISIBILITY_PUBLIC,
        )

        self.club = Club.objects.create(
            name="Test Club",
            type=self.club_type,
            manager=self.manager,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_INVITE,
        )

        self.members = []
        for i in range(5):
            user = User.objects.create_user(
                username=f"member_user_{i}",
                email=f"member{i}@example.com",
                password="testpassword",
                fullname=f"Member User {i}",
            )
            self.members.append(user)

            invitation = ClubInvitation.objects.create(
                user=user,
                sender=self.manager,
                club=self.club,
                status=ClubInvitation.STATUS_ACCEPTED,
            )

            self.club.members.add(user)

        self.pending_user = User.objects.create_user(
            username="pending_user",
            email="<EMAIL>",
            password="testpassword",
            fullname="Pending User",
        )

        ClubInvitation.objects.create(
            user=self.pending_user,
            sender=self.manager,
            club=self.club,
            status=ClubInvitation.STATUS_PENDING,
        )

        self.client = APIClient()
        self.client.force_authenticate(user=self.manager)

    def test_club_members_consistency(self):
        """Test that members are consistent between ClubMembersAPIView and MyClubsAPIView"""

        members_url = reverse("clubs:club-members", kwargs={"club_id": self.club.id})
        members_response = self.client.get(members_url)
        self.assertEqual(members_response.status_code, status.HTTP_200_OK)

        my_clubs_url = reverse("clubs:my-clubs")
        my_clubs_response = self.client.get(my_clubs_url)
        self.assertEqual(my_clubs_response.status_code, status.HTTP_200_OK)

        test_club_data = None
        for club in my_clubs_response.data["results"]:
            if club["id"] == str(self.club.id):
                test_club_data = club
                break

        self.assertIsNotNone(
            test_club_data, "Test club not found in MyClubsAPIView response"
        )

        club_members_view_ids = set(
            item["id"] for item in members_response.data["results"]
        )
        my_clubs_view_ids = set(member["id"] for member in test_club_data["members"])

        self.assertEqual(
            club_members_view_ids,
            my_clubs_view_ids,
            f"Member IDs don't match. ClubMembersAPIView: {club_members_view_ids}, MyClubsAPIView: {my_clubs_view_ids}",
        )

    def test_accepted_members_only(self):
        """Test that only accepted invitations are included in both views"""

        members_url = reverse("clubs:club-members", kwargs={"club_id": self.club.id})
        members_response = self.client.get(f"{members_url}?invitation_status=accepted")
        self.assertEqual(members_response.status_code, status.HTTP_200_OK)

        my_clubs_url = reverse("clubs:my-clubs")
        my_clubs_response = self.client.get(my_clubs_url)
        self.assertEqual(my_clubs_response.status_code, status.HTTP_200_OK)

        test_club_data = None
        for club in my_clubs_response.data["results"]:
            if club["id"] == str(self.club.id):
                test_club_data = club
                break

        self.assertIsNotNone(
            test_club_data, "Test club not found in MyClubsAPIView response"
        )

        club_members_view_ids = set(
            item["id"] for item in members_response.data["results"]
        )
        my_clubs_view_ids = set(member["id"] for member in test_club_data["members"])

        pending_user_id = str(self.pending_user.id)
        self.assertNotIn(
            pending_user_id,
            club_members_view_ids,
            "ClubMembersAPIView should not include pending user",
        )
        self.assertNotIn(
            pending_user_id,
            my_clubs_view_ids,
            "MyClubsAPIView should not include pending user",
        )

        self.assertEqual(
            club_members_view_ids,
            my_clubs_view_ids,
            f"Member IDs don't match. ClubMembersAPIView: {club_members_view_ids}, MyClubsAPIView: {my_clubs_view_ids}",
        )

    def test_member_count_consistency(self):
        """Test that member counts are consistent between both views"""

        members_url = reverse("clubs:club-members", kwargs={"club_id": self.club.id})
        members_response = self.client.get(members_url)
        self.assertEqual(members_response.status_code, status.HTTP_200_OK)

        my_clubs_url = reverse("clubs:my-clubs")
        my_clubs_response = self.client.get(my_clubs_url)
        self.assertEqual(my_clubs_response.status_code, status.HTTP_200_OK)

        test_club_data = None
        for club in my_clubs_response.data["results"]:
            if club["id"] == str(self.club.id):
                test_club_data = club
                break

        self.assertIsNotNone(
            test_club_data, "Test club not found in MyClubsAPIView response"
        )

        club_members_count = members_response.data["meta"]["count"]
        my_clubs_members_count = len(test_club_data["members"])

        self.assertEqual(
            club_members_count,
            my_clubs_members_count,
            f"Member counts don't match. ClubMembersAPIView: {club_members_count}, MyClubsAPIView: {my_clubs_members_count}",
        )
