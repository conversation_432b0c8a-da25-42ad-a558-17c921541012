from django.test import TestCase
from django.utils.translation import activate
from rest_framework.exceptions import ValidationError
from apps.clubs.models import Club, ClubType, ClubInvitation
from apps.clubs.serializers.club import ClubSerializer
from apps.clubs.serializers.invite import ClubInviteSerializer
from apps.clubs.serializers.remove_member import RemoveMemberSerializer
from django.contrib.auth import get_user_model
import uuid
from django.urls import reverse
from rest_framework.test import APIClient, APITestCase
from apps.clubs.serializers.club_serializers.detail import ClubDetailSerializer
from apps.clubs.serializers.members import ClubMembersSerializer
from unittest.mock import patch
from django.utils import timezone
from datetime import timedelta

User = get_user_model()


class ClubSerializerTest(TestCase):
    def setUp(self):
        self.club_type = ClubType.objects.create(name="Test Type")
        self.manager = User.objects.create_user(
            email="<EMAIL>", password="password123", username="manager"
        )
        self.club = Club.objects.create(
            name="Test Club",
            type=self.club_type,
            manager=self.manager,
        )
        self.user1 = User.objects.create_user(
            email="<EMAIL>", password="password123", username="user1"
        )
        self.user2 = User.objects.create_user(
            email="<EMAIL>", password="password123", username="user2"
        )

    def test_club_serializer(self):
        serializer = ClubSerializer(self.club)
        data = serializer.data

        self.assertEqual(data["name"], "Test Club")
        self.assertEqual(data["type"], self.club_type.id)
        self.assertEqual(data["manager"], self.manager.id)


class ClubInviteSerializerTest(TestCase):
    def setUp(self):
        self.user1 = User.objects.create_user(
            email="<EMAIL>", password="password123", username="user1"
        )
        self.user2 = User.objects.create_user(
            email="<EMAIL>", password="password123", username="user2"
        )

    def test_club_invite_serializer_valid(self):
        serializer = ClubInviteSerializer(
            data={"emails": [self.user1.email, self.user2.email]}
        )
        self.assertTrue(serializer.is_valid())
        self.assertEqual(
            serializer.validated_data["emails"], [self.user1.email, self.user2.email]
        )

    def test_club_invite_serializer_invalid_email(self):
        invalid_email = "invalid-email"
        serializer = ClubInviteSerializer(data={"emails": [invalid_email]})
        self.assertFalse(serializer.is_valid())
        self.assertIn("emails", serializer.errors)


class RemoveMemberSerializerTests(TestCase):
    def setUp(self):
        activate("en")
        self.manager = User.objects.create_user(
            username="manager", password="testpass123", email="<EMAIL>"
        )
        self.member = User.objects.create_user(
            username="member", password="testpass123", email="<EMAIL>"
        )
        self.non_member = User.objects.create_user(
            username="nonmember", password="testpass123", email="<EMAIL>"
        )

        self.club_type = ClubType.objects.create(name="Test Type")
        self.club = Club.objects.create(
            name="Test Club", manager=self.manager, type=self.club_type
        )
        self.club.members.add(self.manager, self.member)

    def test_valid_member_removal(self):
        context = {
            "request": type("Request", (), {"user": self.manager})(),
            "club_id": str(self.club.id),
        }
        data = {"member_id": str(self.member.id)}
        serializer = RemoveMemberSerializer(data=data, context=context)

        self.assertTrue(serializer.is_valid())

    def test_invalid_member_not_in_club(self):
        context = {
            "request": type("Request", (), {"user": self.manager})(),
            "club_id": str(self.club.id),
        }
        data = {"member_id": str(self.non_member.id)}
        serializer = RemoveMemberSerializer(data=data, context=context)

        self.assertFalse(serializer.is_valid())
        self.assertIn("Member not found in the club", str(serializer.errors))

    def test_non_manager_cannot_remove(self):
        context = {
            "request": type("Request", (), {"user": self.member})(),
            "club_id": str(self.club.id),
        }
        data = {"member_id": str(self.member.id)}
        serializer = RemoveMemberSerializer(data=data, context=context)

        self.assertFalse(serializer.is_valid())
        self.assertIn("Only the manager can remove members", str(serializer.errors))


class ClubDetailSerializerTest(TestCase):
    def setUp(self):
        self.club_type = ClubType.objects.create(name="Test Club Type")
        self.manager = User.objects.create_user(
            username="manager", email="<EMAIL>", password="password123"
        )
        self.member1 = User.objects.create_user(
            username="member1", email="<EMAIL>", password="password123"
        )
        self.member2 = User.objects.create_user(
            username="member2", email="<EMAIL>", password="password123"
        )

        self.club = Club.objects.create(
            name="Test Club",
            type=self.club_type,
            manager=self.manager,
            privacy=Club.PRIVACY_OPEN,
        )
        self.club.members.add(self.member1, self.member2)

    def test_serialize_club(self):
        serializer = ClubDetailSerializer(self.club)
        data = serializer.data

        self.assertEqual(data["name"], self.club.name)
        self.assertEqual(data["type"]["name"], self.club_type.name)
        self.assertEqual(data["manager"]["id"], str(self.manager.id))
        self.assertEqual(data["members_count"], 2)


class ClubMembersSerializerTest(TestCase):
    def setUp(self):
        self.club_type = ClubType.objects.create(name="Test Club Type")
        self.manager = User.objects.create_user(
            username="manager", email="<EMAIL>", password="password123"
        )
        self.member1 = User.objects.create_user(
            username="member1", email="<EMAIL>", password="password123"
        )
        self.member2 = User.objects.create_user(
            username="member2", email="<EMAIL>", password="password123"
        )

        self.club = Club.objects.create(
            name="Test Club",
            type=self.club_type,
            manager=self.manager,
            privacy=Club.PRIVACY_OPEN,
        )

        self.invitation1 = ClubInvitation.objects.create(
            user=self.member1, club=self.club, status=ClubInvitation.STATUS_ACCEPTED
        )

        self.invitation2 = ClubInvitation.objects.create(
            user=self.member2, club=self.club, status=ClubInvitation.STATUS_PENDING
        )

        self.club.members.add(self.member1)

    @patch("apps.clubs.models.Club.get_member_activity")
    def test_serializer_fields(self, mock_get_member_activity):
        mock_get_member_activity.return_value = {
            "total_possible_updates": 10,
            "actual_updates": 5,
            "activity_percentage": 50.0,
        }

        context = {"club": self.club}
        serializer = ClubMembersSerializer(self.member1, context=context)
        data = serializer.data

        self.assertEqual(data["id"], str(self.member1.id))
        self.assertEqual(data["fullname"], self.member1.fullname)
        self.assertEqual(data["email"], self.member1.email)

        self.assertEqual(data["status"], ClubInvitation.STATUS_ACCEPTED)
        self.assertEqual(data["missions_count"], self.member1.weekly_missions.count())
        self.assertEqual(data["activity_percentage"], 50.0)

        self.assertIsNotNone(data["invitation_date"])

    def test_status_field(self):
        context = {"club": self.club}

        serializer1 = ClubMembersSerializer(self.member1, context=context)
        self.assertEqual(serializer1.data["status"], ClubInvitation.STATUS_ACCEPTED)

        serializer2 = ClubMembersSerializer(self.member2, context=context)
        self.assertEqual(serializer2.data["status"], ClubInvitation.STATUS_PENDING)

        new_user = User.objects.create_user(
            username="new_user", email="<EMAIL>", password="password123"
        )
        serializer3 = ClubMembersSerializer(new_user, context=context)
        self.assertIsNone(serializer3.data["status"])

    @patch("apps.clubs.models.Club.get_member_activity")
    def test_activity_percentage_field(self, mock_get_member_activity):
        mock_get_member_activity.return_value = {"activity_percentage": 75.5}

        context = {"club": self.club}
        serializer = ClubMembersSerializer(self.member1, context=context)
        self.assertEqual(serializer.data["activity_percentage"], 75.5)

        mock_get_member_activity.return_value = {"activity_percentage": 0}

        serializer = ClubMembersSerializer(self.member2, context=context)
        self.assertEqual(serializer.data["activity_percentage"], 0)

    def test_invitation_date_field(self):
        original_created = self.invitation1.created
        self.invitation1.updated = original_created + timedelta(days=2)
        self.invitation1.save()

        context = {"club": self.club}

        serializer1 = ClubMembersSerializer(self.member1, context=context)
        self.assertEqual(serializer1.data["invitation_date"], self.invitation1.updated)

        serializer2 = ClubMembersSerializer(self.member2, context=context)
        self.assertEqual(serializer2.data["invitation_date"], self.invitation2.created)
