import uuid
from unittest.mock import patch
from rest_framework.test import APIClient, APITestCase
from rest_framework import status
from django.urls import reverse
from django.contrib.auth import get_user_model
from apps.clubs.models import ClubType, Club, ClubInvitation

User = get_user_model()


class ClubWithMembersTest(APITestCase):
    def setUp(self):
        self.client = APIClient()

        self.manager = User.objects.create_user(
            email="<EMAIL>", password="password123", username="manager"
        )
        self.member1 = User.objects.create_user(
            email="<EMAIL>", password="password123", username="member1"
        )
        self.member2 = User.objects.create_user(
            email="<EMAIL>", password="password123", username="member2"
        )

        self.client.force_authenticate(user=self.manager)

        self.club_type = ClubType.objects.create(name="Test Club Type")

        self.create_url = reverse("clubs:club-create")
        self.list_url = reverse("clubs:club-list")

    @patch("apps.clubs.views.club_views.create.send_email_with_html")
    def test_create_club_with_existing_users_as_members(self, mock_send_email):
        """Test creating a club with existing users as members"""

        club_name = f"Test Club {uuid.uuid4()}"
        data = {
            "name": club_name,
            "type": str(self.club_type.id),
            "privacy": Club.PRIVACY_OPEN,
            "join_permissions": Club.JOIN_PERMISSIONS_OPEN,
            "members": [
                {"email": self.member1.email, "name": "Member One"},
                {"email": self.member2.email, "name": "Member Two"},
            ],
        }

        response = self.client.post(self.create_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        club = Club.objects.get(name=club_name)
        self.assertEqual(club.manager, self.manager)

        invitations = ClubInvitation.objects.filter(club=club)
        self.assertEqual(invitations.count(), 2)

        invited_users = [invitation.user for invitation in invitations]
        self.assertIn(self.member1, invited_users)
        self.assertIn(self.member2, invited_users)

        self.assertEqual(club.members.count(), 2)
        club_members = club.members.all()
        self.assertIn(self.member1, club_members)
        self.assertIn(self.member2, club_members)

        response_data = response.json()
        self.assertIn("member_ids", response_data)
        self.assertEqual(len(response_data["member_ids"]), 2)
        member_ids = [str(m.id) for m in club_members]
        for member_id in response_data["member_ids"]:
            self.assertIn(member_id, member_ids)

    @patch("apps.clubs.views.club_views.create.send_email_with_html")
    def test_create_club_with_new_users_as_members(self, mock_send_email):
        """Test creating a club with non-existing users as members"""

        club_name = f"Test Club New Users {uuid.uuid4()}"
        new_email1 = f"new1_{uuid.uuid4()}@example.com"
        new_email2 = f"new2_{uuid.uuid4()}@example.com"

        data = {
            "name": club_name,
            "type": str(self.club_type.id),
            "privacy": Club.PRIVACY_OPEN,
            "join_permissions": Club.JOIN_PERMISSIONS_OPEN,
            "members": [
                {"email": new_email1, "name": "New User One"},
                {"email": new_email2, "name": "New User Two"},
            ],
        }

        response = self.client.post(self.create_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        club = Club.objects.get(name=club_name)

        new_user1 = User.objects.get(email=new_email1)
        new_user2 = User.objects.get(email=new_email2)

        invitations = ClubInvitation.objects.filter(club=club)
        self.assertEqual(invitations.count(), 2)

        invited_users = [invitation.user for invitation in invitations]
        self.assertIn(new_user1, invited_users)
        self.assertIn(new_user2, invited_users)

        self.assertEqual(club.members.count(), 2)
        club_members = club.members.all()
        self.assertIn(new_user1, club_members)
        self.assertIn(new_user2, club_members)

        response_data = response.json()
        self.assertIn("member_ids", response_data)
        self.assertEqual(len(response_data["member_ids"]), 2)
        member_ids = [str(m.id) for m in club_members]
        for member_id in response_data["member_ids"]:
            self.assertIn(member_id, member_ids)

    @patch("apps.clubs.views.club_views.create.send_email_with_html")
    def test_create_club_with_mixed_users_as_members(self, mock_send_email):
        """Test creating a club with both existing and new users as members"""

        club_name = f"Test Club Mixed Users {uuid.uuid4()}"
        new_email = f"new_{uuid.uuid4()}@example.com"

        data = {
            "name": club_name,
            "type": str(self.club_type.id),
            "privacy": Club.PRIVACY_OPEN,
            "join_permissions": Club.JOIN_PERMISSIONS_OPEN,
            "members": [
                {"email": self.member1.email, "name": "Existing Member"},
                {"email": new_email, "name": "New User"},
            ],
        }

        response = self.client.post(self.create_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        club = Club.objects.get(name=club_name)

        new_user = User.objects.get(email=new_email)

        invitations = ClubInvitation.objects.filter(club=club)
        self.assertEqual(invitations.count(), 2)

        invited_users = [invitation.user for invitation in invitations]
        self.assertIn(self.member1, invited_users)
        self.assertIn(new_user, invited_users)

        self.assertEqual(club.members.count(), 2)
        club_members = club.members.all()
        self.assertIn(self.member1, club_members)
        self.assertIn(new_user, club_members)

        response_data = response.json()
        self.assertIn("member_ids", response_data)
        self.assertEqual(len(response_data["member_ids"]), 2)
        member_ids = [str(m.id) for m in club_members]
        for member_id in response_data["member_ids"]:
            self.assertIn(member_id, member_ids)

    def test_list_clubs_includes_member_ids(self):
        """Test that listing clubs includes the correct member_ids"""

        club = Club.objects.create(
            name=f"Member Test Club {uuid.uuid4()}",
            type=self.club_type,
            manager=self.manager,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_OPEN,
        )
        club.members.add(self.member1, self.member2)

        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        club_in_response = None
        if isinstance(response.data, list):
            for c in response.data:
                if str(c.get("id")) == str(club.id):
                    club_in_response = c
                    break
        elif isinstance(response.data, dict) and "results" in response.data:
            for c in response.data["results"]:
                if str(c.get("id")) == str(club.id):
                    club_in_response = c
                    break

        self.assertIsNotNone(club_in_response, "Club should be in the response data")

        self.assertIn("member_ids", club_in_response)
        member_ids = [str(m.id) for m in club.members.all()]
        self.assertEqual(len(club_in_response["member_ids"]), 2)
        for member_id in club_in_response["member_ids"]:
            self.assertIn(str(member_id), member_ids)
