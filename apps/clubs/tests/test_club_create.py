from django .test import TestCase 
from rest_framework .test import APIClient 
from rest_framework import status 
from django .urls import reverse 
from django .contrib .auth import get_user_model 
from django .conf import settings 
from unittest .mock import patch 
import json 

from apps .clubs .models import Club ,ClubType 
from apps .clubs .serializers .club_serializers .detail import ClubDetailSerializer 

User =get_user_model ()


class ClubCreateAPITest (TestCase ):
    def setUp (self ):
        self .client =APIClient ()

        self .client .defaults ["HTTP_ACCEPT_LANGUAGE"]="en"

        self .user =User .objects .create_user (
        email ="<EMAIL>",password ="password123",username ="testuser"
        )
        self .member_email ="<EMAIL>"
        self .member_name ="Test Member"

        self .club_type =ClubType .objects .create (name ="Test Type")

        self .create_url =reverse ("clubs:club-create")

        self .valid_payload ={
        "name":"New Test Club",
        "type":str (self .club_type .id ),
        "privacy":Club .PRIVACY_OPEN ,
        "join_permissions":Club .JOIN_PERMISSIONS_OPEN ,
        "members":[{"email":self .member_email ,"name":self .member_name }],
        }

    def test_create_club_returns_populated_data (self ):
        """Test that creating a club returns the club with populated data"""
        self .client .force_authenticate (user =self .user )

        response =self .client .post (self .create_url ,self .valid_payload ,format ="json")

        if response .status_code !=status .HTTP_201_CREATED :
            print (f"Response status: {response.status_code}")
            print (f"Response content: {response.content.decode()}")
            print (f"Data sent: {self.valid_payload}")

        self .assertEqual (response .status_code ,status .HTTP_201_CREATED )

        club =Club .objects .get (name ="New Test Club")
        self .assertEqual (club .manager ,self .user )
        self .assertEqual (club .type ,self .club_type )

        member_user =User .objects .get (email =self .member_email )
        self .assertIn (member_user ,club .members .all ())

        response_data =response .json ()

        self .assertEqual (response_data ["name"],"New Test Club")
        self .assertEqual (response_data ["privacy"],Club .PRIVACY_OPEN )
        self .assertEqual (response_data ["join_permissions"],Club .JOIN_PERMISSIONS_OPEN )

        self .assertIn ("manager",response_data )
        self .assertIsInstance (response_data ["manager"],dict )
        self .assertEqual (response_data ["manager"]["id"],str (self .user .id ))
        self .assertEqual (response_data ["manager"]["username"],self .user .username )

        self .assertIn ("type",response_data )
        self .assertIsInstance (response_data ["type"],dict )
        self .assertEqual (response_data ["type"]["id"],str (self .club_type .id ))
        self .assertEqual (response_data ["type"]["name"],self .club_type .name )

        self .assertIn ("members",response_data )
        self .assertIsInstance (response_data ["members"],list )
        self .assertEqual (len (response_data ["members"]),1 )
        self .assertEqual (response_data ["members"][0 ]["email"],self .member_email )

        expected_data =ClubDetailSerializer (club ).data 
        self .assertEqual (set (response_data .keys ()),set (expected_data .keys ()))

    def test_create_club_without_members (self ):
        """Test that creating a club without members works and returns populated data"""
        self .client .force_authenticate (user =self .user )

        payload_without_members ={
        "name":"Club Without Members",
        "type":str (self .club_type .id ),
        "privacy":Club .PRIVACY_CLOSED ,
        "join_permissions":Club .JOIN_PERMISSIONS_INVITE ,
        }

        response =self .client .post (
        self .create_url ,payload_without_members ,format ="json"
        )

        self .assertEqual (response .status_code ,status .HTTP_201_CREATED )

        club =Club .objects .get (name ="Club Without Members")
        self .assertEqual (club .manager ,self .user )

        response_data =response .json ()

        self .assertIn ("members",response_data )
        self .assertEqual (len (response_data ["members"]),0 )

        self .assertEqual (response_data ["name"],"Club Without Members")
        self .assertEqual (response_data ["privacy"],Club .PRIVACY_CLOSED )
        self .assertEqual (
        response_data ["join_permissions"],Club .JOIN_PERMISSIONS_INVITE 
        )
        self .assertIn ("created_at",response_data )
        self .assertIn ("updated_at",response_data )

    def test_create_club_unauthorized (self ):
        """Test that creating a club without authentication fails"""
        response =self .client .post (self .create_url ,self .valid_payload ,format ="json")

        self .assertEqual (response .status_code ,status .HTTP_401_UNAUTHORIZED )

    def test_create_club_with_invalid_data (self ):
        """Test that creating a club with invalid data returns appropriate errors"""
        self .client .force_authenticate (user =self .user )

        invalid_payload ={
        "name":"",
        "type":str (self .club_type .id ),
        "privacy":"invalid_privacy",
        "join_permissions":Club .JOIN_PERMISSIONS_OPEN ,
        }

        response =self .client .post (self .create_url ,invalid_payload ,format ="json")

        print (f"Invalid data response: {response.content.decode()}")

        self .assertEqual (response .status_code ,status .HTTP_400_BAD_REQUEST )

        response_data =response .json ()
        self .assertIn ("error",response_data )

        self .assertTrue (response_data ["error"])

        invalid_payload_2 ={
        "type":str (self .club_type .id ),
        "privacy":Club .PRIVACY_OPEN ,
        }

        response =self .client .post (self .create_url ,invalid_payload_2 ,format ="json")

        self .assertEqual (response .status_code ,status .HTTP_400_BAD_REQUEST )

    @patch (
    "apps.clubs.views.club_views.create.settings.CLUB_MANAGER_CAN_CREATE_X_CLUBS",
    "2",
    )
    def test_club_creation_limit (self ):
        """Test that users cannot create more clubs than the limit allows"""
        self .client .force_authenticate (user =self .user )

        payload1 ={
        "name":"First Club",
        "type":str (self .club_type .id ),
        "privacy":Club .PRIVACY_OPEN ,
        "join_permissions":Club .JOIN_PERMISSIONS_OPEN ,
        }

        response1 =self .client .post (self .create_url ,payload1 ,format ="json")
        self .assertEqual (response1 .status_code ,status .HTTP_201_CREATED )

        payload2 ={
        "name":"Second Club",
        "type":str (self .club_type .id ),
        "privacy":Club .PRIVACY_OPEN ,
        "join_permissions":Club .JOIN_PERMISSIONS_OPEN ,
        }

        response2 =self .client .post (self .create_url ,payload2 ,format ="json")
        self .assertEqual (response2 .status_code ,status .HTTP_201_CREATED )

        payload3 ={
        "name":"Third Club",
        "type":str (self .club_type .id ),
        "privacy":Club .PRIVACY_OPEN ,
        "join_permissions":Club .JOIN_PERMISSIONS_OPEN ,
        }

        response3 =self .client .post (self .create_url ,payload3 ,format ="json")

        print (f"Limit reached response: {response3.content.decode()}")

        self .assertEqual (response3 .status_code ,status .HTTP_403_FORBIDDEN )

        response_data =response3 .json ()
        self .assertIn ("error",response_data )

        error_message =response_data ["error"].lower ()
        self .assertTrue (
        ("limit"in error_message )or ("حد"in response_data ["error"]),
        f"Expected limit-related error message, got: {response_data['error']}"
        )

        user_clubs =Club .objects .filter (manager =self .user ).count ()
        self .assertEqual (user_clubs ,2 )


class ClubCreateViewTests (TestCase ):
    def setUp (self ):

        self .user =User .objects .create_user (
        username ="testuser",
        email ="<EMAIL>",
        password ="testpassword123",
        fullname ="Test User",
        )

        self .member1 =User .objects .create_user (
        username ="member1",
        email ="<EMAIL>",
        password ="testpassword123",
        fullname ="Member One",
        )

        self .member2 =User .objects .create_user (
        username ="member2",
        email ="<EMAIL>",
        password ="testpassword123",
        fullname ="Member Two",
        )

        self .club_type =ClubType .objects .create (
        name ="Test Club Type",visibility ="public"
        )

        self .client =APIClient ()
        self .client .force_authenticate (user =self .user )

        self .url =reverse ("clubs:club-create")

    def test_create_club_with_list_of_user_ids (self ):
        """Test creating a club with a list of user IDs as members"""
        data ={
        "name":"Test Club with IDs",
        "type":str (self .club_type .id ),
        "privacy":"public",
        "join_permissions":"open",
        "members":[str (self .member1 .id ),str (self .member2 .id )],
        }

        response =self .client .post (self .url ,data ,format ="json")

        self .assertEqual (response .status_code ,status .HTTP_201_CREATED )

        self .assertTrue (Club .objects .filter (name ="Test Club with IDs").exists ())

        club =Club .objects .get (name ="Test Club with IDs")
        self .assertEqual (club .members .count (),2 )
        self .assertIn (self .member1 ,club .members .all ())
        self .assertIn (self .member2 ,club .members .all ())

    def test_create_club_with_string_of_user_ids (self ):
        """Test creating a club with a string containing a JSON list of user IDs as members"""
        data ={
        "name":"Test Club with String IDs",
        "type":str (self .club_type .id ),
        "privacy":"public",
        "join_permissions":"open",
        "members":json .dumps ([str (self .member1 .id ),str (self .member2 .id )]),
        }

        response =self .client .post (self .url ,data ,format ="json")

        self .assertEqual (response .status_code ,status .HTTP_201_CREATED )

        self .assertTrue (Club .objects .filter (name ="Test Club with String IDs").exists ())

        club =Club .objects .get (name ="Test Club with String IDs")
        self .assertEqual (club .members .count (),2 )
        self .assertIn (self .member1 ,club .members .all ())
        self .assertIn (self .member2 ,club .members .all ())

    def test_create_club_with_email_list (self ):
        """Test creating a club with the original format (list of email strings)"""
        data ={
        "name":"Test Club with Emails",
        "type":str (self .club_type .id ),
        "privacy":"public",
        "join_permissions":"open",
        "members":["<EMAIL>","<EMAIL>"],
        }

        response =self .client .post (self .url ,data ,format ="json")

        self .assertEqual (response .status_code ,status .HTTP_201_CREATED )

        self .assertTrue (Club .objects .filter (name ="Test Club with Emails").exists ())

        club =Club .objects .get (name ="Test Club with Emails")
        self .assertEqual (club .members .count (),2 )
        self .assertIn (self .member1 ,club .members .all ())
        self .assertIn (self .member2 ,club .members .all ())

    def test_create_club_with_invalid_members_format (self ):
        """Test creating a club with invalid members format"""
        data ={
        "name":"Test Club Invalid",
        "type":str (self .club_type .id ),
        "privacy":"public",
        "join_permissions":"open",
        "members":"invalid-format",
        }

        response =self .client .post (self .url ,data ,format ="json")

        self .assertEqual (response .status_code ,status .HTTP_400_BAD_REQUEST )

        self .assertFalse (Club .objects .filter (name ="Test Club Invalid").exists ())
