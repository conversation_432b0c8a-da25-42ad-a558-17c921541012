from django.test import TestCase
from rest_framework.test import APIClient
from rest_framework import status
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.conf import settings
from unittest.mock import patch, MagicMock
from django.db import transaction

from apps.clubs.models import Club, ClubType, ClubInvitation
from apps.accounts.user.models import UserToken

User = get_user_model()


class ImprovedClubInviteAPITest(TestCase):
    """
    Comprehensive test suite for improved club invitation functionality.
    Tests validation, security, and business logic improvements.
    """

    def setUp(self):
        """Set up test data for each test case."""
        self.client = APIClient()

        self.manager = User.objects.create_user(
            email="<EMAIL>",
            password="password123",
            username="manager",
            fullname="Club Manager",
        )

        self.existing_user = User.objects.create_user(
            email="<EMAIL>",
            password="password123",
            username="existing",
            fullname="Existing User",
        )

        self.member_user = User.objects.create_user(
            email="<EMAIL>",
            password="password123",
            username="member",
            fullname="Current Member",
        )

        self.club_type = ClubType.objects.create(name="Test Club Type")
        self.club = Club.objects.create(
            name="Test Club",
            type=self.club_type,
            manager=self.manager,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_INVITE,
        )

        self.club.members.add(self.member_user)

        ClubInvitation.objects.create(
            user=self.existing_user,
            club=self.club,
            email=self.existing_user.email,
            status=ClubInvitation.STATUS_PENDING,
            sender=self.manager,
        )

        self.invite_url = reverse("clubs:invite-members", args=[self.club.id])

    def test_prevent_duplicate_invitations_for_pending_users(self):
        """Test that users with pending invitations cannot be invited again."""
        self.client.force_authenticate(user=self.manager)

        payload = {"emails": ["<EMAIL>"]}
        response = self.client.post(self.invite_url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("errors", response.json())
        self.assertIn("pending_invitations", response.json()["errors"])

        pending_error = response.json()["errors"]["pending_invitations"]

        if isinstance(pending_error, list):
            pending_error = pending_error[0]
        self.assertIn("<EMAIL>", pending_error)

    def test_prevent_inviting_existing_members(self):
        """Test that existing club members cannot be invited."""
        self.client.force_authenticate(user=self.manager)

        payload = {"emails": ["<EMAIL>"]}
        response = self.client.post(self.invite_url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("errors", response.json())
        self.assertIn("existing_members", response.json()["errors"])

        existing_error = response.json()["errors"]["existing_members"]

        if isinstance(existing_error, list):
            existing_error = existing_error[0]
        self.assertIn("<EMAIL>", existing_error)

    def test_prevent_self_invitation(self):
        """Test that managers cannot invite themselves."""
        self.client.force_authenticate(user=self.manager)

        payload = {"emails": ["<EMAIL>"]}
        response = self.client.post(self.invite_url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("errors", response.json())
        self.assertIn("self_invitation", response.json()["errors"])

    @patch("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_successful_invitation_new_user(self, mock_send_email):
        """Test successful invitation of a new user."""
        self.client.force_authenticate(user=self.manager)

        payload = {
            "invitations": [{"email": "<EMAIL>", "fullname": "New User"}]
        }

        response = self.client.post(self.invite_url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertTrue(User.objects.filter(email="<EMAIL>").exists())
        new_user = User.objects.get(email="<EMAIL>")
        self.assertEqual(new_user.fullname, "New User")

        invitation = ClubInvitation.objects.get(user=new_user, club=self.club)
        self.assertEqual(invitation.status, ClubInvitation.STATUS_PENDING)
        self.assertEqual(invitation.sender, self.manager)
        self.assertEqual(invitation.fullname, "New User")

        self.assertFalse(self.club.members.filter(id=new_user.id).exists())

        self.assertEqual(mock_send_email.call_count, 1)

        response_data = response.json()
        self.assertIn("invited_members", response_data)
        self.assertIn("invitation_summary", response_data)
        self.assertEqual(response_data["invitation_summary"]["successful"], 1)
        self.assertEqual(response_data["invitation_summary"]["failed"], 0)

    @patch("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_successful_invitation_existing_user_no_conflicts(self, mock_send_email):
        """Test successful invitation of an existing user with no conflicts."""

        new_user = User.objects.create_user(
            email="<EMAIL>",
            username="available",
            fullname="Available User",
        )

        self.client.force_authenticate(user=self.manager)

        payload = {"emails": ["<EMAIL>"]}
        response = self.client.post(self.invite_url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        invitation = ClubInvitation.objects.get(user=new_user, club=self.club)
        self.assertEqual(invitation.status, ClubInvitation.STATUS_PENDING)
        self.assertEqual(invitation.sender, self.manager)

        self.assertFalse(self.club.members.filter(id=new_user.id).exists())

        self.assertEqual(mock_send_email.call_count, 1)

    def test_multiple_validation_errors(self):
        """Test that multiple validation errors are returned properly."""
        self.client.force_authenticate(user=self.manager)

        payload = {
            "emails": [
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            ]
        }

        response = self.client.post(self.invite_url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        errors = response.json()["errors"]

        self.assertIn("self_invitation", errors)
        self.assertIn("existing_members", errors)
        self.assertIn("pending_invitations", errors)

    def test_remove_duplicates_between_emails_and_invitations(self):
        """Test that duplicates between emails list and invitations are handled."""

        available_user = User.objects.create_user(
            email="<EMAIL>", username="available2"
        )

        self.client.force_authenticate(user=self.manager)

        payload = {
            "emails": ["<EMAIL>"],
            "invitations": [
                {"email": "<EMAIL>", "fullname": "Available User 2"}
            ],
        }

        with patch(
            "apps.clubs.views.club_management.invite.send_email_with_html"
        ) as mock_send_email:
            response = self.client.post(self.invite_url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        invitations = ClubInvitation.objects.filter(user=available_user, club=self.club)
        self.assertEqual(invitations.count(), 1)

        self.assertEqual(mock_send_email.call_count, 1)

    @patch("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_unique_username_generation(self, mock_send_email):
        """Test that unique usernames are generated for new users."""

        User.objects.create_user(email="<EMAIL>", username="testuser")

        self.client.force_authenticate(user=self.manager)

        payload = {"emails": ["<EMAIL>"]}
        response = self.client.post(self.invite_url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        new_user = User.objects.get(email="<EMAIL>")
        self.assertEqual(new_user.username, "testuser1")

    def test_non_manager_permission_denied(self):
        """Test that non-managers are denied permission to invite."""
        self.client.force_authenticate(user=self.existing_user)

        payload = {"emails": ["<EMAIL>"]}
        response = self.client.post(self.invite_url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertIn("error", response.json())

    def test_invalid_club_id(self):
        """Test invitation to non-existent club."""
        self.client.force_authenticate(user=self.manager)

        invalid_url = reverse(
            "clubs:invite-members", args=["00000000-0000-0000-0000-000000000000"]
        )
        payload = {"emails": ["<EMAIL>"]}
        response = self.client.post(invalid_url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_empty_payload_validation(self):
        """Test that empty payload returns validation error."""
        self.client.force_authenticate(user=self.manager)

        payload = {}
        response = self.client.post(self.invite_url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("errors", response.json())

    @patch("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_invitation_includes_sender_information(self, mock_send_email):
        """Test that invitations properly record sender information."""
        available_user = User.objects.create_user(
            email="<EMAIL>", username="testsender"
        )

        self.client.force_authenticate(user=self.manager)

        payload = {"emails": ["<EMAIL>"]}
        response = self.client.post(self.invite_url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        invitation = ClubInvitation.objects.get(user=available_user, club=self.club)
        self.assertEqual(invitation.sender, self.manager)
        self.assertEqual(invitation.email, "<EMAIL>")

    @patch("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_case_insensitive_email_handling(self, mock_send_email):
        """Test that email addresses are handled case-insensitively."""
        self.client.force_authenticate(user=self.manager)

        payload = {"emails": ["<EMAIL>"]}
        response = self.client.post(self.invite_url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        new_user = User.objects.get(email="<EMAIL>")
        invitation = ClubInvitation.objects.get(user=new_user, club=self.club)
        self.assertEqual(invitation.email, "<EMAIL>")

    @patch(
        "apps.clubs.views.club_management.invite.send_email_with_html",
        side_effect=Exception("Email failed"),
    )
    def test_email_failure_handling(self, mock_send_email):
        """Test proper handling when email sending fails."""
        self.client.force_authenticate(user=self.manager)

        payload = {"emails": ["<EMAIL>"]}
        response = self.client.post(self.invite_url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        response_data = response.json()
        self.assertEqual(response_data["invitation_summary"]["successful"], 0)
        self.assertEqual(response_data["invitation_summary"]["failed"], 1)

        self.assertFalse(
            ClubInvitation.objects.filter(
                email="<EMAIL>", club=self.club
            ).exists()
        )

    @patch("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_partial_success_scenario(self, mock_send_email):
        """Test scenario where some invitations succeed and others fail."""

        def side_effect(*args, **kwargs):
            if kwargs.get("to_email") == "<EMAIL>":
                raise Exception("Email failed")
            return True

        mock_send_email.side_effect = side_effect

        self.client.force_authenticate(user=self.manager)

        payload = {"emails": ["<EMAIL>", "<EMAIL>"]}
        response = self.client.post(self.invite_url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_207_MULTI_STATUS)

        response_data = response.json()
        self.assertEqual(response_data["invitation_summary"]["successful"], 1)
        self.assertEqual(response_data["invitation_summary"]["failed"], 1)

    def test_duplicate_emails_in_same_request(self):
        """Test handling of duplicate emails within the same request."""

        available_user = User.objects.create_user(
            email="<EMAIL>", username="duplicate"
        )

        self.client.force_authenticate(user=self.manager)

        payload = {"emails": ["<EMAIL>", "<EMAIL>"]}

        with patch(
            "apps.clubs.views.club_management.invite.send_email_with_html"
        ) as mock_send_email:
            response = self.client.post(self.invite_url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        invitations = ClubInvitation.objects.filter(user=available_user, club=self.club)
        self.assertEqual(invitations.count(), 1)

        self.assertEqual(mock_send_email.call_count, 1)

    @patch("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_transaction_rollback_on_failure(self, mock_send_email):
        """Test that database operations are rolled back on critical failures."""
        self.client.force_authenticate(user=self.manager)

        payload = {"emails": ["<EMAIL>"]}

        with patch.object(
            User.objects, "create_user", side_effect=Exception("DB Error")
        ):
            response = self.client.post(self.invite_url, payload, format="json")

        response_data = response.json()
        self.assertEqual(response_data["invitation_summary"]["failed"], 1)

        self.assertFalse(User.objects.filter(email="<EMAIL>").exists())
        self.assertFalse(
            ClubInvitation.objects.filter(email="<EMAIL>").exists()
        )

    def tearDown(self):
        """Clean up after each test."""

        User.objects.all().delete()
        Club.objects.all().delete()
        ClubType.objects.all().delete()
        ClubInvitation.objects.all().delete()
        UserToken.objects.all().delete()
