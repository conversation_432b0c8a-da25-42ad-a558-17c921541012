from django.test import TestCase
from django.utils import timezone
from django.contrib.auth import get_user_model
from datetime import timedelta
import uuid

from apps.clubs.models import Club, ClubType
from apps.clubs.filters.club_filters import ClubFilter

User = get_user_model()


class ClubFilterTests(TestCase):
    """Test suite for ClubFilter functionality"""

    def setUp(self):
        """Set up test data"""

        self.manager1 = User.objects.create_user(
            email="<EMAIL>",
            password="password123",
            username="manager1",
        )

        self.member1 = User.objects.create_user(
            email="<EMAIL>",
            password="password123",
            username="member1",
        )

        self.member2 = User.objects.create_user(
            email="<EMAIL>",
            password="password123",
            username="member2",
        )

        self.type1 = ClubType.objects.create(name="Sports Club")
        self.type2 = ClubType.objects.create(name="Book Club")

        self.today = timezone.now()
        self.yesterday = self.today - timedelta(days=1)
        self.last_week = self.today - timedelta(days=7)
        self.last_month = self.today - timedelta(days=30)

        self.club1 = Club.objects.create(
            name="Club Alpha",
            manager=self.manager1,
            type=self.type1,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_OPEN,
            created=self.today,
        )
        self.club1.members.add(self.member1)

        self.club2 = Club.objects.create(
            name="Beta Reading Club",
            manager=self.manager1,
            type=self.type2,
            privacy=Club.PRIVACY_CLOSED,
            join_permissions=Club.JOIN_PERMISSIONS_INVITE,
            created=self.yesterday,
        )
        self.club2.members.add(self.member1, self.member2)

        self.club3 = Club.objects.create(
            name="Gamma Sports",
            manager=self.manager1,
            type=self.type1,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_INVITE,
            created=self.last_week,
        )

        self.club4 = Club.objects.create(
            name="Delta Book Club",
            manager=self.manager1,
            type=self.type2,
            privacy=Club.PRIVACY_CLOSED,
            join_permissions=Club.JOIN_PERMISSIONS_INVITE,
            created=self.last_month,
        )

        self.queryset = Club.objects.all()

    def test_filter_by_name(self):
        """Test filtering by club name"""

        filtered = ClubFilter({"name": "Club Alpha"}, queryset=self.queryset).qs
        self.assertEqual(filtered.count(), 1)
        self.assertEqual(filtered.first(), self.club1)

        filtered = ClubFilter({"name": "alpha"}, queryset=self.queryset).qs
        self.assertEqual(filtered.count(), 1)
        self.assertEqual(filtered.first(), self.club1)

        filtered = ClubFilter({"name": "Club"}, queryset=self.queryset).qs
        self.assertEqual(filtered.count(), 3)
        self.assertIn(self.club1, filtered)
        self.assertIn(self.club2, filtered)
        self.assertIn(self.club4, filtered)

    def test_filter_by_privacy(self):
        """Test filtering by privacy setting"""

        filtered = ClubFilter({"privacy": Club.PRIVACY_OPEN}, queryset=self.queryset).qs
        self.assertEqual(filtered.count(), 2)
        self.assertIn(self.club1, filtered)
        self.assertIn(self.club3, filtered)

        filtered = ClubFilter(
            {"privacy": Club.PRIVACY_CLOSED}, queryset=self.queryset
        ).qs
        self.assertEqual(filtered.count(), 2)
        self.assertIn(self.club2, filtered)
        self.assertIn(self.club4, filtered)

        filtered = ClubFilter(
            {"privacy": Club.PRIVACY_CLOSED}, queryset=self.queryset
        ).qs
        self.assertEqual(filtered.count(), 2)
        self.assertIn(self.club2, filtered)
        self.assertIn(self.club4, filtered)

    def test_filter_by_join_permissions(self):
        """Test filtering by join permissions"""

        filtered = ClubFilter(
            {"join_permissions": Club.JOIN_PERMISSIONS_OPEN}, queryset=self.queryset
        ).qs
        self.assertEqual(filtered.count(), 1)
        self.assertEqual(filtered.first(), self.club1)

        filtered = ClubFilter(
            {"join_permissions": Club.JOIN_PERMISSIONS_INVITE}, queryset=self.queryset
        ).qs
        self.assertEqual(filtered.count(), 3)
        self.assertIn(self.club2, filtered)
        self.assertIn(self.club3, filtered)
        self.assertIn(self.club4, filtered)

    def test_filter_by_type(self):
        """Test filtering by club type"""

        filtered = ClubFilter({"type": self.type1.id}, queryset=self.queryset).qs
        self.assertEqual(filtered.count(), 2)
        self.assertIn(self.club1, filtered)
        self.assertIn(self.club3, filtered)

        filtered = ClubFilter({"type": self.type2.id}, queryset=self.queryset).qs
        self.assertEqual(filtered.count(), 2)
        self.assertIn(self.club2, filtered)
        self.assertIn(self.club4, filtered)

        filtered = ClubFilter({"type": uuid.uuid4()}, queryset=self.queryset).qs
        self.assertEqual(filtered.count(), 0)

    def test_filter_by_type_name(self):
        """Test filtering by club type name"""

        filtered = ClubFilter({"type_name": "Sports Club"}, queryset=self.queryset).qs
        self.assertEqual(filtered.count(), 2)
        self.assertIn(self.club1, filtered)
        self.assertIn(self.club3, filtered)

        filtered = ClubFilter({"type_name": "Book"}, queryset=self.queryset).qs
        self.assertEqual(filtered.count(), 2)
        self.assertIn(self.club2, filtered)
        self.assertIn(self.club4, filtered)

    def test_filter_by_manager(self):
        """Test filtering by manager"""

        filtered = ClubFilter({"manager": self.manager1.id}, queryset=self.queryset).qs
        self.assertEqual(filtered.count(), 4)

        filtered = ClubFilter({"manager": uuid.uuid4()}, queryset=self.queryset).qs
        self.assertEqual(filtered.count(), 0)

    def test_filter_by_member(self):
        """Test filtering by member"""

        filtered = ClubFilter({"member": self.member1.id}, queryset=self.queryset).qs
        self.assertEqual(filtered.count(), 2)
        self.assertIn(self.club1, filtered)
        self.assertIn(self.club2, filtered)

        filtered = ClubFilter({"member": self.member2.id}, queryset=self.queryset).qs
        self.assertEqual(filtered.count(), 1)
        self.assertEqual(filtered.first(), self.club2)

        filtered = ClubFilter({"member": uuid.uuid4()}, queryset=self.queryset).qs
        self.assertEqual(filtered.count(), 0)

        filtered = ClubFilter({"member": "invalid-uuid"}, queryset=self.queryset).qs
        self.assertEqual(filtered.count(), 4)

    def test_filter_by_created_date(self):
        """Test filtering by creation date"""

        today_str = self.today.date().isoformat()
        yesterday_str = self.yesterday.date().isoformat()
        last_week_str = self.last_week.date().isoformat()

        filtered = ClubFilter(
            {"created_after": last_week_str}, queryset=self.queryset
        ).qs
        self.assertEqual(filtered.count(), 3)
        self.assertIn(self.club1, filtered)
        self.assertIn(self.club2, filtered)
        self.assertIn(self.club3, filtered)

        filtered = ClubFilter(
            {"created_before": yesterday_str}, queryset=self.queryset
        ).qs
        self.assertEqual(filtered.count(), 2)
        self.assertIn(self.club3, filtered)
        self.assertIn(self.club4, filtered)

    def test_combining_filters(self):
        """Test combining multiple filters"""

        filtered = ClubFilter(
            {"type": self.type1.id, "privacy": Club.PRIVACY_OPEN},
            queryset=self.queryset,
        ).qs
        self.assertEqual(filtered.count(), 2)
        self.assertIn(self.club1, filtered)
        self.assertIn(self.club3, filtered)

        filtered = ClubFilter(
            {
                "type": self.type1.id,
                "name": "Gamma",
                "created_after": self.last_month.date().isoformat(),
            },
            queryset=self.queryset,
        ).qs
        self.assertEqual(filtered.count(), 1)
        self.assertEqual(filtered.first(), self.club3)
