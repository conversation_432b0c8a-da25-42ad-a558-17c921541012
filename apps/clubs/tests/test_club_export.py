import pytest
import csv
import io
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model
from apps.clubs.models import Club, ClubType

User = get_user_model()


@pytest.mark.django_db
class TestClubExportAPIView:
    """Test suite for the club export API."""

    def setup_method(self):

        self.client = APIClient()

        self.admin_user = User.objects.create_user(
            username="admin",
            email="<EMAIL>",
            password="password123",
            role="admin",
            is_staff=True,
        )
        self.manager_user = User.objects.create_user(
            username="manager",
            email="<EMAIL>",
            password="password123",
            role="club_manager",
        )
        self.member_user = User.objects.create_user(
            username="member",
            email="<EMAIL>",
            password="password123",
            role="member",
        )
        self.other_user = User.objects.create_user(
            username="other",
            email="<EMAIL>",
            password="password123",
            role="member",
        )

        self.club_type1 = ClubType.objects.create(
            name="Type 1", created_by=self.admin_user
        )
        self.club_type2 = ClubType.objects.create(
            name="Type 2", created_by=self.admin_user
        )

        self.club1 = Club.objects.create(
            name="Club 1",
            type=self.club_type1,
            manager=self.manager_user,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_OPEN,
        )
        self.club2 = Club.objects.create(
            name="Club 2",
            type=self.club_type1,
            manager=self.manager_user,
            privacy=Club.PRIVACY_CLOSED,
            join_permissions=Club.JOIN_PERMISSIONS_INVITE,
        )
        self.club3 = Club.objects.create(
            name="Club 3",
            type=self.club_type2,
            manager=self.manager_user,
            privacy=Club.PRIVACY_CLOSED,
            join_permissions=Club.JOIN_PERMISSIONS_INVITE,
        )

        self.club1.members.add(self.member_user)
        self.club2.members.add(self.member_user)

        self.export_url = reverse("clubs:club-export")

    def test_export_as_admin(self):
        """Test that admins can export all clubs."""
        self.client.force_authenticate(user=self.admin_user)
        response = self.client.get(self.export_url)

        assert response.status_code == status.HTTP_200_OK
        assert response["Content-Type"] == "text/csv"
        assert "attachment; filename=" in response["Content-Disposition"]

        content = response.content.decode("utf-8")
        csv_reader = csv.reader(io.StringIO(content))
        rows = list(csv_reader)

        assert rows[0] == [
            "ID",
            "Name",
            "Type",
            "Manager",
            "Privacy",
            "Join Permissions",
            "Member Count",
            "Created Date",
        ]

        assert len(rows) == 4

        club_names = [row[1] for row in rows[1:]]
        assert "Club 1" in club_names
        assert "Club 2" in club_names
        assert "Club 3" in club_names

    def test_export_as_manager(self):
        """Test that managers can export only clubs they manage."""
        self.client.force_authenticate(user=self.manager_user)
        response = self.client.get(self.export_url)

        assert response.status_code == status.HTTP_200_OK

        content = response.content.decode("utf-8")
        csv_reader = csv.reader(io.StringIO(content))
        rows = list(csv_reader)

        assert len(rows) == 4

        club_names = [row[1] for row in rows[1:]]
        assert "Club 1" in club_names
        assert "Club 2" in club_names
        assert "Club 3" in club_names

    def test_export_as_member(self):
        """Test that members can export clubs they're a member of or public clubs."""
        self.client.force_authenticate(user=self.member_user)
        response = self.client.get(self.export_url)

        assert response.status_code == status.HTTP_200_OK

        content = response.content.decode("utf-8")
        csv_reader = csv.reader(io.StringIO(content))
        rows = list(csv_reader)

        assert len(rows) == 3

        club_names = [row[1] for row in rows[1:]]
        assert "Club 1" in club_names
        assert "Club 2" in club_names
        assert "Club 3" not in club_names

    def test_export_as_other_user(self):
        """Test that users can only export public clubs if they're not members."""
        self.client.force_authenticate(user=self.other_user)
        response = self.client.get(self.export_url)

        assert response.status_code == status.HTTP_200_OK

        content = response.content.decode("utf-8")
        csv_reader = csv.reader(io.StringIO(content))
        rows = list(csv_reader)

        assert len(rows) == 2

        club_names = [row[1] for row in rows[1:]]
        assert "Club 1" in club_names
        assert "Club 2" not in club_names
        assert "Club 3" not in club_names

    def test_export_with_filters(self):
        """Test exporting clubs with filters applied."""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(f"{self.export_url}?name=Club 1")

        assert response.status_code == status.HTTP_200_OK

        content = response.content.decode("utf-8")
        csv_reader = csv.reader(io.StringIO(content))
        rows = list(csv_reader)

        assert len(rows) == 2
        assert rows[1][1] == "Club 1"

        response = self.client.get(f"{self.export_url}?type={self.club_type2.id}")

        assert response.status_code == status.HTTP_200_OK

        content = response.content.decode("utf-8")
        csv_reader = csv.reader(io.StringIO(content))
        rows = list(csv_reader)

        assert len(rows) == 2
        assert rows[1][1] == "Club 3"

        response = self.client.get(f"{self.export_url}?privacy={Club.PRIVACY_CLOSED}")

        assert response.status_code == status.HTTP_200_OK

        content = response.content.decode("utf-8")
        csv_reader = csv.reader(io.StringIO(content))
        rows = list(csv_reader)

        assert len(rows) == 3
        club_names = [row[1] for row in rows[1:]]
        assert "Club 2" in club_names
        assert "Club 3" in club_names

    def test_authentication_required(self):
        """Test that authentication is required to export clubs."""
        response = self.client.get(self.export_url)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
