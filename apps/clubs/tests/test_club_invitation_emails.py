import uuid
from django.test import TestCase, override_settings
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from django.conf import settings
from unittest.mock import patch, MagicMock, call
from django.template.loader import render_to_string
from django.utils import translation

from apps.clubs.models import Club, ClubType, ClubInvitation
from apps.accounts.user.models import UserToken

User = get_user_model()


@override_settings(
    MEMBER_BASE_UI_URL="http://localhost:4189",
    DEFAULT_FROM_EMAIL="<EMAIL>",
    CLUB_MANAGER_CAN_CREATE_X_CLUBS=100,
    LANGUAGE_CODE="en",
    USE_I18N=True,
)
class ClubInvitationEmailContentTest(TestCase):
    """Test cases to validate that club invitation emails contain correct information."""

    def setUp(self):
        """Set up test data."""

        translation.activate("en")

        self.client = APIClient()

        self.manager = User.objects.create_user(
            email="<EMAIL>",
            username="club_manager",
            password="testpass123",
            fullname="Club Manager",
        )

        self.existing_user = User.objects.create_user(
            email="<EMAIL>",
            username="existing_user",
            password="testpass123",
            fullname="Existing User",
        )

        self.club_type = ClubType.objects.create(name="Test Club Type")

        self.club = Club.objects.create(
            name="Test Innovation Club",
            type=self.club_type,
            manager=self.manager,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_INVITE,
        )

        self.invite_url = reverse("clubs:invite-members", args=[self.club.id])
        self.create_club_url = reverse("clubs:club-create")

        self.client.force_authenticate(user=self.manager)

    @patch("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_invite_existing_user_email_content(self, mock_send_email):
        """Test that invitation email for existing user contains correct information."""
        data = {
            "invitations": [
                {"email": "<EMAIL>", "fullname": "Existing Member"}
            ]
        }

        response = self.client.post(self.invite_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertTrue(mock_send_email.called)
        self.assertEqual(mock_send_email.call_count, 1)

        call_args = mock_send_email.call_args
        call_kwargs = call_args.kwargs

        self.assertIn("subject", call_kwargs)
        self.assertIn("text_content", call_kwargs)
        self.assertIn("html_content", call_kwargs)
        self.assertIn("from_email", call_kwargs)
        self.assertIn("to_email", call_kwargs)

        self.assertEqual(call_kwargs["to_email"], "<EMAIL>")
        self.assertEqual(call_kwargs["from_email"], settings.DEFAULT_FROM_EMAIL)

        subject = call_kwargs["subject"]
        self.assertTrue(
            "Test Innovation Club" in subject,
            f"Club name should be in subject, got: {subject}",
        )

        html_content = call_kwargs["html_content"]
        text_content = call_kwargs["text_content"]

        self.assertIn(self.club.name, html_content)
        self.assertIn(self.club.name, text_content)
        self.assertIn(self.club.type.name, html_content)
        self.assertIn(self.club.type.name, text_content)

        self.assertIn(self.manager.username, html_content)
        self.assertIn(self.manager.username, text_content)

        self.assertIn(self.existing_user.username, html_content)
        self.assertIn(self.existing_user.username, text_content)

        expected_invitation_url = "http://localhost:4189/invitations"
        self.assertIn(expected_invitation_url, html_content)
        self.assertIn(expected_invitation_url, text_content)

        self.assertNotIn("Set Your Password", html_content)
        self.assertNotIn("reset-password", html_content)

    @patch("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_invite_new_user_email_content(self, mock_send_email):
        """Test that invitation email for new user contains password reset link."""
        new_email = "<EMAIL>"
        data = {"invitations": [{"email": new_email, "fullname": "New User"}]}

        response = self.client.post(self.invite_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        new_user = User.objects.get(email=new_email)
        self.assertIsNotNone(new_user)

        self.assertTrue(mock_send_email.called)
        call_kwargs = mock_send_email.call_args.kwargs

        subject = call_kwargs["subject"]
        self.assertTrue(
            "Welcome" in subject or self.club.name in subject,
            f"Subject should contain welcome or club name, got: {subject}",
        )

        html_content = call_kwargs["html_content"]
        text_content = call_kwargs["text_content"]

        self.assertIn("Set Your Password", html_content)
        self.assertIn("reset-password", html_content)
        self.assertIn("reset-password", text_content)

        self.assertIn("http://localhost:4189", html_content)
        self.assertIn("http://localhost:4189", text_content)

        expected_invitation_url = "http://localhost:4189/invitations"
        self.assertIn(expected_invitation_url, html_content)
        self.assertIn(expected_invitation_url, text_content)

        self.assertIn(self.club.name, html_content)
        self.assertIn(self.manager.username, html_content)

    @patch("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_multiple_invitations_email_content(self, mock_send_email):
        """Test that multiple invitations send correct individual emails."""
        data = {
            "emails": ["<EMAIL>"],
            "invitations": [
                {"email": "<EMAIL>", "fullname": "New User One"},
                {"email": "<EMAIL>", "fullname": "New User Two"},
            ],
        }

        response = self.client.post(self.invite_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertEqual(mock_send_email.call_count, 3)

        all_calls = mock_send_email.call_args_list

        recipients = [call.kwargs["to_email"] for call in all_calls]
        self.assertIn("<EMAIL>", recipients)
        self.assertIn("<EMAIL>", recipients)
        self.assertIn("<EMAIL>", recipients)

        expected_invitation_url = "http://localhost:4189/invitations"

        for call_args in all_calls:
            call_kwargs = call_args.kwargs
            recipient_email = call_kwargs["to_email"]
            html_content = call_kwargs["html_content"]

            self.assertIn(self.club.name, html_content)
            self.assertIn(self.manager.username, html_content)

            self.assertIn(expected_invitation_url, html_content)

            if recipient_email == "<EMAIL>":

                self.assertNotIn("Set Your Password", html_content)
                subject = call_kwargs["subject"]
                self.assertTrue(
                    self.club.name in subject,
                    f"Subject should contain club name, got: {subject}",
                )
            else:

                self.assertIn("Set Your Password", html_content)
                subject = call_kwargs["subject"]
                self.assertTrue(
                    "Welcome" in subject or self.club.name in subject,
                    f"Subject should contain welcome or club name, got: {subject}",
                )

    @patch("apps.clubs.views.club_views.create.send_email_with_html")
    def test_club_creation_with_members_email_content(self, mock_send_email):
        """Test that club creation with members sends correct invitation emails."""
        unique_club_name = f"New Test Club {uuid.uuid4()}"
        data = {
            "name": unique_club_name,
            "type": str(self.club_type.id),
            "privacy": Club.PRIVACY_OPEN,
            "join_permissions": Club.JOIN_PERMISSIONS_OPEN,
            "members": [
                {"email": "<EMAIL>", "name": "Existing Member"},
                {"email": "<EMAIL>", "name": "New Member"},
            ],
        }

        response = self.client.post(self.create_club_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        created_club = Club.objects.get(name=unique_club_name)
        self.assertEqual(created_club.manager, self.manager)

        self.assertEqual(mock_send_email.call_count, 2)

        expected_invitation_url = "http://localhost:4189/invitations"

        all_calls = mock_send_email.call_args_list
        for call_args in all_calls:
            call_kwargs = call_args.kwargs
            html_content = call_kwargs["html_content"]
            text_content = call_kwargs["text_content"]

            self.assertIn(unique_club_name, html_content)
            self.assertIn(unique_club_name, text_content)
            self.assertIn(self.club_type.name, html_content)
            self.assertIn(self.manager.username, html_content)

            self.assertIn(expected_invitation_url, html_content)
            self.assertIn(expected_invitation_url, text_content)

    @patch("apps.clubs.views.club_management.invite.render_to_string")
    @patch("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_email_template_context_variables(self, mock_send_email, mock_render):
        """Test that email templates receive correct context variables."""

        mock_render.side_effect = lambda template, context: f"Rendered {template}"

        data = {
            "invitations": [
                {"email": "<EMAIL>", "fullname": "Context Test User"}
            ]
        }

        response = self.client.post(self.invite_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertEqual(mock_render.call_count, 2)

        html_call = None
        txt_call = None

        for call in mock_render.call_args_list:
            template_name = call[0][0]
            context = call[0][1]

            if template_name == "clubs/emails/invites/invite.html":
                html_call = context
            elif template_name == "clubs/emails/invites/invite.txt":
                txt_call = context

        self.assertIsNotNone(html_call)
        self.assertIsNotNone(txt_call)

        if html_call is not None:
            self.assertIn("club", html_call)
            self.assertIn("inviter", html_call)
            self.assertIn("invited_user", html_call)
            self.assertIn("frontend_url", html_call)
            self.assertIn("invitation_token", html_call)
            self.assertIn("fullname", html_call)

            self.assertEqual(html_call["club"], self.club)
            self.assertEqual(html_call["inviter"], self.manager)
            self.assertEqual(html_call["frontend_url"], "http://localhost:4189")
            self.assertEqual(html_call["fullname"], "Context Test User")

            self.assertIsNotNone(html_call["invitation_token"])
            self.assertNotEqual(html_call["invitation_token"], "")
        else:
            self.fail("HTML template context was not captured")

    @patch("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_email_content_with_special_characters(self, mock_send_email):
        """Test email content with special characters in club name and user details."""

        special_club = Club.objects.create(
            name="Test Club - Special & Characters! (2024)",
            type=self.club_type,
            manager=self.manager,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_INVITE,
        )

        invite_url = reverse("clubs:invite-members", args=[special_club.id])

        data = {
            "invitations": [
                {
                    "email": "<EMAIL>",
                    "fullname": "Üser Wîth Spëcîál Chàractërs",
                }
            ]
        }

        response = self.client.post(invite_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertTrue(mock_send_email.called)
        call_kwargs = mock_send_email.call_args.kwargs

        html_content = call_kwargs["html_content"]
        text_content = call_kwargs["text_content"]

        # Note: HTML templates will escape & as &amp;
        self.assertTrue(
            "Test Club - Special &amp; Characters! (2024)" in html_content
            or "Test Club - Special & Characters! (2024)" in html_content,
            f"Club name with special characters not found in HTML content",
        )

        self.assertTrue(
            "Test Club - Special &amp; Characters! (2024)" in text_content
            or "Test Club - Special & Characters! (2024)" in text_content,
            f"Club name with special characters not found in text content",
        )

        expected_invitation_url = "http://localhost:4189/invitations"
        self.assertIn(expected_invitation_url, html_content)
        self.assertIn(expected_invitation_url, text_content)

        # Note: Template uses username, not fullname in greeting
        self.assertIn("special", html_content)

    @patch("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_email_from_address_configuration(self, mock_send_email):
        """Test that emails are sent from the correct configured address."""
        data = {"emails": ["<EMAIL>"]}

        response = self.client.post(self.invite_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        call_kwargs = mock_send_email.call_args.kwargs
        self.assertEqual(call_kwargs["from_email"], "<EMAIL>")

    @patch("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_invitation_token_uniqueness(self, mock_send_email):
        """Test that each invitation gets a unique token."""
        data = {
            "emails": ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
        }

        response = self.client.post(self.invite_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        tokens = []
        for call_args in mock_send_email.call_args_list:
            html_content = call_args.kwargs["html_content"]

            self.assertIn("http://localhost:4189/invitations", html_content)

        self.assertEqual(mock_send_email.call_count, 3)

    @override_settings(MEMBER_BASE_UI_URL="https://production-frontend.com")
    @patch("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_frontend_url_configuration(self, mock_send_email):
        """Test that email content uses the correct frontend URL from settings."""
        data = {"emails": ["<EMAIL>"]}

        response = self.client.post(self.invite_url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        call_kwargs = mock_send_email.call_args.kwargs
        html_content = call_kwargs["html_content"]

        self.assertIn("https://production-frontend.com/invitations", html_content)
        self.assertNotIn("http://localhost:4189", html_content)

    def tearDown(self):
        """Clean up test data."""

        ClubInvitation.objects.all().delete()
        UserToken.objects.all().delete()
        Club.objects.all().delete()
        User.objects.filter(email__endswith="@example.com").delete()

        translation.deactivate()
