from django .test import TestCase 
from rest_framework .test import APIClient 
from rest_framework import status 
from django .urls import reverse 
from django .contrib .auth import get_user_model 
from unittest .mock import patch 
from django .conf import settings 

from apps .clubs .models import Club ,ClubType ,ClubInvitation 
from apps .clubs .serializers .invite import ClubInviteSerializer 
from apps .accounts .user .models import UserToken 

User =get_user_model ()


class EnhancedClubInviteTests (TestCase ):
    def setUp (self ):
        self .client =APIClient ()

        self .manager =User .objects .create_user (
        email ="<EMAIL>",password ="password123",username ="manager"
        )
        self .existing_user =User .objects .create_user (
        email ="<EMAIL>",password ="password123",username ="existing"
        )

        self .club_type =ClubType .objects .create (name ="Test Type")
        self .club =Club .objects .create (
        name ="Test Club",
        type =self .club_type ,
        manager =self .manager ,
        privacy =Club .PRIVACY_OPEN ,
        join_permissions =Club .JOIN_PERMISSIONS_INVITE ,
        )

        self .invite_url =reverse ("clubs:invite-members",args =[self .club .id ])
        self .client .force_authenticate (user =self .manager )

    def test_serializer_deduplicates_emails (self ):
        """Test that the serializer deduplicates emails in emails and invitations lists"""
        data ={
        "emails":["<EMAIL>","<EMAIL>"],
        "invitations":[
        {"email":"<EMAIL>","fullname":"Existing User"},
        {"email":"<EMAIL>","fullname":"New User 2"},
        ],
        }

        serializer =ClubInviteSerializer (data =data )
        self .assertTrue (serializer .is_valid ())

        self .assertEqual (serializer .validated_data ["emails"],["<EMAIL>"])
        self .assertEqual (len (serializer .validated_data ["invitations"]),2 )

    @patch ("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_invite_creates_users_and_invitations (self ,mock_send_email ):
        """Test that inviting creates new users for non-existent emails"""
        data ={
        "invitations":[
        {"email":"<EMAIL>","fullname":"New User 1"},
        {"email":"<EMAIL>","fullname":"New User 2"},
        ]
        }

        response =self .client .post (self .invite_url ,data ,format ="json")
        self .assertEqual (response .status_code ,status .HTTP_200_OK )

        self .assertTrue (User .objects .filter (email ="<EMAIL>").exists ())
        self .assertTrue (User .objects .filter (email ="<EMAIL>").exists ())

        new_user1 =User .objects .get (email ="<EMAIL>")
        new_user2 =User .objects .get (email ="<EMAIL>")

        self .assertTrue (
        ClubInvitation .objects .filter (user =new_user1 ,club =self .club ).exists ()
        )
        self .assertTrue (
        ClubInvitation .objects .filter (user =new_user2 ,club =self .club ).exists ()
        )

        invitation1 =ClubInvitation .objects .get (user =new_user1 ,club =self .club )
        invitation2 =ClubInvitation .objects .get (user =new_user2 ,club =self .club )

        self .assertEqual (invitation1 .fullname ,"New User 1")
        self .assertEqual (invitation2 .fullname ,"New User 2")



        self .assertFalse (self .club .members .filter (id =new_user1 .id ).exists ())
        self .assertFalse (self .club .members .filter (id =new_user2 .id ).exists ())

    @patch ("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_mixed_existing_and_new_users (self ,mock_send_email ):
        """Test inviting both existing and new users"""
        data ={
        "emails":["<EMAIL>"],
        "invitations":[
        {"email":"<EMAIL>","fullname":"New User 1"},
        ],
        }

        response =self .client .post (self .invite_url ,data ,format ="json")
        self .assertEqual (response .status_code ,status .HTTP_200_OK )

        self .assertTrue (
        ClubInvitation .objects .filter (
        user =self .existing_user ,club =self .club 
        ).exists ()
        )

        new_user =User .objects .get (email ="<EMAIL>")
        self .assertTrue (
        ClubInvitation .objects .filter (user =new_user ,club =self .club ).exists ()
        )



        self .assertFalse (self .club .members .filter (id =self .existing_user .id ).exists ())
        self .assertFalse (self .club .members .filter (id =new_user .id ).exists ())

    @patch ("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_duplicate_email_handles_correctly (self ,mock_send_email ):
        """Test that duplicate emails between emails and invitations are handled correctly"""
        data ={
        "emails":["<EMAIL>"],
        "invitations":[
        {"email":"<EMAIL>","fullname":"Duplicate User"},
        ],
        }

        response =self .client .post (self .invite_url ,data ,format ="json")
        self .assertEqual (response .status_code ,status .HTTP_200_OK )

        self .assertEqual (User .objects .filter (email ="<EMAIL>").count (),1 )

        duplicate_user =User .objects .get (email ="<EMAIL>")
        self .assertEqual (
        ClubInvitation .objects .filter (user =duplicate_user ,club =self .club ).count (),
        1 ,
        )

        self .assertEqual (mock_send_email .call_count ,1 )

        invitation =ClubInvitation .objects .get (user =duplicate_user ,club =self .club )
        self .assertEqual (invitation .fullname ,"Duplicate User")
