from django .test import TestCase 
from rest_framework .test import APIClient 
from rest_framework import status 
from django .urls import reverse 
from django .contrib .auth import get_user_model 
from django .conf import settings 
from unittest .mock import patch 

from apps .clubs .models import Club ,ClubType ,ClubInvitation 

User =get_user_model ()


class ClubInviteAPITest (TestCase ):
    def setUp (self ):
        self .client =APIClient ()

        self .manager =User .objects .create_user (
        email ="<EMAIL>",password ="password123",username ="manager"
        )
        self .existing_user =User .objects .create_user (
        email ="<EMAIL>",password ="password123",username ="existing"
        )

        self .club_type =ClubType .objects .create (name ="Test Type")
        self .club =Club .objects .create (
        name ="Test Club",
        type =self .club_type ,
        manager =self .manager ,
        privacy =Club .PRIVACY_OPEN ,
        join_permissions =Club .JOIN_PERMISSIONS_INVITE ,
        )

        self .invite_url =reverse ("clubs:invite-members",args =[self .club .id ])

        self .valid_payload ={
        "emails":["<EMAIL>"],
        "invitations":[{"email":"<EMAIL>","fullname":"New User"}],
        }

    @patch ("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_invite_members_returns_populated_data (self ,mock_send_email ):
        """Test that inviting members returns populated data of invited users"""
        self .client .force_authenticate (user =self .manager )

        payload ={"emails":["<EMAIL>"]}

        response =self .client .post (self .invite_url ,payload ,format ="json")

        if response .status_code !=status .HTTP_200_OK :
            print (f"Response status: {response.status_code}")
            print (f"Response content: {response.content.decode()}")
            print (f"Data sent: {payload}")

        self .assertEqual (response .status_code ,status .HTTP_200_OK )

        response_data =response .json ()
        self .assertIn ("message",response_data )
        self .assertIn ("invited_members",response_data )

        invited_members =response_data ["invited_members"]
        self .assertIsInstance (invited_members ,list )
        self .assertEqual (len (invited_members ),1 )

        member =invited_members [0 ]
        self .assertIn ("id",member )
        self .assertIn ("username",member )
        self .assertIn ("email",member )
        self .assertEqual (member ["email"],"<EMAIL>")
        self .assertEqual (member ["username"],"existing")

        invitations =ClubInvitation .objects .filter (club =self .club )
        self .assertEqual (invitations .count (),1 )

        self .assertEqual (mock_send_email .call_count ,1 )

    @patch ("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_invite_nonexistent_user_creates_account (self ,mock_send_email ):
        """Test inviting a non-existent user creates a new user account"""
        self .client .force_authenticate (user =self .manager )

        payload ={
        "invitations":[
        {"email":"<EMAIL>","fullname":"Non Existent"}
        ]
        }

        response =self .client .post (self .invite_url ,payload ,format ="json")

        self .assertEqual (response .status_code ,status .HTTP_200_OK )

        self .assertTrue (User .objects .filter (email ="<EMAIL>").exists ())
        new_user =User .objects .get (email ="<EMAIL>")

        response_data =response .json ()
        self .assertIn ("invited_members",response_data )
        self .assertEqual (len (response_data ["invited_members"]),1 )

        member =response_data ["invited_members"][0 ]
        self .assertEqual (member ["email"],"<EMAIL>")



        self .assertFalse (self .club .members .filter (id =new_user .id ).exists ())


        self .assertTrue (
        ClubInvitation .objects .filter (
        user =new_user ,
        club =self .club ,
        status =ClubInvitation .STATUS_PENDING 
        ).exists ()
        )

    def test_unauthorized_invite (self ):
        """Test that non-manager users cannot invite members"""
        self .client .force_authenticate (user =self .existing_user )

        response =self .client .post (self .invite_url ,self .valid_payload ,format ="json")

        self .assertEqual (response .status_code ,status .HTTP_403_FORBIDDEN )
        self .assertIn ("error",response .json ())

    @patch ("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_invited_members_structure (self ,mock_send_email ):
        """Test the detailed structure of the invited_members field in the response"""
        self .client .force_authenticate (user =self .manager )

        user1 =User .objects .create_user (
        email ="<EMAIL>",password ="password123",username ="user1"
        )
        user2 =User .objects .create_user (
        email ="<EMAIL>",password ="password123",username ="user2"
        )

        payload ={"emails":["<EMAIL>","<EMAIL>"]}

        response =self .client .post (self .invite_url ,payload ,format ="json")

        self .assertEqual (response .status_code ,status .HTTP_200_OK )

        print (f"Full response: {response.content.decode()}")

        response_data =response .json ()
        invited_members =response_data ["invited_members"]

        self .assertEqual (len (invited_members ),2 )

        for member in invited_members :
            self .assertIn ("id",member )
            self .assertIn ("username",member )
            self .assertIn ("email",member )

            self .assertIn (member ["email"],["<EMAIL>","<EMAIL>"])

            if member ["email"]=="<EMAIL>":
                self .assertEqual (member ["username"],"user1")
            elif member ["email"]=="<EMAIL>":
                self .assertEqual (member ["username"],"user2")
