from django.utils.translation import activate
from apps.clubs.models import Club, ClubType, ClubInvitation
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from django.urls import reverse
from unittest.mock import patch
from django.test import TestCase
from rest_framework.test import APITestCase
from apps.clubs.models import Club, ClubType
from apps.clubs.models import Club, ClubInvitation
import uuid
from django.test import TestCase
from unittest.mock import patch
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model
from apps.clubs.models import Club, ClubType, ClubInvitation

User = get_user_model()


class ClubInviteViewTest(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.club_type = ClubType.objects.create(name="Test Type")
        self.manager = User.objects.create_user(
            username="manager", email="<EMAIL>", password="password123"
        )
        self.client.force_authenticate(user=self.manager)
        self.client.force_authenticate(user=self.manager)

        self.club = Club.objects.create(
            name="Test Club",
            type=self.club_type,
            manager=self.manager,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_INVITE,
        )

        self.user1 = User.objects.create_user(
            username="user1", email="<EMAIL>", password="password123"
        )

        self.user2 = User.objects.create_user(
            username="user2", email="<EMAIL>", password="password123"
        )

        self.invite_url = reverse("clubs:invite-members", args=[self.club.id])

    @patch("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_invite_members_success(self, mock_send_email):
        data = {"emails": [self.user1.email, self.user2.email]}
        response = self.client.post(self.invite_url, data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(
            ClubInvitation.objects.filter(user=self.user1, club=self.club).exists()
        )
        self.assertTrue(
            ClubInvitation.objects.filter(user=self.user2, club=self.club).exists()
        )
        mock_send_email.assert_called()
        mock_send_email.assert_called()

    @patch("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_invite_members_with_fullname(self, mock_send_email):
        data = {
            "invitations": [
                {"email": self.user1.email, "fullname": "User One"},
                {"email": self.user2.email, "fullname": "User Two"},
            ]
        }
        response = self.client.post(self.invite_url, data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        invitation1 = ClubInvitation.objects.get(user=self.user1, club=self.club)
        self.assertEqual(invitation1.fullname, "User One")

        invitation2 = ClubInvitation.objects.get(user=self.user2, club=self.club)
        self.assertEqual(invitation2.fullname, "User Two")

        mock_send_email.assert_called()

    @patch("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_invite_non_existing_email(self, mock_send_email):
        data = {"emails": ["<EMAIL>"]}
        response = self.client.post(self.invite_url, data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(ClubInvitation.objects.filter(user__isnull=False).count(), 0)
        self.assertIn("emails_not_found", response.json())
        mock_send_email.assert_not_called()

    @patch("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_invite_non_existing_email_with_fullname(self, mock_send_email):
        non_existent_email = "<EMAIL>"
        data = {
            "invitations": [
                {"email": non_existent_email, "fullname": "Non Existent User"}
            ]
        }
        response = self.client.post(self.invite_url, data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("emails_not_found", response.json())

        invitation = ClubInvitation.objects.get(email=non_existent_email)
        self.assertIsNone(invitation.user)
        self.assertEqual(invitation.fullname, "Non Existent User")

        mock_send_email.assert_not_called()

    def test_invite_without_permission(self):
        self.client.force_authenticate(user=self.user1)
        data = {"emails": [self.user2.email]}
        response = self.client.post(self.invite_url, data)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(ClubInvitation.objects.count(), 0)

    @patch("apps.clubs.views.club_management.invite.send_email_with_html")
    def test_invite_with_message(self, mock_send_email):
        data = {"emails": [self.user1.email]}
        response = self.client.post(self.invite_url, data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        mock_send_email.assert_called()

    def test_invite_to_nonexistent_club(self):
        non_existent_club_id = "12345678-1234-5678-1234-567812345678"
        url = reverse("clubs:invite-members", args=[non_existent_club_id])
        data = {"emails": [self.user1.email]}
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class JoinClubViewTest(TestCase):
    def setUp(self):
        self.client.defaults["HTTP_ACCEPT_LANGUAGE"] = "en"

        self.client = APIClient()
        self.manager = User.objects.create_user(
            email="<EMAIL>", password="password123"
        )
        self.club_type = ClubType.objects.create(name="Test Type")
        self.club = Club.objects.create(
            name="Test Club", type=self.club_type, manager=self.manager
        )
        self.invited_user = User.objects.create_user(
            email="<EMAIL>", password="password123"
        )
        self.join_url = reverse("clubs:join-club", args=[self.club.id])

    def test_join_club_with_user_invitation(self):
        ClubInvitation.objects.create(
            user=self.invited_user, club=self.club, status="pending"
        )

        self.client.force_authenticate(user=self.invited_user)

        response = self.client.post(self.join_url)

        print(response.data, "response.data\n\n")

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response.data["error"], "Waiting for approval")

    def test_join_club_with_email_invitation(self):
        ClubInvitation.objects.create(
            user=self.invited_user,
            sender=self.club.manager,
            email=self.invited_user.email,
            club=self.club,
            fullname="Invited User",
            status="pending",
        )

        self.client.force_authenticate(user=self.invited_user)

        response = self.client.post(self.join_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn(self.invited_user, self.club.members.all())
        self.assertTrue(
            ClubInvitation.objects.filter(
                email=self.invited_user.email, club=self.club, status="accepted"
            ).exists()
        )

    def test_join_club_already_member(self):
        self.client.defaults["HTTP_ACCEPT_LANGUAGE"] = "en"

        self.club.members.add(self.invited_user)
        self.club.save()
        self.club.refresh_from_db()

        self.client.force_authenticate(user=self.invited_user)

        response = self.client.post(self.join_url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(
            response.data["error"],
            f"""You are already a member of {
                self.club.name}""",
        )

    def test_join_club_no_invitation(self):
        activate("en")

        self.client.force_authenticate(user=self.invited_user)

        response = self.client.post(self.join_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertNotIn(self.invited_user, self.club.members.all())
        self.assertTrue(
            ClubInvitation.objects.filter(
                email=self.invited_user.email, club=self.club, status="pending"
            ).exists()
        )

    def test_join_club_nonexistent_club(self):
        nonexistent_club_id = "00000000-0000-0000-0000-000000000000"
        url = reverse("clubs:join-club", args=[nonexistent_club_id])

    def test_join_club_nonexistent_club(self):
        nonexistent_club_id = "00000000-0000-0000-0000-000000000000"
        url = reverse("clubs:join-club", args=[nonexistent_club_id])

        self.client.force_authenticate(user=self.invited_user)
        response = self.client.post(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_join_club_unauthenticated(self):
        self.client.force_authenticate(user=None)

        response = self.client.post(self.join_url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_join_club_private_club(self):
        self.club.privacy = Club.PRIVACY_CLOSED
        self.club.save()

        self.client.force_authenticate(user=self.invited_user)
        response = self.client.post(self.join_url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


User = get_user_model()


class RemoveMemberViewTests(APITestCase):
    def setUp(self):
        self.manager = User.objects.create_user(
            username="manager", password="testpass123", email="<EMAIL>"
        )
        self.member = User.objects.create_user(
            username="member", password="testpass123", email="<EMAIL>"
        )

        self.club_type = ClubType.objects.create(name="Test Type")
        self.club = Club.objects.create(
            name="Test Club", manager=self.manager, type=self.club_type
        )
        self.club.members.add(self.manager, self.member)

        self.url = reverse("clubs:remove-member", kwargs={"club_id": str(self.club.id)})

    def test_remove_member_success(self):
        self.client.force_authenticate(user=self.manager)
        response = self.client.post(self.url, {"member_id": str(self.member.id)})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertNotIn(self.member, self.club.members.all())
        self.assertIn(
            f"Successfully removed {self.member.username}", response.data["message"]
        )

    def test_unauthorized_user(self):
        response = self.client.post(self.url, {"member_id": str(self.member.id)})
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_non_manager_cannot_remove(self):
        self.client.force_authenticate(user=self.member)
        response = self.client.post(self.url, {"member_id": str(self.member.id)})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn(self.member, self.club.members.all())

    def test_invalid_member_id(self):
        self.client.force_authenticate(user=self.manager)
        response = self.client.post(self.url, {"member_id": str(uuid.uuid4())})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_invalid_club_id(self):
        invalid_url = reverse(
            "clubs:remove-member", kwargs={"club_id": str(uuid.uuid4())}
        )
        self.client.force_authenticate(user=self.manager)
        response = self.client.post(invalid_url, {"member_id": str(self.member.id)})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


User = get_user_model()


class AcceptJoinRequestTest(APITestCase):
    def setUp(self):
        activate("en")
        self.client = APIClient()

        self.manager = User.objects.create_user(
            email="<EMAIL>", password="password123"
        )
        self.invited_user = User.objects.create_user(
            email="<EMAIL>", password="password123"
        )
        self.club_type = ClubType.objects.create(name="Test Type")
        self.club = Club.objects.create(
            name="Test Club", type=self.club_type, manager=self.manager
        )

        self.invitation = ClubInvitation.objects.create(
            user=self.invited_user, club=self.club, status=ClubInvitation.STATUS_PENDING
        )

        self.accept_url = reverse(
            "clubs:accept-join-request", args=[str(self.invitation.id)]
        )

    @patch(
        "apps.clubs.views.club_management.accept_club_join_request.send_email_with_html"
    )
    def test_accept_join_request_success(self, mock_send_email):
        self.client.force_authenticate(user=self.manager)

        response = self.client.post(self.accept_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn(self.invited_user, self.club.members.all())
        self.invitation.refresh_from_db()
        self.assertEqual(self.invitation.status, ClubInvitation.STATUS_ACCEPTED)

        mock_send_email.assert_called()

    @patch(
        "apps.clubs.views.club_management.accept_club_join_request.send_email_with_html"
    )
    def test_accept_join_request_not_authorized(self, mock_send_email):
        self.client.force_authenticate(user=self.invited_user)

        response = self.client.post(self.accept_url)
        print(response.data, "response.data\n\n")

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        mock_send_email.assert_not_called()

    @patch(
        "apps.clubs.views.club_management.accept_club_join_request.send_email_with_html"
    )
    def test_accept_join_request_not_found(self, mock_send_email):
        self.invitation.status = ClubInvitation.STATUS_REJECTED
        self.invitation.save()

        self.client.force_authenticate(user=self.manager)

        response = self.client.post(self.accept_url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        mock_send_email.assert_not_called()


class ClubMembersAPIViewTest(TestCase):
    def setUp(self):
        self.client = APIClient()

        self.club_type = ClubType.objects.create(name="Test Type")
        self.manager = User.objects.create_user(
            username="manager", email="<EMAIL>", password="password123"
        )
        self.user1 = User.objects.create_user(
            username="user1",
            email="<EMAIL>",
            password="password123",
            fullname="Ali One",
        )
        self.user2 = User.objects.create_user(
            username="user2",
            email="<EMAIL>",
            password="password123",
            fullname="Ali Two",
        )
        self.other_user = User.objects.create_user(
            username="other", email="<EMAIL>", password="password123"
        )

        self.club = Club.objects.create(
            name="Test Club",
            type=self.club_type,
            manager=self.manager,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_INVITE,
        )

        ClubInvitation.objects.create(
            user=self.user1, club=self.club, status="accepted"
        )
        ClubInvitation.objects.create(
            user=self.user2, club=self.club, status="accepted"
        )

        self.url = reverse("clubs:club-members", args=[self.club.id])

    def test_get_members_success(self):
        self.client.force_authenticate(user=self.manager)
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)

    def test_get_members_unauthorized_user(self):
        self.client.force_authenticate(user=self.other_user)
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertIn("error", response.data)

    def test_get_members_club_not_found(self):
        self.client.force_authenticate(user=self.manager)
        url = reverse(
            "clubs:club-members", args=["00000000-0000-0000-0000-000000000000"]
        )
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn("error", response.data)


class RejectInvitationViewTests(APITestCase):
    def setUp(self):
        self.manager = User.objects.create_user(
            username="manager", email="<EMAIL>", password="password123"
        )
        self.user = User.objects.create_user(
            username="user", email="<EMAIL>", password="password123"
        )
        self.other_user = User.objects.create_user(
            username="other", email="<EMAIL>", password="password123"
        )
        self.club_type = ClubType.objects.create(name="Test Club Type")
        self.club = Club.objects.create(
            name="Test Club",
            type=self.club_type,
            manager=self.manager,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_INVITE,
        )
        self.invitation = ClubInvitation.objects.create(
            user=self.user,
            club=self.club,
            sender=self.manager,
            email=self.user.email,
            status=ClubInvitation.STATUS_PENDING,
        )
        self.reject_url = reverse(
            "clubs:reject-join-request", args=[self.invitation.id]
        )

    def test_user_rejects_own_invitation_successfully(self):
        self.client.force_authenticate(user=self.user)
        response = self.client.post(self.reject_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.invitation.refresh_from_db()
        self.assertEqual(self.invitation.status, ClubInvitation.STATUS_REJECTED)

    def test_manager_rejects_invitation_successfully(self):
        self.invitation.sender = self.user
        self.invitation.save()

        self.client.force_authenticate(user=self.manager)
        response = self.client.post(self.reject_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.invitation.refresh_from_db()
        self.assertEqual(self.invitation.status, ClubInvitation.STATUS_REJECTED)

    def test_user_cannot_reject_if_not_related(self):
        self.client.force_authenticate(user=self.other_user)
        response = self.client.post(self.reject_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_reject_already_handled_invitation(self):
        self.invitation.status = ClubInvitation.STATUS_REJECTED
        self.invitation.save()

        self.client.force_authenticate(user=self.manager)
        response = self.client.post(self.reject_url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_manager_reject_sent_invitation(self):
        self.client.force_authenticate(user=self.manager)
        response = self.client.post(self.reject_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_user_reject_sent_invitation(self):
        self.invitation.sender = self.user
        self.invitation.save()

        self.client.force_authenticate(user=self.user)
        response = self.client.post(self.reject_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_user_reject_not_pending_invitation(self):
        self.invitation.status = ClubInvitation.STATUS_ACCEPTED
        self.invitation.save()

        self.client.force_authenticate(user=self.user)
        response = self.client.post(self.reject_url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_manager_reject_not_pending_invitation(self):
        self.invitation.status = ClubInvitation.STATUS_ACCEPTED
        self.invitation.save()

        self.client.force_authenticate(user=self.manager)
        response = self.client.post(self.reject_url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class TestClubInvitationsViews(APITestCase):
    def setUp(self):
        self.client = APIClient()

        self.manager = User.objects.create_user(
            username="manager", email="<EMAIL>", password="password"
        )
        self.member = User.objects.create_user(
            username="member", email="<EMAIL>", password="password"
        )
        self.other_user = User.objects.create_user(
            username="other", email="<EMAIL>", password="password"
        )

        self.club_type = ClubType.objects.create(name="Test Club Type")
        self.club = Club.objects.create(
            name="Test Club", manager=self.manager, type=self.club_type
        )

        ClubInvitation.objects.create(
            club=self.club, user=self.member, email=self.member.email, status="pending"
        )
        ClubInvitation.objects.create(
            club=self.club, email="<EMAIL>", status="accepted"
        )
        ClubInvitation.objects.create(
            club=self.club, user=self.other_user, status="rejected"
        )

    def test_managed_invitations_authenticated(self):
        self.client.force_authenticate(user=self.manager)

        url = reverse("clubs:managed-invitations")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 3)

    def test_managed_invitations_filtered_by_status(self):
        self.client.force_authenticate(user=self.manager)

        url = reverse("clubs:managed-invitations") + "?status=accepted"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["status"], "accepted")

    def test_my_invitations_authenticated_by_user(self):
        self.client.force_authenticate(user=self.member)

        url = reverse("clubs:my-invitations")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["email"], self.member.email)

    def test_my_invitations_authenticated_by_email(self):
        temp_user = User.objects.create_user(
            username="ghost", email="<EMAIL>", password="password"
        )
        self.client.force_authenticate(user=temp_user)

        url = reverse("clubs:my-invitations")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["email"], "<EMAIL>")

    def test_managed_invitations_requires_auth(self):
        url = reverse("clubs:managed-invitations")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_my_invitations_requires_auth(self):
        url = reverse("clubs:my-invitations")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
