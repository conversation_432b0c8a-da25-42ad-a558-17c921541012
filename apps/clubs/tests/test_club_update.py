from django.test import TestCase
from rest_framework.test import APIClient
from rest_framework import status
from django.urls import reverse
from django.contrib.auth import get_user_model
import json

from apps.clubs.models import Club, ClubType
from apps.clubs.serializers.club_serializers.detail import ClubDetailSerializer

User = get_user_model()


class ClubUpdateAPITest(TestCase):
    def setUp(self):
        self.client = APIClient()

        self.manager = User.objects.create_user(
            email="<EMAIL>", password="password123", username="manager"
        )
        self.member = User.objects.create_user(
            email="<EMAIL>", password="password123", username="member"
        )
        self.other_user = User.objects.create_user(
            email="<EMAIL>", password="password123", username="otheruser"
        )

        self.club_type = ClubType.objects.create(name="Test Type")

        self.club = Club.objects.create(
            name="Test Club",
            type=self.club_type,
            manager=self.manager,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_OPEN,
        )

        self.club.members.add(self.member)

        self.update_url = reverse("clubs:club-update", args=[self.club.id])
        self.partial_update_url = reverse(
            "clubs:club-partial-update", args=[self.club.id]
        )

    def test_full_update_returns_populated_data(self):
        """Test that a full update returns the club with populated data"""
        self.client.force_authenticate(user=self.manager)

        current_data = {
            "name": self.club.name,
            "type": str(self.club.type.id),
            "privacy": self.club.privacy,
            "join_permissions": self.club.join_permissions,
        }

        updated_data = {
            "name": "Updated Club Name",
            "type": str(self.club_type.id),
            "privacy": Club.PRIVACY_CLOSED,
            "join_permissions": Club.JOIN_PERMISSIONS_INVITE,
        }

        response = self.client.put(self.update_url, updated_data, format="json")

        if response.status_code != status.HTTP_200_OK:
            print(f"Response status: {response.status_code}")
            print(f"Response content: {response.content.decode()}")
            print(f"Data sent: {updated_data}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.club.refresh_from_db()

        self.assertEqual(self.club.name, "Updated Club Name")
        self.assertEqual(self.club.privacy, Club.PRIVACY_CLOSED)
        self.assertEqual(self.club.join_permissions, Club.JOIN_PERMISSIONS_INVITE)

        response_data = response.json()

        self.assertEqual(response_data["name"], "Updated Club Name")
        self.assertEqual(response_data["privacy"], Club.PRIVACY_CLOSED)
        self.assertEqual(
            response_data["join_permissions"], Club.JOIN_PERMISSIONS_INVITE
        )

        self.assertIn("manager", response_data)
        self.assertIsInstance(response_data["manager"], dict)
        self.assertEqual(response_data["manager"]["id"], str(self.manager.id))
        self.assertEqual(response_data["manager"]["username"], self.manager.username)

        self.assertIn("type", response_data)
        self.assertIsInstance(response_data["type"], dict)
        self.assertEqual(response_data["type"]["id"], str(self.club_type.id))
        self.assertEqual(response_data["type"]["name"], self.club_type.name)

        self.assertIn("members", response_data)
        self.assertIsInstance(response_data["members"], list)
        self.assertEqual(len(response_data["members"]), 1)
        self.assertEqual(response_data["members"][0]["id"], str(self.member.id))

        expected_data = ClubDetailSerializer(self.club).data
        self.assertEqual(set(response_data.keys()), set(expected_data.keys()))

    def test_partial_update_returns_populated_data(self):
        """Test that a partial update returns the club with populated data"""
        self.client.force_authenticate(user=self.manager)

        updated_data = {"name": "Partially Updated Club"}

        response = self.client.patch(
            self.partial_update_url, updated_data, format="json"
        )

        if response.status_code != status.HTTP_200_OK:
            print(f"Response status: {response.status_code}")
            print(f"Response content: {response.content.decode()}")
            print(f"Data sent: {updated_data}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.club.refresh_from_db()

        self.assertEqual(self.club.name, "Partially Updated Club")

        response_data = response.json()

        self.assertEqual(response_data["name"], "Partially Updated Club")

        self.assertIn("manager", response_data)
        self.assertIsInstance(response_data["manager"], dict)

        self.assertIn("type", response_data)
        self.assertIsInstance(response_data["type"], dict)

        self.assertIn("members", response_data)
        self.assertIsInstance(response_data["members"], list)

        expected_data = ClubDetailSerializer(self.club).data
        self.assertEqual(set(response_data.keys()), set(expected_data.keys()))

    def test_non_manager_cannot_update(self):
        """Test that a non-manager cannot update the club"""
        self.client.force_authenticate(user=self.member)

        updated_data = {"name": "Unauthorized Update"}

        response = self.client.patch(
            self.partial_update_url, updated_data, format="json"
        )

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        self.club.refresh_from_db()
        self.assertEqual(self.club.name, "Test Club")

    def test_put_with_only_one_field(self):
        """Test that a PUT request works with only one field provided"""
        self.client.force_authenticate(user=self.manager)

        updated_data = {"name": "Single Field Updated Club"}

        response = self.client.put(self.update_url, updated_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.club.refresh_from_db()

        self.assertEqual(self.club.name, "Single Field Updated Club")

        self.assertEqual(self.club.privacy, Club.PRIVACY_OPEN)
        self.assertEqual(self.club.join_permissions, Club.JOIN_PERMISSIONS_OPEN)

    def test_put_with_no_fields_fails(self):
        """Test that a PUT request with no fields fails"""
        self.client.force_authenticate(user=self.manager)

        updated_data = {}

        response = self.client.put(self.update_url, updated_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        self.assertIn("error", response.json())

        self.club.refresh_from_db()
        self.assertEqual(self.club.name, "Test Club")
