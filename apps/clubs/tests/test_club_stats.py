from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
import uuid

from apps.clubs.models import Club, ClubType
from apps.missions.models import (
    Goal,
    DailyGoal,
    DailyGoalProgress,
    WeeklyMission,
    WeeklySuccessRating,
)
from apps.okrs.models import SixWeekPeriod
from apps.domains.models import Domain

User = get_user_model()


class ClubProgressMethodsTests(TestCase):
    """Test the Club model methods for calculating progress and activity."""

    def setUp(self):
        self.manager = User.objects.create_user(
            email="<EMAIL>", password="password123", username="manager"
        )
        self.member1 = User.objects.create_user(
            email="<EMAIL>", password="password123", username="member1"
        )
        self.member2 = User.objects.create_user(
            email="<EMAIL>", password="password123", username="member2"
        )

        self.club_type = ClubType.objects.create(name="Test Club Type")
        self.club = Club.objects.create(
            name="Test Club",
            type=self.club_type,
            manager=self.manager,
        )
        self.club.members.add(self.member1, self.member2)

        self.domain = Domain.objects.create(
            name="Test Domain", category="personal", description="Test description"
        )

        self.start_date = timezone.now().date()
        self.end_date = self.start_date + timedelta(days=42)

        self.period_member1 = SixWeekPeriod.objects.create(
            user=self.member1,
            title="Test Period Member 1",
            start_date=self.start_date,
            end_date=self.end_date,
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
        )

        self.period_member2 = SixWeekPeriod.objects.create(
            user=self.member2,
            title="Test Period Member 2",
            start_date=self.start_date,
            end_date=self.end_date,
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
        )

        self.goal_member1 = Goal.objects.create(
            user=self.member1,
            six_week_period=self.period_member1,
            title="Test Goal Member 1",
        )

        self.goal_member2 = Goal.objects.create(
            user=self.member2,
            six_week_period=self.period_member2,
            title="Test Goal Member 2",
        )

        self.mission_member1 = WeeklyMission.objects.create(
            user=self.member1,
            six_week_period=self.period_member1,
            domain=self.domain,
            week_number=2,
            practice_days=[1, 2, 3],
        )
        self.mission_member1.goals.add(self.goal_member1)

        self.mission_member2 = WeeklyMission.objects.create(
            user=self.member2,
            six_week_period=self.period_member2,
            domain=self.domain,
            week_number=2,
            practice_days=[1, 2, 3],
        )
        self.mission_member2.goals.add(self.goal_member2)

        self.daily_goal_member1 = DailyGoal.objects.create(
            weekly_mission=self.mission_member1,
            title="Daily Goal Member 1",
        )
        self.daily_goal_member1.create_daily_progress_entries()

        self.daily_goal_member2 = DailyGoal.objects.create(
            weekly_mission=self.mission_member2,
            title="Daily Goal Member 2",
        )
        self.daily_goal_member2.create_daily_progress_entries()

        progress_entry = DailyGoalProgress.objects.get(
            daily_goal=self.daily_goal_member1, day_number=1
        )
        progress_entry.mark_completed()

        progress_entry = DailyGoalProgress.objects.get(
            daily_goal=self.daily_goal_member1, day_number=2
        )
        progress_entry.mark_completed()

        progress_entry = DailyGoalProgress.objects.get(
            daily_goal=self.daily_goal_member2, day_number=1
        )
        progress_entry.mark_completed()

    def test_get_member_progress(self):
        """Test the get_member_progress method."""

        progress_data = self.club.get_member_progress(self.member1)
        self.assertEqual(progress_data["total_goals"], 1)
        self.assertEqual(progress_data["completed_goals"], 0)
        self.assertAlmostEqual(progress_data["progress_percentage"], 0)

        progress_data = self.club.get_member_progress(self.member2)
        self.assertEqual(progress_data["total_goals"], 1)
        self.assertEqual(progress_data["completed_goals"], 0)
        self.assertAlmostEqual(progress_data["progress_percentage"], 0)

        empty_member = User.objects.create_user(
            email="<EMAIL>", password="password123", username="empty"
        )
        progress_data = self.club.get_member_progress(empty_member)
        self.assertEqual(progress_data["total_goals"], 0)
        self.assertEqual(progress_data["completed_goals"], 0)
        self.assertEqual(progress_data["progress_percentage"], 0)

    def test_get_all_members_progress(self):
        """Test the get_all_members_progress method."""
        progress_data = self.club.get_all_members_progress()

        self.assertAlmostEqual(progress_data["overall_progress"], 0)

        member1_id = str(self.member1.id)
        member2_id = str(self.member2.id)

        self.assertIn(member1_id, progress_data["member_progress"])
        self.assertIn(member2_id, progress_data["member_progress"])

    def test_get_member_activity(self):
        """Test the get_member_activity method."""
        activity_data = self.club.get_member_activity(self.member1)

        self.assertIn("activity_percentage", activity_data)

        activity_data = self.club.get_member_activity(self.member1, days=7)
        self.assertIn("activity_percentage", activity_data)

        empty_member = User.objects.create_user(
            email="<EMAIL>", password="password123", username="inactive"
        )
        activity_data = self.club.get_member_activity(empty_member)
        self.assertEqual(activity_data["activity_percentage"], 0)

    def test_get_all_members_activity(self):
        """Test the get_all_members_activity method."""
        activity_data = self.club.get_all_members_activity()

        self.assertIn("overall_activity", activity_data)

        self.assertIn("active_members_percentage", activity_data)

        member1_id = str(self.member1.id)
        member2_id = str(self.member2.id)

        self.assertIn(member1_id, activity_data["member_activity"])
        self.assertIn(member2_id, activity_data["member_activity"])

        activity_data = self.club.get_all_members_activity(days=7)
        self.assertIn("overall_activity", activity_data)

    def test_get_club_stats(self):
        """Test the get_club_stats method."""
        stats = self.club.get_club_stats()

        self.assertEqual(stats["total_members"], 2)
        self.assertIn("overall_progress", stats)
        self.assertIn("overall_activity", stats)
        self.assertIn("active_members_percentage", stats)

        member1_id = str(self.member1.id)
        member2_id = str(self.member2.id)

        self.assertIn(member1_id, stats["member_stats"])
        self.assertIn(member2_id, stats["member_stats"])

        member1_stats = stats["member_stats"][member1_id]
        self.assertEqual(member1_stats["username"], self.member1.username)
        self.assertIn("progress", member1_stats)
        self.assertIn("activity", member1_stats)

        stats = self.club.get_club_stats(activity_days=7)
        self.assertIn("overall_activity", stats)


class ClubStatsAPITests(TestCase):
    """Test the Club Stats API endpoints."""

    def setUp(self):
        self.client = APIClient()

        self.manager = User.objects.create_user(
            email="<EMAIL>", password="password123", username="manager"
        )
        self.member = User.objects.create_user(
            email="<EMAIL>", password="password123", username="member"
        )
        self.non_member = User.objects.create_user(
            email="<EMAIL>", password="password123", username="nonmember"
        )

        self.club_type = ClubType.objects.create(name="Test Club Type")
        self.club = Club.objects.create(
            name="Test Club",
            type=self.club_type,
            manager=self.manager,
        )
        self.club.members.add(self.member)

        self.domain = Domain.objects.create(
            name="Test Domain", category="personal", description="Test description"
        )

        self.start_date = timezone.now().date()
        self.end_date = self.start_date + timedelta(days=42)

        self.period_member = SixWeekPeriod.objects.create(
            user=self.member,
            title="Test Period Member",
            start_date=self.start_date,
            end_date=self.end_date,
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
        )

        self.goal_member = Goal.objects.create(
            user=self.member,
            six_week_period=self.period_member,
            title="Test Goal Member",
        )

        self.mission_member = WeeklyMission.objects.create(
            user=self.member,
            six_week_period=self.period_member,
            domain=self.domain,
            week_number=2,
            practice_days=[1, 2, 3],
        )
        self.mission_member.goals.add(self.goal_member)

        self.daily_goal_member = DailyGoal.objects.create(
            weekly_mission=self.mission_member,
            title="Daily Goal Member",
        )
        self.daily_goal_member.create_daily_progress_entries()

        progress_entry = DailyGoalProgress.objects.get(
            daily_goal=self.daily_goal_member, day_number=1
        )
        progress_entry.mark_completed()

        self.stats_url = reverse("clubs:club-stats", args=[self.club.id])

    def test_get_club_stats_as_manager(self):
        """Test retrieving club stats as the club manager."""
        self.client.force_authenticate(user=self.manager)
        response = self.client.get(self.stats_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertIn("total_members", response.data)
        self.assertIn("overall_progress", response.data)
        self.assertIn("overall_activity", response.data)
        self.assertIn("active_members_percentage", response.data)
        self.assertIn("member_stats", response.data)

        member_id = str(self.member.id)
        self.assertIn(member_id, response.data["member_stats"])

        member_stats = response.data["member_stats"][member_id]
        self.assertEqual(member_stats["username"], self.member.username)
        self.assertIn("progress", member_stats)
        self.assertIn("activity", member_stats)

        response = self.client.get(f"{self.stats_url}?activity_days=14")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_get_club_stats_as_non_manager(self):
        """Test that non-manager cannot retrieve club stats."""
        self.client.force_authenticate(user=self.member)
        response = self.client.get(self.stats_url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        self.client.force_authenticate(user=self.non_member)
        response = self.client.get(self.stats_url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_get_club_stats_not_authenticated(self):
        """Test that unauthenticated users cannot retrieve club stats."""
        self.client.logout()
        response = self.client.get(self.stats_url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_get_club_stats_nonexistent_club(self):
        """Test retrieving stats for a club that doesn't exist."""
        self.client.force_authenticate(user=self.manager)
        nonexistent_url = reverse("clubs:club-stats", args=[uuid.uuid4()])
        response = self.client.get(nonexistent_url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
