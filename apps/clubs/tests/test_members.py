from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from apps.clubs.models import Club, ClubType, ClubInvitation
from django.contrib.auth import get_user_model
from unittest.mock import patch
from datetime import timedelta
from django.utils import timezone

User = get_user_model()


class ClubMembersAPIViewTest(TestCase):
    def setUp(self):
        self.client = APIClient()

        self.manager = User.objects.create_user(
            username="manager", email="<EMAIL>", password="password123"
        )
        self.member1 = User.objects.create_user(
            username="member1",
            email="<EMAIL>",
            password="password123",
            fullname="Member One",
        )
        self.member2 = User.objects.create_user(
            username="member2",
            email="<EMAIL>",
            password="password123",
            fullname="Member Two",
        )
        self.other_user = User.objects.create_user(
            username="other", email="<EMAIL>", password="password123"
        )

        self.club_type = ClubType.objects.create(name="Test Club Type")
        self.club = Club.objects.create(
            name="Test Club",
            type=self.club_type,
            manager=self.manager,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_INVITE,
        )

        self.invitation1 = ClubInvitation.objects.create(
            user=self.member1,
            club=self.club,
            status=ClubInvitation.STATUS_ACCEPTED,
            email=self.member1.email,
        )

        self.invitation2 = ClubInvitation.objects.create(
            user=self.member2,
            club=self.club,
            status=ClubInvitation.STATUS_PENDING,
            email=self.member2.email,
        )

        self.club.members.add(self.member1)

        self.url = reverse("clubs:club-members", args=[self.club.id])

    @patch("apps.clubs.models.Club.get_member_activity")
    def test_club_members_list_includes_new_fields(self, mock_get_member_activity):
        """Test that the API response includes the new serializer fields"""

        mock_get_member_activity.return_value = {"activity_percentage": 85.5}

        self.client.force_authenticate(user=self.manager)

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertIn("results", response.data)

        self.assertTrue(len(response.data["results"]) > 0)

        member1_data = None
        for member in response.data["results"]:
            if member["email"] == self.member1.email:
                member1_data = member
                break

        self.assertIsNotNone(member1_data)

        self.assertIn("id", member1_data)
        self.assertIn("fullname", member1_data)
        self.assertIn("email", member1_data)
        self.assertIn("status", member1_data)
        self.assertIn("missions_count", member1_data)
        self.assertIn("activity_percentage", member1_data)
        self.assertIn("invitation_date", member1_data)

        self.assertEqual(member1_data["fullname"], self.member1.fullname)
        self.assertEqual(member1_data["email"], self.member1.email)
        self.assertEqual(member1_data["status"], ClubInvitation.STATUS_ACCEPTED)
        self.assertEqual(member1_data["activity_percentage"], 85.5)

    def test_unauthorized_access_forbidden(self):
        """Test that non-managers cannot access the member list"""

        self.client.force_authenticate(user=self.other_user)

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_filter_by_invitation_status(self):
        """Test that members can be filtered by invitation status"""

        self.client.force_authenticate(user=self.manager)

        response = self.client.get(
            f"{self.url}?invitation_status={ClubInvitation.STATUS_PENDING}"
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("results", response.data)

        if len(response.data["results"]) > 0:
            member_data = response.data["results"][0]
            self.assertEqual(member_data["email"], self.member2.email)
            self.assertEqual(member_data["status"], ClubInvitation.STATUS_PENDING)

    def test_invitation_date_field(self):
        """Test that the invitation_date field shows the correct date"""

        now = timezone.now()
        self.invitation1.created = now - timedelta(days=5)
        self.invitation1.updated = now - timedelta(days=2)
        self.invitation1.save()

        self.client.force_authenticate(user=self.manager)

        response = self.client.get(self.url)
        self.assertIn("results", response.data)

        member1_data = None
        for member in response.data["results"]:
            if member["email"] == self.member1.email:
                member1_data = member
                break

        self.assertIsNotNone(member1_data)

        self.assertEqual(member1_data["invitation_date"], self.invitation1.updated)
