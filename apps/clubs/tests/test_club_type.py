from django.test import TestCase
from rest_framework.test import APIClient
from rest_framework import status
from django.urls import reverse
from django.contrib.auth import get_user_model
from apps.clubs.models import ClubType

User = get_user_model()


class ClubTypeViewSetTest(TestCase):
    def setUp(self):
        ClubType.objects.all().delete()

        self.client = APIClient()
        self.user = User.objects.create_user(
            email="<EMAIL>", password="password123", username="testuser"
        )
        self.admin_user = User.objects.create_user(
            email="<EMAIL>", password="password123", username="admin"
        )
        self.admin_user.is_superuser = True
        self.admin_user.save()

        self.other_user = User.objects.create_user(
            email="<EMAIL>", password="password123", username="otheruser"
        )

        self.client.force_authenticate(user=self.user)

        self.club_type = ClubType.objects.create(
            name="Test Type", created_by=self.user, visibility="private"
        )

        self.other_private_type = ClubType.objects.create(
            name="Other Private Type", created_by=self.other_user, visibility="private"
        )

        self.public_type = ClubType.objects.create(
            name="Public Type", created_by=self.other_user, visibility="public"
        )

        self.list_url = reverse("clubs:club-type-list")
        self.retrieve_url = reverse(
            "clubs:club-type-retrieve", args=[self.club_type.id]
        )
        self.update_url = reverse("clubs:club-type-update", args=[self.club_type.id])
        self.destroy_url = reverse("clubs:club-type-destroy", args=[self.club_type.id])
        self.create_url = reverse("clubs:club-type-create")

    def test_list_club_types_regular_user(self):
        self.client.force_authenticate(user=self.user)

        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        response_data = response.json()

        if "results" in response_data:
            results = response_data["results"]
        else:
            results = response_data

        club_type_names = [ct.get("name") for ct in results]

        self.assertIn("Test Type", club_type_names)

        self.assertIn("Public Type", club_type_names)

        self.assertNotIn("Other Private Type", club_type_names)

        self.assertEqual(len(results), 2)

    def test_list_club_types_admin_user(self):
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        response_data = response.json()

        if "results" in response_data:
            results = response_data["results"]
        else:
            results = response_data

        self.assertEqual(len(results), 3)

        club_type_names = [ct.get("name") for ct in results]
        self.assertIn("Test Type", club_type_names)
        self.assertIn("Other Private Type", club_type_names)
        self.assertIn("Public Type", club_type_names)

    def test_create_club_type(self):
        data = {"name": "New Type", "visibility": "public"}
        response = self.client.post(self.create_url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(ClubType.objects.count(), 4)
        self.assertEqual(response.json()["name"], "New Type")
        self.assertEqual(response.json()["visibility"], "public")

        created_club_type = ClubType.objects.get(name="New Type")
        self.assertEqual(str(created_club_type.created_by.id), str(self.user.id))

    def test_retrieve_own_club_type(self):
        response = self.client.get(self.retrieve_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["name"], "Test Type")

    def test_retrieve_public_club_type(self):
        retrieve_url = reverse("clubs:club-type-retrieve", args=[self.public_type.id])
        response = self.client.get(retrieve_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["name"], "Public Type")

    def test_retrieve_other_private_club_type(self):
        retrieve_url = reverse(
            "clubs:club-type-retrieve", args=[self.other_private_type.id]
        )
        response = self.client.get(retrieve_url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_admin_retrieve_any_club_type(self):
        self.client.force_authenticate(user=self.admin_user)

        retrieve_url = reverse(
            "clubs:club-type-retrieve", args=[self.other_private_type.id]
        )
        response = self.client.get(retrieve_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["name"], "Other Private Type")

    def test_partial_update_club_type(self):
        data = {"name": "Partially Updated Type"}
        response = self.client.patch(self.update_url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.club_type.refresh_from_db()
        self.assertEqual(self.club_type.name, "Partially Updated Type")

    def test_update_other_private_club_type(self):
        update_url = reverse(
            "clubs:club-type-update", args=[self.other_private_type.id]
        )
        data = {"name": "Trying to Update Other's Type"}
        response = self.client.patch(update_url, data)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.other_private_type.refresh_from_db()
        self.assertEqual(self.other_private_type.name, "Other Private Type")

    def test_update_public_club_type(self):
        update_url = reverse("clubs:club-type-update", args=[self.public_type.id])
        data = {"name": "Trying to Update Public Type"}
        response = self.client.patch(update_url, data)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.public_type.refresh_from_db()
        self.assertEqual(self.public_type.name, "Public Type")

    def test_admin_update_any_club_type(self):
        self.client.force_authenticate(user=self.admin_user)

        update_url = reverse(
            "clubs:club-type-update", args=[self.other_private_type.id]
        )
        data = {"name": "Admin Updated Private Type"}
        response = self.client.patch(update_url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.other_private_type.refresh_from_db()
        self.assertEqual(self.other_private_type.name, "Admin Updated Private Type")

    def test_delete_club_type(self):
        initial_count = ClubType.objects.count()
        response = self.client.delete(self.destroy_url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(ClubType.objects.count(), initial_count - 1)

    def test_delete_other_private_club_type(self):
        delete_url = reverse(
            "clubs:club-type-destroy", args=[self.other_private_type.id]
        )
        initial_count = ClubType.objects.count()
        response = self.client.delete(delete_url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(ClubType.objects.count(), initial_count)

    def test_delete_public_club_type(self):
        delete_url = reverse("clubs:club-type-destroy", args=[self.public_type.id])
        initial_count = ClubType.objects.count()
        response = self.client.delete(delete_url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(ClubType.objects.count(), initial_count)

    def test_admin_delete_any_club_type(self):
        self.client.force_authenticate(user=self.admin_user)

        delete_url = reverse(
            "clubs:club-type-destroy", args=[self.other_private_type.id]
        )
        initial_count = ClubType.objects.count()
        response = self.client.delete(delete_url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(ClubType.objects.count(), initial_count - 1)

    def test_unauthenticated_access(self):
        self.client.force_authenticate(user=None)

        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        response = self.client.post(self.list_url, {"name": "New Type"})
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        response = self.client.get(self.retrieve_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        response = self.client.put(self.update_url, {"name": "Updated Type"})
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        response = self.client.delete(self.destroy_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
