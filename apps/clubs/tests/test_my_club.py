import pytest
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model
from apps.clubs.models import Club, ClubType
from datetime import timedelta
from django.utils import timezone

User = get_user_model()


@pytest.mark.django_db
class TestMyClubAPIView:
    """
    Test suite for the MyClubAPIView.
    """

    def setup_method(self):
        """Set up test data."""
        self.client = APIClient()

        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpassword",
        )
        self.manager = User.objects.create_user(
            username="manager",
            email="<EMAIL>",
            password="managerpassword",
        )

        self.club_type = ClubType.objects.create(
            name="Test Club Type",
            created_by=self.manager,
        )

        self.old_club = Club.objects.create(
            name="Old Club",
            type=self.club_type,
            manager=self.manager,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_OPEN,
            created=timezone.now() - timedelta(days=10),
        )

        self.recent_club = Club.objects.create(
            name="Recent Club",
            type=self.club_type,
            manager=self.manager,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_OPEN,
            created=timezone.now() - timedelta(days=5),
        )

        self.most_recent_club = Club.objects.create(
            name="Most Recent Club",
            type=self.club_type,
            manager=self.manager,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_OPEN,
            created=timezone.now() - timedelta(days=1),
        )

        self.old_club.members.add(self.user)
        self.recent_club.members.add(self.user)

        self.url = reverse("clubs:my-club")

    def test_my_club_unauthenticated(self):
        """Test that unauthenticated users cannot access the endpoint."""
        response = self.client.get(self.url)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_my_club_as_member(self):
        """Test retrieving the most recent club where the user is a member."""
        self.client.force_authenticate(user=self.user)
        response = self.client.get(self.url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["id"] == str(self.recent_club.id)
        assert response.data["name"] == "Recent Club"

    def test_my_club_as_manager(self):
        """Test retrieving the most recent club where the user is a manager."""
        self.client.force_authenticate(user=self.manager)
        response = self.client.get(self.url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["id"] == str(self.most_recent_club.id)
        assert response.data["name"] == "Most Recent Club"

    def test_my_club_as_both_manager_and_member(self):
        """Test retrieving the most recent club when user is both manager and member."""

        self.most_recent_club.members.add(self.user)

        self.client.force_authenticate(user=self.user)
        response = self.client.get(self.url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["id"] == str(self.most_recent_club.id)
        assert response.data["name"] == "Most Recent Club"

    def test_my_club_no_clubs(self):
        """Test response when user doesn't belong to any club."""
        new_user = User.objects.create_user(
            username="newuser",
            email="<EMAIL>",
            password="newpassword",
        )

        self.client.force_authenticate(user=new_user)
        response = self.client.get(self.url)

        assert response.status_code == status.HTTP_204_NO_CONTENT
        assert "detail" in response.data
        assert response.data["detail"] == "You don't belong to any club."

    def test_my_club_serializer_fields(self):
        """Test that the serializer includes all expected fields."""
        self.client.force_authenticate(user=self.user)
        response = self.client.get(self.url)

        assert response.status_code == status.HTTP_200_OK

        expected_fields = [
            "id",
            "name",
            "icon_url",
            "type",
            "member_ids",
            "manager",
            "privacy",
            "join_permissions",
            "members",
            "members_count",
            "created_at",
            "updated_at",
        ]

        for field in expected_fields:
            assert field in response.data
