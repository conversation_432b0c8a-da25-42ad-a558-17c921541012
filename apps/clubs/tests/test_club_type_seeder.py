from django.test import TestCase
from django.core.management import call_command
from io import StringIO
from apps.clubs.models import ClubType


class ClubTypeSeederTestCase(TestCase):
    """Test case for the ClubType seeder management command."""

    def test_seed_club_types(self):
        """Test that the seed_club_types command creates the correct club types."""

        ClubType.objects.all().delete()

        expected_club_types = [
            "نادي الرياضة",
            "نادي القراءة",
            "نادي الفنون",
            "نادي العلوم",
            "نادي الأعمال",
        ]

        self.assertEqual(ClubType.objects.count(), 0)

        out = StringIO()
        call_command("seed_club_types", stdout=out)

        output = out.getvalue()
        self.assertIn("Starting to seed Arabic club types", output)
        self.assertIn("Successfully seeded 5 new Arabic club types", output)

        self.assertEqual(ClubType.objects.count(), 5)

        for club_type_name in expected_club_types:
            self.assertTrue(
                ClubType.objects.filter(name=club_type_name).exists(),
                f"Club type '{club_type_name}' was not created",
            )

    def test_seed_club_types_idempotent(self):
        """Test that running the seeder twice doesn't create duplicates."""

        ClubType.objects.all().delete()

        call_command("seed_club_types")

        self.assertEqual(ClubType.objects.count(), 5)

        out = StringIO()
        call_command("seed_club_types", stdout=out)

        self.assertEqual(ClubType.objects.count(), 5)

        output = out.getvalue()
        self.assertIn("Successfully seeded 0 new Arabic club types", output)
