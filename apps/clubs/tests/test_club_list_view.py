from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
import uuid
from datetime import timedelta, date

from apps.clubs.models import Club, ClubType, ClubInvitation
from apps.clubs.filters.club_filters import ClubFilter

User = get_user_model()


class ClubListViewTests(TestCase):
    """Test suite for Club list view"""

    def setUp(self):
        """Set up test data"""

        self.admin_user = User.objects.create_user(
            email="<EMAIL>",
            password="password123",
            username="admin_user",
            role="admin",
            is_staff=True,
        )

        self.manager1 = User.objects.create_user(
            email="<EMAIL>",
            password="password123",
            username="manager1",
            role="club_manager",
        )

        self.manager2 = User.objects.create_user(
            email="<EMAIL>",
            password="password123",
            username="manager2",
            role="club_manager",
        )

        self.member1 = User.objects.create_user(
            email="<EMAIL>",
            password="password123",
            username="member1",
        )

        self.member2 = User.objects.create_user(
            email="<EMAIL>",
            password="password123",
            username="member2",
        )

        self.type1 = ClubType.objects.create(name="Sports Club")
        self.type2 = ClubType.objects.create(name="Book Club")

        self.club1 = Club.objects.create(
            name="Club One",
            manager=self.manager1,
            type=self.type1,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_OPEN,
        )
        self.club1.members.add(self.member1)

        self.club2 = Club.objects.create(
            name="Club Two",
            manager=self.manager1,
            type=self.type2,
            privacy=Club.PRIVACY_CLOSED,
            join_permissions=Club.JOIN_PERMISSIONS_INVITE,
        )
        self.club2.members.add(self.member1)

        self.club3 = Club.objects.create(
            name="Club Three",
            manager=self.manager2,
            type=self.type1,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_INVITE,
            created=timezone.now() - timedelta(days=30),
        )
        self.club3.members.add(self.member2)

        self.club4 = Club.objects.create(
            name="Secret Club",
            manager=self.manager2,
            type=self.type2,
            privacy=Club.PRIVACY_CLOSED,
            join_permissions=Club.JOIN_PERMISSIONS_INVITE,
        )

        self.client = APIClient()

        self.url = reverse("clubs:club-list")

    def test_admin_can_see_all_clubs(self):
        """Test that admin users can see all clubs"""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 4)

    def test_manager_can_see_only_managed_clubs(self):
        """Test that club managers can see only clubs they manage"""
        self.client.force_authenticate(user=self.manager1)

        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 2)

        club_ids = {str(club["id"]) for club in results}
        expected_ids = {str(self.club1.id), str(self.club2.id)}
        self.assertEqual(club_ids, expected_ids)

    def test_regular_user_sees_member_and_public_clubs(self):
        """Test that regular users see clubs they're members of and public clubs"""
        self.client.force_authenticate(user=self.member1)

        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)

        self.assertEqual(len(results), 3)

        club_ids = {str(club["id"]) for club in results}
        expected_ids = {str(self.club1.id), str(self.club2.id), str(self.club3.id)}
        self.assertEqual(club_ids, expected_ids)

    def test_unauthenticated_user_cannot_access(self):
        """Test that unauthenticated users cannot access the view"""
        self.client.logout()

        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_filter_by_name(self):
        """Test filtering clubs by name"""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(f"{self.url}?name=Club One")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["name"], "Club One")

    def test_filter_by_privacy(self):
        """Test filtering clubs by privacy setting"""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(f"{self.url}?privacy={Club.PRIVACY_OPEN}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 2)

        for club in results:
            self.assertEqual(club["privacy"], Club.PRIVACY_OPEN)

    def test_filter_by_type(self):
        """Test filtering clubs by type"""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(f"{self.url}?type={self.type1.id}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 2)

        for club in results:
            self.assertEqual(club["type"]["id"], str(self.type1.id))

    def test_filter_by_manager(self):
        """Test filtering clubs by manager"""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(f"{self.url}?manager={self.manager1.id}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 2)

        club_ids = {str(club["id"]) for club in results}
        expected_ids = {str(self.club1.id), str(self.club2.id)}
        self.assertEqual(club_ids, expected_ids)

    def test_filter_by_member(self):
        """Test filtering clubs by member"""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(f"{self.url}?member={self.member1.id}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 2)

        club_ids = {str(club["id"]) for club in results}
        expected_ids = {str(self.club1.id), str(self.club2.id)}
        self.assertEqual(club_ids, expected_ids)

    def test_filter_by_created_date(self):
        """Test filtering clubs by creation date"""
        self.client.force_authenticate(user=self.admin_user)

        fifteen_days_ago = (timezone.now() - timedelta(days=15)).date().isoformat()

        response = self.client.get(f"{self.url}?created_before={fifteen_days_ago}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["name"], "Club Three")

    def test_search_functionality(self):
        """Test searching clubs by name or type name"""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(f"{self.url}?search=Sports")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 2)

        for club in results:
            self.assertEqual(club["type"]["name"], "Sports Club")

    def test_ordering(self):
        """Test ordering clubs by name and created date"""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(f"{self.url}?ordering=name")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 4)
        self.assertEqual(results[0]["name"], "Club One")
        self.assertEqual(results[1]["name"], "Club Three")
        self.assertEqual(results[2]["name"], "Club Two")
        self.assertEqual(results[3]["name"], "Secret Club")

        response = self.client.get(f"{self.url}?ordering=-created")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 4)

        self.assertEqual(results[3]["name"], "Club Three")

    def test_combined_filters(self):
        """Test combining multiple filters"""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(
            f"{self.url}?type={self.type1.id}&privacy={Club.PRIVACY_OPEN}&ordering=name"
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0]["name"], "Club One")
        self.assertEqual(results[1]["name"], "Club Three")

    def _get_results_from_response(self, response):
        """Helper method to extract results from paginated response"""
        if "results" in response.data:
            return response.data["results"]
        return response.data
