from django.test import TestCase
from rest_framework.test import APIClient
from rest_framework import status
from django.urls import reverse
from django.contrib.auth import get_user_model
from apps.clubs.models import Club, ClubType

User = get_user_model()


class ClubPermissionsTest(TestCase):
    def setUp(self):
        self.client = APIClient()

        self.manager = User.objects.create_user(
            email="<EMAIL>", password="password123", username="manager"
        )
        self.member = User.objects.create_user(
            email="<EMAIL>", password="password123", username="member"
        )
        self.non_member = User.objects.create_user(
            email="<EMAIL>", password="password123", username="nonmember"
        )

        self.club_type = ClubType.objects.create(name="Test Type")
        self.club = Club.objects.create(
            name="Test Club", type=self.club_type, manager=self.manager
        )

        self.club.members.add(self.member)

        self.invite_url = reverse("clubs:invite-members", args=[self.club.id])
        self.remove_member_url = reverse(
            "clubs:remove-member", kwargs={"club_id": self.club.id}
        )

    def test_manager_can_invite(self):
        """Test that the club manager can invite members"""
        self.client.force_authenticate(user=self.manager)
        data = {"emails": ["<EMAIL>"]}

        response = self.client.post(self.invite_url, data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_member_cannot_invite(self):
        """Test that a regular member cannot invite others"""
        self.client.force_authenticate(user=self.member)
        data = {"emails": ["<EMAIL>"]}

        response = self.client.post(self.invite_url, data)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_non_member_cannot_invite(self):
        """Test that a non-member cannot invite others"""
        self.client.force_authenticate(user=self.non_member)
        data = {"emails": ["<EMAIL>"]}

        response = self.client.post(self.invite_url, data)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_unauthenticated_cannot_invite(self):
        """Test that unauthenticated users cannot invite others"""
        self.client.force_authenticate(user=None)
        data = {"emails": ["<EMAIL>"]}

        response = self.client.post(self.invite_url, data)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_manager_can_remove_member(self):
        """Test that the club manager can remove members"""
        self.client.force_authenticate(user=self.manager)
        data = {"member_id": str(self.member.id)}

        response = self.client.post(self.remove_member_url, data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertNotIn(self.member, self.club.members.all())

    def test_member_cannot_remove_member(self):
        """Test that a regular member cannot remove others"""
        self.client.force_authenticate(user=self.member)
        data = {"member_id": str(self.member.id)}

        response = self.client.post(self.remove_member_url, data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_non_member_cannot_remove_member(self):
        """Test that a non-member cannot remove others"""
        self.client.force_authenticate(user=self.non_member)
        data = {"member_id": str(self.member.id)}

        response = self.client.post(self.remove_member_url, data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_unauthenticated_cannot_remove_member(self):
        """Test that unauthenticated users cannot remove others"""
        self.client.force_authenticate(user=None)
        data = {"member_id": str(self.member.id)}

        response = self.client.post(self.remove_member_url, data)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
