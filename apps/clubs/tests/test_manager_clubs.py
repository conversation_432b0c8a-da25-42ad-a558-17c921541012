import uuid
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status

from apps.clubs.models import Club, ClubType

User = get_user_model()


class ManagerClubsApiViewTest(TestCase):
    """
    Test suite for the ManagerClubsAPIView.
    """

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()

        self.admin_user = User.objects.create_user(
            email="<EMAIL>",
            username="admin",
            password="testpass123",
            is_staff=True,
        )
        self.manager_user = User.objects.create_user(
            email="<EMAIL>", username="manager", password="testpass123"
        )
        self.other_user = User.objects.create_user(
            email="<EMAIL>", username="other", password="testpass123"
        )

        self.club_type = ClubType.objects.create(name="Test Club Type")

        self.club1 = Club.objects.create(
            name=f"Manager Club 1 {uuid.uuid4()}",
            manager=self.manager_user,
            type=self.club_type,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_OPEN,
        )

        self.club2 = Club.objects.create(
            name=f"Manager Club 2 {uuid.uuid4()}",
            manager=self.manager_user,
            type=self.club_type,
            privacy=Club.PRIVACY_CLOSED,
            join_permissions=Club.JOIN_PERMISSIONS_INVITE,
        )

        self.other_club = Club.objects.create(
            name=f"Other Club {uuid.uuid4()}",
            manager=self.other_user,
            type=self.club_type,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_OPEN,
        )

        self.manager_clubs_url = reverse(
            "clubs:manager-clubs", kwargs={"manager_id": self.manager_user.id}
        )
        self.other_manager_clubs_url = reverse(
            "clubs:manager-clubs", kwargs={"manager_id": self.other_user.id}
        )
        self.nonexistent_manager_url = reverse(
            "clubs:manager-clubs", kwargs={"manager_id": uuid.uuid4()}
        )

    def test_manager_clubs_as_admin_successful(self):
        """Test that admin can view clubs managed by a specific user."""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(self.manager_clubs_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 2)

        club_ids = [club["id"] for club in response.data["results"]]
        self.assertIn(str(self.club1.id), club_ids)
        self.assertIn(str(self.club2.id), club_ids)

        self.assertNotIn(str(self.other_club.id), club_ids)

    def test_manager_clubs_unauthenticated(self):
        """Test that unauthenticated users cannot access manager clubs."""
        response = self.client.get(self.manager_clubs_url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_manager_clubs_non_admin_forbidden(self):
        """Test that non-admin users cannot access manager clubs."""
        self.client.force_authenticate(user=self.manager_user)

        response = self.client.get(self.manager_clubs_url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_manager_clubs_nonexistent_manager(self):
        """Test getting clubs for a non-existent manager."""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(self.nonexistent_manager_url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_manager_clubs_other_manager(self):
        """Test admin viewing clubs for a different manager."""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(self.other_manager_clubs_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)

        club_ids = [club["id"] for club in response.data["results"]]
        self.assertIn(str(self.other_club.id), club_ids)

        self.assertNotIn(str(self.club1.id), club_ids)
        self.assertNotIn(str(self.club2.id), club_ids)

    def test_manager_clubs_search_filter(self):
        """Test searching for clubs by name."""

        search_name = f"SearchableClub-{uuid.uuid4()}"
        Club.objects.create(
            name=search_name,
            manager=self.manager_user,
            type=self.club_type,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_OPEN,
        )

        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(f"{self.manager_clubs_url}?search=SearchableClub")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["name"], search_name)

    def test_manager_clubs_ordering(self):
        """Test ordering clubs by name."""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(f"{self.manager_clubs_url}?ordering=name")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        names = [club["name"] for club in response.data["results"]]
        self.assertEqual(names, sorted(names))

        response = self.client.get(f"{self.manager_clubs_url}?ordering=-name")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        names = [club["name"] for club in response.data["results"]]
        self.assertEqual(names, sorted(names, reverse=True))
