import uuid
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status

from apps.clubs.models import Club, ClubType

User = get_user_model()


class MyClubsApiViewTest(TestCase):
    """
    Test suite for the MyClubsAPIView.
    """

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()

        self.user = User.objects.create_user(
            email="<EMAIL>", username="testuser", password="testpass123"
        )
        self.other_user = User.objects.create_user(
            email="<EMAIL>", username="otheruser", password="testpass123"
        )

        self.club_type = ClubType.objects.create(name="Test Club Type")

        self.club1 = Club.objects.create(
            name=f"Test Club 1 {uuid.uuid4()}",
            manager=self.user,
            type=self.club_type,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_OPEN,
        )

        self.club2 = Club.objects.create(
            name=f"Test Club 2 {uuid.uuid4()}",
            manager=self.user,
            type=self.club_type,
            privacy=Club.PRIVACY_CLOSED,
            join_permissions=Club.JOIN_PERMISSIONS_INVITE,
        )

        self.other_club = Club.objects.create(
            name=f"Other Test Club {uuid.uuid4()}",
            manager=self.other_user,
            type=self.club_type,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_OPEN,
        )

        self.my_clubs_url = reverse("clubs:my-clubs")

    def test_my_clubs_successful(self):
        """Test getting my clubs successfully."""
        self.client.force_authenticate(user=self.user)

        response = self.client.get(self.my_clubs_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 2)

        club_ids = [club["id"] for club in response.data["results"]]
        self.assertIn(str(self.club1.id), club_ids)
        self.assertIn(str(self.club2.id), club_ids)

        self.assertNotIn(str(self.other_club.id), club_ids)

    def test_my_clubs_unauthenticated(self):
        """Test that unauthenticated users cannot access my clubs."""
        response = self.client.get(self.my_clubs_url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_my_clubs_other_user(self):
        """Test that a different user only sees their own clubs."""
        self.client.force_authenticate(user=self.other_user)

        response = self.client.get(self.my_clubs_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)

        club_ids = [club["id"] for club in response.data["results"]]
        self.assertIn(str(self.other_club.id), club_ids)

        self.assertNotIn(str(self.club1.id), club_ids)
        self.assertNotIn(str(self.club2.id), club_ids)

    def test_my_clubs_search_filter(self):
        """Test searching for clubs by name."""

        search_name = f"SearchableClub-{uuid.uuid4()}"
        Club.objects.create(
            name=search_name,
            manager=self.user,
            type=self.club_type,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_OPEN,
        )

        self.client.force_authenticate(user=self.user)

        response = self.client.get(f"{self.my_clubs_url}?search=SearchableClub")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["name"], search_name)

    def test_my_clubs_ordering(self):
        """Test ordering clubs by name."""
        self.client.force_authenticate(user=self.user)

        response = self.client.get(f"{self.my_clubs_url}?ordering=name")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        names = [club["name"] for club in response.data["results"]]
        self.assertEqual(names, sorted(names))

        response = self.client.get(f"{self.my_clubs_url}?ordering=-name")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        names = [club["name"] for club in response.data["results"]]
        self.assertEqual(names, sorted(names, reverse=True))
