import uuid
from datetime import timedelta

from django.test import TestCase, override_settings
from django.urls import reverse
from django.utils import timezone
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status

from apps.clubs.models import Club, ClubType, ClubInvitation
from apps.domains.models import Domain
from apps.missions.models import (
    Goal,
    DailyGoal,
    DailyGoalProgress,
    WeeklyMission,
)
from apps.okrs.models import SixWeekPeriod

User = get_user_model()


@override_settings(CLUB_MANAGER_CAN_CREATE_X_CLUBS=100)
class ClubE2ETest(TestCase):
    """
    End-to-end tests for the Club functionality.
    This test covers the entire workflow from club creation to statistics verification.
    """

    def setUp(self):
        """Set up the test environment with necessary users and data."""

        self.manager = User.objects.create_user(
            email="<EMAIL>", password="password123", username="manager"
        )
        self.member1 = User.objects.create_user(
            email="<EMAIL>", password="password123", username="member1"
        )
        self.member2 = User.objects.create_user(
            email="<EMAIL>", password="password123", username="member2"
        )

        self.invited_email = "<EMAIL>"

        self.club_type = ClubType.objects.create(name="Test Club Type")

        self.domain = Domain.objects.create(
            name="Test Domain", category="personal", description="Test description"
        )

        self.start_date = timezone.now().date()
        self.end_date = self.start_date + timedelta(days=42)

        self.client = APIClient()

        self.club_create_url = reverse("clubs:club-create")
        self.club_type_list_url = reverse("clubs:club-type-list")

    def test_club_full_workflow(self):
        """
        Test the complete workflow of club functionality:
        1. Create a club
        2. Invite members
        3. Create six-week periods for members
        4. Create weekly missions
        5. Create daily goals
        6. Update goal priorities
        7. Mark some goals as completed
        8. Verify club statistics
        """

        self.client.force_authenticate(user=self.manager)

        club_data = {
            "name": f"Test Club {uuid.uuid4()}",
            "type": str(self.club_type.id),
            "privacy": Club.PRIVACY_OPEN,
            "join_permissions": Club.JOIN_PERMISSIONS_OPEN,
            "members": [
                {"email": self.member1.email, "name": "Member One"},
                {"email": self.member2.email, "name": "Member Two"},
                {"email": self.invited_email, "name": "Invited User"},
            ],
        }

        response = self.client.post(self.club_create_url, club_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        club_id = response.data["id"]
        club = Club.objects.get(id=club_id)

        self.assertEqual(club.name, club_data["name"])
        self.assertEqual(club.manager, self.manager)

        self.assertEqual(club.members.count(), 3)
        self.assertIn(self.member1, club.members.all())
        self.assertIn(self.member2, club.members.all())

        invited_user = User.objects.get(email=self.invited_email)
        self.assertIn(invited_user, club.members.all())

        self.assertEqual(ClubInvitation.objects.filter(club=club).count(), 3)

        self.client.force_authenticate(user=self.member1)

        period_url = reverse("okrs:period-list")
        period_data = {
            "title": "Test Period Member 1",
            "description": "Test description",
            "start_date": self.start_date.isoformat(),
            "end_date": self.end_date.isoformat(),
            "is_public": True,
        }

        response = self.client.post(period_url, period_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        period1_id = response.data["id"]
        period1 = SixWeekPeriod.objects.get(id=period1_id)

        start_period_url = reverse("okrs:period-start", args=[period1_id])
        response = self.client.post(start_period_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        goal_url = reverse("missions:goal-list")
        goal_data = {
            "six_week_period": period1_id,
            "title": "Test Goal Member 1",
            "priority": "high",
        }

        response = self.client.post(goal_url, goal_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        goal1_id = response.data["id"]
        goal1 = Goal.objects.get(id=goal1_id)

        mission_url = reverse("missions:weekly-mission-list")
        mission_data = {
            "six_week_period": period1_id,
            "domain": str(self.domain.id),
            "week_number": 2,
            "practice_days": [1, 3, 5],
            "goal_ids": [goal1_id],
        }

        response = self.client.post(mission_url, mission_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        mission1_id = response.data["id"]
        mission1 = WeeklyMission.objects.get(id=mission1_id)

        daily_goal_url = reverse("missions:daily-goal-list")
        daily_goal_data = {
            "weekly_mission": mission1_id,
            "title": "Daily Goal Member 1",
        }

        response = self.client.post(daily_goal_url, daily_goal_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        daily_goal1_id = response.data["id"]
        daily_goal1 = DailyGoal.objects.get(id=daily_goal1_id)

        progress_entries = DailyGoalProgress.objects.filter(daily_goal=daily_goal1)
        self.assertEqual(progress_entries.count(), 3)

        for day_number in [1, 3]:
            update_progress_url = reverse(
                "missions:daily-goal-update-progress", args=[daily_goal1_id]
            )
            progress_data = {"day_number": day_number, "completed": True}

            response = self.client.post(
                update_progress_url, progress_data, format="json"
            )
            self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.client.force_authenticate(user=self.member2)

        period_data = {
            "title": "Test Period Member 2",
            "description": "Test description",
            "start_date": self.start_date.isoformat(),
            "end_date": self.end_date.isoformat(),
            "is_public": True,
        }

        response = self.client.post(period_url, period_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        period2_id = response.data["id"]

        start_period_url = reverse("okrs:period-start", args=[period2_id])
        response = self.client.post(start_period_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        goal_data = {
            "six_week_period": period2_id,
            "title": "Test Goal Member 2",
            "priority": "high",
        }

        response = self.client.post(goal_url, goal_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        goal2_id = response.data["id"]

        mission_data = {
            "six_week_period": period2_id,
            "domain": str(self.domain.id),
            "week_number": 2,
            "practice_days": [1, 2, 3, 4, 5],
            "goal_ids": [goal2_id],
        }

        response = self.client.post(mission_url, mission_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        mission2_id = response.data["id"]

        daily_goal_data = {
            "weekly_mission": mission2_id,
            "title": "Daily Goal Member 2",
        }

        response = self.client.post(daily_goal_url, daily_goal_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        daily_goal2_id = response.data["id"]

        update_progress_url = reverse(
            "missions:daily-goal-update-progress", args=[daily_goal2_id]
        )
        progress_data = {"day_number": 1, "completed": True}

        response = self.client.post(update_progress_url, progress_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.client.force_authenticate(user=self.manager)
        stats_url = reverse("clubs:club-stats", args=[club_id])
        response = self.client.get(stats_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        stats_data = response.data

        self.assertEqual(stats_data["total_members"], 3)
        self.assertIn("overall_progress", stats_data)
        self.assertIn("overall_activity", stats_data)
        self.assertIn("active_members_percentage", stats_data)

        member1_id = str(self.member1.id)
        member2_id = str(self.member2.id)

        self.assertIn(member1_id, stats_data["member_stats"])
        self.assertIn(member2_id, stats_data["member_stats"])

        member1_stats = stats_data["member_stats"][member1_id]
        member2_stats = stats_data["member_stats"][member2_id]

        self.assertGreater(member1_stats["progress"], member2_stats["progress"])

        response = self.client.get(f"{stats_url}?activity_days=14")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.client.force_authenticate(user=self.member1)
        response = self.client.get(stats_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
