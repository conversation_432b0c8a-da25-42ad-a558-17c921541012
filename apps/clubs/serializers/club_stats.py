from rest_framework import serializers
from django.contrib.auth import get_user_model
from apps.clubs.models import Club

User = get_user_model()


class MemberStatsSerializer(serializers.Serializer):
    """Serializer for individual member statistics"""

    member_id = serializers.CharField()
    username = serializers.CharField()
    fullname = serializers.CharField()
    email = serializers.EmailField()
    progress = serializers.FloatField()
    activity = serializers.FloatField()


class ClubStatsSerializer(serializers.Serializer):
    """Serializer for club statistics"""

    id = serializers.CharField(source="pk")
    name = serializers.CharField()

    # Note: These fields will be populated in to_representation

    total_members = serializers.IntegerField(read_only=True, required=False)
    overall_progress = serializers.FloatField(read_only=True, required=False)
    overall_activity = serializers.FloatField(read_only=True, required=False)
    active_members_percentage = serializers.FloatField(read_only=True, required=False)
    member_stats = serializers.DictField(
        child=MemberStatsSerializer(), read_only=True, required=False
    )

    def to_representation(self, instance):
        """
        Override the representation to include calculated statistics
        """

        ret = {
            "id": str(instance.pk),
            "name": instance.name,
        }

        activity_days = self.context.get("activity_days", 30)
        stats = instance.get_club_stats(activity_days=activity_days)

        ret.update(
            {
                "total_members": stats["total_members"],
                "overall_progress": stats["overall_progress"],
                "overall_activity": stats["overall_activity"],
                "active_members_percentage": stats["active_members_percentage"],
                "member_stats": stats["member_stats"],
            }
        )

        return ret
