from rest_framework import serializers
from rest_framework.exceptions import ValidationError
from django.contrib.auth import get_user_model
from django.conf import settings
from django.utils.translation import gettext_lazy as _

from core.tasks.emails import send_email_with_html
from apps.clubs.models import Club

User = get_user_model()


class ClubSerializer(serializers.ModelSerializer):
    manager = serializers.ReadOnlyField(source="manager.id")
    members = serializers.ListField(child=serializers.EmailField(), write_only=True)
    member_ids = serializers.PrimaryKeyRelatedField(
        many=True, read_only=True, source="members"
    )
    icon_url = serializers.SerializerMethodField()

    class Meta:
        model = Club
        fields = [
            "id",
            "name",
            "icon_url",
            "type",
            "members",
            "member_ids",
            "manager",
            "privacy",
            "join_permissions",
        ]

    def get_icon_url(self, obj):
        if not obj.icon:
            return None

        request = self.context.get("request")
        if request:
            return request.build_absolute_uri(f"{settings.MEDIA_URL}{obj.icon}")
        return f"{settings.SITE_URL}{settings.MEDIA_URL}{obj.icon}"

    def validate_members(self, emails):
        if not emails:
            raise ValidationError(_("Members list cannot be empty."))
        return emails

    def create(self, validated_data):
        members_emails = validated_data.pop("members")
        validated_data["members"] = []
        club = super().create(validated_data)
        return club
