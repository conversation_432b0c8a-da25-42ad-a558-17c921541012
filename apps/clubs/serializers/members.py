from rest_framework import serializers
from apps.accounts.user.models import User
from apps.clubs.models import ClubInvitation


class ClubMembersSerializer(serializers.ModelSerializer):
    status = serializers.SerializerMethodField()
    missions_count = serializers.SerializerMethodField()
    activity_percentage = serializers.SerializerMethodField()
    invitation_date = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            "id",
            "fullname",
            "email",
            "status",
            "missions_count",
            "activity_percentage",
            "invitation_date",
        ]

    def get_status(self, obj):
        invitation = ClubInvitation.objects.filter(
            user=obj, club=self.context["club"]
        ).first()
        return invitation.status if invitation else None

    def get_missions_count(self, obj):
        """Return the count of missions for this user"""
        return obj.weekly_missions.count()

    def get_activity_percentage(self, obj):
        """Return the activity percentage for this user in the club"""
        club = self.context["club"]
        activity_data = club.get_member_activity(obj)
        return activity_data.get("activity_percentage", 0)

    def get_invitation_date(self, obj):
        """Return the date when the user was invited or joined the club"""
        invitation = ClubInvitation.objects.filter(
            user=obj, club=self.context["club"]
        ).first()

        if invitation:
            if invitation.status == ClubInvitation.STATUS_ACCEPTED:
                return invitation.updated

            return invitation.created

        return None
