from rest_framework import serializers
from django.utils.translation import gettext as _
from apps.clubs.models import Club


class RemoveMemberSerializer(serializers.Serializer):
    member_id = serializers.CharField()

    def validate(self, data):
        club = Club.objects.filter(pk=self.context["club_id"]).first()
        if not club:
            raise serializers.ValidationError(_("Club not found."))

        member = club.members.filter(pk=data["member_id"]).first()
        if not member:
            raise serializers.ValidationError(_("Member not found in the club."))

        if self.context["request"].user != club.manager:
            raise serializers.ValidationError(_("Only the manager can remove members."))

        return data
