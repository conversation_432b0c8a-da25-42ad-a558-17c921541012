from apps.accounts.user.serializers.user import UserSerializer
from apps.clubs.serializers.club import ClubSerializer
from rest_framework import serializers
from apps.clubs.models import ClubInvitation


class ClubInvitationSerializer(serializers.ModelSerializer):
    club = ClubSerializer(read_only=True)
    user = UserSerializer(read_only=True)

    class Meta:
        model = ClubInvitation
        fields = "__all__"
