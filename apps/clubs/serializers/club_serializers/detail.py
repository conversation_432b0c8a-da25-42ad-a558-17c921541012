from rest_framework import serializers
from django.contrib.auth import get_user_model
from apps.clubs.serializers.club_serializers.base import ClubBaseSerializer
from apps.clubs.models import ClubType

User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    id = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ["id", "username", "email", "fullname"]

    def get_id(self, obj):
        return str(obj.id)


class ClubDetailSerializer(ClubBaseSerializer):
    type = serializers.SerializerMethodField()
    manager = serializers.SerializerMethodField()
    members_count = serializers.SerializerMethodField()
    members = serializers.SerializerMethodField()
    created_at = serializers.DateTimeField(source="created", read_only=True)
    updated_at = serializers.DateTimeField(source="updated", read_only=True)

    class Meta(ClubBaseSerializer.Meta):
        fields = ClubBaseSerializer.Meta.fields + [
            "members",
            "members_count",
            "created_at",
            "updated_at",
        ]

    def get_type(self, obj):
        return {"id": str(obj.type.id), "name": obj.type.name}

    def get_manager(self, obj):
        return {
            "id": str(obj.manager.id),
            "username": obj.manager.username,
            "email": obj.manager.email,
        }

    def get_members_count(self, obj):
        return obj.members.count()

    def get_members(self, obj):
        members = obj.members.all()
        return UserSerializer(members, many=True).data
