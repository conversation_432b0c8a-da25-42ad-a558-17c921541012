from rest_framework import serializers
from rest_framework.exceptions import ValidationError
import json
from django.contrib.auth import get_user_model
from apps.clubs.serializers.club_serializers.base import ClubBaseSerializer
from django.utils.translation import gettext_lazy as _

User = get_user_model()


class MemberSerializer(serializers.Serializer):
    email = serializers.EmailField()
    name = serializers.CharField(required=False, allow_blank=True)


class ClubCreateSerializer(ClubBaseSerializer):
    members = serializers.JSONField(required=False)
    icon = serializers.ImageField(required=False)

    class Meta(ClubBaseSerializer.Meta):
        fields = ClubBaseSerializer.Meta.fields + ["members", "icon"]

    def validate_members(self, members_data):
        if not members_data:
            return []

        if isinstance(members_data, str):
            try:
                members_data = json.loads(members_data)
            except (json.JSONDecodeError, TypeError):
                raise ValidationError(_("Invalid JSON string for members"))

        if all(
            isinstance(item, (int, str)) and not isinstance(item, dict)
            for item in members_data
        ):
            validated_members = []
            for user_id in members_data:

                if isinstance(user_id, str) and "@" in user_id:
                    member_serializer = MemberSerializer(data={"email": user_id})
                    if member_serializer.is_valid():
                        validated_members.append(member_serializer.validated_data)
                    else:
                        raise ValidationError(f"Invalid email: {user_id}")
                    continue

                try:
                    user = User.objects.get(id=user_id)
                    validated_members.append(user.id)
                except User.DoesNotExist:
                    raise ValidationError(f"User with ID {user_id} does not exist")
            return validated_members

        validated_members = []
        for member in members_data:
            if isinstance(member, dict):
                if "id" in member:
                    try:
                        user = User.objects.get(id=member["id"])
                        validated_members.append(user.id)
                    except User.DoesNotExist:
                        raise ValidationError(
                            f"User with ID {member['id']} does not exist"
                        )
                elif "user" in member:
                    try:
                        user = User.objects.get(id=member["user"])
                        validated_members.append(user.id)
                    except User.DoesNotExist:
                        raise ValidationError(
                            f"User with ID {member['user']} does not exist"
                        )
                elif "email" in member:

                    member_serializer = MemberSerializer(data=member)
                    if member_serializer.is_valid():
                        validated_members.append(member_serializer.validated_data)
                    else:
                        raise ValidationError(
                            f"Invalid member data: {member_serializer.errors}"
                        )
                else:

                    raise ValidationError(
                        "Member dictionaries must contain 'id', 'user', or 'email' key"
                    )
            elif isinstance(member, str) and "@" in member:
                member_serializer = MemberSerializer(data={"email": member})
                if member_serializer.is_valid():
                    validated_members.append(member_serializer.validated_data)
                else:
                    raise ValidationError(f"Invalid email: {member}")
            else:
                raise ValidationError(
                    "Each member must be a user ID, an email string, or an object with id/user/email field"
                )

        return validated_members

    def create(self, validated_data):
        members_data = validated_data.pop("members", [])
        club = super().create(validated_data)

        for member_data in members_data:

            if isinstance(member_data, int) or (
                isinstance(member_data, str) and member_data.isdigit()
            ):
                try:
                    user = User.objects.get(id=member_data)
                    club.members.add(user)
                except User.DoesNotExist:

                    continue

            elif isinstance(member_data, dict) and "email" in member_data:

                pass

        return club
