from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.conf import settings

from apps.clubs.models import Club

User = get_user_model()


class ClubBaseSerializer(serializers.ModelSerializer):
    manager = serializers.ReadOnlyField(source="manager.id")
    member_ids = serializers.PrimaryKeyRelatedField(
        many=True, read_only=True, source="members"
    )
    icon_url = serializers.SerializerMethodField()

    class Meta:
        model = Club
        fields = [
            "id",
            "name",
            "icon_url",
            "type",
            "member_ids",
            "manager",
            "privacy",
            "join_permissions",
        ]

    def get_icon_url(self, obj):
        if not obj.icon:
            return None

        request = self.context.get("request")
        if request:
            return request.build_absolute_uri(f"{settings.MEDIA_URL}{obj.icon}")
        return f"{settings.SITE_URL}{settings.MEDIA_URL}{obj.icon}"
