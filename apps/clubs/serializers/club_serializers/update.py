from rest_framework import serializers
import json
from django.contrib.auth import get_user_model
from rest_framework.exceptions import ValidationError
from apps.clubs.serializers.club_serializers.base import ClubBaseSerializer
from django.utils.translation import gettext_lazy as _

User = get_user_model()


class ClubUpdateSerializer(ClubBaseSerializer):
    icon = serializers.ImageField(required=False)
    members = serializers.JSONField(required=False)

    class Meta(ClubBaseSerializer.Meta):
        read_only_fields = ["manager"]
        fields = ClubBaseSerializer.Meta.fields + ["members", "icon"]

    def validate_members(self, members_data):
        if not members_data:
            return []

        if isinstance(members_data, str):
            try:
                members_data = json.loads(members_data)
            except (json.JSONDecodeError, TypeError):
                raise ValidationError(_("Invalid JSON string for members"))

        if all(
            isinstance(item, (int, str)) and not isinstance(item, dict)
            for item in members_data
        ):
            validated_members = []
            for user_id in members_data:
                try:
                    user = User.objects.get(id=user_id)
                    validated_members.append(user.id)
                except User.DoesNotExist:
                    raise ValidationError(f"User with ID {user_id} does not exist")
            return validated_members

        validated_members = []
        for member in members_data:
            if isinstance(member, dict):
                if "id" in member:
                    try:
                        user = User.objects.get(id=member["id"])
                        validated_members.append(user.id)
                    except User.DoesNotExist:
                        raise ValidationError(
                            f"User with ID {member['id']} does not exist"
                        )
                elif "user" in member:
                    try:
                        user = User.objects.get(id=member["user"])
                        validated_members.append(user.id)
                    except User.DoesNotExist:
                        raise ValidationError(
                            f"User with ID {member['user']} does not exist"
                        )
                else:

                    raise ValidationError(
                        "Member dictionaries must contain 'id' or 'user' key"
                    )
            else:
                raise ValidationError(
                    "Each member must be either a user ID or an object with an id/user field"
                )

        return validated_members

    def update(self, instance, validated_data):
        members_data = validated_data.pop("members", None)

        club = super().update(instance, validated_data)

        if members_data is not None:

            club.members.clear()
            for user_id in members_data:
                try:
                    user = User.objects.get(id=user_id)
                    club.members.add(user)
                except User.DoesNotExist:

                    continue

        return club
