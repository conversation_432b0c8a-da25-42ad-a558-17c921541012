from rest_framework import serializers
from django.contrib.auth import get_user_model
from apps.clubs.models import Club, ClubInvitation
from django.utils.translation import gettext as _

User = get_user_model()


class InvitationSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    fullname = serializers.CharField(
        max_length=150, required=False, allow_blank=True, allow_null=True
    )


class ClubInviteSerializer(serializers.Serializer):
    emails = serializers.ListField(
        child=serializers.EmailField(),
        required=False,
        help_text="List of emails to invite",
    )

    invitations = serializers.ListField(
        child=InvitationSerializer(),
        required=False,
        help_text="List of invitations with email and optional fullname",
    )

    def __init__(self, *args, **kwargs):
        self.club = kwargs.pop("club", None)
        self.current_user = kwargs.pop("current_user", None)
        super().__init__(*args, **kwargs)

    def validate(self, data):
        """
        Comprehensive validation for club invitations:
        - At least one invitation method must be provided
        - Remove duplicates between emails and invitations
        - Validate against existing members
        - Validate against pending invitations
        - Prevent self-invitation
        """
        emails = data.get("emails", [])
        invitations = data.get("invitations", [])

        if not emails and not invitations:
            raise serializers.ValidationError(
                _(
                    "At least one invitation method (emails or invitations) must be provided."
                )
            )

        all_emails = set(emails)
        invitation_emails = {inv["email"] for inv in invitations}
        all_emails.update(invitation_emails)

        if emails and invitations:
            emails_set = set(emails)
            duplicates = emails_set.intersection(invitation_emails)
            if duplicates:
                data["emails"] = list(emails_set - duplicates)

        if not self.club or not self.current_user:
            return data

        errors = {}

        if self.current_user.email in all_emails:
            errors["self_invitation"] = _("You cannot invite yourself to the club.")

        existing_members = list(
            User.objects.filter(
                email__in=all_emails, club_members=self.club
            ).values_list("email", flat=True)
        )

        if existing_members:
            errors["existing_members"] = _(
                "The following users are already members of this club: {emails}"
            ).format(emails=", ".join(existing_members))

        pending_invitations = list(
            ClubInvitation.objects.filter(
                email__in=all_emails,
                club=self.club,
                status=ClubInvitation.STATUS_PENDING,
            ).values_list("email", flat=True)
        )

        if pending_invitations:
            errors["pending_invitations"] = _(
                "The following users already have pending invitations: {emails}"
            ).format(emails=", ".join(pending_invitations))

        users_with_pending_invitations = list(
            User.objects.filter(
                email__in=all_emails,
                invited_user__club=self.club,
                invited_user__status=ClubInvitation.STATUS_PENDING,
            ).values_list("email", flat=True)
        )

        if users_with_pending_invitations:
            pending_via_user = set(users_with_pending_invitations) - set(
                pending_invitations
            )
            if pending_via_user:
                if "pending_invitations" in errors:

                    existing_emails = errors["pending_invitations"].split(": ")[1]
                    all_pending = set(existing_emails.split(", ")).union(
                        pending_via_user
                    )
                    errors["pending_invitations"] = _(
                        "The following users already have pending invitations: {emails}"
                    ).format(emails=", ".join(all_pending))
                else:
                    errors["pending_invitations"] = _(
                        "The following users already have pending invitations: {emails}"
                    ).format(emails=", ".join(pending_via_user))

        if errors:
            raise serializers.ValidationError(errors)

        return data

    def validate_emails(self, emails):
        """Validate individual emails and remove duplicates."""
        if not emails:
            return emails

        seen = set()
        unique_emails = []
        for email in emails:
            if email.lower() not in seen:
                seen.add(email.lower())
                unique_emails.append(email)

        return unique_emails

    def validate_invitations(self, invitations):
        """Validate invitations and remove email duplicates."""
        if not invitations:
            return invitations

        seen = set()
        unique_invitations = []
        for invitation in invitations:
            email = invitation["email"].lower()
            if email not in seen:
                seen.add(email)
                unique_invitations.append(invitation)

        return unique_invitations
