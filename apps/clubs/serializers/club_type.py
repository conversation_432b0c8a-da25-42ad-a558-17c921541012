from rest_framework import serializers
from apps.clubs.models import ClubType


class ClubTypeSerializer(serializers.ModelSerializer):
    """
    Serializer for the ClubType model.

    Fields:
    - id: The unique identifier for the club type
    - name: The name of the club type
    - created_by: The user who created this club type
    - visibility: Whether the club type is public or private
    - created: When the club type was created
    - updated: When the club type was last updated
    """

    class Meta:
        model = ClubType
        fields = ["id", "name", "created_by", "visibility", "created", "updated"]
        read_only_fields = ["created", "updated"]
