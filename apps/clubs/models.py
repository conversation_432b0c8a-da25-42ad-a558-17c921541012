from tokenize import blank_re
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.cache import cache
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from core.abstract.models import AbstractModel
from django.conf import settings


class ClubType(AbstractModel):
    """
    Represents a type or category of club.

    A club type categorizes clubs and helps users find the right kind of club for their interests.
    Examples include sports clubs, reading clubs, arts clubs, etc.

    Club types can be either public (visible to all users) or private (limited visibility).
    """

    VISIBILITY_PRIVATE = "private"
    VISIBILITY_PUBLIC = "public"
    VISIBILITY_CHOICES = [
        (VISIBILITY_PRIVATE, "Private"),
        (VISIBILITY_PUBLIC, "Public"),
    ]

    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_("Name"),
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        related_name="created_club_types",
        null=True,
        blank=True,
    )
    visibility = models.CharField(
        max_length=10,
        choices=VISIBILITY_CHOICES,
        default=VISIBILITY_PRIVATE,
        verbose_name=_("Visibility"),
    )

    class Meta:
        verbose_name = _("ClubType")
        verbose_name_plural = _("ClubTypes")
        ordering = ["name"]

    def __str__(self):
        return self.name


class Club(AbstractModel):
    PRIVACY_CLOSED = "private"
    PRIVACY_OPEN = "public"
    PRIVACY_CHOICES = [
        (PRIVACY_CLOSED, "Private"),
        (PRIVACY_OPEN, "public"),
    ]

    JOIN_PERMISSIONS_OPEN = "open"
    JOIN_PERMISSIONS_INVITE = "invite-only"
    JOIN_PERMISSIONS_CHOICES = [
        (JOIN_PERMISSIONS_OPEN, "Open"),
        (JOIN_PERMISSIONS_INVITE, "Invite-only"),
    ]

    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_("Name"),
    )
    icon = models.ImageField(
        upload_to="clubs/icons/",
        blank=True,
        verbose_name=_("Icon"),
    )
    type = models.ForeignKey(
        ClubType,
        on_delete=models.CASCADE,
        related_name="club_type",
    )
    manager = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="club_manager",
    )
    members = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        related_name="club_members",
    )
    privacy = models.CharField(
        max_length=7, choices=PRIVACY_CHOICES, default=PRIVACY_OPEN
    )
    join_permissions = models.CharField(
        max_length=11, choices=JOIN_PERMISSIONS_CHOICES, default=JOIN_PERMISSIONS_OPEN
    )

    class Meta:
        verbose_name = _("Club")
        verbose_name_plural = _("Clubs")
        ordering = ["name"]

    def __str__(self):
        return self.name

    def get_member_progress(self, member):
        """
        Calculate the progress percentage for a specific club member

        Returns a dictionary with:
        - total_goals: Total number of daily goals
        - completed_goals: Number of completed daily goals
        - progress_percentage: Percentage of completed goals (0-100)
        """
        from apps.missions.models import DailyGoal, WeeklyMission

        missions = WeeklyMission.objects.filter(user=member)

        if not missions.exists():
            return {"total_goals": 0, "completed_goals": 0, "progress_percentage": 0}

        daily_goals = DailyGoal.objects.filter(weekly_mission__in=missions)

        total_goals = daily_goals.count()
        if total_goals == 0:
            return {"total_goals": 0, "completed_goals": 0, "progress_percentage": 0}

        completed_goals = 0
        for goal in daily_goals:
            if goal.is_completed():
                completed_goals += 1

        progress_percentage = (
            (completed_goals / total_goals) * 100 if total_goals > 0 else 0
        )

        return {
            "total_goals": total_goals,
            "completed_goals": completed_goals,
            "progress_percentage": round(progress_percentage, 2),
        }

    def get_all_members_progress(self):
        """
        Calculate progress for all members in the club

        Returns a dictionary with:
        - overall_progress: Overall club progress percentage
        - member_progress: Dictionary of member progress by member_id
        """
        members = self.members.all()

        if not members.exists():
            return {"overall_progress": 0, "member_progress": {}}

        total_progress = 0
        member_progress = {}

        for member in members:
            member_id = str(member.id)
            progress_data = self.get_member_progress(member)
            member_progress[member_id] = progress_data
            total_progress += progress_data["progress_percentage"]

        overall_progress = (
            total_progress / members.count() if members.count() > 0 else 0
        )

        return {
            "overall_progress": round(overall_progress, 2),
            "member_progress": member_progress,
        }

    def get_member_activity(self, member, days=30):
        """
        Calculate activity level for a specific member over the last X days

        Returns a dictionary with:
        - total_possible_updates: Total possible goal updates in the period
        - actual_updates: Number of updates made by the member
        - activity_percentage: Percentage of possible updates that were made
        """
        from apps.missions.models import DailyGoalProgress
        from django.utils import timezone

        end_date = timezone.now()
        start_date = end_date - timezone.timedelta(days=days)

        progress_updates = DailyGoalProgress.objects.filter(
            daily_goal__weekly_mission__user=member,
            updated__gte=start_date,
            updated__lte=end_date,
        )

        actual_updates = progress_updates.count()

        from apps.missions.models import DailyGoal

        possible_goals = DailyGoal.objects.filter(
            weekly_mission__user=member, created__lte=end_date
        )

        total_possible_updates = possible_goals.count()

        if total_possible_updates == 0:
            return {
                "total_possible_updates": 0,
                "actual_updates": 0,
                "activity_percentage": 0,
            }

        activity_percentage = (
            (actual_updates / total_possible_updates) * 100
            if total_possible_updates > 0
            else 0
        )

        return {
            "total_possible_updates": total_possible_updates,
            "actual_updates": actual_updates,
            "activity_percentage": round(activity_percentage, 2),
        }

    def get_all_members_activity(self, days=30):
        """
        Calculate activity level for all members over the last X days

        Returns a dictionary with:
        - overall_activity: Overall club activity percentage
        - active_members_percentage: Percentage of members with activity > 0
        - member_activity: Dictionary of member activity by member_id
        """
        members = self.members.all()

        if not members.exists():
            return {
                "overall_activity": 0,
                "active_members_percentage": 0,
                "member_activity": {},
            }

        total_activity = 0
        active_members_count = 0
        member_activity = {}

        for member in members:
            member_id = str(member.id)
            activity_data = self.get_member_activity(member, days)
            member_activity[member_id] = activity_data

            if activity_data["activity_percentage"] > 0:
                active_members_count += 1

            total_activity += activity_data["activity_percentage"]

        overall_activity = (
            total_activity / members.count() if members.count() > 0 else 0
        )
        active_members_percentage = (
            (active_members_count / members.count()) * 100 if members.count() > 0 else 0
        )

        return {
            "overall_activity": round(overall_activity, 2),
            "active_members_percentage": round(active_members_percentage, 2),
            "member_activity": member_activity,
        }

    def get_club_stats(self, activity_days=30):
        """
        Get comprehensive statistics for the club

        Returns a dictionary with:
        - total_members: Total number of members in the club
        - overall_progress: Overall club progress percentage
        - overall_activity: Overall club activity percentage
        - active_members_percentage: Percentage of members with activity > 0
        - member_stats: Dictionary of detailed member statistics by member_id
        """
        members = self.members.all()
        total_members = members.count()

        progress_data = self.get_all_members_progress()
        activity_data = self.get_all_members_activity(days=activity_days)

        member_stats = {}
        for member in members:
            member_id = str(member.id)

            member_progress = progress_data["member_progress"].get(
                member_id, {"progress_percentage": 0}
            )

            member_activity = activity_data["member_activity"].get(
                member_id, {"activity_percentage": 0}
            )

            member_stats[member_id] = {
                "member_id": member_id,
                "username": member.username,
                "fullname": member.fullname,
                "email": member.email,
                "progress": member_progress["progress_percentage"],
                "activity": member_activity["activity_percentage"],
            }

        return {
            "total_members": total_members,
            "overall_progress": progress_data["overall_progress"],
            "overall_activity": activity_data["overall_activity"],
            "active_members_percentage": activity_data["active_members_percentage"],
            "member_stats": member_stats,
        }


class ClubInvitation(AbstractModel):
    STATUS_PENDING = "pending"
    STATUS_ACCEPTED = "accepted"
    STATUS_REJECTED = "rejected"
    STATUS_CHOICES = [
        (STATUS_ACCEPTED, "Accepted"),
        (STATUS_REJECTED, "Rejected"),
        (STATUS_PENDING, "Pending"),
    ]
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="invited_user",
        null=True,
        blank=True,
    )
    sender = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="sender",
        null=True,
        blank=True,
    )
    club = models.ForeignKey(
        Club,
        on_delete=models.CASCADE,
        related_name="invited_to_club",
    )
    email = models.EmailField(
        verbose_name=_("Email"),
        blank=True,
        null=True,
    )
    fullname = models.CharField(
        max_length=150,
        verbose_name=_("Full Name"),
        blank=True,
        null=True,
    )
    status = models.CharField(
        max_length=10,
        choices=STATUS_CHOICES,
        default=STATUS_PENDING,
    )

    class Meta:
        verbose_name = _("ClubInvitation")
        verbose_name_plural = _("ClubInvitations")
        ordering = ["user"]
        unique_together = [("email", "club")]
