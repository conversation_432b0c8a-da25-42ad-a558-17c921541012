from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
from apps.clubs.models import ClubType


class Command(BaseCommand):
    help = "Seeds the database with predefined Arabic club types"

    @transaction.atomic
    def handle(self, *args, **kwargs):
        club_types = [
            "نادي الرياضة",
            "نادي القراءة",
            "نادي الفنون",
            "نادي العلوم",
            "نادي الأعمال",
        ]

        self.stdout.write(self.style.SUCCESS("Starting to seed Arabic club types..."))

        existing_count = ClubType.objects.count()
        self.stdout.write(f"Found {existing_count} existing club types.")

        created_count = 0

        for club_type_name in club_types:
            _, created = ClubType.objects.get_or_create(
                name=club_type_name, defaults={"visibility": "public"}
            )
            if created:
                self.stdout.write(f"Created club type: {club_type_name}")
                created_count += 1
            else:
                self.stdout.write(f"Club type already exists: {club_type_name}")

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully seeded {created_count} new Arabic club types."
            )
        )
        self.stdout.write(
            self.style.SUCCESS(
                f"Total club types in database: {existing_count + created_count}"
            )
        )
