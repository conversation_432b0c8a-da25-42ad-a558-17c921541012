from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from apps.clubs.views.club_management.invite import InviteMembersAPIView
from apps.clubs.views.club_management.join import JoinClubAPIView
from apps.clubs.views.club_management.remove_member import RemoveMemberClubAPIView
from apps.clubs.views.club_management.members import ClubMembersAPIView
from apps.clubs.views.club_management.accept_club_join_request import (
    AcceptClubJoinRequestAPIView,
)
from apps.clubs.views.club_management.reject_invitation import RejectInvitationView
from apps.clubs.views.club_management.my_invitations import MyInvitationsAPIView
from apps.clubs.views.club_management.managed_invitations import (
    ManagedInvitationsAPIView,
)
from apps.clubs.views.club_management.my_clubs import MyClubsAPIView
from apps.clubs.views.club_management.my_club import MyClubAPIView
from apps.clubs.views.club_management.manager_clubs import ManagerClubsAPIView

from apps.clubs.views.club_views.list import ClubListAPIView
from apps.clubs.views.club_views.export import ClubExportAPIView
from apps.clubs.views.club_views.retrieve import ClubRetrieveAPIView
from apps.clubs.views.club_views.create import ClubCreateAPIView
from apps.clubs.views.club_views.update import ClubUpdateAPIView
from apps.clubs.views.club_views.partial_update import ClubPartialUpdateAPIView
from apps.clubs.views.club_views.delete import ClubDeleteAPIView
from apps.clubs.views.club_type.list import ClubTypeListAPIView
from apps.clubs.views.club_type.create import ClubTypeCreateAPIView
from apps.clubs.views.club_type.retrieve import ClubTypeRetrieveAPIView
from apps.clubs.views.club_type.update import ClubTypeUpdateAPIView
from apps.clubs.views.club_type.destroy import ClubTypeDestroyAPIView
from apps.clubs.views.club_stats import ClubStatsAPIView

app_name = "clubs"

router = DefaultRouter()


club_urls = [
    path("", ClubListAPIView.as_view(), name="club-list"),
    path("export/", ClubExportAPIView.as_view(), name="club-export"),
    path("create/", ClubCreateAPIView.as_view(), name="club-create"),
    path("<uuid:pk>/", ClubRetrieveAPIView.as_view(), name="club-detail"),
    path("<uuid:pk>/stats/", ClubStatsAPIView.as_view(), name="club-stats"),
    path("<uuid:pk>/update/", ClubUpdateAPIView.as_view(), name="club-update"),
    path(
        "<uuid:pk>/partial-update/",
        ClubPartialUpdateAPIView.as_view(),
        name="club-partial-update",
    ),
    path("<uuid:pk>/delete/", ClubDeleteAPIView.as_view(), name="club-destroy"),
    path("<uuid:pk>/invite/", InviteMembersAPIView.as_view(), name="invite-members"),
    path("<uuid:pk>/join/", JoinClubAPIView.as_view(), name="join-club"),
    path(
        "<uuid:club_id>/remove-member/",
        RemoveMemberClubAPIView.as_view(),
        name="remove-member",
    ),
    path("<uuid:club_id>/members/", ClubMembersAPIView.as_view(), name="club-members"),
    path(
        "<uuid:invitation_id>/accept-join-request/",
        AcceptClubJoinRequestAPIView.as_view(),
        name="accept-join-request",
    ),
    path(
        "<uuid:invitation_id>/reject-join-request/",
        RejectInvitationView.as_view(),
        name="reject-join-request",
    ),
    path("my-invitations/", MyInvitationsAPIView.as_view(), name="my-invitations"),
    path(
        "managed-invitations/",
        ManagedInvitationsAPIView.as_view(),
        name="managed-invitations",
    ),
    path("my-clubs/", MyClubsAPIView.as_view(), name="my-clubs"),
    path("my-club/", MyClubAPIView.as_view(), name="my-club"),
    path(
        "managers/<uuid:manager_id>/clubs/",
        ManagerClubsAPIView.as_view(),
        name="manager-clubs",
    ),
]

club_type_urls = [
    path("", ClubTypeListAPIView.as_view(), name="club-type-list"),
    path("create/", ClubTypeCreateAPIView.as_view(), name="club-type-create"),
    path("<uuid:pk>/", ClubTypeRetrieveAPIView.as_view(), name="club-type-retrieve"),
    path("<uuid:pk>/update/", ClubTypeUpdateAPIView.as_view(), name="club-type-update"),
    path(
        "<uuid:pk>/delete/", ClubTypeDestroyAPIView.as_view(), name="club-type-destroy"
    ),
]


urlpatterns = [
    path("", include(club_urls)),
    path("types/", include(club_type_urls)),
    path("meetings/", include("apps.clubs.meetings.urls", namespace="club_meetings")),
]
