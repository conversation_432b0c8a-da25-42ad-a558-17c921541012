import django_filters
from apps.clubs.models import Club
from django.db.models import Q
import uuid


class ClubFilter(django_filters.FilterSet):
    """
    Filter for Club model allowing filtering by various fields.
    """

    name = django_filters.CharFilter(field_name="name", lookup_expr="icontains")
    privacy = django_filters.ChoiceFilter(choices=Club.PRIVACY_CHOICES)
    join_permissions = django_filters.ChoiceFilter(
        choices=Club.JOIN_PERMISSIONS_CHOICES
    )
    type = django_filters.UUIDFilter(field_name="type__id")
    type_name = django_filters.CharFilter(
        field_name="type__name", lookup_expr="icontains"
    )
    manager = django_filters.UUIDFilter(field_name="manager__id")
    member = django_filters.UUIDFilter(method="filter_by_member")
    created_after = django_filters.DateFilter(field_name="created", lookup_expr="gte")
    created_before = django_filters.DateFilter(field_name="created", lookup_expr="lte")

    class Meta:
        model = Club
        fields = [
            "name",
            "privacy",
            "join_permissions",
            "type",
            "manager",
            "created_after",
            "created_before",
        ]

    def filter_by_member(self, queryset, name, value):
        """
        Filter clubs by member ID
        """
        try:
            member_id = uuid.UUID(str(value))
            return queryset.filter(members__id=member_id)
        except (ValueError, TypeError):
            return queryset.none()
