from apps.clubs.serializers.invite import ClubInviteSerializer
from django.contrib import admin

from apps.clubs.models import ClubType, Club, ClubInvitation


class ClubTypeAdmin(admin.ModelAdmin):
    list_display = ["id", "name", "created", "updated"]
    search_fields = ["name"]
    list_filter = ["created", "updated"]
    readonly_fields = ["created", "updated"]


class ClubAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "name",
        "manager",
        "type",
        "privacy",
        "join_permissions",
        "created",
        "updated",
    ]
    search_fields = ["name"]
    list_filter = [
        "manager",
        "type",
        "privacy",
        "join_permissions",
        "created",
        "updated",
    ]
    readonly_fields = ["created", "updated"]
    filter_horizontal = ["members"]


class ClubInvitationAdmin(admin.ModelAdmin):
    list_display = ["id", "user", "club", "status", "created", "updated"]
    search_fields = ["user", "club"]
    list_filter = ["user", "club", "created", "updated"]
    readonly_fields = ["created", "updated"]


admin.site.register(ClubType, ClubTypeAdmin)
admin.site.register(Club, ClubAdmin)
admin.site.register(ClubInvitation, ClubInvitationAdmin)
