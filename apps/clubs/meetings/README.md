# Club Meetings Google Calendar Integration

This document outlines the integration between the Club Meetings feature and Google Calendar, allowing scheduled meetings to be automatically created, updated, and deleted on the organizer's Google Calendar, including a Google Meet link.

## Overview

When a `ClubMeeting` is created or updated in the application, and the meeting's organizer has previously authorized Google Calendar access, a corresponding event is created or updated in their primary Google Calendar. Similarly, when a `ClubMeeting` is deleted, the linked Google Calendar event is also removed. This synchronization includes generating and adding a Google Meet link to the calendar event and storing it in the `ClubMeeting` model.

## Key Components

*   **Model:** `apps.clubs.meetings.models.ClubMeeting`
    *   `google_calendar_event_id`: Stores the ID of the linked Google Calendar event.
    *   `meeting_link`: Stores the generated Google Meet link.
    *   `organizer`: Foreign key to the `User` model; the organizer must authorize calendar access.
*   **Service:** `apps.google_calendar.services.py`
    *   `create_or_update_google_event(meeting)`: Handles the core logic of interacting with the Google Calendar API to create/update events and generate Meet links. Requires `conferenceDataVersion=1` parameter.
    *   `delete_google_event(google_event_id, user)`: Handles deleting events from Google Calendar.
    *   `get_user_credentials(user)`: Retrieves and refreshes stored OAuth 2.0 credentials for a user.
*   **Tasks:** `apps.google_calendar.tasks.py`
    *   `schedule_google_calendar_event(meeting_id)`: Celery task triggered by signals to asynchronously call `create_or_update_google_event`.
    *   `delete_google_calendar_event_task(google_event_id, user_id)`: Celery task triggered by signals to asynchronously call `delete_google_event`.
*   **Signals:** `apps.clubs.meetings.signals.py`
    *   `handle_meeting_save_for_google_calendar`: Listens for `post_save` on `ClubMeeting` and triggers the `schedule_google_calendar_event` task if the organizer is synced.
    *   `handle_meeting_delete_for_google_calendar`: Listens for `post_delete` on `ClubMeeting` and triggers the `delete_google_calendar_event_task` task if an event ID exists.
*   **Authentication Views:** `apps.google_calendar.views/`
    *   `AuthorizeView`: Initiates the Google OAuth 2.0 flow.
    *   `OAuth2CallbackView`: Handles the redirect back from Google, exchanges the code for tokens, stores credentials (`GoogleCalendarCredentials` model), and marks the user as synced (`user.google_calendar_synced = True`).
*   **Settings:** `config/settings/base.py`
    *   `SOCIAL_AUTH_GOOGLE_OAUTH2_KEY`, `SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET`: Store Google Client ID and Secret (loaded from environment variables `GOOGLE_CLIENT_ID`, `GOOGLE_CLIENT_SECRET`).
    *   `SOCIAL_AUTH_GOOGLE_OAUTH2_SCOPE`: Must include `https://www.googleapis.com/auth/calendar.events` for creating/editing events, alongside other necessary scopes like `userinfo.email`, `userinfo.profile`.
    *   `GOOGLE_OAUTH2_REDIRECT_URI`: The URI registered in Google Cloud Console for the callback (loaded from environment variable).

## Configuration Steps

### 1. Google Cloud Project Setup

*   Go to the [Google Cloud Console](https://console.cloud.google.com/).
*   Create a new project or select an existing one.
*   **Enable the Google Calendar API:**
    *   Navigate to "APIs & Services" > "Library".
    *   Search for "Google Calendar API" and enable it.

### 2. Configure OAuth Consent Screen

*   Navigate to "APIs & Services" > "OAuth consent screen".
*   Choose "User Type" (Internal or External). If External, you might need app verification later.
*   Fill in the required app information (App name, User support email, Developer contact information).
*   **Add Scopes:** Add the following scopes required by the application:
    *   `https://www.googleapis.com/auth/userinfo.email`
    *   `https://www.googleapis.com/auth/userinfo.profile`
    *   `https://www.googleapis.com/auth/calendar.events`
    *   `openid`
*   Add test users if your app is in testing mode and external.

### 3. Create OAuth 2.0 Credentials

*   Navigate to "APIs & Services" > "Credentials".
*   Click "Create Credentials" > "OAuth client ID".
*   Select "Web application" as the application type.
*   Give it a name (e.g., "TAJ Web App").
*   **Add Authorized redirect URIs:** Add the callback URL configured in the Django application. This corresponds to the `GOOGLE_OAUTH2_REDIRECT_URI` setting. Example: `http://localhost:8000/api/google-calendar/callback/` or your production callback URL.
*   Click "Create".
*   Copy the **Client ID** and **Client Secret**.

### 4. Configure Django Application Settings

*   Set the following environment variables for your Django application:
    *   `GOOGLE_CLIENT_ID`: Your OAuth Client ID from step 3.
    *   `GOOGLE_CLIENT_SECRET`: Your OAuth Client Secret from step 3.
    *   `GOOGLE_OAUTH2_REDIRECT_URI`: The exact redirect URI you registered in step 3.
*   Ensure the `config/settings/base.py` file correctly loads these environment variables into `SOCIAL_AUTH_GOOGLE_OAUTH2_KEY` and `SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET`.
*   Verify that the `SOCIAL_AUTH_GOOGLE_OAUTH2_SCOPE` list in `config/settings/base.py` includes `https://www.googleapis.com/auth/calendar.events`.

## User Authentication Flow

1.  The user (meeting organizer) initiates the Google Calendar sync process, typically through a button in their profile or settings page which links to the `AuthorizeView` endpoint (e.g., `/api/google-calendar/authorize/`).
2.  `AuthorizeView` redirects the user to Google's OAuth 2.0 authorization page, requesting permission for the configured scopes.
3.  The user logs into their Google account (if not already) and consents to the requested permissions.
4.  Google redirects the user back to the application's `GOOGLE_OAUTH2_REDIRECT_URI`, handled by `OAuth2CallbackView`.
5.  `OAuth2CallbackView` receives an authorization code, exchanges it for access and refresh tokens using the Client ID and Secret.
6.  The view fetches the user's email via the OAuth2 API to identify the corresponding Django user.
7.  The access token, refresh token (important for offline access), token expiry, and scopes are stored securely, linked to the user (using the `GoogleCalendarCredentials` model).
8.  The user's profile is marked as synced (`user.google_calendar_synced = True`).
9.  Subsequent meeting creations/updates/deletions for this user will now trigger the Google Calendar synchronization via Celery tasks. Credentials will be refreshed automatically using the stored refresh token if the access token expires.

## Important Notes

*   **Meet Link Generation:** The Google Meet link is generated by including the `conferenceData` field with `conferenceSolutionKey.type` set to `hangoutsMeet` and passing `conferenceDataVersion=1` in the API request when creating/updating the event.
*   **Error Handling:** The service functions and Celery tasks include logging and basic error handling (e.g., for `HttpError`, permissions issues, expired credentials). Check logs for details on failures.
*   **Security:** Client secrets and user credentials (tokens) must be stored securely. Ensure appropriate environment variable management and database security.
*   **Revoking Access:** Users can revoke the application's access through their Google Account settings. The application should handle `403 Forbidden` errors gracefully, potentially marking the user as not synced and requiring re-authentication. 