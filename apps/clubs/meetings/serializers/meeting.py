from rest_framework import serializers 
from django .utils import timezone 
from django .utils .translation import gettext_lazy as _ 
from django .contrib .auth import get_user_model 
from django .core .validators import EmailValidator 

from apps .clubs .meetings .models import ClubMeeting ,MeetingAttendee 
from apps .clubs .serializers .club_serializers .detail import UserSerializer 
from apps .clubs .meetings .serializers .meeting_attendee import MeetingAttendeeSerializer 

User =get_user_model ()


class ClubMeetingSerializer (serializers .ModelSerializer ):
    organizer_details =UserSerializer (source ="organizer",read_only =True )
    duration =serializers .SerializerMethodField (read_only =True )
    attendees_details =serializers .SerializerMethodField (read_only =True )

    class Meta :
        model =ClubMeeting 
        fields =[
        "id",
        "club",
        "title",
        "description",
        "start_time",
        "end_time",
        "duration",
        "meeting_link",
        "jitsi_room_name",
        "jitsi_meeting_id",
        "jitsi_embed_link",
        "is_jitsi_meeting",
        "organizer",
        "organizer_details",
        "attendees_details",
        "status",
        "created",
        "updated",
        ]
        read_only_fields =[
        "id",
        "created",
        "updated",
        "status",
        "organizer",
        "meeting_link",
        "jitsi_room_name",
        "jitsi_meeting_id",
        "jitsi_embed_link",
        "duration",
        "attendees_details",
        ]

    def get_duration (self ,obj ):
        """
        Calculate the duration of the meeting in minutes.

        Returns:
            int: Duration in minutes, or None if start_time or end_time is not set
        """
        if not obj .start_time or not obj .end_time :
            return None 

        duration_seconds =(obj .end_time -obj .start_time ).total_seconds ()
        return int (duration_seconds /60 )

    def get_attendees_details (self ,obj ):
        """
        Get attendees details for the meeting.
        
        Returns:
            list: List of attendee details with user information and status
        """
        attendees =MeetingAttendee .objects .filter (meeting =obj ).select_related ('user')
        return MeetingAttendeeSerializer (attendees ,many =True ).data 


class ClubMeetingUpdateSerializer (serializers .ModelSerializer ):
    """
    Serializer for updating club meetings with restricted fields.
    Only allows updating: title, description, start_time, end_time
    
    Following senior Django developer best practices:
    - Explicit field restrictions for security
    - All fields are optional but at least one must be provided
    - Comprehensive validation
    - Clear error messages
    """


    title =serializers .CharField (max_length =200 ,required =False )
    description =serializers .CharField (required =False ,allow_blank =True )
    start_time =serializers .DateTimeField (required =False )
    end_time =serializers .DateTimeField (required =False )

    class Meta :
        model =ClubMeeting 
        fields =["title","description","start_time","end_time"]

    def validate (self ,attrs ):
        """
        Validate that at least one field is provided and perform cross-field validation.
        """

        if not attrs :
            raise serializers .ValidationError (
            _ ("At least one field must be provided for update")
            )

        start_time =attrs .get ('start_time')
        end_time =attrs .get ('end_time')
        instance =getattr (self ,'instance',None )


        current_start =start_time or (instance .start_time if instance else None )
        current_end =end_time or (instance .end_time if instance else None )


        if current_start and current_end and current_start >=current_end :
            raise serializers .ValidationError ({
            'end_time':_ ("End time must be after start time")
            })


        if current_start and current_end :
            duration =(current_end -current_start ).total_seconds ()/60 
            if duration <15 :
                raise serializers .ValidationError ({
                'end_time':_ ("Meeting must be at least 15 minutes long")
                })

        return attrs 

    def validate_start_time (self ,value ):
        """Validate start_time is not in the past (only for updates to future meetings)."""
        instance =getattr (self ,'instance',None )



        if instance and instance .start_time and instance .start_time <timezone .now ():

            return value 

        if value <timezone .now ():
            raise serializers .ValidationError (
            _ ("Cannot set meeting start time in the past")
            )
        return value 

    def validate_end_time (self ,value ):
        """Validate end_time is after start_time."""
        start_time =self .initial_data .get ('start_time')
        instance =getattr (self ,'instance',None )


        if not start_time and instance :
            start_time =instance .start_time 
        elif start_time :

            if isinstance (start_time ,str ):
                try :
                    from django .utils .dateparse import parse_datetime 
                    start_time =parse_datetime (start_time )
                except (ValueError ,TypeError ):

                    return value 

        if start_time and value <=start_time :
            raise serializers .ValidationError (
            _ ("End time must be after start time")
            )
        return value 


class ClubMeetingDetailSerializer (ClubMeetingSerializer ):
    """
    Detail serializer that inherits from ClubMeetingSerializer.
    Now both inherit the same attendees_details field for consistency.
    """
    pass 


class EmailAttendeeSerializer (serializers .Serializer ):
    """Serializer for email attendees."""

    email =serializers .EmailField ()
    name =serializers .CharField (required =False ,allow_blank =True )


class ClubMeetingCreateSerializer (ClubMeetingSerializer ):
    """Serializer for creating club meetings with email invitations."""

    email_attendees =EmailAttendeeSerializer (many =True ,required =False )

    class Meta (ClubMeetingSerializer .Meta ):
        fields =ClubMeetingSerializer .Meta .fields +["email_attendees"]

    def validate_email_attendees (self ,value ):
        """Validate email attendees."""
        if not isinstance (value ,list ):
            raise serializers .ValidationError (_ ("Email attendees must be a list"))

        emails =[item ["email"].lower ()for item in value ]
        if len (emails )!=len (set (emails )):
            raise serializers .ValidationError (_ ("Duplicate emails are not allowed"))

        return value 

    def create (self ,validated_data ):
        """Create a club meeting with email attendees."""
        email_attendees =validated_data .pop ("email_attendees",[])

        meeting =super ().create (validated_data )

        meeting ._email_attendees =email_attendees 

        return meeting 
