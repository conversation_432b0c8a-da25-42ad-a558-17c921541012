from rest_framework import serializers 
from django .utils .translation import gettext_lazy as _ 
from django .contrib .auth import get_user_model 

from apps .clubs .meetings .models import MeetingAttendee 
from apps .clubs .serializers .club_serializers .detail import UserSerializer 

User =get_user_model ()


class MeetingAttendeeSerializer (serializers .ModelSerializer ):
    user_details =UserSerializer (source ="user",read_only =True )

    class Meta :
        model =MeetingAttendee 
        fields =[
        "id",
        "meeting",
        "user",
        "user_details",
        "status",
        "is_notified",
        "created",
        "updated",
        ]
        read_only_fields =["id","created","updated","is_notified","user_details"]


class ManageAttendeeSerializer (serializers .Serializer ):
    """
    Serializer for managing meeting attendees.
    Supports both single user (user_id) and bulk operations (user_ids).
    
    Following senior Django developer best practices:
    - Explicit validation for club membership
    - Support for both single and bulk operations
    - Comprehensive error handling
    - Clear field restrictions for security
    """
    user_id =serializers .UUIDField (required =False ,help_text ="Single user ID to add/remove")
    user_ids =serializers .ListField (
    child =serializers .UUIDField (),
    required =False ,
    min_length =1 ,
    max_length =50 ,
    help_text ="List of user IDs to add/remove (bulk operation)"
    )

    def validate (self ,attrs ):
        """Validate that exactly one of user_id or user_ids is provided."""
        user_id =attrs .get ('user_id')
        user_ids =attrs .get ('user_ids')

        if not user_id and not user_ids :
            raise serializers .ValidationError (
            _ ("Either 'user_id' or 'user_ids' must be provided")
            )

        if user_id and user_ids :
            raise serializers .ValidationError (
            _ ("Provide either 'user_id' or 'user_ids', not both")
            )


        if user_id :
            attrs ['user_ids']=[user_id ]


        attrs .pop ('user_id',None )

        return attrs 

    def validate_user_ids (self ,value ):
        """Validate that all user IDs exist and remove duplicates."""
        if not value :
            return value 


        unique_ids =[]
        seen =set ()
        for user_id in value :
            if user_id not in seen :
                unique_ids .append (user_id )
                seen .add (user_id )


        existing_users =User .objects .filter (pk__in =unique_ids ).values_list ('pk',flat =True )
        existing_ids =set (str (uid )for uid in existing_users )
        provided_ids =set (str (uid )for uid in unique_ids )

        non_existent_ids =provided_ids -existing_ids 
        if non_existent_ids :
            raise serializers .ValidationError (
            _ ("The following user IDs do not exist: {ids}").format (
            ids =", ".join (sorted (non_existent_ids ))
            )
            )

        return unique_ids 

    def validate_club_membership (self ,user_ids ,club ):
        """
        Validate that all user IDs belong to club members or manager.
        This method should be called from the view with the club context.
        """
        if not user_ids :
            return []


        club_member_ids =set (
        str (uid )for uid in club .members .values_list ('pk',flat =True )
        )
        club_member_ids .add (str (club .manager .pk ))

        provided_ids =set (str (uid )for uid in user_ids )
        non_member_ids =provided_ids -club_member_ids 

        if non_member_ids :
            raise serializers .ValidationError (
            _ ("The following users are not members of this club: {ids}").format (
            ids =", ".join (sorted (non_member_ids ))
            )
            )

        return user_ids 


class BulkAttendeeResponseSerializer (serializers .Serializer ):
    """Serializer for bulk attendee operation responses."""

    total_processed =serializers .IntegerField (
    help_text ="Total number of users processed"
    )
    successful =serializers .IntegerField (
    help_text ="Number of successful operations"
    )
    failed =serializers .IntegerField (
    help_text ="Number of failed operations"
    )
    successful_users =serializers .ListField (
    child =serializers .UUIDField (),
    help_text ="List of user IDs that were successfully processed"
    )
    failed_users =serializers .ListField (
    child =serializers .DictField (),
    help_text ="List of failed operations with user ID and error message"
    )
    attendees =MeetingAttendeeSerializer (
    many =True ,
    required =False ,
    help_text ="List of created/remaining attendees"
    )
