# Generated by Django 4.2.9 on 2025-04-29 18:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("club_meetings", "0002_add_google_calendar_event_id"),
    ]

    operations = [
        migrations.AddField(
            model_name="clubmeeting",
            name="is_jitsi_meeting",
            field=models.BooleanField(default=True, verbose_name="Is Jitsi Meeting"),
        ),
        migrations.AddField(
            model_name="clubmeeting",
            name="jitsi_embed_link",
            field=models.URLField(
                blank=True, null=True, verbose_name="Jitsi Embed Link"
            ),
        ),
        migrations.AddField(
            model_name="clubmeeting",
            name="jitsi_meeting_id",
            field=models.Char<PERSON>ield(
                blank=True, max_length=255, null=True, verbose_name="Jitsi Meeting ID"
            ),
        ),
        migrations.AddField(
            model_name="clubmeeting",
            name="jitsi_room_name",
            field=models.Char<PERSON>ield(
                blank=True, max_length=255, null=True, verbose_name="Jitsi Room Name"
            ),
        ),
    ]
