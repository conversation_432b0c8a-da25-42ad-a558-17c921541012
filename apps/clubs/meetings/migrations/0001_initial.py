# Generated by Django 4.2.9 on 2025-04-22 11:37

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("clubs", "0009_clubtype_created_by_clubtype_visibility"),
    ]

    operations = [
        migrations.CreateModel(
            name="ClubMeeting",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("title", models.CharField(max_length=200, verbose_name="Title")),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="Description"),
                ),
                ("start_time", models.DateTimeField(verbose_name="Start Time")),
                ("end_time", models.DateTimeField(verbose_name="End Time")),
                (
                    "meeting_link",
                    models.URLField(blank=True, null=True, verbose_name="Meeting Link"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("scheduled", "Scheduled"),
                            ("ongoing", "Ongoing"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="scheduled",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
            ],
            options={
                "verbose_name": "ClubMeeting",
                "verbose_name_plural": "ClubMeetings",
                "ordering": ["-start_time"],
            },
        ),
        migrations.CreateModel(
            name="MeetingAttendee",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("invited", "Invited"),
                            ("accepted", "Accepted"),
                            ("declined", "Declined"),
                            ("tentative", "Tentative"),
                        ],
                        default="invited",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "is_notified",
                    models.BooleanField(default=False, verbose_name="Is Notified"),
                ),
                (
                    "meeting",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="meeting_attendees",
                        to="club_meetings.clubmeeting",
                        verbose_name="Meeting",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="meeting_attendees",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "MeetingAttendee",
                "verbose_name_plural": "MeetingAttendees",
                "ordering": ["meeting", "user"],
                "unique_together": {("meeting", "user")},
            },
        ),
        migrations.AddField(
            model_name="clubmeeting",
            name="attendees",
            field=models.ManyToManyField(
                related_name="attended_meetings",
                through="club_meetings.MeetingAttendee",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Attendees",
            ),
        ),
        migrations.AddField(
            model_name="clubmeeting",
            name="club",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="meetings",
                to="clubs.club",
                verbose_name="Club",
            ),
        ),
        migrations.AddField(
            model_name="clubmeeting",
            name="organizer",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="organized_meetings",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Organizer",
            ),
        ),
        migrations.AddIndex(
            model_name="clubmeeting",
            index=models.Index(
                fields=["club", "start_time"], name="club_meetin_club_id_fb8569_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="clubmeeting",
            index=models.Index(fields=["status"], name="club_meetin_status_6cf986_idx"),
        ),
    ]
