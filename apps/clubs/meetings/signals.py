from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
import logging
from django.utils.translation import gettext as _

from .models import ClubMeeting, MeetingAttendee
from apps.google_calendar.tasks import (
    schedule_google_calendar_event,
    delete_google_calendar_event_task,
)
from apps.notifications.utils import create_notification

logger = logging.getLogger(__name__)


@receiver(post_save, sender=ClubMeeting)
def handle_meeting_save_for_google_calendar(sender, instance, created, **kwargs):
    """
    When a ClubMeeting is saved, schedule a Celery task to create or update
    the corresponding Google Calendar event.
    """
    if (
        instance.organizer
        and hasattr(instance.organizer, "google_calendar_synced")
        and instance.organizer.google_calendar_synced
    ):
        logger.info(
            f"Scheduling Google Calendar event task for meeting {instance.id} (Created: {created})"
        )
        schedule_google_calendar_event.delay(instance.id)  # type: ignore
    else:
        logger.warning(
            f"Skipping Google Calendar sync for meeting {instance.id}: Organizer not found or not synced."
        )


@receiver(post_delete, sender=ClubMeeting)
def handle_meeting_delete_for_google_calendar(sender, instance, **kwargs):
    """
    When a ClubMeeting is deleted, schedule a Celery task to delete
    the corresponding Google Calendar event, if it exists.
    """
    if instance.google_calendar_event_id and instance.organizer:
        logger.info(
            f"Scheduling Google Calendar event deletion task for event {instance.google_calendar_event_id}"
        )
        delete_google_calendar_event_task.delay(  # type: ignore
            instance.google_calendar_event_id, instance.organizer.id
        )
    else:
        logger.info(
            f"Skipping Google Calendar event deletion for meeting {instance.id}: No event ID or organizer found."
        )


@receiver(post_save, sender=ClubMeeting)
def club_meeting_notification(sender, instance, created, **kwargs):
    """Send notification when a club meeting is created or updated"""
    if created:

        club = instance.club
        for member in club.members.all():
            if member != instance.organizer:
                create_notification(
                    message=_("New club meeting: {title}").format(title=instance.title),
                    notification_type="Appointment Reminder",
                    target=member,
                    description=_(
                        "A new meeting has been scheduled for {club_name} on {meeting_time}"
                    ).format(
                        club_name=instance.club.name,
                        meeting_time=instance.start_time.strftime("%Y-%m-%d %H:%M"),
                    ),
                )
    elif instance.status == ClubMeeting.STATUS_CANCELLED:

        for attendee in instance.attendees.all():
            if attendee != instance.organizer:
                create_notification(
                    message=_("Meeting cancelled: {title}").format(
                        title=instance.title
                    ),
                    notification_type="Process",
                    target=attendee,
                    description=_(
                        "The meeting for {club_name} scheduled for {meeting_time} has been cancelled."
                    ).format(
                        club_name=instance.club.name,
                        meeting_time=instance.start_time.strftime("%Y-%m-%d %H:%M"),
                    ),
                )


@receiver(post_save, sender=MeetingAttendee)
def meeting_attendee_notification(sender, instance, created, **kwargs):
    """Send notification when meeting attendee status changes"""
    if created:

        create_notification(
            message=_("You've been added to a meeting: {title}").format(
                title=instance.meeting.title
            ),
            notification_type="Appointment Reminder",
            target=instance.user,
            description=_(
                "You've been added to a meeting for {club_name} on {meeting_time}"
            ).format(
                club_name=instance.meeting.club.name,
                meeting_time=instance.meeting.start_time.strftime("%Y-%m-%d %H:%M"),
            ),
        )

    elif instance.status == MeetingAttendee.STATUS_ACCEPTED:

        create_notification(
            message=_("{user_name} accepted meeting invitation").format(
                user_name=instance.user.fullname
            ),
            notification_type="Process",
            target=instance.meeting.organizer,
            description=_(
                "{user_name} will attend the meeting {title} on {meeting_time}"
            ).format(
                user_name=instance.user.fullname,
                title=instance.meeting.title,
                meeting_time=instance.meeting.start_time.strftime("%Y-%m-%d %H:%M"),
            ),
        )
    elif instance.status == MeetingAttendee.STATUS_DECLINED:

        create_notification(
            message=_("{user_name} declined meeting invitation").format(
                user_name=instance.user.fullname
            ),
            notification_type="Process",
            target=instance.meeting.organizer,
            description=_(
                "{user_name} cannot attend the meeting {title} on {meeting_time}"
            ).format(
                user_name=instance.user.fullname,
                title=instance.meeting.title,
                meeting_time=instance.meeting.start_time.strftime("%Y-%m-%d %H:%M"),
            ),
        )
