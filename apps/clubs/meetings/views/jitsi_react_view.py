from rest_framework import views, permissions, status
from rest_framework.response import Response
from django.utils.translation import gettext_lazy as _
from drf_spectacular.utils import extend_schema, OpenApiParameter
from drf_spectacular.types import OpenApiTypes
from apps.clubs.meetings.models import ClubMeeting
from apps.clubs.meetings.utils.jitsi_utils import get_jitsi_meeting_info
from django.shortcuts import get_object_or_404
from django.conf import settings


class JitsiReactSchema:
    """Schema for Jitsi React integration response"""

    @staticmethod
    def get_react_response():
        return {
            "type": "object",
            "properties": {
                "domain": {
                    "type": "string",
                    "description": "Jitsi domain without protocol (e.g., 'jitsi.example.com')",
                },
                "room": {"type": "string", "description": "Jitsi room name"},
                "jwt": {"type": "string", "description": "JWT token for Jitsi meeting"},
                "userInfo": {
                    "type": "object",
                    "properties": {
                        "displayName": {
                            "type": "string",
                            "description": "User's display name",
                        },
                        "email": {"type": "string", "description": "User's email"},
                        "avatarURL": {
                            "type": "string",
                            "description": "URL to user's avatar",
                        },
                    },
                },
                "configOverwrite": {
                    "type": "object",
                    "description": "Configuration overrides for Jitsi Meet",
                },
                "interfaceConfigOverwrite": {
                    "type": "object",
                    "description": "Interface configuration overrides for Jitsi Meet",
                },
                "meetingURL": {
                    "type": "string",
                    "description": "Direct URL to join the meeting",
                },
                "embedURL": {
                    "type": "string",
                    "description": "URL for embedding in an iframe",
                },
                "isModerator": {
                    "type": "boolean",
                    "description": "Whether the user is a moderator",
                },
            },
        }


@extend_schema(
    tags=["Club Meetings"],
    parameters=[
        OpenApiParameter(
            name="meeting_id",
            type=OpenApiTypes.UUID,
            location=OpenApiParameter.PATH,
            description="ID of the club meeting",
        ),
    ],
    responses={
        200: JitsiReactSchema.get_react_response(),
        400: {"type": "object", "properties": {"error": {"type": "string"}}},
        403: {"type": "object", "properties": {"error": {"type": "string"}}},
        404: {"type": "object", "properties": {"error": {"type": "string"}}},
    },
    description="Get Jitsi meeting configuration for React integration",
    summary="Get Jitsi React configuration",
)
class JitsiReactConfigView(views.APIView):
    """
    View to provide Jitsi configuration for React integration.

    This endpoint provides all necessary information to integrate with Jitsi Meet React SDK.
    It includes domain, room name, JWT token, and configuration overrides.

    * Only authenticated users can access this endpoint
    * Users can only get configuration for meetings they are part of
    * Club managers and meeting organizers are automatically assigned as moderators
    """

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, meeting_id):
        """
        Get Jitsi meeting configuration for React integration.
        """
        try:
            meeting = get_object_or_404(ClubMeeting, pk=meeting_id)
            club = meeting.club
            user = request.user

            if club.manager != user and not club.members.filter(id=user.id).exists():
                return Response(
                    {"error": _("You are not authorized to join this meeting")},
                    status=status.HTTP_403_FORBIDDEN,
                )

            if (
                meeting.status != ClubMeeting.STATUS_SCHEDULED
                and meeting.status != ClubMeeting.STATUS_ONGOING
            ):
                return Response(
                    {"error": _("This meeting is not currently active")},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not meeting.is_jitsi_meeting or not meeting.jitsi_room_name:
                return Response(
                    {
                        "error": _(
                            "This meeting does not have a Jitsi meeting configured"
                        )
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            is_moderator = user == club.manager or user == meeting.organizer

            meeting_info = get_jitsi_meeting_info(
                user=user,
                room_name=meeting.jitsi_room_name,
                is_moderator=is_moderator,
            )

            jitsi_domain = getattr(settings, "JITSI_DOMAIN", "").rstrip("/")
            domain = jitsi_domain.replace("https://", "").replace("http://", "")

            display_name = user.username
            if hasattr(user, "fullname") and user.fullname:
                display_name = user.fullname
            elif (
                hasattr(user, "get_full_name")
                and callable(user.get_full_name)
                and user.get_full_name()
            ):
                display_name = user.get_full_name()

            avatar_url = ""
            if hasattr(user, "avatar") and user.avatar:
                avatar_url = user.avatar.url

            config_overwrite = {
                "startWithAudioMuted": False,
                "startWithVideoMuted": False,
                "prejoinPageEnabled": False,
                "disableDeepLinking": True,
                "enableClosePage": True,
                "enableWelcomePage": False,
                "defaultLanguage": "en",
                "enableInsecureRoomNameWarning": False,
            }

            interface_config_overwrite = {
                "TOOLBAR_BUTTONS": [
                    "microphone",
                    "camera",
                    "closedcaptions",
                    "desktop",
                    "fullscreen",
                    "fodeviceselection",
                    "hangup",
                    "profile",
                    "chat",
                    "recording",
                    "livestreaming",
                    "etherpad",
                    "sharedvideo",
                    "settings",
                    "raisehand",
                    "videoquality",
                    "filmstrip",
                    "invite",
                    "feedback",
                    "stats",
                    "shortcuts",
                    "tileview",
                    "videobackgroundblur",
                    "download",
                    "help",
                    "mute-everyone",
                    "security",
                ],
                "SHOW_JITSI_WATERMARK": False,
                "SHOW_WATERMARK_FOR_GUESTS": False,
                "SHOW_BRAND_WATERMARK": False,
                "BRAND_WATERMARK_LINK": "",
                "SHOW_POWERED_BY": False,
                "SHOW_PROMOTIONAL_CLOSE_PAGE": False,
                "MOBILE_APP_PROMO": False,
                "HIDE_INVITE_MORE_HEADER": True,
                "DISABLE_JOIN_LEAVE_NOTIFICATIONS": True,
                "DISABLE_FOCUS_INDICATOR": True,
                "DEFAULT_BACKGROUND": "#ffffff",
                "DEFAULT_REMOTE_DISPLAY_NAME": "Participant",
                "DEFAULT_LOCAL_DISPLAY_NAME": "Me",
                "TOOLBAR_ALWAYS_VISIBLE": True,
            }

            return Response(
                {
                    "domain": domain,
                    "room": meeting_info["room_name"],
                    "jwt": meeting_info["jwt_token"],
                    "userInfo": {
                        "displayName": display_name,
                        "email": user.email,
                        "avatarURL": avatar_url,
                    },
                    "configOverwrite": config_overwrite,
                    "interfaceConfigOverwrite": interface_config_overwrite,
                    "meetingURL": meeting_info["meeting_url"],
                    "embedURL": meeting_info["embed_url"],
                    "isModerator": meeting_info["is_moderator"],
                }
            )

        except ValueError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            import logging

            logger = logging.getLogger(__name__)
            logger.error(f"Error generating Jitsi React configuration: {str(e)}")

            return Response(
                {"error": _("Failed to generate meeting configuration")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
