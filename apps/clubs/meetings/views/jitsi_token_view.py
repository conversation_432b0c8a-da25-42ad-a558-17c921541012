from rest_framework import views, permissions, status
from rest_framework.response import Response
from django.utils.translation import gettext_lazy as _
from drf_spectacular.utils import extend_schema, OpenApiParameter
from drf_spectacular.types import OpenApiTypes
from apps.clubs.meetings.models import ClubMeeting
from apps.clubs.meetings.utils.jitsi_utils import get_jitsi_meeting_info
from django.shortcuts import get_object_or_404


class JitsiTokenSchema:
    """Schema for Jitsi token response"""

    @staticmethod
    def get_token_response():
        return {
            "type": "object",
            "properties": {
                "token": {
                    "type": "string",
                    "description": "JWT token for Jitsi meeting",
                },
                "room_name": {"type": "string", "description": "Jitsi room name"},
                "meeting_url": {
                    "type": "string",
                    "description": "Direct URL to join the Jitsi meeting",
                },
                "embed_url": {
                    "type": "string",
                    "description": "URL for embedding the Jitsi meeting in an iframe",
                },
                "is_moderator": {
                    "type": "boolean",
                    "description": "Whether the user is a moderator",
                },
            },
        }


@extend_schema(
    tags=["Club Meetings"],
    parameters=[
        OpenApiParameter(
            name="meeting_id",
            type=OpenApiTypes.UUID,
            location=OpenApiParameter.PATH,
            description="ID of the club meeting",
        ),
    ],
    responses={
        200: JitsiTokenSchema.get_token_response(),
        400: {"type": "object", "properties": {"error": {"type": "string"}}},
        403: {"type": "object", "properties": {"error": {"type": "string"}}},
        404: {"type": "object", "properties": {"error": {"type": "string"}}},
    },
    description="Generate a JWT token for joining a Jitsi meeting",
    summary="Get Jitsi meeting token",
)
class JitsiTokenView(views.APIView):
    """
    View to generate JWT tokens for Jitsi meetings.

    * Only authenticated users can access this endpoint
    * Users can only get tokens for meetings they are part of
    * Club managers and meeting organizers are automatically assigned as moderators
    """

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, meeting_id):
        """
        Generate a JWT token for a Jitsi meeting.
        """
        try:
            meeting = get_object_or_404(ClubMeeting, pk=meeting_id)
            club = meeting.club
            user = request.user

            if club.manager != user and not club.members.filter(id=user.id).exists():
                return Response(
                    {"error": _("You are not authorized to join this meeting")},
                    status=status.HTTP_403_FORBIDDEN,
                )

            if (
                meeting.status != ClubMeeting.STATUS_SCHEDULED
                and meeting.status != ClubMeeting.STATUS_ONGOING
            ):
                return Response(
                    {"error": _("This meeting is not currently active")},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not meeting.is_jitsi_meeting or not meeting.jitsi_room_name:
                return Response(
                    {
                        "error": _(
                            "This meeting does not have a Jitsi meeting configured"
                        )
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            is_moderator = user == club.manager or user == meeting.organizer

            meeting_info = get_jitsi_meeting_info(
                user=user,
                room_name=meeting.jitsi_room_name,
                is_moderator=is_moderator,
            )

            return Response(
                {
                    "token": meeting_info["jwt_token"],
                    "room_name": meeting_info["room_name"],
                    "meeting_url": meeting_info["meeting_url"],
                    "embed_url": meeting_info["embed_url"],
                    "is_moderator": meeting_info["is_moderator"],
                }
            )

        except ValueError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            import logging

            logger = logging.getLogger(__name__)
            logger.error(f"Error generating Jitsi token: {str(e)}")

            return Response(
                {"error": _("Failed to generate meeting token")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
