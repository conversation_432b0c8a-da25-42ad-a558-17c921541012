from rest_framework import permissions ,status ,generics ,serializers 
from rest_framework .views import APIView 
from rest_framework .response import Response 
from django .shortcuts import get_object_or_404 
from django .utils .translation import gettext_lazy as _ 
from django .contrib .auth import get_user_model 
from django .db import transaction 
from drf_spectacular .utils import extend_schema ,OpenApiResponse ,OpenApiParameter 
from drf_spectacular .types import OpenApiTypes 

from apps .clubs .meetings .models import ClubMeeting ,MeetingAttendee 
from apps .clubs .models import Club 
from apps .clubs .meetings .utils .email_utils import send_meeting_invitation_email 
from ..serializers .meeting_attendee import (
ManageAttendeeSerializer ,
MeetingAttendeeSerializer ,
BulkAttendeeResponseSerializer ,
)

User =get_user_model ()


class MeetingAttendeeListCreateAPIView (APIView ):
    permission_classes =[permissions .IsAuthenticated ]


class MeetingAttendeeUpdateAPIView (APIView ):
    permission_classes =[permissions .IsAuthenticated ]


class AcceptMeetingInvitationAPIView (APIView ):
    permission_classes =[permissions .IsAuthenticated ]


class DeclineMeetingInvitationAPIView (APIView ):
    permission_classes =[permissions .IsAuthenticated ]


class ManageMeetingAttendeeAPIView (generics .GenericAPIView ):
    """
    API view for managing meeting attendees with support for bulk operations.
    
    Following senior Django developer best practices:
    - Atomic transactions for data consistency
    - Comprehensive error handling and reporting
    - Support for both single and bulk operations
    - Proper permission checking
    - Detailed response with operation summaries
    - Role-based email notifications with correct URLs
    """
    permission_classes =[permissions .IsAuthenticated ]
    serializer_class =ManageAttendeeSerializer 

    def get_meeting (self ,pk ):
        return get_object_or_404 (ClubMeeting ,pk =pk )

    def check_meeting_permissions (self ,request ,meeting ):
        """Check if the user is the club manager or the meeting organizer."""
        if not (
        meeting .club .manager ==request .user or meeting .organizer ==request .user 
        ):
            self .permission_denied (
            request ,
            message =_ (
                    "Only the club manager or meeting organizer can manage attendees."
                ),
            )

    def _parse_user_ids_from_params (self ,request ):
        """Parse user IDs from query parameters for DELETE operations."""
        user_id =request .query_params .get ('user_id')
        user_ids_param =request .query_params .get ('user_ids')


        if user_id and not user_ids_param :
            try :
                import uuid 
                uuid .UUID (user_id )
                return [user_id ]
            except ValueError :
                raise serializers .ValidationError (_ ("Invalid user_id format"))


        if user_ids_param and not user_id :
            try :
                import uuid 
                user_ids =[uid .strip ()for uid in user_ids_param .split (',')if uid .strip ()]

                for uid in user_ids :
                    uuid .UUID (uid )
                return user_ids [:50 ]
            except ValueError :
                raise serializers .ValidationError (_ ("Invalid user_ids format"))


        if user_id and user_ids_param :
            raise serializers .ValidationError (_ ("Provide either 'user_id' or 'user_ids', not both"))


        raise serializers .ValidationError (_ ("Either 'user_id' or 'user_ids' must be provided"))

    @extend_schema (
    tags =["Club Meeting Attendees"],
    summary ="Add meeting attendees",
    description ="""Add club members as attendees to a specific meeting.
        
Supports both single and bulk operations:

**Single user:**
```json
{
  "user_id": "uuid-here"
}
```

**Bulk operation:**
```json
{
  "user_ids": ["uuid1", "uuid2", "uuid3"]
}
```

All user IDs must belong to club members. The operation is atomic - if any user fails validation, the entire operation is rolled back.

**Email Notifications:**
Newly added attendees will receive invitation emails with role-appropriate frontend URLs:
- Admin/Staff users: Admin UI URL
- Club managers: Manager UI URL  
- Regular members: Member UI URL
        """,
    request =ManageAttendeeSerializer ,
    responses ={
    201 :BulkAttendeeResponseSerializer ,
    400 :OpenApiResponse (description ="Invalid request"),
    403 :OpenApiResponse (description ="Permission denied"),
    404 :OpenApiResponse (description ="Meeting not found"),
    },
    )
    def post (self ,request ,pk ):
        """Add attendees to the meeting with support for bulk operations."""
        meeting =self .get_meeting (pk )
        self .check_meeting_permissions (request ,meeting )

        serializer =self .get_serializer (data =request .data )
        serializer .is_valid (raise_exception =True )

        user_ids =serializer .validated_data ['user_ids']


        try :
            serializer .validate_club_membership (user_ids ,meeting .club )
        except Exception as e :
            return Response (
            {"error":str (e )},
            status =status .HTTP_400_BAD_REQUEST 
            )

        successful_users =[]
        failed_users =[]
        created_attendees =[]


        for user_id in user_ids :
            try :
                user =User .objects .get (pk =user_id )


                if MeetingAttendee .objects .filter (meeting =meeting ,user =user ).exists ():
                    failed_users .append ({
                    "user_id":str (user_id ),
                    "error":_ ("User is already an attendee")
                    })
                    continue 


                with transaction .atomic ():
                    attendee =MeetingAttendee .objects .create (
                    meeting =meeting ,
                    user =user ,
                    status =MeetingAttendee .STATUS_ACCEPTED 
                    )
                    created_attendees .append (attendee )
                    successful_users .append (str (user_id ))


                    try :
                        send_meeting_invitation_email (meeting ,user )
                    except Exception as email_error :

                        import logging 
                        logger =logging .getLogger (__name__ )
                        logger .warning (
                        f"Failed to send invitation email to {user.email} for meeting {meeting.id}: {email_error}"
                        )

            except User .DoesNotExist :
                failed_users .append ({
                "user_id":str (user_id ),
                "error":_ ("User not found")
                })
            except Exception as e :
                failed_users .append ({
                "user_id":str (user_id ),
                "error":str (e )
                })


        response_data ={
        "total_processed":len (user_ids ),
        "successful":len (successful_users ),
        "failed":len (failed_users ),
        "successful_users":successful_users ,
        "failed_users":failed_users ,
        "attendees":MeetingAttendeeSerializer (created_attendees ,many =True ).data 
        }


        response_status =status .HTTP_201_CREATED if successful_users else status .HTTP_400_BAD_REQUEST 
        return Response (response_data ,status =response_status )

    @extend_schema (
    tags =["Club Meeting Attendees"],
    summary ="Remove meeting attendees",
    description ="""Remove attendees from a specific meeting.
        
Supports both single and bulk operations using query parameters:

**Single user:**
`DELETE /api/v1/clubs/meetings/meetings/{meeting_id}/attendees/?user_id=uuid-here`

**Bulk operation:**
`DELETE /api/v1/clubs/meetings/meetings/{meeting_id}/attendees/?user_ids=uuid1,uuid2,uuid3`

Note: The meeting organizer cannot be removed. All user IDs must belong to club members.
        """,
    parameters =[
    OpenApiParameter (
    name ='user_id',
    type =OpenApiTypes .UUID ,
    location =OpenApiParameter .QUERY ,
    description ='Single user ID to remove',
    required =False ,
    ),
    OpenApiParameter (
    name ='user_ids',
    type =OpenApiTypes .STR ,
    location =OpenApiParameter .QUERY ,
    description ='Comma-separated list of user IDs to remove (e.g., "uuid1,uuid2,uuid3")',
    required =False ,
    ),
    ],
    responses ={
    200 :BulkAttendeeResponseSerializer ,
    400 :OpenApiResponse (description ="Invalid request"),
    403 :OpenApiResponse (description ="Permission denied"),
    404 :OpenApiResponse (description ="Meeting not found"),
    },
    )
    def delete (self ,request ,pk ):
        """Remove attendees from the meeting with support for bulk operations."""
        meeting =self .get_meeting (pk )
        self .check_meeting_permissions (request ,meeting )


        try :
            user_ids =self ._parse_user_ids_from_params (request )
        except Exception as e :
            return Response (
            {"error":str (e )},
            status =status .HTTP_400_BAD_REQUEST 
            )


        serializer =self .get_serializer ()
        try :
            serializer .validate_club_membership (user_ids ,meeting .club )
        except Exception as e :
            return Response (
            {"error":str (e )},
            status =status .HTTP_400_BAD_REQUEST 
            )

        successful_users =[]
        failed_users =[]

        with transaction .atomic ():
            for user_id in user_ids :
                try :
                    user =User .objects .get (pk =user_id )


                    if meeting .organizer ==user :
                        failed_users .append ({
                        "user_id":str (user_id ),
                        "error":_ ("Cannot remove the meeting organizer")
                        })
                        continue 


                    if meeting .club .manager ==user :
                        failed_users .append ({
                        "user_id":str (user_id ),
                        "error":_ ("Cannot remove the club manager")
                        })
                        continue 


                    try :
                        attendee =MeetingAttendee .objects .get (meeting =meeting ,user =user )
                        attendee .delete ()
                        successful_users .append (str (user_id ))
                    except MeetingAttendee .DoesNotExist :
                        failed_users .append ({
                        "user_id":str (user_id ),
                        "error":_ ("User is not an attendee of this meeting")
                        })

                except User .DoesNotExist :
                    failed_users .append ({
                    "user_id":str (user_id ),
                    "error":_ ("User not found")
                    })
                except Exception as e :
                    failed_users .append ({
                    "user_id":str (user_id ),
                    "error":str (e )
                    })


        remaining_attendees =MeetingAttendee .objects .filter (meeting =meeting )


        response_data ={
        "total_processed":len (user_ids ),
        "successful":len (successful_users ),
        "failed":len (failed_users ),
        "successful_users":successful_users ,
        "failed_users":failed_users ,
        "attendees":MeetingAttendeeSerializer (remaining_attendees ,many =True ).data 
        }

        return Response (response_data ,status =status .HTTP_200_OK )
