from rest_framework import permissions, status
from rest_framework.views import APIView
from rest_framework.response import Response
from django.db.models import Q
from drf_spectacular.utils import extend_schema, OpenApiResponse

from apps.clubs.meetings.models import ClubMeeting
from apps.clubs.meetings.serializers.meeting import ClubMeetingSerializer
from apps.clubs.meetings.filters import Club<PERSON>eeting<PERSON>ilter
from core.abstract.paginations import MetaPageNumberPagination


class UserMeetingsListAPIView(APIView):
    """
    API view to list all meetings for the authenticated user.
    This includes meetings where the user is the organizer or an attendee.
    """

    permission_classes = [permissions.IsAuthenticated]
    pagination_class = MetaPageNumberPagination
    filter_class = ClubMeetingFilter

    def get_filter_context(self):
        return {"request": self.request}

    @extend_schema(
        tags=["Club Meetings"],
        summary="List user meetings",
        description="""List all meetings for the authenticated user with pagination and filtering.
        This includes meetings where the user is the organizer or an attendee.
        
Available filters:
- title: Filter by title (case-insensitive, contains)
- description: Filter by description (case-insensitive, contains)
- status: Filter by status (scheduled, ongoing, completed, cancelled)
- start_time_after: Filter by start time (greater than or equal to)
- start_time_before: Filter by start time (less than or equal to)
- end_time_after: Filter by end time (greater than or equal to)
- end_time_before: Filter by end time (less than or equal to)
- is_jitsi_meeting: Filter by whether the meeting is a Jitsi meeting
- user_role: Filter by user role (organizer, attendee, all)

Pagination parameters:
- page: Page number
- limit: Number of items per page (default: 5, max: 100)
        """,
        responses={
            200: ClubMeetingSerializer(many=True),
        },
    )
    def get(self, request):
        user = request.user

        queryset = ClubMeeting.objects.filter(
            Q(organizer=user) | Q(attendees=user)
        ).distinct()

        filter_instance = self.filter_class(
            request.query_params, queryset=queryset, request=request
        )
        filtered_queryset = filter_instance.qs

        paginator = self.pagination_class()
        paginated_queryset = paginator.paginate_queryset(filtered_queryset, request)

        serializer = ClubMeetingSerializer(paginated_queryset, many=True)

        return paginator.get_paginated_response(serializer.data)
