from rest_framework import permissions ,status 
from rest_framework .views import APIView 
from rest_framework .response import Response 
from django .shortcuts import get_object_or_404 
from django .utils .translation import gettext_lazy as _ 
from django .db .models import Q 
from drf_spectacular .utils import extend_schema ,OpenApiResponse 
from django_filters .rest_framework import DjangoFilterBackend 

from apps .clubs .meetings .models import ClubMeeting ,MeetingAttendee 
from apps .clubs .meetings .serializers .meeting import (
ClubMeetingSerializer ,
ClubMeetingDetailSerializer ,
ClubMeetingCreateSerializer ,
ClubMeetingUpdateSerializer ,
)
from apps .clubs .models import Club 
from apps .clubs .meetings .filters import ClubMeetingFilter 
from core .abstract .paginations import MetaPageNumberPagination 


class ClubMeetingListCreateAPIView (APIView ):
    permission_classes =[permissions .IsAuthenticated ]
    pagination_class =MetaPageNumberPagination 
    filter_class =ClubMeetingFilter 

    def get_filter_context (self ):
        return {"request":self .request }

    def _check_club_permissions (self ,request ,club ):
        """Check if user is a member or manager of the club"""
        user =request .user 
        if club .manager !=user and not club .members .filter (id =user .id ).exists ():
            return False
        return True

    @extend_schema (
    tags =["Club Meetings"],
    summary ="List club meetings",
    description ="""List meetings for a specific club with pagination and filtering.

Available filters:
- title: Filter by title (case-insensitive, contains)
- description: Filter by description (case-insensitive, contains)
- status: Filter by status (scheduled, ongoing, completed, cancelled)
- start_time_after: Filter by start time (greater than or equal to)
- start_time_before: Filter by start time (less than or equal to)
- end_time_after: Filter by end time (greater than or equal to)
- end_time_before: Filter by end time (less than or equal to)
- is_jitsi_meeting: Filter by whether the meeting is a Jitsi meeting
- user_role: Filter by user role (organizer, attendee, all)

Pagination parameters:
- page: Page number
- limit: Number of items per page (default: 5, max: 100)
        """,
    responses ={
    200 :ClubMeetingSerializer (many =True ),
    403 :OpenApiResponse (
    description ="Not authorized to view this club's meetings"
            ),
    404 :OpenApiResponse (description ="Club not found"),
        },
    )
    def get (self ,request ,club_id ):
        club =get_object_or_404 (Club ,pk =club_id )

        if not self ._check_club_permissions (request ,club ):
            return Response (
                {
            "error":_ (
                        "You must be a member or manager of this club to view meetings"
                    )
                },
            status =status .HTTP_403_FORBIDDEN ,
            )

        queryset =ClubMeeting .objects .filter (club =club )

        filter_instance =self .filter_class (
        request .query_params ,queryset =queryset ,request =request 
        )
        filtered_queryset =filter_instance .qs 

        paginator =self .pagination_class ()
        paginated_queryset =paginator .paginate_queryset (filtered_queryset ,request )

        serializer =ClubMeetingSerializer (paginated_queryset ,many =True )

        return paginator .get_paginated_response (serializer .data )

    @extend_schema (
    tags =["Club Meetings"],
    summary ="Create club meeting",
    description ="""Create a new meeting for a specific club.

You can optionally invite external users by email by providing the `email_attendees` field.

Example:
```json
{
  "title": "Weekly Meeting",
  "description": "Our weekly team meeting",
  "start_time": "2023-10-15T14:00:00Z",
  "end_time": "2023-10-15T15:00:00Z",
  "is_jitsi_meeting": true,
  "email_attendees": [
    {
      "email": "<EMAIL>",
      "name": "John Doe"
    },
    {
      "email": "<EMAIL>"
    }
  ]
}
```
        """,
    request =ClubMeetingCreateSerializer ,
    responses ={
    201 :ClubMeetingDetailSerializer ,
    400 :OpenApiResponse (description ="Invalid request"),
    403 :OpenApiResponse (
    description ="Not authorized to create meetings for this club"
            ),
    404 :OpenApiResponse (description ="Club not found"),
        },
    )
    def post (self ,request ,club_id ):
        club =get_object_or_404 (Club ,pk =club_id )

        if club .manager !=request .user :
            return Response (
            {"error":_ ("Only club manager can create meetings")},
            status =status .HTTP_403_FORBIDDEN ,
            )

        data =request .data .copy ()
        data ["club"]=club .id 

        serializer =ClubMeetingCreateSerializer (data =data )
        if serializer .is_valid ():
            is_jitsi_meeting =serializer .validated_data .get ("is_jitsi_meeting",True )
            jitsi_data ={}

            if is_jitsi_meeting :
                from apps .clubs .meetings .utils .jitsi_utils import (
                generate_room_name ,
                get_jitsi_meeting_url ,
                get_jitsi_embed_url ,
                )

                room_name =generate_room_name ()
                meeting_url =get_jitsi_meeting_url (room_name )
                embed_url =get_jitsi_embed_url (room_name )

                jitsi_data ={
                "jitsi_room_name":room_name ,
                "jitsi_meeting_id":room_name ,
                "jitsi_embed_link":embed_url ,
                "meeting_link":meeting_url ,
                }

            meeting =serializer .save (organizer =request .user ,**jitsi_data )

            email_attendees =serializer .validated_data .get ("email_attendees",[])

            members =club .members .all ()
            attendees_to_create =[]

            attendees_to_create .append (
            MeetingAttendee (
            meeting =meeting ,
            user =request .user ,
            status =MeetingAttendee .STATUS_ACCEPTED ,
                )
            )

            for member in members :
                if member !=request .user :
                    attendees_to_create .append (
                    MeetingAttendee (
                    meeting =meeting ,
                    user =member ,
                    status =MeetingAttendee .STATUS_ACCEPTED ,
                        )
                    )

            if attendees_to_create :
                MeetingAttendee .objects .bulk_create (
                attendees_to_create ,ignore_conflicts =True 
                )

                from apps .clubs .meetings .utils .email_utils import (
                send_meeting_invitation_email ,
                )

                for attendee in members :
                    if attendee !=request .user :
                        send_meeting_invitation_email (meeting ,attendee ,force_send =True )

            if email_attendees :
                from apps .clubs .meetings .utils .email_utils import (
                process_email_attendees ,
                )

                process_email_attendees (meeting ,email_attendees )

            response_serializer =ClubMeetingDetailSerializer (meeting )
            return Response (response_serializer .data ,status =status .HTTP_201_CREATED )
        return Response (serializer .errors ,status =status .HTTP_400_BAD_REQUEST )


class ClubMeetingDetailAPIView (APIView ):
    permission_classes =[permissions .IsAuthenticated ]

    def _check_meeting_permissions (self ,request ,meeting ,write_access =False ):
        """Check if user has permissions for this meeting"""
        user =request .user 
        club =meeting .club 

        if not (club .manager ==user or club .members .filter (id =user .id ).exists ()):
            return False

        if write_access and club .manager !=user and meeting .organizer !=user :
            return False

        return True

    @extend_schema (
    tags =["Club Meetings"],
    summary ="Retrieve club meeting",
    description ="Get details of a specific club meeting",
    responses ={
    200 :ClubMeetingDetailSerializer ,
    403 :OpenApiResponse (description ="Not authorized to view this meeting"),
    404 :OpenApiResponse (description ="Meeting not found"),
        },
    )
    def get (self ,request ,pk ):
        meeting =get_object_or_404 (ClubMeeting ,pk =pk )

        if not self ._check_meeting_permissions (request ,meeting ):
            return Response (
                {
            "error":_ (
                        "You must be a member or manager of this club to view meetings"
                    )
                },
            status =status .HTTP_403_FORBIDDEN ,
            )

        serializer =ClubMeetingDetailSerializer (meeting )
        return Response (serializer .data )

    @extend_schema (
    tags =["Club Meetings"],
    summary ="Update club meeting",
    description ="""Update details of a specific club meeting.
        
Only the following fields can be updated:
- title: Meeting title
- description: Meeting description  
- start_time: Meeting start time
- end_time: Meeting end time

Other fields like club, organizer, attendees are managed separately for security.
        """,
    request =ClubMeetingUpdateSerializer ,
    responses ={
    200 :ClubMeetingDetailSerializer ,
    400 :OpenApiResponse (description ="Invalid request"),
    403 :OpenApiResponse (description ="Not authorized to update this meeting"),
    404 :OpenApiResponse (description ="Meeting not found"),
        },
    )
    def put (self ,request ,pk ):
        meeting =get_object_or_404 (ClubMeeting ,pk =pk )

        if not self ._check_meeting_permissions (request ,meeting ,write_access =True ):
            return Response (
                {
            "error":_ (
                        "Only club manager or meeting organizer can update this meeting"
                    )
                },
            status =status .HTTP_403_FORBIDDEN ,
            )

        serializer =ClubMeetingUpdateSerializer (meeting ,data =request .data )
        if serializer .is_valid ():
            serializer .save ()

            response_serializer =ClubMeetingDetailSerializer (meeting )
            return Response (response_serializer .data )
        return Response (serializer .errors ,status =status .HTTP_400_BAD_REQUEST )

    @extend_schema (
    tags =["Club Meetings"],
    summary ="Partially update club meeting",
    description ="""Partially update details of a specific club meeting.
        
Only the following fields can be updated:
- title: Meeting title
- description: Meeting description  
- start_time: Meeting start time
- end_time: Meeting end time

Other fields like club, organizer, attendees are managed separately for security.
        """,
    request =ClubMeetingUpdateSerializer ,
    responses ={
    200 :ClubMeetingDetailSerializer ,
    400 :OpenApiResponse (description ="Invalid request"),
    403 :OpenApiResponse (description ="Not authorized to update this meeting"),
    404 :OpenApiResponse (description ="Meeting not found"),
    },
    )
    def patch (self ,request ,pk ):
        meeting =get_object_or_404 (ClubMeeting ,pk =pk )

        if not self ._check_meeting_permissions (request ,meeting ,write_access =True ):
            return Response (
            {
            "error":_ (
            "Only club manager or meeting organizer can update this meeting"
            )
            },
            status =status .HTTP_403_FORBIDDEN ,
            )

        serializer =ClubMeetingUpdateSerializer (
        meeting ,data =request .data ,partial =True 
        )
        if serializer .is_valid ():
            serializer .save ()

            response_serializer =ClubMeetingDetailSerializer (meeting )
            return Response (response_serializer .data )
        return Response (serializer .errors ,status =status .HTTP_400_BAD_REQUEST )

    @extend_schema (
    tags =["Club Meetings"],
    summary ="Delete club meeting",
    description ="Delete a specific club meeting",
    responses ={
    204 :OpenApiResponse (description ="Meeting deleted"),
    403 :OpenApiResponse (description ="Not authorized to delete this meeting"),
    404 :OpenApiResponse (description ="Meeting not found"),
        },
    )
    def delete (self ,request ,pk ):
        meeting =get_object_or_404 (ClubMeeting ,pk =pk )

        if not self ._check_meeting_permissions (request ,meeting ,write_access =True ):
            return Response (
                {
            "error":_ (
                        "Only club manager or meeting organizer can delete this meeting"
                    )
                },
            status =status .HTTP_403_FORBIDDEN ,
            )

        meeting .delete ()
        return Response (status =status .HTTP_204_NO_CONTENT )


class CancelClubMeetingAPIView (APIView ):
    permission_classes =[permissions .IsAuthenticated ]

    @extend_schema (
    tags =["Club Meetings"],
    summary ="Cancel club meeting",
    description ="Cancel a specific club meeting",
    responses ={
    200 :OpenApiResponse (description ="Meeting cancelled"),
    400 :OpenApiResponse (
    description ="Meeting is already cancelled or completed"
            ),
    403 :OpenApiResponse (description ="Not authorized to cancel this meeting"),
    404 :OpenApiResponse (description ="Meeting not found"),
        },
    )
    def post (self ,request ,pk ):
        meeting =get_object_or_404 (ClubMeeting ,pk =pk )

        user =request .user 
        club =meeting .club 

        if club .manager !=user and meeting .organizer !=user :
            return Response (
                {
            "error":_ (
                        "Only club manager or meeting organizer can cancel this meeting"
                    )
                },
            status =status .HTTP_403_FORBIDDEN ,
            )

        if meeting .status in [
        ClubMeeting .STATUS_COMPLETED ,
        ClubMeeting .STATUS_CANCELLED ,
        ]:
            return Response (
            {"error":_ ("Meeting is already cancelled or completed")},
            status =status .HTTP_400_BAD_REQUEST ,
            )

        meeting .status =ClubMeeting .STATUS_CANCELLED 
        meeting .save ()

        return Response (
        {"message":_ ("Meeting cancelled successfully")},status =status .HTTP_200_OK 
        )
