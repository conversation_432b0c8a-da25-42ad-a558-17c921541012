import django_filters
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from django.utils import timezone

from apps.clubs.meetings.models import ClubMeeting


class ClubMeetingFilter(django_filters.FilterSet):
    """
    Filter for ClubMeeting model.
    """

    title = django_filters.CharFilter(lookup_expr="icontains")
    description = django_filters.CharFilter(lookup_expr="icontains")
    status = django_filters.ChoiceFilter(choices=ClubMeeting.STATUS_CHOICES)
    start_time_after = django_filters.DateTimeFilter(
        field_name="start_time", lookup_expr="gte"
    )
    start_time_before = django_filters.DateTimeFilter(
        field_name="start_time", lookup_expr="lte"
    )
    end_time_after = django_filters.DateTimeFilter(
        field_name="end_time", lookup_expr="gte"
    )
    end_time_before = django_filters.DateTimeFilter(
        field_name="end_time", lookup_expr="lte"
    )
    is_jitsi_meeting = django_filters.BooleanFilter()

    user_role = django_filters.ChoiceFilter(
        label=_("User Role"),
        method="filter_user_role",
        choices=(
            ("organizer", _("Organizer")),
            ("attendee", _("Attendee")),
            ("all", _("All")),
        ),
        required=False,
    )

    class Meta:
        model = ClubMeeting
        fields = [
            "title",
            "description",
            "status",
            "start_time_after",
            "start_time_before",
            "end_time_after",
            "end_time_before",
            "is_jitsi_meeting",
            "user_role",
        ]

    def filter_user_role(self, queryset, name, value):
        """
        Filter meetings by user role.

        Args:
            queryset: The queryset to filter
            name: The name of the filter (not used)
            value: The value of the filter (organizer, attendee, all)

        Returns:
            Filtered queryset
        """
        user = self.request.user

        if not value:
            return queryset

        if value == "organizer":
            return queryset.filter(organizer=user)
        elif value == "attendee":
            return queryset.filter(attendees=user)
        elif value == "all":
            return queryset.filter(Q(organizer=user) | Q(attendees=user)).distinct()

        return queryset
