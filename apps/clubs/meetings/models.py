from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.conf import settings

from core.abstract.models import AbstractModel
from apps.clubs.models import Club


User = get_user_model()


class ClubMeeting(AbstractModel):
    STATUS_SCHEDULED = "scheduled"
    STATUS_ONGOING = "ongoing"
    STATUS_COMPLETED = "completed"
    STATUS_CANCELLED = "cancelled"
    STATUS_CHOICES = [
        (STATUS_SCHEDULED, "Scheduled"),
        (STATUS_ONGOING, "Ongoing"),
        (STATUS_COMPLETED, "Completed"),
        (STATUS_CANCELLED, "Cancelled"),
    ]

    club = models.ForeignKey(
        Club,
        on_delete=models.CASCADE,
        related_name="meetings",
        verbose_name=_("Club"),
    )
    title = models.CharField(
        max_length=200,
        verbose_name=_("Title"),
    )
    description = models.TextField(
        verbose_name=_("Description"),
        blank=True,
        null=True,
    )
    start_time = models.DateTimeField(
        verbose_name=_("Start Time"),
    )
    end_time = models.DateTimeField(
        verbose_name=_("End Time"),
    )
    meeting_link = models.URLField(
        verbose_name=_("Meeting Link"),
        blank=True,
        null=True,
    )
    google_calendar_event_id = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_("Google Calendar Event ID"),
    )
    jitsi_room_name = models.CharField(
        _("Jitsi Room Name"), max_length=255, blank=True, null=True
    )
    jitsi_meeting_id = models.CharField(
        _("Jitsi Meeting ID"), max_length=255, blank=True, null=True
    )
    jitsi_embed_link = models.URLField(_("Jitsi Embed Link"), blank=True, null=True)
    is_jitsi_meeting = models.BooleanField(_("Is Jitsi Meeting"), default=True)
    organizer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="organized_meetings",
        verbose_name=_("Organizer"),
    )
    attendees = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        through="MeetingAttendee",
        related_name="attended_meetings",
        verbose_name=_("Attendees"),
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default=STATUS_SCHEDULED,
        verbose_name=_("Status"),
    )

    class Meta:
        verbose_name = _("ClubMeeting")
        verbose_name_plural = _("ClubMeetings")
        ordering = ["-start_time"]
        indexes = [
            models.Index(fields=["club", "start_time"]),
            models.Index(fields=["status"]),
        ]

    def __str__(self):
        return f"{self.title} - {self.start_time.strftime('%Y-%m-%d %H:%M')}"

    def clean(self):
        from django.core.exceptions import ValidationError

        if self.start_time and self.end_time and self.start_time >= self.end_time:
            raise ValidationError(_("Start time must be before end time"))

        if not self.pk and self.start_time and self.start_time < timezone.now():
            raise ValidationError(_("Cannot schedule a meeting in the past"))

    def is_past(self):
        return self.end_time < timezone.now()

    def is_active(self):
        now = timezone.now()
        return self.start_time <= now <= self.end_time

    def update_status(self):
        now = timezone.now()

        if self.status == self.STATUS_CANCELLED:
            return

        if now > self.end_time:
            self.status = self.STATUS_COMPLETED
        elif now >= self.start_time:
            self.status = self.STATUS_ONGOING
        else:
            self.status = self.STATUS_SCHEDULED

        self.save(update_fields=["status"])


class MeetingAttendee(AbstractModel):
    STATUS_INVITED = "invited"
    STATUS_ACCEPTED = "accepted"
    STATUS_DECLINED = "declined"
    STATUS_TENTATIVE = "tentative"
    STATUS_CHOICES = [
        (STATUS_INVITED, "Invited"),
        (STATUS_ACCEPTED, "Accepted"),
        (STATUS_DECLINED, "Declined"),
        (STATUS_TENTATIVE, "Tentative"),
    ]

    meeting = models.ForeignKey(
        ClubMeeting,
        on_delete=models.CASCADE,
        related_name="meeting_attendees",
        verbose_name=_("Meeting"),
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="meeting_attendees",
        verbose_name=_("User"),
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default=STATUS_INVITED,
        verbose_name=_("Status"),
    )
    is_notified = models.BooleanField(
        default=False,
        verbose_name=_("Is Notified"),
    )

    class Meta:
        verbose_name = _("MeetingAttendee")
        verbose_name_plural = _("MeetingAttendees")
        ordering = ["meeting", "user"]
        unique_together = [("meeting", "user")]

    def __str__(self):
        return f"{self.user} - {self.meeting.title}"
