import uuid
import jwt
from unittest.mock import patch
from django.test import TestCase, override_settings
from django.urls import reverse
from django.utils import timezone
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from datetime import timedelta

from apps.clubs.models import Club, ClubType
from apps.clubs.meetings.models import ClubMeeting, MeetingAttendee
from apps.clubs.meetings.utils.jitsi_utils import (
    generate_jitsi_jwt,
    generate_room_name,
    get_jitsi_meeting_url,
    get_jitsi_embed_url,
    get_jitsi_meeting_info,
)

User = get_user_model()


@override_settings(
    JITSI_DOMAIN="https://test-jitsi.example.com",
    JITSI_JWT_APP_ID="test-app-id",
    JITSI_JWT_APP_SECRET="test-app-secret",
    JITSI_JWT_AUDIENCE="test-audience",
    JITSI_JWT_EXPIRY=3600,
)
class JitsiUtilsTestCase(TestCase):
    """Test the Jitsi utility functions."""

    def setUp(self):
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpassword",
            fullname="Test User",
        )
        self.room_name = "test-room-123"

    def test_generate_room_name(self):
        """Test that generate_room_name returns a string with the expected format."""
        room_name = generate_room_name()
        self.assertTrue(room_name.startswith("meeting-"))
        self.assertEqual(len(room_name), len("meeting-") + 12)

    def test_get_jitsi_meeting_url(self):
        """Test that get_jitsi_meeting_url returns the correct URL."""
        url = get_jitsi_meeting_url(self.room_name)
        self.assertEqual(url, f"https://test-jitsi.example.com/{self.room_name}")

    def test_get_jitsi_embed_url_without_token(self):
        """Test that get_jitsi_embed_url returns the correct URL without a token."""
        url = get_jitsi_embed_url(self.room_name)
        self.assertEqual(url, f"https://test-jitsi.example.com/{self.room_name}")

    def test_get_jitsi_embed_url_with_token(self):
        """Test that get_jitsi_embed_url returns the correct URL with a token."""
        token = "test-token"
        url = get_jitsi_embed_url(self.room_name, token)
        self.assertEqual(
            url, f"https://test-jitsi.example.com/{self.room_name}?jwt={token}"
        )

    def test_generate_jitsi_jwt(self):
        """Test that generate_jitsi_jwt returns a valid JWT token with the expected claims."""
        token = generate_jitsi_jwt(self.user, self.room_name, is_moderator=True)

        decoded = jwt.decode(
            token,
            "test-app-secret",
            algorithms=["HS256"],
            audience="test-audience",
        )

        self.assertEqual(decoded["iss"], "test-app-id")
        self.assertEqual(decoded["aud"], "test-audience")
        self.assertEqual(decoded["room"], self.room_name)
        self.assertEqual(decoded["context"]["user"]["id"], str(self.user.id))
        self.assertEqual(decoded["context"]["user"]["name"], "Test User")
        self.assertEqual(decoded["context"]["user"]["email"], "<EMAIL>")
        self.assertEqual(decoded["context"]["user"]["moderator"], "true")
        self.assertEqual(decoded["context"]["features"]["livestreaming"], "true")
        self.assertEqual(decoded["context"]["features"]["recording"], "true")

    def test_generate_jitsi_jwt_non_moderator(self):
        """Test that generate_jitsi_jwt sets moderator to false for non-moderators."""
        token = generate_jitsi_jwt(self.user, self.room_name, is_moderator=False)

        decoded = jwt.decode(
            token,
            "test-app-secret",
            algorithms=["HS256"],
            audience="test-audience",
        )

        self.assertEqual(decoded["context"]["user"]["moderator"], "false")
        self.assertEqual(decoded["context"]["features"]["livestreaming"], "false")
        self.assertEqual(decoded["context"]["features"]["recording"], "false")

    def test_get_jitsi_meeting_info(self):
        """Test that get_jitsi_meeting_info returns a dictionary with all the expected keys."""
        info = get_jitsi_meeting_info(self.user, self.room_name, is_moderator=True)

        self.assertIn("meeting_url", info)
        self.assertIn("embed_url", info)
        self.assertIn("jwt_token", info)
        self.assertIn("room_name", info)
        self.assertIn("is_moderator", info)

        self.assertEqual(info["room_name"], self.room_name)
        self.assertEqual(info["is_moderator"], True)
        self.assertEqual(
            info["meeting_url"], f"https://test-jitsi.example.com/{self.room_name}"
        )
        self.assertTrue(
            info["embed_url"].startswith(
                f"https://test-jitsi.example.com/{self.room_name}?jwt="
            )
        )


@override_settings(
    JITSI_DOMAIN="https://test-jitsi.example.com",
    JITSI_JWT_APP_ID="test-app-id",
    JITSI_JWT_APP_SECRET="test-app-secret",
    JITSI_JWT_AUDIENCE="test-audience",
    JITSI_JWT_EXPIRY=3600,
)
class JitsiMeetingModelTestCase(TestCase):
    """Test the ClubMeeting model with Jitsi fields."""

    def setUp(self):
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpassword",
        )
        self.club_type = ClubType.objects.create(
            name="Test Club Type",
            created_by=self.user,
        )
        self.club = Club.objects.create(
            name="Test Club",
            type=self.club_type,
            manager=self.user,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_OPEN,
        )

        start_time = timezone.now() + timedelta(hours=1)

        end_time = timezone.now() + timedelta(hours=2)

        self.meeting = ClubMeeting.objects.create(
            club=self.club,
            title="Test Meeting",
            description="Test Description",
            start_time=start_time,
            end_time=end_time,
            organizer=self.user,
            is_jitsi_meeting=True,
            jitsi_room_name="test-room-123",
            jitsi_meeting_id="test-meeting-123",
            jitsi_embed_link="https://test-jitsi.example.com/test-room-123?jwt=token",
        )

    def test_jitsi_fields(self):
        """Test that the Jitsi fields are saved correctly."""
        self.assertEqual(self.meeting.is_jitsi_meeting, True)
        self.assertEqual(self.meeting.jitsi_room_name, "test-room-123")
        self.assertEqual(self.meeting.jitsi_meeting_id, "test-meeting-123")
        self.assertEqual(
            self.meeting.jitsi_embed_link,
            "https://test-jitsi.example.com/test-room-123?jwt=token",
        )


@override_settings(
    JITSI_DOMAIN="https://test-jitsi.example.com",
    JITSI_JWT_APP_ID="test-app-id",
    JITSI_JWT_APP_SECRET="test-app-secret",
    JITSI_JWT_AUDIENCE="test-audience",
    JITSI_JWT_EXPIRY=3600,
)
class JitsiAPITestCase(TestCase):
    """Test the Jitsi API endpoints."""

    def setUp(self):
        self.client = APIClient()

        self.manager = User.objects.create_user(
            username="manager",
            email="<EMAIL>",
            password="managerpassword",
        )
        self.member = User.objects.create_user(
            username="member",
            email="<EMAIL>",
            password="memberpassword",
        )
        self.non_member = User.objects.create_user(
            username="nonmember",
            email="<EMAIL>",
            password="nonmemberpassword",
        )

        self.club_type = ClubType.objects.create(
            name="Test Club Type",
            created_by=self.manager,
        )
        self.club = Club.objects.create(
            name="Test Club",
            type=self.club_type,
            manager=self.manager,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_OPEN,
        )
        self.club.members.add(self.member)

        start_time = timezone.now() + timedelta(hours=1)
        end_time = timezone.now() + timedelta(hours=2)

        self.meeting = ClubMeeting.objects.create(
            club=self.club,
            title="Test Meeting",
            description="Test Description",
            start_time=start_time,
            end_time=end_time,
            organizer=self.manager,
            is_jitsi_meeting=True,
            jitsi_room_name="test-room-123",
            jitsi_meeting_id="test-meeting-123",
            status=ClubMeeting.STATUS_SCHEDULED,
        )

        MeetingAttendee.objects.create(
            meeting=self.meeting,
            user=self.manager,
            status=MeetingAttendee.STATUS_ACCEPTED,
        )
        MeetingAttendee.objects.create(
            meeting=self.meeting,
            user=self.member,
            status=MeetingAttendee.STATUS_ACCEPTED,
        )

    def test_jitsi_token_endpoint_manager(self):
        """Test that the manager can get a Jitsi token."""
        self.client.force_authenticate(user=self.manager)
        url = f"/api/v1/clubs/meetings/{self.meeting.id}/jitsi-token/"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("token", response.data)
        self.assertIn("room_name", response.data)
        self.assertIn("meeting_url", response.data)
        self.assertIn("embed_url", response.data)
        self.assertIn("is_moderator", response.data)

        self.assertEqual(response.data["room_name"], "test-room-123")
        self.assertEqual(response.data["is_moderator"], True)

    def test_jitsi_token_endpoint_member(self):
        """Test that a member can get a Jitsi token but is not a moderator."""
        self.client.force_authenticate(user=self.member)
        url = f"/api/v1/clubs/meetings/{self.meeting.id}/jitsi-token/"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("token", response.data)
        self.assertIn("is_moderator", response.data)

        self.assertEqual(response.data["is_moderator"], False)

    def test_jitsi_token_endpoint_non_member(self):
        """Test that a non-member cannot get a Jitsi token."""
        self.client.force_authenticate(user=self.non_member)
        url = f"/api/v1/clubs/meetings/{self.meeting.id}/jitsi-token/"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_jitsi_token_endpoint_unauthenticated(self):
        """Test that an unauthenticated user cannot get a Jitsi token."""
        url = f"/api/v1/clubs/meetings/{self.meeting.id}/jitsi-token/"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_jitsi_react_config_endpoint_manager(self):
        """Test that the manager can get Jitsi React configuration."""
        self.client.force_authenticate(user=self.manager)
        url = f"/api/v1/clubs/meetings/{self.meeting.id}/jitsi-react-config/"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("domain", response.data)
        self.assertIn("room", response.data)
        self.assertIn("jwt", response.data)
        self.assertIn("userInfo", response.data)
        self.assertIn("configOverwrite", response.data)
        self.assertIn("interfaceConfigOverwrite", response.data)
        self.assertIn("meetingURL", response.data)
        self.assertIn("embedURL", response.data)
        self.assertIn("isModerator", response.data)

        self.assertEqual(response.data["domain"], "test-jitsi.example.com")
        self.assertEqual(response.data["room"], "test-room-123")
        self.assertEqual(response.data["isModerator"], True)

    def test_jitsi_react_config_endpoint_member(self):
        """Test that a member can get Jitsi React configuration but is not a moderator."""
        self.client.force_authenticate(user=self.member)
        url = f"/api/v1/clubs/meetings/{self.meeting.id}/jitsi-react-config/"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("isModerator", response.data)
        self.assertEqual(response.data["isModerator"], False)

    def test_jitsi_react_config_endpoint_non_member(self):
        """Test that a non-member cannot get Jitsi React configuration."""
        self.client.force_authenticate(user=self.non_member)
        url = f"/api/v1/clubs/meetings/{self.meeting.id}/jitsi-react-config/"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_jitsi_react_config_endpoint_unauthenticated(self):
        """Test that an unauthenticated user cannot get Jitsi React configuration."""
        url = f"/api/v1/clubs/meetings/{self.meeting.id}/jitsi-react-config/"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_meeting_creation_with_jitsi(self):
        """Test that a meeting is created with Jitsi details."""
        self.client.force_authenticate(user=self.manager)
        url = f"/api/v1/clubs/meetings/club/{self.club.id}/"

        start_time = timezone.now() + timedelta(hours=3)
        end_time = timezone.now() + timedelta(hours=4)

        data = {
            "title": "New Jitsi Meeting",
            "description": "Meeting with Jitsi",
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "is_jitsi_meeting": True,
        }

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn("jitsi_room_name", response.data)
        self.assertIn("jitsi_meeting_id", response.data)
        self.assertIn("jitsi_embed_link", response.data)
        self.assertIn("is_jitsi_meeting", response.data)

        self.assertEqual(response.data["is_jitsi_meeting"], True)
        self.assertIsNotNone(response.data["jitsi_room_name"])
        self.assertIsNotNone(response.data["jitsi_meeting_id"])
        self.assertIsNotNone(response.data["jitsi_embed_link"])

    def test_meeting_creation_without_jitsi(self):
        """Test that a meeting can be created without Jitsi."""
        self.client.force_authenticate(user=self.manager)
        url = f"/api/v1/clubs/meetings/club/{self.club.id}/"

        start_time = timezone.now() + timedelta(hours=3)
        end_time = timezone.now() + timedelta(hours=4)

        data = {
            "title": "Regular Meeting",
            "description": "Meeting without Jitsi",
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "is_jitsi_meeting": False,
        }

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn("is_jitsi_meeting", response.data)
        self.assertEqual(response.data["is_jitsi_meeting"], False)
