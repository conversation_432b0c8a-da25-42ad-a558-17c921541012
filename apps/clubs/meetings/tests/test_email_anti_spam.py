import pytest 
from unittest .mock import patch ,MagicMock 
from django .utils import timezone 
from datetime import timedelta 
from django .contrib .auth import get_user_model 
from django .test import TestCase 
from rest_framework .test import APIClient 

from apps .clubs .models import Club ,ClubType 
from apps .clubs .meetings .models import ClubMeeting ,MeetingAttendee 
from apps .clubs .meetings .utils .email_utils import (
has_received_meeting_invitation_today ,
send_meeting_invitation_email ,
)

User =get_user_model ()


@pytest .mark .django_db 
class TestEmailAntiSpamProtection (TestCase ):
    """
    Test suite for email anti-spam protection functionality.
    
    Following senior Django developer best practices:
    - Prevents email flooding/spam
    - Uses MeetingAttendee creation date as proxy for email sent date
    - Comprehensive edge case testing
    """

    def setUp (self ):
        """Set up test data."""
        self .client =APIClient ()


        self .manager =User .objects .create_user (
        username ="manager",
        email ="<EMAIL>",
        password ="password123",
        )

        self .member =User .objects .create_user (
        username ="member",
        email ="<EMAIL>",
        password ="password123",
        )


        self .club_type =ClubType .objects .create (
        name ="Test Club Type",
        created_by =self .manager ,
        )

        self .club =Club .objects .create (
        name ="Test Club",
        type =self .club_type ,
        manager =self .manager ,
        )


        self .club .members .add (self .manager ,self .member )


        future_time =timezone .now ()+timedelta (days =1 )
        self .meeting =ClubMeeting .objects .create (
        club =self .club ,
        title ="Test Meeting",
        description ="Test meeting description",
        start_time =future_time ,
        end_time =future_time +timedelta (hours =1 ),
        organizer =self .manager ,
        )

    def test_has_received_invitation_today_no_previous_invitations (self ):
        """Test user with no previous invitations returns False."""
        result =has_received_meeting_invitation_today (self .member )
        assert result is False 

    def test_has_received_invitation_today_with_invitation_today (self ):
        """Test user who received invitation today returns True."""

        MeetingAttendee .objects .create (
        meeting =self .meeting ,
        user =self .member ,
        status =MeetingAttendee .STATUS_INVITED ,
        )

        result =has_received_meeting_invitation_today (self .member )
        assert result is True 

    def test_has_received_invitation_today_with_invitation_yesterday (self ):
        """Test user who received invitation yesterday returns False."""

        yesterday =timezone .now ()-timedelta (days =1 )

        attendee =MeetingAttendee .objects .create (
        meeting =self .meeting ,
        user =self .member ,
        status =MeetingAttendee .STATUS_INVITED ,
        )


        attendee .created =yesterday 
        attendee .save (update_fields =['created'])

        result =has_received_meeting_invitation_today (self .member )
        assert result is False 

    def test_has_received_invitation_today_with_multiple_meetings_today (self ):
        """Test user with multiple meeting invitations today returns True."""

        for i in range (3 ):
            future_time =timezone .now ()+timedelta (days =i +1 )
            meeting =ClubMeeting .objects .create (
            club =self .club ,
            title =f"Meeting {i}",
            description =f"Meeting {i} description",
            start_time =future_time ,
            end_time =future_time +timedelta (hours =1 ),
            organizer =self .manager ,
            )

            MeetingAttendee .objects .create (
            meeting =meeting ,
            user =self .member ,
            status =MeetingAttendee .STATUS_INVITED ,
            )

        result =has_received_meeting_invitation_today (self .member )
        assert result is True 

    @patch ('apps.clubs.meetings.utils.email_utils.has_received_meeting_invitation_today')
    @patch ('apps.clubs.meetings.utils.email_utils._send_email_sync')
    @patch ('apps.clubs.meetings.utils.email_utils.render_to_string')
    def test_send_meeting_invitation_skips_when_already_sent_today (
    self ,mock_render ,mock_send_email ,mock_has_received 
    ):
        """Test that invitation email is skipped when user already received one today."""
        mock_render .return_value ="Test content"
        mock_has_received .return_value =True 

        result =send_meeting_invitation_email (self .meeting ,self .member )


        assert result is False 


        mock_send_email .assert_not_called ()
        mock_render .assert_not_called ()


        mock_has_received .assert_called_once_with (self .member )

    @patch ('apps.clubs.meetings.utils.email_utils.has_received_meeting_invitation_today')
    @patch ('apps.clubs.meetings.utils.email_utils._send_email_sync')
    @patch ('apps.clubs.meetings.utils.email_utils.render_to_string')
    def test_send_meeting_invitation_sends_when_force_send_true (
    self ,mock_render ,mock_send_email ,mock_has_received 
    ):
        """Test that invitation email is sent when force_send=True even if already sent today."""
        mock_render .return_value ="Test content"
        mock_has_received .return_value =True 

        result =send_meeting_invitation_email (self .meeting ,self .member ,force_send =True )


        assert result is True 


        mock_send_email .assert_called_once ()
        mock_render .assert_called ()


        mock_has_received .assert_not_called ()

    @patch ('apps.clubs.meetings.utils.email_utils.has_received_meeting_invitation_today')
    @patch ('apps.clubs.meetings.utils.email_utils._send_email_sync')
    @patch ('apps.clubs.meetings.utils.email_utils.render_to_string')
    def test_send_meeting_invitation_sends_when_no_previous_invitation (
    self ,mock_render ,mock_send_email ,mock_has_received 
    ):
        """Test that invitation email is sent when user hasn't received one today."""
        mock_render .return_value ="Test content"
        mock_has_received .return_value =False 

        result =send_meeting_invitation_email (self .meeting ,self .member )


        assert result is True 


        mock_send_email .assert_called_once ()
        mock_render .assert_called ()


        mock_has_received .assert_called_once_with (self .member )

    def test_anti_spam_protection_timezone_boundaries (self ):
        """Test anti-spam protection works correctly across timezone boundaries."""

        yesterday_late =timezone .now ().replace (hour =23 ,minute =59 ,second =59 )-timedelta (days =1 )

        attendee =MeetingAttendee .objects .create (
        meeting =self .meeting ,
        user =self .member ,
        status =MeetingAttendee .STATUS_INVITED ,
        )


        attendee .created =yesterday_late 
        attendee .save (update_fields =['created'])


        result =has_received_meeting_invitation_today (self .member )
        assert result is False 



        today_early =timezone .now ().replace (hour =0 ,minute =1 ,second =0 )
        future_time2 =timezone .now ()+timedelta (days =2 )
        meeting2 =ClubMeeting .objects .create (
        club =self .club ,
        title ="Test Meeting 2",
        description ="Test meeting 2 description",
        start_time =future_time2 ,
        end_time =future_time2 +timedelta (hours =1 ),
        organizer =self .manager ,
        )

        attendee2 =MeetingAttendee .objects .create (
        meeting =meeting2 ,
        user =self .member ,
        status =MeetingAttendee .STATUS_ACCEPTED ,
        )

        attendee2 .created =today_early 
        attendee2 .save (update_fields =['created'])


        result =has_received_meeting_invitation_today (self .member )
        assert result is True 

    @patch ('apps.clubs.meetings.utils.email_utils.logger')
    def test_anti_spam_logging (self ,mock_logger ):
        """Test that anti-spam protection logs appropriately."""

        MeetingAttendee .objects .create (
        meeting =self .meeting ,
        user =self .member ,
        status =MeetingAttendee .STATUS_INVITED ,
        )

        result =has_received_meeting_invitation_today (self .member )

        assert result is True 
        mock_logger .info .assert_called_once ()


        log_message =mock_logger .info .call_args [0 ][0 ]
        assert "already received invitation today"in log_message 
        assert self .member .email in log_message 