import pytest
import json
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model
from django.core import mail
from django.utils import timezone
from datetime import timedelta
from unittest.mock import patch

from apps.clubs.models import Club, ClubType
from apps.clubs.meetings.models import ClubMeeting, MeetingAttendee
from apps.clubs.meetings.utils.email_utils import (
    send_meeting_invitation_email,
    send_email_invitation,
    process_email_attendees,
)

User = get_user_model()


@pytest.mark.django_db
class TestMeetingEmailInvitations:
    """
    Test suite for meeting email invitations functionality.
    """

    def setup_method(self):
        """Set up test data."""
        self.client = APIClient()

        self.manager = User.objects.create_user(
            username="manager",
            email="<EMAIL>",
            password="password123",
            fullname="Club Manager",
        )
        self.member1 = User.objects.create_user(
            username="member1",
            email="<EMAIL>",
            password="password123",
            fullname="Club Member 1",
        )
        self.member2 = User.objects.create_user(
            username="member2",
            email="<EMAIL>",
            password="password123",
            fullname="Club Member 2",
        )

        self.club_type = ClubType.objects.create(
            name="Test Club Type",
            created_by=self.manager,
        )

        self.club = Club.objects.create(
            name="Test Club",
            type=self.club_type,
            manager=self.manager,
            privacy=Club.PRIVACY_CLOSED,
            join_permissions=Club.JOIN_PERMISSIONS_INVITE,
        )

        self.club.members.add(self.member1)
        self.club.members.add(self.member2)

        self.create_meeting_url = f"/api/v1/clubs/meetings/club/{self.club.id}/"

        self.meeting_data = {
            "title": "Test Meeting",
            "description": "This is a test meeting",
            "start_time": (timezone.now() + timedelta(days=1)).isoformat(),
            "end_time": (timezone.now() + timedelta(days=1, hours=1)).isoformat(),
            "is_jitsi_meeting": True,
        }

        mail.outbox = []

    def test_send_meeting_invitation_email(self):
        """Test the utility function for sending meeting invitation emails."""

        meeting = ClubMeeting.objects.create(
            club=self.club,
            title="Test Meeting",
            description="This is a test meeting",
            start_time=timezone.now() + timedelta(days=1),
            end_time=timezone.now() + timedelta(days=1, hours=1),
            organizer=self.manager,
            is_jitsi_meeting=True,
        )

        result = send_meeting_invitation_email(meeting, self.member1)

        assert result is True
        assert len(mail.outbox) == 1
        assert (
            mail.outbox[0].subject
            == f"Invitation to {self.club.name} Meeting: {meeting.title}"
        )
        assert self.member1.email in mail.outbox[0].to

        assert meeting.title in mail.outbox[0].body
        assert self.club.name in mail.outbox[0].body
        assert self.manager.fullname in mail.outbox[0].body

    def test_send_email_invitation_existing_user(self):
        """Test sending an invitation to an existing user by email."""

        meeting = ClubMeeting.objects.create(
            club=self.club,
            title="Test Meeting",
            description="This is a test meeting",
            start_time=timezone.now() + timedelta(days=1),
            end_time=timezone.now() + timedelta(days=1, hours=1),
            organizer=self.manager,
            is_jitsi_meeting=True,
        )

        success, user = send_email_invitation(meeting, self.member1.email)

        assert success is True
        assert user == self.member1
        assert len(mail.outbox) == 1
        assert (
            mail.outbox[0].subject
            == f"Invitation to {self.club.name} Meeting: {meeting.title}"
        )
        assert self.member1.email in mail.outbox[0].to

    def test_send_email_invitation_new_user(self):
        """Test sending an invitation to a new user by email."""

        meeting = ClubMeeting.objects.create(
            club=self.club,
            title="Test Meeting",
            description="This is a test meeting",
            start_time=timezone.now() + timedelta(days=1),
            end_time=timezone.now() + timedelta(days=1, hours=1),
            organizer=self.manager,
            is_jitsi_meeting=True,
        )

        new_email = "<EMAIL>"
        new_name = "New User"
        success, user = send_email_invitation(meeting, new_email, new_name)

        assert success is True
        assert user is not None
        assert user.email == new_email

        if hasattr(user, "fullname"):
            assert user.fullname == new_name
        elif hasattr(user, "first_name"):
            assert user.get_full_name() == new_name or user.first_name == new_name
        assert len(mail.outbox) == 1
        assert (
            mail.outbox[0].subject
            == f"Invitation to {self.club.name} Meeting: {meeting.title}"
        )
        assert new_email in mail.outbox[0].to

        assert user in meeting.club.members.all()

    def test_process_email_attendees(self):
        """Test processing multiple email attendees."""

        meeting = ClubMeeting.objects.create(
            club=self.club,
            title="Test Meeting",
            description="This is a test meeting",
            start_time=timezone.now() + timedelta(days=1),
            end_time=timezone.now() + timedelta(days=1, hours=1),
            organizer=self.manager,
            is_jitsi_meeting=True,
        )

        email_attendees = [
            {"email": "<EMAIL>", "name": "New User 1"},
            {"email": "<EMAIL>", "name": "New User 2"},
            {"email": self.member1.email},
        ]

        created_attendees = process_email_attendees(meeting, email_attendees)

        assert len(created_attendees) == 3

        assert len(mail.outbox) == 3

        assert User.objects.filter(email="<EMAIL>").exists()
        assert User.objects.filter(email="<EMAIL>").exists()

        new_user1 = User.objects.get(email="<EMAIL>")
        new_user2 = User.objects.get(email="<EMAIL>")

        assert new_user1 in meeting.club.members.all()
        assert new_user2 in meeting.club.members.all()

        assert MeetingAttendee.objects.filter(meeting=meeting, user=new_user1).exists()
        assert MeetingAttendee.objects.filter(meeting=meeting, user=new_user2).exists()
        assert MeetingAttendee.objects.filter(
            meeting=meeting, user=self.member1
        ).exists()

    @patch("apps.clubs.meetings.utils.email_utils.send_email_invitation")
    def test_create_meeting_with_email_attendees(self, mock_send_email):
        """Test creating a meeting with email attendees."""

        mock_send_email.side_effect = lambda meeting, email, name: (
            True,
            User.objects.create_user(
                username=f"user_{email.split('@')[0]}",
                email=email,
                fullname=name or "",
            ),
        )

        self.client.force_authenticate(user=self.manager)

        meeting_data = self.meeting_data.copy()
        meeting_data["email_attendees"] = [
            {"email": "<EMAIL>", "name": "New User 1"},
            {"email": "<EMAIL>", "name": "New User 2"},
        ]

        response = self.client.post(
            self.create_meeting_url,
            json.dumps(meeting_data),
            content_type="application/json",
        )

        assert response.status_code == status.HTTP_201_CREATED

        assert mock_send_email.call_count == 2

        meeting = ClubMeeting.objects.get(id=response.data["id"])

        attendees = MeetingAttendee.objects.filter(meeting=meeting)
        assert attendees.count() == 5

    def test_create_meeting_with_invalid_email_attendees(self):
        """Test creating a meeting with invalid email attendees."""

        self.client.force_authenticate(user=self.manager)

        meeting_data = self.meeting_data.copy()
        meeting_data["email_attendees"] = "not a list"

        response = self.client.post(
            self.create_meeting_url,
            json.dumps(meeting_data),
            content_type="application/json",
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "email_attendees" in response.data

    def test_create_meeting_with_duplicate_email_attendees(self):
        """Test creating a meeting with duplicate email attendees."""

        self.client.force_authenticate(user=self.manager)

        meeting_data = self.meeting_data.copy()
        meeting_data["email_attendees"] = [
            {"email": "<EMAIL>", "name": "User 1"},
            {"email": "<EMAIL>", "name": "User 2"},
        ]

        response = self.client.post(
            self.create_meeting_url,
            json.dumps(meeting_data),
            content_type="application/json",
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "email_attendees" in response.data
