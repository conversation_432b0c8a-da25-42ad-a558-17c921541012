import pytest 
from django .utils import timezone 
from datetime import timed<PERSON><PERSON> 
from rest_framework .test import APIClient 
from rest_framework import status 
from django .contrib .auth import get_user_model 
from django .db import transaction 
from django .urls import reverse 
from unittest .mock import patch 
import uuid 
from django .test import TestCase 
from rest_framework_simplejwt .tokens import RefreshToken 

from apps .clubs .models import Club ,ClubType 
from apps .clubs .meetings .models import ClubMeeting ,MeetingAttendee 

User =get_user_model ()


@pytest .mark .django_db 
class TestBulkAttendeeOperations :
    """
    Comprehensive test suite for bulk meeting attendee operations.
    
    Tests cover:
    - Single and bulk operations for adding/removing attendees
    - Permission validation
    - Club membership validation
    - Error handling and transaction safety
    - Response structure validation
    """

    def setup_method (self ):
        """Set up test data for each test method."""
        self .client =APIClient ()

        self .user =User .objects .create_user (
        username ="testuser",
        email ="<EMAIL>",
        password ="password123"
        )

        self .manager =User .objects .create_user (
        username ="manager",
        email ="<EMAIL>",
        password ="password123"
        )


        self .organizer =User .objects .create_user (
        username ="organizer",
        email ="<EMAIL>",
        password ="password123"
        )


        self .member1 =User .objects .create_user (
        username ="member1",
        email ="<EMAIL>",
        password ="password123"
        )
        self .member2 =User .objects .create_user (
        username ="member2",
        email ="<EMAIL>",
        password ="password123"
        )
        self .member3 =User .objects .create_user (
        username ="member3",
        email ="<EMAIL>",
        password ="password123"
        )


        self .non_member =User .objects .create_user (
        username ="nonmember",
        email ="<EMAIL>",
        password ="password123"
        )

        self .club_type =ClubType .objects .create (
        name ="Test Club Type",
        created_by =self .manager 
        )

        self .club =Club .objects .create (
        name ="Test Club",
        type =self .club_type ,
        manager =self .manager 
        )


        self .club .members .add (
        self .manager ,
        self .organizer ,
        self .user ,
        self .member1 ,
        self .member2 ,
        self .member3 
        )

        future_time =timezone .now ()+timedelta (hours =1 )
        self .meeting =ClubMeeting .objects .create (
        club =self .club ,
        title ="Test Meeting",
        description ="A test meeting",
        start_time =future_time ,
        end_time =future_time +timedelta (hours =1 ),
        organizer =self .organizer ,
        is_jitsi_meeting =True 
        )


        MeetingAttendee .objects .create (
        meeting =self .meeting ,
        user =self .organizer ,
        status =MeetingAttendee .STATUS_ACCEPTED 
        )

    def get_tokens_for_user (self ,user ):
        """Generate JWT tokens for a user."""
        refresh =RefreshToken .for_user (user )
        return {
        'access':str (refresh .access_token ),
        'refresh':str (refresh )
        }

    def test_bulk_add_attendees_success (self ):
        """Test successfully adding multiple attendees."""
        self .client .force_authenticate (user =self .manager )

        data ={
        "user_ids":[str (self .member1 .id ),str (self .member2 .id ),str (self .member3 .id )]
        }

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/"
        response =self .client .post (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_201_CREATED 


        assert "total_processed"in response .data 
        assert "successful"in response .data 
        assert "failed"in response .data 
        assert "successful_users"in response .data 
        assert "failed_users"in response .data 
        assert "attendees"in response .data 


        assert response .data ["total_processed"]==3 
        assert response .data ["successful"]==3 
        assert response .data ["failed"]==0 
        assert len (response .data ["successful_users"])==3 
        assert len (response .data ["failed_users"])==0 


        assert MeetingAttendee .objects .filter (meeting =self .meeting ).count ()==4 

    def test_single_add_attendee_success (self ):
        """Test successfully adding a single attendee using user_id."""
        self .client .force_authenticate (user =self .manager )

        data ={"user_id":str (self .member1 .id )}

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/"
        response =self .client .post (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_201_CREATED 
        assert response .data ["total_processed"]==1 
        assert response .data ["successful"]==1 
        assert response .data ["failed"]==0 

    def test_add_non_member_fails (self ):
        """Test that adding non-club members fails."""
        self .client .force_authenticate (user =self .manager )

        data ={
        "user_ids":[str (self .member1 .id ),str (self .non_member .id )]
        }

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/"
        response =self .client .post (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_400_BAD_REQUEST
        error_text = str(response.data)
        assert ("not members of this club" in error_text) or ("ليسوا أعضاء في هذا النادي" in error_text)

    def test_add_already_attendee_partial_success (self ):
        """Test adding users where some are already attendees."""

        MeetingAttendee .objects .create (
        meeting =self .meeting ,
        user =self .member1 ,
        status =MeetingAttendee .STATUS_ACCEPTED ,
        )

        self .client .force_authenticate (user =self .manager )

        data ={
        "user_ids":[str (self .member1 .id ),str (self .member2 .id )]
        }

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/"
        response =self .client .post (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_201_CREATED 
        assert response .data ["total_processed"]==2
        assert response .data ["successful"]==1
        assert response .data ["failed"]==1
        assert len (response .data ["failed_users"])==1
        error_text = str(response.data["failed_users"][0]["error"])
        assert ("already an attendee" in error_text) or ("حاضر بالفعل" in error_text)

    def test_add_duplicate_ids_in_request (self ):
        """Test that duplicate IDs in the request are handled correctly."""
        self .client .force_authenticate (user =self .manager )

        data ={
        "user_ids":[str (self .member1 .id ),str (self .member1 .id ),str (self .member2 .id )]
        }

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/"
        response =self .client .post (url ,data ,format ="json")


        assert response .status_code ==status .HTTP_201_CREATED 
        assert response .data ["total_processed"]==2 

    def test_add_nonexistent_user_fails (self ):
        """Test that adding non-existent users fails."""
        self .client .force_authenticate (user =self .manager )

        fake_uuid ="00000000-0000-0000-0000-000000000000"
        data ={
        "user_ids":[str (self .member1 .id ),fake_uuid ]
        }

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/"
        response =self .client .post (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_400_BAD_REQUEST
        error_text = str(response.data)
        assert ("do not exist" in error_text) or ("غير موجودة" in error_text)



    def test_bulk_remove_attendees_success (self ):
        """Test successfully removing multiple attendees."""

        MeetingAttendee .objects .create (
        meeting =self .meeting ,
        user =self .user ,
        status =MeetingAttendee .STATUS_ACCEPTED ,
        )

        self .client .force_authenticate (user =self .manager )

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/?user_ids={self.user.id}"
        response =self .client .delete (url )

        assert response .status_code ==status .HTTP_200_OK 
        assert response .data ["total_processed"]==1 
        assert response .data ["successful"]==1 
        assert response .data ["failed"]==0 


        assert MeetingAttendee .objects .filter (meeting =self .meeting ).count ()==1 

    def test_single_remove_attendee_success (self ):
        """Test successfully removing a single attendee."""

        MeetingAttendee .objects .create (
        meeting =self .meeting ,
        user =self .user ,
        status =MeetingAttendee .STATUS_ACCEPTED ,
        )

        self .client .force_authenticate (user =self .manager )

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/?user_id={self.user.id}"
        response =self .client .delete (url )

        assert response .status_code ==status .HTTP_200_OK 
        assert response .data ["total_processed"]==1 
        assert response .data ["successful"]==1 
        assert response .data ["failed"]==0 

    def test_remove_organizer_fails (self ):
        """Test that removing the meeting organizer fails."""
        self .client .force_authenticate (user =self .manager )

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/?user_id={self.organizer.id}"
        response =self .client .delete (url )

        assert response .status_code ==status .HTTP_200_OK
        assert response .data ["total_processed"]==1
        assert response .data ["successful"]==0
        assert response .data ["failed"]==1
        error_text = str(response.data["failed_users"][0]["error"])
        assert ("Cannot remove the meeting organizer" in error_text) or ("لا يمكن إزالة منظم الاجتماع" in error_text)

    def test_remove_non_attendee_user (self ):
        """Test removing a user who is not an attendee."""
        self .client .force_authenticate (user =self .manager )

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/?user_id={self.user.id}"
        response =self .client .delete (url )

        assert response .status_code ==status .HTTP_200_OK
        assert response .data ["total_processed"]==1
        assert response .data ["successful"]==0
        assert response .data ["failed"]==1
        error_text = str(response.data["failed_users"][0]["error"])
        assert ("not an attendee" in error_text) or ("ليس من المشاركين" in error_text)



    def test_empty_request_fails (self ):
        """Test that requests with no user data fail."""
        self .client .force_authenticate (user =self .manager )

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/"
        response =self .client .post (url ,{},format ="json")

        assert response .status_code ==status .HTTP_400_BAD_REQUEST 
        error_text =str (response .data )
        assert ("Either"in error_text and "user_id"in error_text and "user_ids"in error_text and "must be provided"in error_text )or "يجب توفير إما"in error_text 

    def test_empty_delete_request_fails (self ):
        """Test that DELETE requests with no query parameters fail."""
        self .client .force_authenticate (user =self .manager )

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/"
        response =self .client .delete (url )

        assert response .status_code ==status .HTTP_400_BAD_REQUEST 
        error_text =str (response .data )
        assert ("Either"in error_text and "user_id"in error_text and "user_ids"in error_text and "must be provided"in error_text )or "يجب توفير إما"in error_text 

    def test_both_user_id_and_user_ids_fails (self ):
        """Test that providing both user_id and user_ids fails."""
        self .client .force_authenticate (user =self .manager )

        data ={
        "user_id":str (self .user .id ),
        "user_ids":[str (self .user .id )]
        }

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/"
        response =self .client .post (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_400_BAD_REQUEST 
        error_text =str (response .data )
        assert ("not both"in error_text )or "وليس كلاهما"in error_text 

    def test_both_query_params_fails_on_delete (self ):
        """Test that providing both user_id and user_ids in query params fails."""
        self .client .force_authenticate (user =self .manager )

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/?user_id={self.user.id}&user_ids={self.user.id}"
        response =self .client .delete (url )

        assert response .status_code ==status .HTTP_400_BAD_REQUEST 
        error_text =str (response .data )
        assert ("not both"in error_text )or "وليس كلاهما"in error_text 

    def test_invalid_uuid_format_fails (self ):
        """Test that invalid UUID formats fail."""
        self .client .force_authenticate (user =self .manager )

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/?user_id=invalid-uuid"
        response =self .client .delete (url )

        assert response .status_code ==status .HTTP_400_BAD_REQUEST 

    def test_invalid_user_ids_format_fails (self ):
        """Test that invalid user_ids format fails."""
        self .client .force_authenticate (user =self .manager )

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/?user_ids=invalid-uuid,another-invalid"
        response =self .client .delete (url )

        assert response .status_code ==status .HTTP_400_BAD_REQUEST 

    def test_empty_user_ids_list_fails (self ):
        """Test that empty user_ids list fails."""
        self .client .force_authenticate (user =self .manager )

        data ={"user_ids":[]}

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/"
        response =self .client .post (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_400_BAD_REQUEST 

    def test_too_many_user_ids_fails (self ):
        """Test that exceeding the maximum user_ids limit fails."""
        self .client .force_authenticate (user =self .manager )


        fake_ids =[f"00000000-0000-0000-0000-{i:012d}"for i in range (51 )]
        data ={"user_ids":fake_ids }

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/"
        response =self .client .post (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_400_BAD_REQUEST 



    def test_non_member_cannot_manage_attendees (self ):
        """Test that non-club members cannot manage attendees."""
        self .client .force_authenticate (user =self .user )

        data ={"user_id":str (self .user .id )}

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/"
        response =self .client .post (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_403_FORBIDDEN 

    def test_regular_member_cannot_manage_attendees (self ):
        """Test that regular club members cannot manage attendees."""
        self .client .force_authenticate (user =self .user )

        data ={"user_id":str (self .user .id )}

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/"
        response =self .client .post (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_403_FORBIDDEN 

    def test_manager_can_manage_attendees (self ):
        """Test that club manager can manage attendees."""
        self .client .force_authenticate (user =self .manager )

        data ={"user_id":str (self .user .id )}

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/"
        response =self .client .post (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_201_CREATED 

    def test_organizer_can_manage_attendees (self ):
        """Test that meeting organizer can manage attendees."""
        self .client .force_authenticate (user =self .manager )

        data ={"user_id":str (self .user .id )}

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/"
        response =self .client .post (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_201_CREATED 

    def test_unauthenticated_cannot_manage_attendees (self ):
        """Test that unauthenticated users cannot manage attendees."""
        data ={"user_id":str (self .user .id )}

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/"
        response =self .client .post (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_401_UNAUTHORIZED 



    def test_transaction_rollback_on_error (self ):
        """Test that transactions are properly rolled back on errors."""

        MeetingAttendee .objects .create (
        meeting =self .meeting ,
        user =self .user ,
        status =MeetingAttendee .STATUS_ACCEPTED 
        )

        self .client .force_authenticate (user =self .manager )


        data ={
        "user_ids":[str (self .user .id )]
        }

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/"

        initial_count =MeetingAttendee .objects .filter (meeting =self .meeting ).count ()

        response =self .client .post (url ,data ,format ="json")


        assert response .status_code ==status .HTTP_400_BAD_REQUEST 


        final_count =MeetingAttendee .objects .filter (meeting =self .meeting ).count ()
        assert final_count ==initial_count 

    def test_mixed_success_failure_response (self ):
        """Test response structure when some operations succeed and others fail."""

        MeetingAttendee .objects .create (
        meeting =self .meeting ,
        user =self .user ,
        status =MeetingAttendee .STATUS_ACCEPTED ,
        )

        self .client .force_authenticate (user =self .manager )

        data ={
        "user_ids":[
        str (self .user .id ),
        str (self .member1 .id )
        ]
        }

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/"
        response =self .client .post (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_201_CREATED 
        assert response .data ["total_processed"]==2 
        assert response .data ["successful"]==1 
        assert response .data ["failed"]==1 

    def test_response_includes_attendee_details (self ):
        """Test that responses include detailed attendee information."""
        self .client .force_authenticate (user =self .manager )

        data ={"user_id":str (self .user .id )}

        url =f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/"
        response =self .client .post (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_201_CREATED 
        assert "attendees"in response .data 
        assert len (response .data ["attendees"])>0 


        attendee =response .data ["attendees"][0 ]
        expected_fields =["id","meeting","user","user_details","status","created","updated"]
        for field in expected_fields :
            assert field in attendee 

    def test_nonexistent_meeting_returns_404 (self ):
        """Test that nonexistent meeting returns 404."""
        self .client .force_authenticate (user =self .manager )

        data ={"user_id":str (self .user .id )}

        fake_meeting_id ="00000000-0000-0000-0000-000000000000"
        url =f"/api/v1/clubs/meetings/meetings/{fake_meeting_id}/attendees/"
        response =self .client .post (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_404_NOT_FOUND 

    def test_bulk_remove_attendees_prevents_organizer_removal (self ):
        """Test that meeting organizer cannot be removed via bulk operations."""


        response =self .client .delete (
        f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/?user_ids={self.organizer.id}",
        **{"HTTP_AUTHORIZATION":f"Bearer {self.get_tokens_for_user(self.manager)['access']}"}
        )

        assert response .status_code ==200 
        data =response .json ()


        assert data ["total_processed"]==1 
        assert data ["successful"]==0 
        assert data ["failed"]==1 
        error_text = str(data["failed_users"][0]["error"])
        assert ("Cannot remove the meeting organizer" in error_text) or ("لا يمكن إزالة منظم الاجتماع" in error_text)

    def test_bulk_remove_attendees_prevents_club_manager_removal (self ):
        """Test that club manager (owner) cannot be removed from meetings."""

        other_organizer =User .objects .create_user (
        username ="other_organizer",
        email ="<EMAIL>",
        password ="password123",
        )
        self .club .members .add (other_organizer )


        future_time =timezone .now ()+timedelta (days =2 )
        new_meeting =ClubMeeting .objects .create (
        club =self .club ,
        title ="Another Meeting",
        description ="Another test meeting",
        start_time =future_time ,
        end_time =future_time +timedelta (hours =1 ),
        organizer =other_organizer ,
        )


        MeetingAttendee .objects .create (
        meeting =new_meeting ,
        user =self .manager ,
        status =MeetingAttendee .STATUS_ACCEPTED 
        )


        auth_headers ={"HTTP_AUTHORIZATION":f"Bearer {self.get_tokens_for_user(other_organizer)['access']}"}

        response =self .client .delete (
        f"/api/v1/clubs/meetings/meetings/{new_meeting.id}/attendees/?user_ids={self.manager.id}",
        **auth_headers 
        )

        assert response .status_code ==200 
        data =response .json ()


        assert data ["total_processed"]==1 
        assert data ["successful"]==0 
        assert data ["failed"]==1 
        error_text = str(data["failed_users"][0]["error"])
        assert ("Cannot remove the club manager" in error_text) or ("لا يمكن إزالة مدير النادي" in error_text)


        assert MeetingAttendee .objects .filter (
        meeting =new_meeting ,
        user =self .manager 
        ).exists ()

    @patch ('apps.clubs.meetings.utils.email_utils.has_received_meeting_invitation_today')
    @patch ('apps.clubs.meetings.utils.email_utils._send_email_sync')
    @patch ('apps.clubs.meetings.utils.email_utils.render_to_string')
    def test_email_anti_spam_protection (self ,mock_render ,mock_send_email ,mock_has_received ):
        """Test that email anti-spam protection prevents duplicate invitations."""
        mock_render .return_value ="Test content"
        mock_has_received .return_value =True 


        new_user =User .objects .create_user (
        username ="newuser",
        email ="<EMAIL>",
        password ="password123",
        )
        self .club .members .add (new_user )

        response =self .client .post (
        f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/",
        {"user_id":str (new_user .id )},
        format ="json",
        **{"HTTP_AUTHORIZATION":f"Bearer {self.get_tokens_for_user(self.manager)['access']}"}
        )

        assert response .status_code ==201 
        data =response .json ()


        assert data ["successful"]==1 
        assert str (new_user .id )in data ["successful_users"]


        mock_send_email .assert_not_called ()
        mock_has_received .assert_called_once_with (new_user )


        assert MeetingAttendee .objects .filter (
        meeting =self .meeting ,
        user =new_user 
        ).exists ()

    @patch ('apps.clubs.meetings.utils.email_utils.has_received_meeting_invitation_today')
    @patch ('apps.clubs.meetings.utils.email_utils._send_email_sync')
    @patch ('apps.clubs.meetings.utils.email_utils.render_to_string')
    def test_email_sent_when_no_spam_protection_triggered (self ,mock_render ,mock_send_email ,mock_has_received ):
        """Test that email is sent when anti-spam protection is not triggered."""
        mock_render .return_value ="Test content"
        mock_has_received .return_value =False 


        new_user =User .objects .create_user (
        username ="freshuser",
        email ="<EMAIL>",
        password ="password123",
        )
        self .club .members .add (new_user )

        response =self .client .post (
        f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/",
        {"user_id":str (new_user .id )},
        format ="json",
        **{"HTTP_AUTHORIZATION":f"Bearer {self.get_tokens_for_user(self.manager)['access']}"}
        )

        assert response .status_code ==201 
        data =response .json ()


        assert data ["successful"]==1 
        assert str (new_user .id )in data ["successful_users"]


        mock_send_email .assert_called_once ()
        mock_has_received .assert_called_once_with (new_user )


        assert MeetingAttendee .objects .filter (
        meeting =self .meeting ,
        user =new_user 
        ).exists ()

    def test_bulk_operations_with_mixed_club_manager_and_regular_users (self ):
        """Test bulk operations with a mix of club manager and regular users."""

        user1 =User .objects .create_user (
        username ="user1",email ="<EMAIL>",password ="password123"
        )
        user2 =User .objects .create_user (
        username ="user2",email ="<EMAIL>",password ="password123"
        )


        self .club .members .add (user1 ,user2 )


        for user in [self .manager ,user1 ,user2 ]:
            MeetingAttendee .objects .create (
            meeting =self .meeting ,
            user =user ,
            status =MeetingAttendee .STATUS_ACCEPTED 
            )


        user_ids =f"{self.manager.id},{user1.id},{user2.id}"
        response =self .client .delete (
        f"/api/v1/clubs/meetings/meetings/{self.meeting.id}/attendees/?user_ids={user_ids}",
        **{"HTTP_AUTHORIZATION":f"Bearer {self.get_tokens_for_user(self.manager)['access']}"}
        )

        assert response .status_code ==200 
        data =response .json ()


        assert data ["total_processed"]==3 
        assert data ["successful"]==2 
        assert data ["failed"]==1 


        assert str (user1 .id )in data ["successful_users"]
        assert str (user2 .id )in data ["successful_users"]


        failed_user_ids =[fu ["user_id"]for fu in data ["failed_users"]]
        assert str (self .manager .id )in failed_user_ids 


        assert not MeetingAttendee .objects .filter (meeting =self .meeting ,user =user1 ).exists ()
        assert not MeetingAttendee .objects .filter (meeting =self .meeting ,user =user2 ).exists ()
        assert MeetingAttendee .objects .filter (meeting =self .meeting ,user =self .manager ).exists ()