from unittest.mock import patch
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase, APIClient
from django.utils import timezone

from apps.accounts.user.models import User
from apps.google_calendar.models import GoogleCalendarCredentials
from apps.clubs.models import Club, ClubType
from apps.clubs.meetings.models import ClubMeeting


class ClubMeetingGoogleCalendarTests(APITestCase):
    def setUp(self):

        self.club_manager = User.objects.create_user(
            email="<EMAIL>",
            password="password123",
            username="clubmanager",
            fullname="Club Manager",
            role="club_manager",
        )

        self.member = User.objects.create_user(
            email="<EMAIL>",
            password="password123",
            username="member",
            fullname="Club Member",
            role="member",
        )

        self.club_type = ClubType.objects.create(
            name="Test Club Type", created_by=self.club_manager, visibility="public"
        )

        self.club = Club.objects.create(
            name="Test Club",
            manager=self.club_manager,
            type=self.club_type,
        )

        self.client = APIClient()

    def test_club_manager_google_calendar_sync_status(self):
        """Test that a club manager's google_calendar_synced status is returned in profile responses"""

        self.assertFalse(self.club_manager.google_calendar_synced)

        self.client.force_authenticate(user=self.club_manager)

        url = reverse("get-me")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("google_calendar_synced", response.data)
        self.assertFalse(response.data["google_calendar_synced"])

        self.club_manager.google_calendar_synced = True
        self.club_manager.save()

        GoogleCalendarCredentials.objects.create(
            user=self.club_manager,
            credentials='{"token": "fake_token", "refresh_token": "fake_refresh_token"}',
        )

        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["google_calendar_synced"])

    @patch("apps.google_calendar.services.create_or_update_google_event")
    def test_create_meeting_uses_google_calendar_if_synced(self, mock_create_event):
        """Test that creating a club meeting tries to create a Google Calendar event if synced"""

        mock_create_event.return_value = (
            "fake_event_id",
            "https://meet.google.com/fake-link",
        )

        self.club_manager.google_calendar_synced = True
        self.club_manager.save()

        GoogleCalendarCredentials.objects.create(
            user=self.club_manager,
            credentials='{"token": "fake_token", "refresh_token": "fake_refresh_token"}',
        )

        self.client.force_authenticate(user=self.club_manager)

        start_time = timezone.now() + timezone.timedelta(days=1)
        end_time = start_time + timezone.timedelta(hours=1)

        meeting = ClubMeeting.objects.create(
            title="Test Meeting",
            description="Meeting description",
            club=self.club,
            organizer=self.club_manager,
            start_time=start_time,
            end_time=end_time,
        )

        self.assertEqual(meeting.organizer.google_calendar_synced, True)

        url = reverse("get-me")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["google_calendar_synced"])
