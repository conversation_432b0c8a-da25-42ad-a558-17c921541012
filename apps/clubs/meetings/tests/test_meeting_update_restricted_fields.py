import pytest 
from django .utils import timezone 
from datetime import timed<PERSON><PERSON> 
from rest_framework .test import APIClient 
from rest_framework import status 
from django .contrib .auth import get_user_model 
from django .utils .dateparse import parse_datetime 

from apps .clubs .models import Club ,ClubType 
from apps .clubs .meetings .models import ClubMeeting ,MeetingAttendee 

User =get_user_model ()


@pytest .mark .django_db 
class TestMeetingUpdateRestrictedFields :
    """
    Test suite for meeting update functionality with restricted fields.
    Ensures only title, description, start_time, and end_time can be updated.
    All fields are optional but at least one must be provided.
    """

    def setup_method (self ):
        """Set up test data for each test method."""
        self .client =APIClient ()


        self .manager =User .objects .create_user (
        username ="manager",
        email ="<EMAIL>",
        password ="password123",
        fullname ="Test Manager",
        )
        self .organizer =User .objects .create_user (
        username ="organizer",
        email ="<EMAIL>",
        password ="password123",
        fullname ="Test Organizer",
        )
        self .member =User .objects .create_user (
        username ="member",
        email ="<EMAIL>",
        password ="password123",
        fullname ="Test Member",
        )
        self .non_member =User .objects .create_user (
        username ="nonmember",
        email ="<EMAIL>",
        password ="password123",
        fullname ="Non Member",
        )


        self .club_type =ClubType .objects .create (
        name ="Test Club Type",
        created_by =self .manager ,
        )

        self .club =Club .objects .create (
        name ="Test Club",
        type =self .club_type ,
        manager =self .manager ,
        )


        self .club .members .add (self .manager ,self .organizer ,self .member )


        self .future_time =timezone .now ()+timedelta (days =2 )
        self .meeting =ClubMeeting .objects .create (
        club =self .club ,
        title ="Original Title",
        description ="Original description",
        start_time =self .future_time ,
        end_time =self .future_time +timedelta (hours =1 ),
        organizer =self .organizer ,
        is_jitsi_meeting =True ,
        )


        MeetingAttendee .objects .create (
        meeting =self .meeting ,
        user =self .organizer ,
        status =MeetingAttendee .STATUS_ACCEPTED ,
        )

    def test_empty_update_request_fails (self ):
        """Test that empty update requests are rejected."""
        self .client .force_authenticate (user =self .organizer )

        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"


        response =self .client .put (url ,{},format ="json")
        assert response .status_code ==status .HTTP_400_BAD_REQUEST 

        error_text =str (response .data )
        assert ("At least one field must be provided for update"in error_text or 
        "يجب توفير حقل واحد على الأقل للتحديث"in error_text )


        response =self .client .patch (url ,{},format ="json")
        assert response .status_code ==status .HTTP_400_BAD_REQUEST 
        error_text =str (response .data )
        assert ("At least one field must be provided for update"in error_text or 
        "يجب توفير حقل واحد على الأقل للتحديث"in error_text )

    def test_patch_single_field_updates_success (self ):
        """Test that PATCH with single field updates work correctly."""
        self .client .force_authenticate (user =self .organizer )
        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"

        original_description =self .meeting .description 
        original_start_time =self .meeting .start_time 
        original_end_time =self .meeting .end_time 


        response =self .client .patch (url ,{"title":"New Title Only"},format ="json")
        assert response .status_code ==status .HTTP_200_OK 

        self .meeting .refresh_from_db ()
        assert self .meeting .title =="New Title Only"
        assert self .meeting .description ==original_description 
        assert self .meeting .start_time ==original_start_time 
        assert self .meeting .end_time ==original_end_time 

    def test_patch_description_only_success (self ):
        """Test updating only description via PATCH."""
        self .client .force_authenticate (user =self .organizer )
        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"

        original_title =self .meeting .title 

        response =self .client .patch (url ,{"description":"New Description Only"},format ="json")
        assert response .status_code ==status .HTTP_200_OK 

        self .meeting .refresh_from_db ()
        assert self .meeting .description =="New Description Only"
        assert self .meeting .title ==original_title 

    def test_patch_start_time_only_success (self ):
        """Test updating only start_time via PATCH."""
        self .client .force_authenticate (user =self .organizer )
        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"



        new_start_time =self .future_time +timedelta (minutes =30 )
        original_title =self .meeting .title 
        original_description =self .meeting .description 
        original_end_time =self .meeting .end_time 

        response =self .client .patch (url ,{"start_time":new_start_time .isoformat ()},format ="json")

        if response .status_code !=status .HTTP_200_OK :
            print (f"Response data: {response.data}")
            print (f"New start time: {new_start_time}")
            print (f"Original start time: {self.meeting.start_time}")
            print (f"Meeting end time: {self.meeting.end_time}")

        assert response .status_code ==status .HTTP_200_OK 

        self .meeting .refresh_from_db ()
        assert self .meeting .start_time .replace (microsecond =0 )==new_start_time .replace (microsecond =0 )
        assert self .meeting .title ==original_title 
        assert self .meeting .description ==original_description 
        assert self .meeting .end_time ==original_end_time 

    def test_patch_end_time_only_success (self ):
        """Test updating only end_time via PATCH."""
        self .client .force_authenticate (user =self .organizer )
        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"

        new_end_time =self .meeting .end_time +timedelta (hours =1 )
        original_title =self .meeting .title 

        response =self .client .patch (url ,{"end_time":new_end_time .isoformat ()},format ="json")
        assert response .status_code ==status .HTTP_200_OK 

        self .meeting .refresh_from_db ()
        assert self .meeting .end_time .replace (microsecond =0 )==new_end_time .replace (microsecond =0 )
        assert self .meeting .title ==original_title 

    def test_patch_multiple_fields_success (self ):
        """Test updating multiple fields via PATCH."""
        self .client .force_authenticate (user =self .organizer )
        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"

        original_start_time =self .meeting .start_time 
        original_end_time =self .meeting .end_time 

        data ={
        "title":"Updated Title",
        "description":"Updated Description"
        }

        response =self .client .patch (url ,data ,format ="json")
        assert response .status_code ==status .HTTP_200_OK 

        self .meeting .refresh_from_db ()
        assert self .meeting .title =="Updated Title"
        assert self .meeting .description =="Updated Description"
        assert self .meeting .start_time ==original_start_time 
        assert self .meeting .end_time ==original_end_time 

    def test_put_requires_all_fields_or_fails (self ):
        """Test that PUT still validates field requirements properly."""
        self .client .force_authenticate (user =self .organizer )
        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"


        data ={
        "title":"New Title"
        }

        response =self .client .put (url ,data ,format ="json")
        assert response .status_code ==status .HTTP_200_OK 

    def test_validation_with_partial_time_update (self ):
        """Test validation when updating only one time field."""
        self .client .force_authenticate (user =self .organizer )
        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"


        invalid_end_time =self .meeting .start_time -timedelta (minutes =30 )

        response =self .client .patch (url ,{"end_time":invalid_end_time .isoformat ()},format ="json")
        assert response .status_code ==status .HTTP_400_BAD_REQUEST 
        assert "end_time"in response .data 

    def test_minimum_duration_validation_with_partial_update (self ):
        """Test minimum duration validation in partial updates."""
        self .client .force_authenticate (user =self .organizer )
        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"


        short_end_time =self .meeting .start_time +timedelta (minutes =10 )

        response =self .client .patch (url ,{"end_time":short_end_time .isoformat ()},format ="json")
        assert response .status_code ==status .HTTP_400_BAD_REQUEST 
        assert "end_time"in response .data 
        error_text =str (response .data )
        assert ("15 minutes"in error_text or "15 دقيقة"in error_text )

    def test_past_start_time_validation_in_patch (self ):
        """Test past time validation in PATCH requests."""
        self .client .force_authenticate (user =self .organizer )
        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"

        past_time =timezone .now ()-timedelta (hours =1 )

        response =self .client .patch (url ,{"start_time":past_time .isoformat ()},format ="json")
        assert response .status_code ==status .HTTP_400_BAD_REQUEST 
        assert "start_time"in response .data 

    def test_all_fields_optional_but_validates_relationships (self ):
        """Test that field relationships are still validated even when all fields are optional."""
        self .client .force_authenticate (user =self .organizer )
        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"


        new_start =timezone .now ()+timedelta (days =4 )
        new_end =new_start +timedelta (hours =1 )

        data ={
        "start_time":new_start .isoformat (),
        "end_time":new_end .isoformat ()
        }

        response =self .client .patch (url ,data ,format ="json")
        assert response .status_code ==status .HTTP_200_OK 


        data ={
        "start_time":new_end .isoformat (),
        "end_time":new_start .isoformat ()
        }

        response =self .client .patch (url ,data ,format ="json")
        assert response .status_code ==status .HTTP_400_BAD_REQUEST 

    def test_put_update_allowed_fields_success (self ):
        """Test that PUT request can update all allowed fields successfully."""
        self .client .force_authenticate (user =self .organizer )

        new_start_time =timezone .now ()+timedelta (days =5 )
        new_end_time =new_start_time +timedelta (hours =2 )

        data ={
        "title":"Updated Title",
        "description":"Updated description",
        "start_time":new_start_time .isoformat (),
        "end_time":new_end_time .isoformat (),
        }

        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"
        response =self .client .put (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_200_OK 


        self .meeting .refresh_from_db ()
        assert self .meeting .title =="Updated Title"
        assert self .meeting .description =="Updated description"
        assert self .meeting .start_time .replace (microsecond =0 )==new_start_time .replace (microsecond =0 )
        assert self .meeting .end_time .replace (microsecond =0 )==new_end_time .replace (microsecond =0 )


        assert "attendees_details"in response .data 
        assert len (response .data ["attendees_details"])==1 

    def test_patch_update_partial_fields_success (self ):
        """Test that PATCH request can update individual fields."""
        self .client .force_authenticate (user =self .manager )

        data ={"title":"Partially Updated Title"}

        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"
        response =self .client .patch (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_200_OK 


        self .meeting .refresh_from_db ()
        assert self .meeting .title =="Partially Updated Title"
        assert self .meeting .description =="Original description"

    def test_update_restricted_fields_ignored (self ):
        """Test that restricted fields are ignored even if provided."""
        self .client .force_authenticate (user =self .organizer )

        original_club =self .meeting .club 
        original_organizer =self .meeting .organizer 
        original_status =self .meeting .status 


        another_club =Club .objects .create (
        name ="Another Club",
        type =self .club_type ,
        manager =self .manager ,
        )

        data ={
        "title":"Updated Title",
        "description":"Updated description",
        "start_time":self .future_time .isoformat (),
        "end_time":(self .future_time +timedelta (hours =1 )).isoformat (),

        "club":str (another_club .id ),
        "organizer":str (self .manager .id ),
        "status":"completed",
        "is_jitsi_meeting":False ,
        "meeting_link":"http://malicious.com",
        "jitsi_room_name":"hacker_room",
        }

        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"
        response =self .client .put (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_200_OK 


        self .meeting .refresh_from_db ()
        assert self .meeting .club ==original_club 
        assert self .meeting .organizer ==original_organizer 
        assert self .meeting .status ==original_status 
        assert self .meeting .is_jitsi_meeting ==True 


        assert self .meeting .title =="Updated Title"
        assert self .meeting .description =="Updated description"

    def test_time_validation_start_after_end (self ):
        """Test validation when start_time is after end_time."""
        self .client .force_authenticate (user =self .organizer )

        data ={
        "title":"Updated Title",
        "description":"Updated description",
        "start_time":(self .future_time +timedelta (hours =2 )).isoformat (),
        "end_time":self .future_time .isoformat (),
        }

        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"
        response =self .client .put (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_400_BAD_REQUEST 
        assert "end_time"in response .data 

    def test_minimum_duration_validation (self ):
        """Test validation for minimum meeting duration."""
        self .client .force_authenticate (user =self .organizer )

        start_time =self .future_time 
        end_time =start_time +timedelta (minutes =10 )

        data ={
        "title":"Short Meeting",
        "description":"Too short",
        "start_time":start_time .isoformat (),
        "end_time":end_time .isoformat (),
        }

        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"
        response =self .client .put (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_400_BAD_REQUEST 
        assert "end_time"in response .data 

    def test_past_start_time_validation_for_future_meetings (self ):
        """Test that start_time cannot be set to past for future meetings."""
        self .client .force_authenticate (user =self .organizer )

        past_time =timezone .now ()-timedelta (hours =1 )

        data ={
        "title":"Past Meeting",
        "description":"Should fail",
        "start_time":past_time .isoformat (),
        "end_time":(past_time +timedelta (hours =1 )).isoformat (),
        }

        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"
        response =self .client .put (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_400_BAD_REQUEST 
        assert "start_time"in response .data 

    def test_update_ongoing_meeting_allows_past_times (self ):
        """Test that ongoing meetings can be updated with past times."""

        past_start =timezone .now ()-timedelta (minutes =30 )
        ongoing_meeting =ClubMeeting .objects .create (
        club =self .club ,
        title ="Ongoing Meeting",
        description ="Currently happening",
        start_time =past_start ,
        end_time =timezone .now ()+timedelta (minutes =30 ),
        organizer =self .organizer ,
        )

        self .client .force_authenticate (user =self .organizer )


        data ={
        "title":"Updated Ongoing Meeting",
        "description":"Updated while ongoing",
        "start_time":past_start .isoformat (),
        "end_time":(timezone .now ()+timedelta (hours =1 )).isoformat (),
        }

        url =f"/api/v1/clubs/meetings/{ongoing_meeting.id}/"
        response =self .client .put (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_200_OK 

    def test_manager_can_update_any_meeting (self ):
        """Test that club manager can update any meeting in their club."""
        self .client .force_authenticate (user =self .manager )

        data ={
        "title":"Manager Updated",
        "description":"Updated by manager",
        }

        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"
        response =self .client .patch (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_200_OK 

    def test_organizer_can_update_their_meeting (self ):
        """Test that meeting organizer can update their own meeting."""
        self .client .force_authenticate (user =self .organizer )

        data ={
        "title":"Organizer Updated",
        }

        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"
        response =self .client .patch (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_200_OK 

    def test_regular_member_cannot_update_meeting (self ):
        """Test that regular members cannot update meetings they didn't organize."""
        self .client .force_authenticate (user =self .member )

        data ={
        "title":"Unauthorized Update",
        }

        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"
        response =self .client .patch (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_403_FORBIDDEN 

    def test_non_member_cannot_update_meeting (self ):
        """Test that non-members cannot update meetings."""
        self .client .force_authenticate (user =self .non_member )

        data ={
        "title":"Unauthorized Update",
        }

        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"
        response =self .client .patch (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_403_FORBIDDEN 

    def test_unauthenticated_cannot_update_meeting (self ):
        """Test that unauthenticated users cannot update meetings."""
        data ={
        "title":"Unauthorized Update",
        }

        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"
        response =self .client .patch (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_401_UNAUTHORIZED 

    def test_patch_time_validation_with_existing_values (self ):
        """Test PATCH validation uses existing values when fields not provided."""
        self .client .force_authenticate (user =self .organizer )


        invalid_end_time =self .meeting .start_time -timedelta (minutes =30 )

        data ={"end_time":invalid_end_time .isoformat ()}

        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"
        response =self .client .patch (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_400_BAD_REQUEST 
        assert "end_time"in response .data 

    def test_response_structure_after_update (self ):
        """Test that update response includes all expected fields."""
        self .client .force_authenticate (user =self .organizer )

        data ={
        "title":"Updated Title",
        }

        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"
        response =self .client .patch (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_200_OK 


        expected_fields =[
        "id","club","title","description","start_time","end_time",
        "duration","meeting_link","organizer","organizer_details",
        "attendees_details","status","created","updated"
        ]

        for field in expected_fields :
            assert field in response .data ,f"Missing field: {field}"


        assert "attendees_details"in response .data 
        assert isinstance (response .data ["attendees_details"],list )

    def test_invalid_datetime_format_validation (self ):
        """Test validation with invalid datetime formats."""
        self .client .force_authenticate (user =self .organizer )

        data ={
        "start_time":"invalid-datetime",
        }

        url =f"/api/v1/clubs/meetings/{self.meeting.id}/"
        response =self .client .patch (url ,data ,format ="json")

        assert response .status_code ==status .HTTP_400_BAD_REQUEST 
        assert "start_time"in response .data 