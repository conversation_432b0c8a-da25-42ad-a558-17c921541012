import pytest
from django.utils import timezone
from datetime import timedelta
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model

from apps.clubs.models import Club, ClubType
from apps.clubs.meetings.models import ClubMeeting, MeetingAttendee
from apps.clubs.meetings.serializers.meeting import ClubMeetingDetailSerializer
from apps.clubs.meetings.serializers.meeting_attendee import MeetingAttendeeSerializer

User = get_user_model()


@pytest.mark.django_db
class TestMeetingAttendeeDetails:
    """
    Test suite for the meeting attendee details functionality.
    """

    def setup_method(self):
        """Set up test data."""
        self.client = APIClient()

        self.manager = User.objects.create_user(
            username="manager",
            email="<EMAIL>",
            password="password123",
        )
        self.member1 = User.objects.create_user(
            username="member1",
            email="<EMAIL>",
            password="password123",
        )
        self.member2 = User.objects.create_user(
            username="member2",
            email="<EMAIL>",
            password="password123",
        )

        self.club_type = ClubType.objects.create(
            name="Test Club Type",
            created_by=self.manager,
        )

        self.club = Club.objects.create(
            name="Test Club",
            type=self.club_type,
            manager=self.manager,
        )

        self.club.members.add(self.member1)
        self.club.members.add(self.member2)

        self.meeting = ClubMeeting.objects.create(
            club=self.club,
            title="Test Meeting",
            description="This is a test meeting",
            start_time=timezone.now() + timedelta(days=1),
            end_time=timezone.now() + timedelta(days=1, hours=1),
            organizer=self.manager,
        )

        self.attendee1 = MeetingAttendee.objects.create(
            meeting=self.meeting,
            user=self.manager,
            status=MeetingAttendee.STATUS_ACCEPTED,
        )
        self.attendee2 = MeetingAttendee.objects.create(
            meeting=self.meeting,
            user=self.member1,
            status=MeetingAttendee.STATUS_INVITED,
        )
        self.attendee3 = MeetingAttendee.objects.create(
            meeting=self.meeting,
            user=self.member2,
            status=MeetingAttendee.STATUS_ACCEPTED,
        )

    def test_meeting_attendee_serializer_includes_user_details(self):
        """Test that the MeetingAttendeeSerializer includes user details."""
        serializer = MeetingAttendeeSerializer(self.attendee1)
        data = serializer.data

        assert "user_details" in data

        user_details = data["user_details"]
        assert "id" in user_details
        assert "username" in user_details
        assert "email" in user_details

        assert user_details["id"] == str(self.manager.id)
        assert user_details["username"] == self.manager.username
        assert user_details["email"] == self.manager.email

    def test_meeting_detail_serializer_includes_attendee_user_details(self):
        """Test that the ClubMeetingDetailSerializer includes user details for attendees."""
        serializer = ClubMeetingDetailSerializer(self.meeting)
        data = serializer.data

        assert "attendees_details" in data

        attendees = data["attendees_details"]
        assert len(attendees) == 3

        for attendee in attendees:
            assert "user_details" in attendee

            user_details = attendee["user_details"]
            assert "id" in user_details
            assert "username" in user_details
            assert "email" in user_details

    def test_meeting_api_response_includes_attendee_user_details(self):
        """Test that the API response includes user details for attendees."""

        self.client.force_authenticate(user=self.manager)

        url = f"/api/v1/clubs/meetings/{self.meeting.id}/"
        response = self.client.get(url)

        assert response.status_code == 200

        assert "attendees_details" in response.data

        attendees = response.data["attendees_details"]
        assert len(attendees) == 3

        for attendee in attendees:
            assert "user_details" in attendee

            user_details = attendee["user_details"]
            assert "id" in user_details
            assert "username" in user_details
            assert "email" in user_details

        manager_attendee = next(
            a
            for a in attendees
            if a["user_details"]["username"] == self.manager.username
        )
        member1_attendee = next(
            a
            for a in attendees
            if a["user_details"]["username"] == self.member1.username
        )
        member2_attendee = next(
            a
            for a in attendees
            if a["user_details"]["username"] == self.member2.username
        )

        assert manager_attendee["status"] == MeetingAttendee.STATUS_ACCEPTED
        assert manager_attendee["user_details"]["username"] == self.manager.username

        assert member1_attendee["status"] == MeetingAttendee.STATUS_INVITED
        assert member1_attendee["user_details"]["username"] == self.member1.username

        assert member2_attendee["status"] == MeetingAttendee.STATUS_ACCEPTED
        assert member2_attendee["user_details"]["username"] == self.member2.username

    def test_create_meeting_includes_attendee_user_details(self):
        """Test that creating a meeting includes user details for attendees."""

        self.client.force_authenticate(user=self.manager)

        url = f"/api/v1/clubs/meetings/club/{self.club.id}/"
        data = {
            "title": "New Meeting",
            "description": "This is a new meeting",
            "start_time": (timezone.now() + timedelta(days=2)).isoformat(),
            "end_time": (timezone.now() + timedelta(days=2, hours=1)).isoformat(),
            "is_jitsi_meeting": True,
        }
        response = self.client.post(url, data, format="json")

        assert response.status_code == 201

        assert "attendees_details" in response.data

        attendees = response.data["attendees_details"]
        assert len(attendees) == 3

        for attendee in attendees:
            assert "user_details" in attendee

            user_details = attendee["user_details"]
            assert "id" in user_details
            assert "username" in user_details
            assert "email" in user_details

        manager_attendee = next(
            a
            for a in attendees
            if a["user_details"]["username"] == self.manager.username
        )
        member1_attendee = next(
            a
            for a in attendees
            if a["user_details"]["username"] == self.member1.username
        )
        member2_attendee = next(
            a
            for a in attendees
            if a["user_details"]["username"] == self.member2.username
        )

        assert manager_attendee["status"] == MeetingAttendee.STATUS_ACCEPTED
        assert manager_attendee["user_details"]["username"] == self.manager.username

        assert member1_attendee["status"] == MeetingAttendee.STATUS_ACCEPTED
        assert member1_attendee["user_details"]["username"] == self.member1.username

        assert member2_attendee["status"] == MeetingAttendee.STATUS_ACCEPTED
        assert member2_attendee["user_details"]["username"] == self.member2.username
