import pytest 
from unittest .mock import patch ,MagicMock 
from django .utils import timezone 
from datetime import timedelta 
from django .contrib .auth import get_user_model 
from django .conf import settings 

from apps .clubs .models import Club ,ClubType 
from apps .clubs .meetings .models import ClubMeeting 
from apps .clubs .meetings .utils .email_utils import (
get_user_frontend_url ,
send_meeting_invitation_email ,
send_email_invitation ,
)

User =get_user_model ()


@pytest .mark .django_db 
class TestRoleBasedEmailUrls :
    """
    Comprehensive test suite for role-based email URL system.
    
    Tests the correct URL assignment based on user roles:
    - Admin/Staff users: ADMIN_BASE_UI_URL
    - Club managers: MANAGER_BASE_UI_URL
    - Regular members: MEMBER_BASE_UI_URL
    """

    def setup_method (self ):
        """Set up test data for each test method."""

        self .admin_user =User .objects .create_user (
        username ="admin",
        email ="<EMAIL>",
        password ="password123",
        is_superuser =True ,
        is_staff =True ,
        )

        self .staff_user =User .objects .create_user (
        username ="staff",
        email ="<EMAIL>",
        password ="password123",
        is_staff =True ,
        )

        self .manager_user =User .objects .create_user (
        username ="manager",
        email ="<EMAIL>",
        password ="password123",
        )

        self .regular_member =User .objects .create_user (
        username ="member",
        email ="<EMAIL>",
        password ="password123",
        )

        self .other_manager =User .objects .create_user (
        username ="othermanager",
        email ="<EMAIL>",
        password ="password123",
        )


        self .club_type =ClubType .objects .create (
        name ="Test Club Type",
        created_by =self .admin_user ,
        )

        self .club =Club .objects .create (
        name ="Test Club",
        type =self .club_type ,
        manager =self .manager_user ,
        )

        self .other_club =Club .objects .create (
        name ="Other Club",
        type =self .club_type ,
        manager =self .other_manager ,
        )


        self .club .members .add (self .manager_user ,self .regular_member ,self .admin_user ,self .staff_user )


        future_time =timezone .now ()+timedelta (days =1 )
        self .meeting =ClubMeeting .objects .create (
        club =self .club ,
        title ="Test Meeting",
        description ="Test meeting description",
        start_time =future_time ,
        end_time =future_time +timedelta (hours =1 ),
        organizer =self .manager_user ,
        )

    def test_admin_user_gets_admin_url (self ):
        """Test that admin/superuser gets admin URL."""
        url =get_user_frontend_url (self .admin_user ,self .club )
        expected_url =getattr (settings ,'ADMIN_BASE_UI_URL','http://localhost:4150')
        assert url ==expected_url 

    def test_staff_user_gets_admin_url (self ):
        """Test that staff user gets admin URL."""
        url =get_user_frontend_url (self .staff_user ,self .club )
        expected_url =getattr (settings ,'ADMIN_BASE_UI_URL','http://localhost:4150')
        assert url ==expected_url 

    def test_club_manager_gets_manager_url (self ):
        """Test that club manager gets manager URL."""
        url =get_user_frontend_url (self .manager_user ,self .club )
        expected_url =getattr (settings ,'MANAGER_BASE_UI_URL','http://localhost:4190')
        assert url ==expected_url 

    def test_manager_of_other_club_gets_manager_url (self ):
        """Test that manager of any club gets manager URL even for different club."""
        url =get_user_frontend_url (self .other_manager ,self .club )
        expected_url =getattr (settings ,'MANAGER_BASE_UI_URL','http://localhost:4190')
        assert url ==expected_url 

    def test_regular_member_gets_member_url (self ):
        """Test that regular member gets member URL."""
        url =get_user_frontend_url (self .regular_member ,self .club )
        expected_url =getattr (settings ,'MEMBER_BASE_UI_URL','http://localhost:4189')
        assert url ==expected_url 

    def test_url_without_club_context (self ):
        """Test URL determination without club context."""

        url =get_user_frontend_url (self .manager_user ,None )
        expected_url =getattr (settings ,'MANAGER_BASE_UI_URL','http://localhost:4190')
        assert url ==expected_url 


        url =get_user_frontend_url (self .regular_member ,None )
        expected_url =getattr (settings ,'MEMBER_BASE_UI_URL','http://localhost:4189')
        assert url ==expected_url 

    def test_role_hierarchy_precedence (self ):
        """Test that role hierarchy is respected (admin > manager > member)."""

        admin_manager =User .objects .create_user (
        username ="adminmanager",
        email ="<EMAIL>",
        password ="password123",
        is_superuser =True ,
        )


        admin_club =Club .objects .create (
        name ="Admin Club",
        type =self .club_type ,
        manager =admin_manager ,
        )


        url =get_user_frontend_url (admin_manager ,admin_club )
        expected_url =getattr (settings ,'ADMIN_BASE_UI_URL','http://localhost:4150')
        assert url ==expected_url 

    @patch ('apps.clubs.meetings.utils.email_utils.send_email_with_html')
    @patch ('apps.clubs.meetings.utils.email_utils.render_to_string')
    def test_meeting_invitation_email_uses_correct_url (self ,mock_render ,mock_send_email ):
        """Test that meeting invitation emails use the correct role-based URL."""
        mock_render .return_value ="Test content"


        test_cases =[
        (self .admin_user ,getattr (settings ,'ADMIN_BASE_UI_URL','http://localhost:4150')),
        (self .staff_user ,getattr (settings ,'ADMIN_BASE_UI_URL','http://localhost:4150')),
        (self .manager_user ,getattr (settings ,'MANAGER_BASE_UI_URL','http://localhost:4190')),
        (self .regular_member ,getattr (settings ,'MEMBER_BASE_UI_URL','http://localhost:4189')),
        ]

        for user ,expected_url in test_cases :
            mock_render .reset_mock ()
            result =send_meeting_invitation_email (self .meeting ,user )

            assert result is True 


            assert mock_render .call_count ==2 


            html_call_args =mock_render .call_args_list [0 ]
            context =html_call_args [0 ][1 ]

            assert context ['frontend_url']==expected_url 
            assert context ['meeting']==self .meeting 
            assert context ['attendee']==user 

    @patch ('apps.clubs.meetings.utils.email_utils.send_email_with_html')
    @patch ('apps.clubs.meetings.utils.email_utils.render_to_string')
    def test_email_invitation_creates_user_with_correct_url (self ,mock_render ,mock_send_email ):
        """Test that email invitations to new users use correct URL based on their role."""
        mock_render .return_value ="Test content"

        new_email ="<EMAIL>"


        assert not User .objects .filter (email =new_email ).exists ()

        success ,user =send_email_invitation (self .meeting ,new_email ,"New User")

        assert success is True 
        assert user is not None 
        assert user .email ==new_email 


        expected_url =getattr (settings ,'MEMBER_BASE_UI_URL','http://localhost:4189')


        html_call_args =mock_render .call_args_list [0 ]
        context =html_call_args [0 ][1 ]

        assert context ['frontend_url']==expected_url 
        assert context ['is_new_user']is True 

    def test_url_fallback_mechanisms (self ):
        """Test that fallback URLs are used when settings are not available."""

        original_admin_url =getattr (settings ,'ADMIN_BASE_UI_URL',None )
        original_manager_url =getattr (settings ,'MANAGER_BASE_UI_URL',None )
        original_member_url =getattr (settings ,'MEMBER_BASE_UI_URL',None )


        if hasattr (settings ,'ADMIN_BASE_UI_URL'):
            delattr (settings ,'ADMIN_BASE_UI_URL')
        if hasattr (settings ,'MANAGER_BASE_UI_URL'):
            delattr (settings ,'MANAGER_BASE_UI_URL')
        if hasattr (settings ,'MEMBER_BASE_UI_URL'):
            delattr (settings ,'MEMBER_BASE_UI_URL')

        try :

            admin_url =get_user_frontend_url (self .admin_user ,self .club )
            manager_url =get_user_frontend_url (self .manager_user ,self .club )
            member_url =get_user_frontend_url (self .regular_member ,self .club )

            assert admin_url =='http://localhost:4150'
            assert manager_url =='http://localhost:4190'
            assert member_url =='http://localhost:4189'

        finally :

            if original_admin_url is not None :
                settings .ADMIN_BASE_UI_URL =original_admin_url 
            if original_manager_url is not None :
                settings .MANAGER_BASE_UI_URL =original_manager_url 
            if original_member_url is not None :
                settings .MEMBER_BASE_UI_URL =original_member_url 

    def test_url_function_with_invalid_user (self ):
        """Test URL function behavior with edge cases."""

        minimal_user =User (id =999 ,email ="<EMAIL>")
        url =get_user_frontend_url (minimal_user ,self .club )
        expected_url =getattr (settings ,'MEMBER_BASE_UI_URL','http://localhost:4189')
        assert url ==expected_url 

    @patch ('apps.clubs.meetings.utils.email_utils.logger')
    def test_email_sending_logs_correct_url (self ,mock_logger ):
        """Test that email sending logs include the correct URL for debugging."""
        with patch ('apps.clubs.meetings.utils.email_utils.send_email_with_html')as mock_send :
            with patch ('apps.clubs.meetings.utils.email_utils.render_to_string')as mock_render :
                mock_render .return_value ="Test content"

                result =send_meeting_invitation_email (self .meeting ,self .manager_user )

                assert result is True 


                mock_logger .info .assert_called_once ()
                log_message =mock_logger .info .call_args [0 ][0 ]
                expected_url =getattr (settings ,'MANAGER_BASE_UI_URL','http://localhost:4190')
                assert expected_url in log_message 
                assert str (self .meeting .id )in log_message 
                assert self .manager_user .email in log_message 