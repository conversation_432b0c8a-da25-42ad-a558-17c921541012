import pytest 
from django .utils import timezone 
from datetime import timed<PERSON><PERSON> 
from rest_framework .test import APIClient 
from rest_framework import status 
from django .contrib .auth import get_user_model 
from django .test .utils import override_settings 
from django .db import connection 
from django .test import TestCase 

from apps .clubs .models import Club ,ClubType 
from apps .clubs .meetings .models import ClubMeeting ,MeetingAttendee 

User =get_user_model ()


@pytest .mark .django_db 
class TestAttendeesInMeetingViews :
    """
    Test suite to ensure attendees are properly returned in both list and detail views.
    This follows senior Django developer best practices for API testing.
    """

    def setup_method (self ):
        """Set up test data for each test method."""
        self .client =APIClient ()


        self .manager =User .objects .create_user (
        username ="manager",
        email ="<EMAIL>",
        password ="password123",
        fullname ="Test Manager",
        )
        self .member1 =User .objects .create_user (
        username ="member1",
        email ="<EMAIL>",
        password ="password123",
        fullname ="Test Member 1",
        )
        self .member2 =User .objects .create_user (
        username ="member2",
        email ="<EMAIL>",
        password ="password123",
        fullname ="Test Member 2",
        )
        self .non_member =User .objects .create_user (
        username ="nonmember",
        email ="<EMAIL>",
        password ="password123",
        fullname ="Non Member",
        )


        self .club_type =ClubType .objects .create (
        name ="Test Club Type",
        created_by =self .manager ,
        )

        self .club =Club .objects .create (
        name ="Test Club",
        type =self .club_type ,
        manager =self .manager ,
        )


        self .club .members .add (self .manager ,self .member1 ,self .member2 )


        self .meeting1 =ClubMeeting .objects .create (
        club =self .club ,
        title ="Test Meeting 1",
        description ="First test meeting",
        start_time =timezone .now ()+timedelta (days =1 ),
        end_time =timezone .now ()+timedelta (days =1 ,hours =1 ),
        organizer =self .manager ,
        )

        self .meeting2 =ClubMeeting .objects .create (
        club =self .club ,
        title ="Test Meeting 2",
        description ="Second test meeting",
        start_time =timezone .now ()+timedelta (days =2 ),
        end_time =timezone .now ()+timedelta (days =2 ,hours =1 ),
        organizer =self .member1 ,
        )


        self ._create_attendees ()

    def _create_attendees (self ):
        """Helper method to create meeting attendees."""

        MeetingAttendee .objects .create (
        meeting =self .meeting1 ,
        user =self .manager ,
        status =MeetingAttendee .STATUS_ACCEPTED ,
        )
        MeetingAttendee .objects .create (
        meeting =self .meeting1 ,
        user =self .member1 ,
        status =MeetingAttendee .STATUS_INVITED ,
        )
        MeetingAttendee .objects .create (
        meeting =self .meeting1 ,
        user =self .member2 ,
        status =MeetingAttendee .STATUS_ACCEPTED ,
        )


        MeetingAttendee .objects .create (
        meeting =self .meeting2 ,
        user =self .member1 ,
        status =MeetingAttendee .STATUS_ACCEPTED ,
        )
        MeetingAttendee .objects .create (
        meeting =self .meeting2 ,
        user =self .manager ,
        status =MeetingAttendee .STATUS_TENTATIVE ,
        )

    def _assert_attendees_structure (self ,attendees_data ,expected_count ):
        """Helper method to assert the structure of attendees data."""
        assert isinstance (attendees_data ,list ),"Attendees should be a list"
        assert len (attendees_data )==expected_count ,f"Expected {expected_count} attendees"

        for attendee in attendees_data :

            assert "id"in attendee ,"Attendee should have id field"
            assert "user"in attendee ,"Attendee should have user field"
            assert "user_details"in attendee ,"Attendee should have user_details field"
            assert "status"in attendee ,"Attendee should have status field"
            assert "meeting"in attendee ,"Attendee should have meeting field"


            user_details =attendee ["user_details"]
            assert "id"in user_details ,"User details should have id"
            assert "username"in user_details ,"User details should have username"
            assert "email"in user_details ,"User details should have email"
            assert "fullname"in user_details ,"User details should have fullname"


            valid_statuses =[
            MeetingAttendee .STATUS_INVITED ,
            MeetingAttendee .STATUS_ACCEPTED ,
            MeetingAttendee .STATUS_DECLINED ,
            MeetingAttendee .STATUS_TENTATIVE ,
            ]
            assert attendee ["status"]in valid_statuses ,f"Invalid status: {attendee['status']}"

    def test_club_meeting_list_includes_attendees (self ):
        """Test that club meeting list view includes attendees details."""
        self .client .force_authenticate (user =self .manager )

        url =f"/api/v1/clubs/meetings/club/{self.club.id}/"
        response =self .client .get (url )

        assert response .status_code ==status .HTTP_200_OK 


        assert "results"in response .data 
        meetings =response .data ["results"]
        assert len (meetings )==2 


        meeting1_data =next (m for m in meetings if m ["title"]=="Test Meeting 1")
        assert "attendees_details"in meeting1_data 
        self ._assert_attendees_structure (meeting1_data ["attendees_details"],3 )


        meeting2_data =next (m for m in meetings if m ["title"]=="Test Meeting 2")
        assert "attendees_details"in meeting2_data 
        self ._assert_attendees_structure (meeting2_data ["attendees_details"],2 )

    def test_club_meeting_detail_includes_attendees (self ):
        """Test that club meeting detail view includes attendees details."""
        self .client .force_authenticate (user =self .manager )

        url =f"/api/v1/clubs/meetings/{self.meeting1.id}/"
        response =self .client .get (url )

        assert response .status_code ==status .HTTP_200_OK 


        assert "attendees_details"in response .data 
        self ._assert_attendees_structure (response .data ["attendees_details"],3 )


        attendees =response .data ["attendees_details"]
        manager_attendee =next (
        a for a in attendees 
        if a ["user_details"]["username"]==self .manager .username 
        )
        assert manager_attendee ["status"]==MeetingAttendee .STATUS_ACCEPTED 

        member1_attendee =next (
        a for a in attendees 
        if a ["user_details"]["username"]==self .member1 .username 
        )
        assert member1_attendee ["status"]==MeetingAttendee .STATUS_INVITED 

    def test_user_meetings_list_includes_attendees (self ):
        """Test that user meetings list view includes attendees details."""
        self .client .force_authenticate (user =self .member1 )

        url ="/api/v1/clubs/meetings/my-meetings/"
        response =self .client .get (url )

        assert response .status_code ==status .HTTP_200_OK 


        assert "results"in response .data 
        meetings =response .data ["results"]


        assert len (meetings )==2 

        for meeting in meetings :
            assert "attendees_details"in meeting 
            attendees_count =len (meeting ["attendees_details"])
            assert attendees_count >0 ,"Meeting should have attendees"
            self ._assert_attendees_structure (meeting ["attendees_details"],attendees_count )

    def test_attendees_include_correct_user_details (self ):
        """Test that attendees include correct and complete user details."""
        self .client .force_authenticate (user =self .manager )

        url =f"/api/v1/clubs/meetings/{self.meeting1.id}/"
        response =self .client .get (url )

        assert response .status_code ==status .HTTP_200_OK 

        attendees =response .data ["attendees_details"]


        manager_attendee =next (
        a for a in attendees 
        if a ["user_details"]["username"]==self .manager .username 
        )

        user_details =manager_attendee ["user_details"]
        assert user_details ["id"]==str (self .manager .id )
        assert user_details ["username"]==self .manager .username 
        assert user_details ["email"]==self .manager .email 
        assert user_details ["fullname"]==self .manager .fullname 

    def test_attendees_permissions_respected_in_list (self ):
        """Test that attendees are only shown to authorized users in list view."""

        self .client .force_authenticate (user =self .non_member )

        url =f"/api/v1/clubs/meetings/club/{self.club.id}/"
        response =self .client .get (url )

        assert response .status_code ==status .HTTP_403_FORBIDDEN 


        self .client .force_authenticate (user =self .member1 )

        response =self .client .get (url )
        assert response .status_code ==status .HTTP_200_OK 

        meetings =response .data ["results"]
        for meeting in meetings :
            assert "attendees_details"in meeting 

    def test_attendees_permissions_respected_in_detail (self ):
        """Test that attendees are only shown to authorized users in detail view."""

        self .client .force_authenticate (user =self .non_member )

        url =f"/api/v1/clubs/meetings/{self.meeting1.id}/"
        response =self .client .get (url )

        assert response .status_code ==status .HTTP_403_FORBIDDEN 


        self .client .force_authenticate (user =self .member2 )

        response =self .client .get (url )
        assert response .status_code ==status .HTTP_200_OK 
        assert "attendees_details"in response .data 

    def test_empty_attendees_handled_correctly (self ):
        """Test that meetings with no attendees are handled correctly."""

        meeting_no_attendees =ClubMeeting .objects .create (
        club =self .club ,
        title ="No Attendees Meeting",
        description ="Meeting with no attendees",
        start_time =timezone .now ()+timedelta (days =3 ),
        end_time =timezone .now ()+timedelta (days =3 ,hours =1 ),
        organizer =self .manager ,
        )

        self .client .force_authenticate (user =self .manager )


        url =f"/api/v1/clubs/meetings/{meeting_no_attendees.id}/"
        response =self .client .get (url )

        assert response .status_code ==status .HTTP_200_OK 
        assert "attendees_details"in response .data 
        assert response .data ["attendees_details"]==[]


        url =f"/api/v1/clubs/meetings/club/{self.club.id}/"
        response =self .client .get (url )

        assert response .status_code ==status .HTTP_200_OK 
        meetings =response .data ["results"]

        no_attendees_meeting =next (
        m for m in meetings 
        if m ["title"]=="No Attendees Meeting"
        )
        assert "attendees_details"in no_attendees_meeting 
        assert no_attendees_meeting ["attendees_details"]==[]

    def test_attendees_consistent_across_views (self ):
        """Test that attendees data is consistent between list and detail views."""
        self .client .force_authenticate (user =self .manager )


        list_url =f"/api/v1/clubs/meetings/club/{self.club.id}/"
        list_response =self .client .get (list_url )
        list_meetings =list_response .data ["results"]
        list_meeting1 =next (m for m in list_meetings if m ["title"]=="Test Meeting 1")


        detail_url =f"/api/v1/clubs/meetings/{self.meeting1.id}/"
        detail_response =self .client .get (detail_url )


        list_attendees =list_meeting1 ["attendees_details"]
        detail_attendees =detail_response .data ["attendees_details"]

        assert len (list_attendees )==len (detail_attendees )


        list_attendees_sorted =sorted (list_attendees ,key =lambda x :x ["user_details"]["id"])
        detail_attendees_sorted =sorted (detail_attendees ,key =lambda x :x ["user_details"]["id"])

        for list_att ,detail_att in zip (list_attendees_sorted ,detail_attendees_sorted ):
            assert list_att ["user_details"]["id"]==detail_att ["user_details"]["id"]
            assert list_att ["status"]==detail_att ["status"]
            assert list_att ["user_details"]["username"]==detail_att ["user_details"]["username"]
            assert list_att ["user_details"]["email"]==detail_att ["user_details"]["email"]

    def test_attendees_serialization_completeness (self ):
        """Test that all attendee fields are properly serialized."""
        self .client .force_authenticate (user =self .manager )

        url =f"/api/v1/clubs/meetings/{self.meeting1.id}/"
        response =self .client .get (url )

        assert response .status_code ==status .HTTP_200_OK 

        attendees =response .data ["attendees_details"]
        assert len (attendees )==3 

        for attendee in attendees :

            expected_fields =["id","meeting","user","user_details","status","is_notified","created","updated"]
            for field in expected_fields :
                assert field in attendee ,f"Missing field: {field}"


            user_details =attendee ["user_details"]
            expected_user_fields =["id","username","email","fullname"]
            for field in expected_user_fields :
                assert field in user_details ,f"Missing user field: {field}"