import pytest
from django.utils import timezone
from datetime import timedelta
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model

from apps.clubs.models import Club, ClubType
from apps.clubs.meetings.models import ClubMeeting
from apps.clubs.meetings.serializers.meeting import (
    ClubMeetingSerializer,
    ClubMeetingDetailSerializer,
)

User = get_user_model()


@pytest.mark.django_db
class TestMeetingDuration:
    """
    Test suite for the meeting duration field in serializers.
    """

    def setup_method(self):
        """Set up test data."""
        self.client = APIClient()

        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="password123",
        )

        self.club_type = ClubType.objects.create(
            name="Test Club Type",
            created_by=self.user,
        )

        self.club = Club.objects.create(
            name="Test Club",
            type=self.club_type,
            manager=self.user,
        )

        now = timezone.now()
        self.meeting_1h = ClubMeeting.objects.create(
            club=self.club,
            title="1 Hour Meeting",
            description="This is a 1 hour meeting",
            start_time=now,
            end_time=now + timedelta(hours=1),
            organizer=self.user,
        )

        self.meeting_30m = ClubMeeting.objects.create(
            club=self.club,
            title="30 Minute Meeting",
            description="This is a 30 minute meeting",
            start_time=now,
            end_time=now + timedelta(minutes=30),
            organizer=self.user,
        )

        self.meeting_2h15m = ClubMeeting.objects.create(
            club=self.club,
            title="2 Hour 15 Minute Meeting",
            description="This is a 2 hour and 15 minute meeting",
            start_time=now,
            end_time=now + timedelta(hours=2, minutes=15),
            organizer=self.user,
        )

    def test_duration_calculation_in_serializer(self):
        """Test that the duration is correctly calculated in the serializer."""

        serializer_1h = ClubMeetingSerializer(self.meeting_1h)
        assert serializer_1h.data["duration"] == 60

        serializer_30m = ClubMeetingSerializer(self.meeting_30m)
        assert serializer_30m.data["duration"] == 30

        serializer_2h15m = ClubMeetingSerializer(self.meeting_2h15m)
        assert serializer_2h15m.data["duration"] == 135

    def test_duration_in_detail_serializer(self):
        """Test that the duration is included in the detail serializer."""
        serializer = ClubMeetingDetailSerializer(self.meeting_1h)
        assert "duration" in serializer.data
        assert serializer.data["duration"] == 60

    def test_duration_with_mock_null_times(self):
        """Test that the duration is None when start_time or end_time is None."""

        meeting = ClubMeeting.objects.create(
            club=self.club,
            title="Mock Null Time Meeting",
            description="This meeting simulates null times",
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(hours=1),
            organizer=self.user,
        )

        serializer = ClubMeetingSerializer(meeting)

        class MockMeeting:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)

        mock_meeting_null_start = MockMeeting(start_time=None, end_time=timezone.now())
        assert serializer.get_duration(mock_meeting_null_start) is None

        mock_meeting_null_end = MockMeeting(start_time=timezone.now(), end_time=None)
        assert serializer.get_duration(mock_meeting_null_end) is None

    def test_duration_in_api_response(self):
        """Test that the duration is included in the API response."""

        self.client.force_authenticate(user=self.user)

        url = f"/api/v1/clubs/meetings/{self.meeting_1h.id}/"
        response = self.client.get(url)

        assert response.status_code == 200
        assert "duration" in response.data
        assert response.data["duration"] == 60

    def test_duration_in_list_response(self):
        """Test that the duration is included in the list API response."""

        self.client.force_authenticate(user=self.user)

        url = f"/api/v1/clubs/meetings/club/{self.club.id}/"
        response = self.client.get(url)

        assert response.status_code == 200

        assert "results" in response.data
        assert len(response.data["results"]) >= 3

        for meeting in response.data["results"]:
            assert "duration" in meeting

        meeting_1h = next(
            (m for m in response.data["results"] if m["title"] == "1 Hour Meeting"),
            None,
        )
        meeting_30m = next(
            (m for m in response.data["results"] if m["title"] == "30 Minute Meeting"),
            None,
        )
        meeting_2h15m = next(
            (
                m
                for m in response.data["results"]
                if m["title"] == "2 Hour 15 Minute Meeting"
            ),
            None,
        )

        assert meeting_1h is not None
        assert meeting_30m is not None
        assert meeting_2h15m is not None

        assert meeting_1h["duration"] == 60
        assert meeting_30m["duration"] == 30
        assert meeting_2h15m["duration"] == 135
