import pytest 
from django .urls import reverse 
from rest_framework import status 
from rest_framework .test import APIClient 
from django .contrib .auth import get_user_model 
from django .core import mail 
from django .utils import timezone 
from datetime import timed<PERSON><PERSON> 
from unittest .mock import patch 

from apps .clubs .models import Club ,ClubType 
from apps .clubs .meetings .models import ClubMeeting ,MeetingAttendee 
from apps .clubs .meetings .utils .email_utils import send_meeting_invitation_email 

User =get_user_model ()


@pytest .mark .django_db 
class TestMeetingInvitations :
    """
    Test suite for meeting invitations functionality.
    """

    def setup_method (self ):
        """Set up test data."""
        self .client =APIClient ()

        self .manager =User .objects .create_user (
        username ="manager",
        email ="<EMAIL>",
        password ="password123",
        fullname ="Club Manager",
        )
        self .member1 =User .objects .create_user (
        username ="member1",
        email ="<EMAIL>",
        password ="password123",
        fullname ="Club Member 1",
        )
        self .member2 =User .objects .create_user (
        username ="member2",
        email ="<EMAIL>",
        password ="password123",
        fullname ="Club Member 2",
        )

        self .club_type =ClubType .objects .create (
        name ="Test Club Type",
        created_by =self .manager ,
        )

        self .club =Club .objects .create (
        name ="Test Club",
        type =self .club_type ,
        manager =self .manager ,
        privacy =Club .PRIVACY_CLOSED ,
        join_permissions =Club .JOIN_PERMISSIONS_INVITE ,
        )


        self .club .members .add (self .manager ,self .member1 ,self .member2 )

        self .create_meeting_url =f"/api/v1/clubs/meetings/club/{self.club.id}/"

        self .meeting_data ={
        "title":"Test Meeting",
        "description":"This is a test meeting",
        "start_time":(timezone .now ()+timedelta (days =1 )).isoformat (),
        "end_time":(timezone .now ()+timedelta (days =1 ,hours =1 )).isoformat (),
        "is_jitsi_meeting":True ,
        }

        mail .outbox =[]

    def test_send_meeting_invitation_email (self ):
        """Test the utility function for sending meeting invitation emails."""

        mail .outbox =[]

        initial_count =len (mail .outbox )

        meeting =ClubMeeting .objects .create (
        club =self .club ,
        title ="Test Meeting",
        description ="This is a test meeting",
        start_time =timezone .now ()+timedelta (days =1 ),
        end_time =timezone .now ()+timedelta (days =1 ,hours =1 ),
        organizer =self .manager ,
        is_jitsi_meeting =True ,
        )

        result =send_meeting_invitation_email (meeting ,self .member1 )

        assert result is True 

        assert len (mail .outbox )>initial_count 

        invitation_email =mail .outbox [-1 ]

        assert (
        invitation_email .subject 
        ==f"Invitation to {self.club.name} Meeting: {meeting.title}"
        )
        assert self .member1 .email in invitation_email .to 
        assert meeting .title in invitation_email .body 
        assert self .club .name in invitation_email .body 
        assert self .manager .fullname in invitation_email .body 

    @patch ("apps.clubs.meetings.utils.email_utils.send_meeting_invitation_email")
    def test_create_meeting_sends_invitations (self ,mock_send_email ):
        """Test that creating a meeting sends invitation emails to club members."""

        self .client .force_authenticate (user =self .manager )

        response =self .client .post (
        self .create_meeting_url ,self .meeting_data ,format ="json"
        )

        assert response .status_code ==status .HTTP_201_CREATED 


        assert mock_send_email .call_count ==2 

        meeting =ClubMeeting .objects .get (id =response .data ["id"])

        attendees =MeetingAttendee .objects .filter (meeting =meeting )
        assert attendees .count ()==3 

        manager_attendee =attendees .get (user =self .manager )
        assert manager_attendee .status ==MeetingAttendee .STATUS_ACCEPTED 

        member1_attendee =attendees .get (user =self .member1 )
        assert member1_attendee .status ==MeetingAttendee .STATUS_ACCEPTED 

        member2_attendee =attendees .get (user =self .member2 )
        assert member2_attendee .status ==MeetingAttendee .STATUS_ACCEPTED 

    def test_meeting_creation_with_real_emails (self ):
        """Test the full flow of creating a meeting and sending real emails."""

        mail .outbox =[]

        self .client .force_authenticate (user =self .manager )

        response =self .client .post (
        self .create_meeting_url ,self .meeting_data ,format ="json"
        )

        assert response .status_code ==status .HTTP_201_CREATED 

        meeting_invitation_count =0 
        recipients =set ()

        for email in mail .outbox :
            if f"Invitation to {self.club.name} Meeting:"in email .subject :
                meeting_invitation_count +=1 
                recipients .update (email .to )

        assert meeting_invitation_count >=2 
        assert self .member1 .email in recipients 
        assert self .member2 .email in recipients 
        assert self .manager .email not in recipients 
