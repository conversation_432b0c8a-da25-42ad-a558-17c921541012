import jwt
import time
import uuid
from django.conf import settings
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

User = get_user_model()


def generate_jitsi_jwt(user, room_name, is_moderator=False):
    """
    Generates a Jitsi-compatible JWT token for a given user, room, and moderator status.

    Args:
        user (User): The Django user object
        room_name (str): The Jitsi room name
        is_moderator (bool): Whether the user should have moderator privileges

    Returns:
        str: The JWT token

    Raises:
        ValueError: If Jitsi JWT settings are not configured
    """

    app_id = getattr(settings, "JITSI_JWT_APP_ID", None)
    app_secret = getattr(settings, "JITSI_JWT_APP_SECRET", None)
    audience = getattr(settings, "JITSI_JWT_AUDIENCE", "jitsi")
    subject = getattr(settings, "JITSI_JWT_SUBJECT", app_id)
    expiry_seconds = getattr(settings, "JITSI_JWT_EXPIRY", 3600)

    if not app_id or not app_secret:
        raise ValueError(
            _(
                "JITSI_JWT_APP_ID and JITSI_JWT_APP_SECRET must be set in Django settings."
            )
        )

    issued_at = int(time.time())
    expires_at = issued_at + expiry_seconds

    avatar_url = ""
    if hasattr(user, "avatar") and user.avatar:
        avatar_url = user.avatar.url

    display_name = user.username
    if hasattr(user, "fullname") and user.fullname:
        display_name = user.fullname
    elif (
        hasattr(user, "get_full_name")
        and callable(user.get_full_name)
        and user.get_full_name()
    ):
        display_name = user.get_full_name()

    payload = {
        "iss": app_id,
        "aud": audience,
        "sub": subject,
        "room": room_name,
        "exp": expires_at,
        "nbf": issued_at,
        "context": {
            "user": {
                "id": str(user.pk),
                "name": display_name,
                "email": user.email,
                "avatar": avatar_url,
                "moderator": str(is_moderator).lower(),
            },
            "features": {
                "livestreaming": "true" if is_moderator else "false",
                "recording": "true" if is_moderator else "false",
                "outbound-call": "false",
                "transcription": "false",
                "sip-outbound-call": "false",
            },
        },
    }

    payload = {k: v for k, v in payload.items() if v is not None}

    token = jwt.encode(payload, app_secret, algorithm="HS256")
    return token


def generate_room_name():
    """
    Generates a unique room name for Jitsi meetings.

    Returns:
        str: A unique room name
    """

    return f"meeting-{uuid.uuid4().hex[:12]}"


def get_jitsi_meeting_url(room_name):
    """
    Generates the full Jitsi meeting URL for a given room name.

    Args:
        room_name (str): The Jitsi room name

    Returns:
        str: The full Jitsi meeting URL
    """
    jitsi_domain = getattr(settings, "JITSI_DOMAIN", None)

    if not jitsi_domain:
        raise ValueError(_("JITSI_DOMAIN must be set in Django settings."))

    jitsi_domain = jitsi_domain.rstrip("/")

    return f"{jitsi_domain}/{room_name}"


def get_jitsi_embed_url(room_name, jwt_token=None):
    """
    Generates the Jitsi embed URL for a given room name and optional JWT token.

    The embed URL is used to embed the Jitsi meeting in an iframe.

    Args:
        room_name (str): The Jitsi room name
        jwt_token (str, optional): JWT token for authentication

    Returns:
        str: The Jitsi embed URL with optional JWT token
    """
    jitsi_domain = getattr(settings, "JITSI_DOMAIN", None)

    if not jitsi_domain:
        raise ValueError(_("JITSI_DOMAIN must be set in Django settings."))

    jitsi_domain = jitsi_domain.rstrip("/")

    embed_url = f"{jitsi_domain}/{room_name}"

    if jwt_token:
        embed_url = f"{embed_url}?jwt={jwt_token}"

    return embed_url


def get_jitsi_meeting_info(user, room_name, is_moderator=False):
    """
    Generates comprehensive Jitsi meeting information including URLs and tokens.

    This is a convenience function that generates all the information needed
    to join a Jitsi meeting, including the meeting URL, embed URL, and JWT token.

    Args:
        user (User): The Django user object
        room_name (str): The Jitsi room name
        is_moderator (bool): Whether the user should have moderator privileges

    Returns:
        dict: Dictionary containing meeting information:
            - meeting_url: The full Jitsi meeting URL
            - embed_url: The URL for embedding in an iframe
            - jwt_token: The JWT token for authentication
            - room_name: The Jitsi room name
            - is_moderator: Whether the user is a moderator

    Raises:
        ValueError: If Jitsi settings are not configured
    """

    jwt_token = generate_jitsi_jwt(user, room_name, is_moderator)

    meeting_url = get_jitsi_meeting_url(room_name)

    embed_url = get_jitsi_embed_url(room_name, jwt_token)

    return {
        "meeting_url": meeting_url,
        "embed_url": embed_url,
        "jwt_token": jwt_token,
        "room_name": room_name,
        "is_moderator": is_moderator,
    }
