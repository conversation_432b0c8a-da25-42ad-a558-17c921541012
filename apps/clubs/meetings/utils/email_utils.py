import logging 
import uuid 
from django .conf import settings 
from django .template .loader import render_to_string 
from django .contrib .auth import get_user_model 
from django .contrib .auth .tokens import PasswordResetTokenGenerator 
from django .core .mail import EmailMultiAlternatives 
from django .utils import timezone 
from datetime import timed<PERSON>ta 
from core .tasks .emails import send_email_with_html 

logger =logging .getLogger (__name__ )
User =get_user_model ()


def get_user_frontend_url (user ,club =None ):
    """
    Determine the appropriate frontend URL based on user role.
    
    Following senior Django developer best practices:
    - Clear role hierarchy and precedence
    - Fallback mechanisms for robustness
    - Security-conscious role checking
    
    Args:
        user: The User instance
        club: Optional Club instance for context-specific role checking
        
    Returns:
        str: The appropriate frontend base URL
    """

    if hasattr (user ,'is_superuser')and user .is_superuser :
        return getattr (settings ,'ADMIN_BASE_UI_URL','http://localhost:4150')


    if hasattr (user ,'is_staff')and user .is_staff :
        return getattr (settings ,'ADMIN_BASE_UI_URL','http://localhost:4150')


    if club :

        if club .manager_id ==user .id :
            return getattr (settings ,'MANAGER_BASE_UI_URL','http://localhost:4190')


    from apps .clubs .models import Club 
    if Club .objects .filter (manager =user ).exists ():
        return getattr (settings ,'MANAGER_BASE_UI_URL','http://localhost:4190')


    return getattr (settings ,'MEMBER_BASE_UI_URL','http://localhost:4189')


def has_received_meeting_invitation_today (user ):
    """
    Check if user has received a meeting invitation email today to prevent spam.
    
    Following senior Django developer best practices:
    - Anti-spam protection
    - Efficient database queries
    - Clear business logic separation
    
    Args:
        user: The User instance to check
        
    Returns:
        bool: True if user already received an invitation today, False otherwise
    """
    from apps .clubs .meetings .models import MeetingAttendee 


    today_start =timezone .now ().replace (hour =0 ,minute =0 ,second =0 ,microsecond =0 )
    today_end =today_start +timedelta (days =1 )



    invitation_today =MeetingAttendee .objects .filter (
    user =user ,
    created__gte =today_start ,
    created__lt =today_end 
    ).exists ()

    if invitation_today :
        logger .info (
        f"Skipping email invitation for {user.email}: already received invitation today"
        )

    return invitation_today 


def _send_email_sync (subject ,text_content ,html_content ,from_email ,to_email ):
    """Send email synchronously for testing environment."""
    msg =EmailMultiAlternatives (
    subject =subject ,
    body =text_content ,
    from_email =from_email ,
    to =[to_email ],
    )
    msg .attach_alternative (html_content ,"text/html")
    msg .send ()


def send_meeting_invitation_email (meeting ,attendee ,force_send =False ):
    """
    Send a meeting invitation email to an attendee with anti-spam protection.

    Args:
        meeting: The ClubMeeting instance
        attendee: The User instance who is being invited
        force_send: If True, bypasses the daily email limit (default: False)

    Returns:
        bool: True if the email was sent successfully, False otherwise
    """
    try :

        if not force_send and has_received_meeting_invitation_today (attendee ):
            logger .info (
            f"Skipping meeting invitation email to {attendee.email}: "
            f"anti-spam protection (already received invitation today)"
            )
            return False 


        frontend_url =get_user_frontend_url (attendee ,meeting .club )

        context ={
        "meeting":meeting ,
        "attendee":attendee ,
        "frontend_url":frontend_url ,
        }

        html_content =render_to_string (
        "clubs/emails/meetings/meeting_invitation.html",context 
        )
        text_content =render_to_string (
        "clubs/emails/meetings/meeting_invitation.txt",context 
        )

        subject =f"Invitation to {meeting.club.name} Meeting: {meeting.title}"

        if getattr (settings ,"TESTING",False ):
            _send_email_sync (
            subject =subject ,
            text_content =text_content ,
            html_content =html_content ,
            from_email =settings .DEFAULT_FROM_EMAIL ,
            to_email =attendee .email ,
            )
        else :
            send_email_with_html .delay (
            subject =subject ,
            text_content =text_content ,
            html_content =html_content ,
            from_email =settings .DEFAULT_FROM_EMAIL ,
            to_email =attendee .email ,
            )

        logger .info (
        f"Meeting invitation email sent to {attendee.email} for meeting {meeting.id} using URL: {frontend_url}"
        )
        return True 
    except Exception as e :
        logger .error (
        f"Failed to send meeting invitation email to {attendee.email}: {str(e)}"
        )
        return False 


def send_email_invitation (meeting ,email ,name ="",force_send =False ):
    """
    Send a meeting invitation email to an email address with anti-spam protection.

    Args:
        meeting: The ClubMeeting instance
        email: The email address to send the invitation to
        name: The name of the person to invite (optional)
        force_send: If True, bypasses the daily email limit (default: False)

    Returns:
        tuple: (bool, User) - Success status and User instance if created/found
    """
    try :
        try :
            user =User .objects .get (email =email )
            is_new_user =False 


            if not force_send and has_received_meeting_invitation_today (user ):
                logger .info (
                f"Skipping email invitation to {email}: "
                f"anti-spam protection (already received invitation today)"
                )
                return False ,user 

        except User .DoesNotExist :
            username =email .split ("@")[0 ]

            base_username =username 
            counter =1 
            while User .objects .filter (username =username ).exists ():
                username =f"{base_username}{counter}"
                counter +=1 

            from django .db .models import signals 
            from apps .accounts .user .signals import handle_user_creation 

            signals .post_save .disconnect (handle_user_creation ,sender =User )

            try :
                user =User .objects .create_user (
                email =email ,
                username =username ,
                is_active =True ,
                )

                if name :
                    if hasattr (user ,"fullname"):
                        user .fullname =name 
                        user .save (update_fields =["fullname"])
                    elif hasattr (user ,"first_name"):
                        user .first_name =name 
                        user .save (update_fields =["first_name"])
            finally :
                signals .post_save .connect (handle_user_creation ,sender =User )

            is_new_user =True 

        if not meeting .club .members .filter (pk =user .pk ).exists ():
            meeting .club .members .add (user )


        frontend_url =get_user_frontend_url (user ,meeting .club )

        context ={
        "meeting":meeting ,
        "attendee":user ,
        "frontend_url":frontend_url ,
        "is_new_user":is_new_user ,
        }

        if name :
            has_name =False 
            if hasattr (user ,"fullname")and user .fullname :
                has_name =True 
            elif hasattr (user ,"first_name")and user .first_name :
                has_name =True 

            if not has_name :
                context ["attendee_name"]=name 

        html_content =render_to_string (
        "clubs/emails/meetings/meeting_invitation.html",context 
        )
        text_content =render_to_string (
        "clubs/emails/meetings/meeting_invitation.txt",context 
        )

        subject =f"Invitation to {meeting.club.name} Meeting: {meeting.title}"

        if getattr (settings ,"TESTING",False ):
            _send_email_sync (
            subject =subject ,
            text_content =text_content ,
            html_content =html_content ,
            from_email =settings .DEFAULT_FROM_EMAIL ,
            to_email =email ,
            )
        else :
            send_email_with_html .delay (
            subject =subject ,
            text_content =text_content ,
            html_content =html_content ,
            from_email =settings .DEFAULT_FROM_EMAIL ,
            to_email =email ,
            )

        logger .info (
        f"Meeting invitation email sent to {email} for meeting {meeting.id} using URL: {frontend_url}"
        )
        return True ,user 
    except Exception as e :
        logger .error (f"Failed to send meeting invitation email to {email}: {str(e)}")
        return False ,None 


def process_email_attendees (meeting ,email_attendees ):
    """
    Process email attendees for a meeting.

    Args:
        meeting: The ClubMeeting instance
        email_attendees: List of dictionaries with email and name keys

    Returns:
        list: List of created MeetingAttendee instances
    """
    from apps .clubs .meetings .models import MeetingAttendee 

    attendees_to_create =[]

    for attendee_data in email_attendees :
        email =attendee_data ["email"]
        name =attendee_data .get ("name","")

        success ,user =send_email_invitation (meeting ,email ,name )

        if success and user :

            if not meeting .club .members .filter (pk =user .pk ).exists ():

                meeting .club .members .add (user )

            attendee =MeetingAttendee (
            meeting =meeting ,
            user =user ,
            status =MeetingAttendee .STATUS_INVITED ,
            )
            attendees_to_create .append (attendee )

    if attendees_to_create :
        created_attendees =MeetingAttendee .objects .bulk_create (
        attendees_to_create ,ignore_conflicts =True 
        )
        return created_attendees 

    return []
