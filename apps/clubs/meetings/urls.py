from django.urls import path
from apps.clubs.meetings.views.meeting import (
    ClubMeetingListCreateAPIView,
    ClubMeetingDetailAPIView,
    CancelClubMeetingAPIView,
)
from apps.clubs.meetings.views.meeting_attendee import (
    MeetingAttendeeListCreateAPIView,
    MeetingAttendeeUpdateAPIView,
    AcceptMeetingInvitationAPIView,
    DeclineMeetingInvitationAPIView,
    ManageMeetingAttendeeAPIView,
)
from apps.clubs.meetings.views.jitsi_token_view import JitsiTokenView
from apps.clubs.meetings.views.jitsi_react_view import JitsiReactConfigView
from apps.clubs.meetings.views.user_meetings import UserMeetingsListAPIView

app_name = "club_meetings"

urlpatterns = [
    path(
        "club/<uuid:club_id>/",
        ClubMeetingListCreateAPIView.as_view(),
        name="club-meeting-list-create",
    ),
    path("my-meetings/", UserMeetingsListAPIView.as_view(), name="user-meetings-list"),
    path("<uuid:pk>/", ClubMeetingDetailAPIView.as_view(), name="club-meeting-detail"),
    path(
        "<uuid:pk>/cancel/",
        CancelClubMeetingAPIView.as_view(),
        name="club-meeting-cancel",
    ),
    path(
        "<uuid:meeting_id>/attendees/",
        MeetingAttendeeListCreateAPIView.as_view(),
        name="meeting-attendee-list-create",
    ),
    path(
        "attendees/<uuid:pk>/",
        MeetingAttendeeUpdateAPIView.as_view(),
        name="meeting-attendee-update",
    ),
    path(
        "meetings/<uuid:pk>/attendees/",
        ManageMeetingAttendeeAPIView.as_view(),
        name="club-meeting-manage-attendees",
    ),
    path(
        "attendees/<uuid:pk>/accept/",
        AcceptMeetingInvitationAPIView.as_view(),
        name="meeting-invitation-accept",
    ),
    path(
        "attendees/<uuid:pk>/decline/",
        DeclineMeetingInvitationAPIView.as_view(),
        name="meeting-invitation-decline",
    ),
    path(
        "<uuid:meeting_id>/jitsi-token/",
        JitsiTokenView.as_view(),
        name="meeting-jitsi-token",
    ),
    path(
        "<uuid:meeting_id>/jitsi-react-config/",
        JitsiReactConfigView.as_view(),
        name="meeting-jitsi-react-config",
    ),
]
