import random
from django.contrib.auth.models import (
    AbstractBaseUser,
    PermissionsMixin,
)
from django.utils.translation import gettext_lazy as _
from django.db import models
from core.abstract.models import AbstractAutoIncrementModel
from .managers import <PERSON>r<PERSON><PERSON><PERSON>, VerificationCodeManager


def user_directory_path(instance, filename):
    return "user_{0}/{1}".format(instance.id, filename)


class User(AbstractAutoIncrementModel, AbstractBaseUser, PermissionsMixin):
    ROLE_CHOICES = [
        ("admin", _("Admin")),
        ("club_manager", _("Club Manager")),
        ("member", _("Member")),
    ]

    ACCOUNT_TYPE_CHOICES = [
        ("free", _("Free")),
        ("paid", _("Paid")),
    ]

    username = models.CharField(db_index=True, max_length=255, unique=True)
    email = models.EmailField(
        db_index=True, unique=True, max_length=255, blank=True, null=True
    )
    fullname = models.CharField(max_length=255, blank=True, null=True)
    is_superuser = models.BooleanField(default=False)
    is_staff = models.BooleanField(default=False)
    is_email_verified = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    avatar = models.ImageField(null=True, blank=True, upload_to=user_directory_path)
    phone_number = models.CharField(max_length=15, unique=True, null=True, blank=True)
    location = models.CharField(max_length=255, blank=True, null=True)
    city = models.CharField(max_length=100, blank=True, null=True)
    company_information = models.TextField(blank=True, null=True)
    company_name = models.CharField(max_length=255, blank=True, null=True)
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default="member")
    account_type = models.CharField(
        max_length=10,
        choices=ACCOUNT_TYPE_CHOICES,
        default="free",
        null=True,
        blank=True,
    )
    country = models.ForeignKey(
        "countries.Country",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="users",
    )
    google_calendar_synced = models.BooleanField(default=False)

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = ["fullname"]

    objects: UserManager = UserManager()

    def __str__(self):
        return f"{self.email}" or f"{self.fullname}"

    def has_perm(self, perm, obj=None):
        return self.is_superuser

    def has_module_perms(self, app_label):
        return self.is_superuser

    @property
    def full_name(self):
        return self.fullname or self.username

    def save(self, *args, **kwargs):

        if self.is_superuser:
            self.is_staff = True

        if not self.username:
            try:
                self.username = self.fullname.strip() if self.fullname else self.email
            except:
                self.username = self.email

        if self.email:
            self.email = self.email.lower()

        super().save(*args, **kwargs)

    class Meta:
        verbose_name = _("User")
        verbose_name_plural = _("Users")
        indexes = [
            models.Index(fields=["email"]),
        ]


class VerificationCode(AbstractAutoIncrementModel):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    code = models.CharField(max_length=6)
    is_used = models.BooleanField(default=False)

    objects: VerificationCodeManager = VerificationCodeManager()

    @staticmethod
    def generate_code():
        return "".join(random.choices("0123456789", k=6))

    def save(self, *args, **kwargs):
        if not self.code:
            self.code = self.generate_code()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.code}"

    class Meta:
        ordering = ["-created"]
        verbose_name = _("Verification Code")
        verbose_name_plural = _("Verification Codes")


class UserToken(AbstractAutoIncrementModel):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    token = models.CharField(max_length=255)

    class Meta:
        verbose_name = _("User Token")
        verbose_name_plural = _("User Tokens")
        indexes = [
            models.Index(fields=["token"]),
        ]
