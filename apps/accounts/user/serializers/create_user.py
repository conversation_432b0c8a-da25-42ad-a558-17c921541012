from rest_framework import serializers as serializeers
from django.conf import settings
import logging

from apps.accounts.user.models import User
from apps.accounts.user.serializers.user import UserSerializer

logger = logging.getLogger(__name__)


class CreateUserSerializer(UserSerializer):
    avatar = serializeers.ImageField(required=False)
    role = serializeers.ChoiceField(
        choices=User.ROLE_CHOICES, default="member", required=False
    )
    account_type = serializeers.ChoiceField(
        choices=User.ACCOUNT_TYPE_CHOICES, default="free", required=False
    )

    class Meta:
        model = User
        fields = [
            "id",
            "email",
            "fullname",
            "avatar",
            "phone_number",
            "location",
            "company_information",
            "company_name",
            "role",
            "account_type",
            "country",
            "country_name",
            "username",
        ]
        read_only_fields = ["id", "country_name", "username"]

    def create(self, validated_data):
        """
        Create a new user.
        """
        fullname = validated_data.pop("fullname")
        username = fullname.replace(" ", "_").lower()

        if User.objects.filter(username=username).exists():
            username = f"{username}_{User.objects.count()}"

        user = User.objects.create(
            username=username, fullname=fullname, **validated_data
        )

        return user

    def to_representation(self, instance):
        """
        Customize the serialized output to ensure all required fields are included.
        """
        data = super().to_representation(instance)
        data.update(
            {
                "email": instance.email,
                "role": instance.role,
                "username": instance.username,
            }
        )
        return data
