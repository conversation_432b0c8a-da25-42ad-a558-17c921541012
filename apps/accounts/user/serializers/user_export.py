from django.conf import settings
from core.utilities import tprint
from rest_framework import serializers as serializeers
from drf_spectacular.utils import extend_schema_serializer, OpenApiExample

from core.abstract.serializers import AbstractSerializer
from apps.accounts.user.models import User
from apps.accounts.profile.models import Point
from apps.countries.serializers import CountryLightSerializer


class UserExportSerializer(AbstractSerializer):
    country_name = serializeers.SerializerMethodField()

    def get_country_name(self, obj):
        if obj.country:
            return obj.country.name
        return None

    class Meta:
        model = User
        fields = [
            "fullname",
            "email",
            "phone_number",
            "location",
            "company_information",
            "company_name",
            "role",
            "account_type",
            "country",
            "country_name",
            "google_calendar_synced",
        ]

    def to_representation(self, instance):
        data = super().to_representation(instance)
        try:
            data["avatar"] = (
                self.context["request"].build_absolute_uri(instance.avatar.url)
                if instance.avatar
                else None
            )
        except (Attri<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, KeyError):
            data["avatar"] = None
        return data
