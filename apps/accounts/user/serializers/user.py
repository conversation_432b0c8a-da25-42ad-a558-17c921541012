from django.conf import settings
from apps.missions.models import WeeklyMission
from core.utilities import tprint
from rest_framework import serializers as serializeers
from drf_spectacular.utils import extend_schema_serializer, OpenApiExample

from core.abstract.serializers import AbstractSerializer
from apps.accounts.user.models import User
from apps.accounts.profile.models import Point
from apps.countries.serializers.country_light import CountryLightSerializer
from apps.okrs.models import SixWeekPeriod


@extend_schema_serializer(
    examples=[
        OpenApiExample(
            "User Example",
            value={
                "id": 1,
                "email": "<EMAIL>",
                "username": "john_doe",
                "fullname": "<PERSON>",
                "phone_number": "+**********",
                "location": "New York",
                "company_information": "Software Company",
                "company_name": "Tech Corp",
                "role": "member",
                "account_type": "free",
                "country": 1,
                "country_name": "United States",
                "avatar": "http://example.com/media/user_1/avatar.jpg",
                "google_calendar_synced": False,
                "has_all_missions": True,
            },
            response_only=True,
        ),
    ],
)
class UserSerializer(AbstractSerializer):
    country_name = serializeers.SerializerMethodField()
    has_all_missions = serializeers.SerializerMethodField()

    def get_country_name(self, obj):
        if obj.country:
            return obj.country.name
        return None

    def get_has_all_missions(self, obj):
        """
        Return True if the user has all six weekly missions created, False otherwise.
        """
        mission_count = WeeklyMission.objects.filter(user=obj).count()
        return mission_count >= 6

    class Meta:
        model = User
        fields = [
            "_id",
            "id",
            "email",
            "username",
            "password",
            "fullname",
            "phone_number",
            "location",
            "company_information",
            "company_name",
            "role",
            "account_type",
            "country",
            "country_name",
            "google_calendar_synced",
            "has_all_missions",
        ]
        read_only_fields = ["id"]
        extra_kwargs = {
            "password": {"write_only": True},
            "role": {
                "choices": [
                    ("admin", "Admin"),
                    ("club_manager", "Club Manager"),
                    ("member", "Member"),
                ]
            },
            "account_type": {
                "choices": [
                    ("free", "Free"),
                    ("paid", "Paid"),
                ]
            },
        }

    def to_representation(self, instance):
        data = super().to_representation(instance)
        try:
            data["avatar"] = (
                self.context["request"].build_absolute_uri(instance.avatar.url)
                if instance.avatar
                else None
            )
        except (AttributeError, ValueError, KeyError):
            try:
                data["avatar"] = instance.avatar.url
            except:
                data["avatar"] = None
        return data
