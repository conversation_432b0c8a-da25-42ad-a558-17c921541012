from rest_framework import serializers as serializeers
from apps.accounts.user.models import User
from apps.accounts.user.serializers.user import UserSerializer


class UpdateUserSerializer(UserSerializer):
    avatar = serializeers.ImageField(required=False)

    class Meta:
        model = User
        fields = [
            "id",
            "avatar",
            "fullname",
            "phone_number",
            "location",
            "company_information",
            "company_name",
            "account_type",
            "country",
            "country_name",
        ]
        read_only_field = ["email", "id", "country_name"]
