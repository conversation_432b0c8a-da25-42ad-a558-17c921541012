from django.contrib.auth.models import BaseUserManager
import random
from django.core.exceptions import ObjectDoesNotExist

from core.abstract.models import AbstractManager


class UserManager(BaseUserManager, AbstractManager):
    def create_user(self, email, password=None, **extra_fields):
        """
        Create and save a User with the given email and password.
        Ensures email is normalized to lowercase for case-insensitive uniqueness.
        """
        if not email:
            raise ValueError("The Email field must be set")

        email = self.normalize_email(email).lower()

        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)
        return self.create_user(email, password, **extra_fields)


class VerificationCodeManager(AbstractManager):
    def create_verification_code(self, user):
        code = "".join(random.choices("0123456789", k=6))
        return self.create(user=user, code=code)

    def validate_code(self, user, code):
        try:
            self.get(user=user, code=code)
            return True
        except ObjectDoesNotExist:
            return False
