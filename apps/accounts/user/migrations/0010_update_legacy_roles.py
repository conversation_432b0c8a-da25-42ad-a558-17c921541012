# Generated by Django 4.2.9 on 2025-03-16 12:47

from django.db import migrations


def update_legacy_roles(apps, schema_editor):
    """
    Update users with legacy roles to new roles:
    - trainee -> member
    - organization -> club_manager
    - admin -> admin (unchanged)
    """
    User = apps.get_model("_user", "User")

    # Update trainees to members
    User.objects.filter(role="trainee").update(role="member")

    # Update organizations to club_managers
    User.objects.filter(role="organization").update(role="club_manager")

    # Admins remain unchanged
    # User.objects.filter(role='admin').update(role='admin')


class Migration(migrations.Migration):
    dependencies = [
        ("_user", "0009_alter_user_role"),
    ]

    operations = [
        migrations.RunPython(update_legacy_roles, migrations.RunPython.noop),
    ]
