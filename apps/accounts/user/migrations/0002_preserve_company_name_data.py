from django.db import migrations


def preserve_company_name_data(apps, schema_editor):
    """
    Move company_name data to company_information for any users that have it set
    """
    User = apps.get_model("_user", "User")
    for user in User.objects.filter(company_name__isnull=False).exclude(
        company_name=""
    ):
        # If company_information is empty, just set it to company_name
        if not user.company_information:
            user.company_information = user.company_name
        # Otherwise, append company_name to company_information
        else:
            user.company_information = (
                f"{user.company_information}\nCompany: {user.company_name}"
            )
        user.save(update_fields=["company_information"])


class Migration(migrations.Migration):

    dependencies = [
        ("_user", "0001_initial"),
    ]

    operations = [
        migrations.RunPython(preserve_company_name_data, migrations.RunPython.noop),
    ]
