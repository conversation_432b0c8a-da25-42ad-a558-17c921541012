from celery import shared_task
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode
from django.utils.encoding import force_bytes
from django.conf import settings
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.translation import gettext_lazy as _
from django.db import transaction
from celery.exceptions import SoftTimeLimitExceeded
import socket
import logging
from core.tasks.emails import send_sendgrid_email

logger = logging.getLogger(__name__)

ROLE_DISPLAY_NAMES = {
    "admin": _("Administrator"),
    "club_manager": _("Club Manager"),
    "member": _("Member"),
}

ROLE_BASE_URLS = {
    "admin": getattr(settings, "ADMIN_BASE_UI_URL", "http://localhost:4150"),
    "club_manager": getattr(settings, "MANAGER_BASE_UI_URL", "http://localhost:4190"),
    "member": getattr(settings, "MEMBER_BASE_UI_URL", "http://localhost:4189"),
}


def _send_welcome_email(user_id):
    return
    """
    Internal function to send welcome email, used by both the Celery task and direct calls in tests.
    """
    from apps.accounts.user.models import User

    with transaction.atomic():
        try:
            user = User.objects.select_for_update().get(pk=user_id)
        except User.DoesNotExist:
            logger.error(f"Failed to send welcome email: User {user_id} does not exist")
            return

        if not user.email:
            logger.warning(
                f"User {user_id} has no email address, skipping welcome email"
            )
            return

        token = default_token_generator.make_token(user)
        base_url = ROLE_BASE_URLS.get(user.role, settings.MEMBER_BASE_UI_URL)
        uid = urlsafe_base64_encode(force_bytes(user.pk))
        reset_url = f"{base_url}/auth/reset-password?uid={uid}&token={token}"

        role_display = ROLE_DISPLAY_NAMES.get(user.role, _("Member"))

        context = {
            "user": user,
            "reset_password_url": reset_url,
            "role": user.role,
            "role_display": role_display,
            "frontend_url": getattr(settings, "FRONTEND_URL", "http://localhost:3000"),
        }

        subject = _("Welcome to Our Platform - Your Account Has Been Created")
        html_content = render_to_string("accounts/emails/user_created.html", context)
        text_content = render_to_string("accounts/emails/user_created.txt", context)

        try:
            send_sendgrid_email(
                to_emails=user.email,
                subject=subject,
                text_content=text_content,
                html_content=html_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
            )
            logger.info(
                f"Welcome email sent successfully to {user.email} with role {user.role}"
            )
        except Exception as e:
            logger.error(f"Failed to send welcome email to {user.email}: {str(e)}")
            raise


@shared_task(
    bind=True, max_retries=3, default_retry_delay=60, soft_time_limit=30, time_limit=60
)
def send_welcome_email_task(self, user_id):
    """
    Celery task to send welcome email to newly created users.

    This task handles sending the welcome email asynchronously with proper error handling,
    retries, and timeouts.

    Args:
        user_id: The ID of the newly created user
    """
    try:
        _send_welcome_email(user_id)
    except (socket.gaierror, socket.timeout) as e:
        logger.error(f"Network error while sending email: {str(e)}")
        retry_countdown = self.request.retries * 60
        raise self.retry(exc=e, countdown=retry_countdown, max_retries=3)
    except SoftTimeLimitExceeded:
        logger.error("Timeout while sending email")
        self.request.kwargs["soft_time_limit"] = (
            self.request.kwargs.get("soft_time_limit", 30) + 30
        )
        raise self.retry(countdown=1)
    except Exception as e:
        logger.error(f"Error sending welcome email: {str(e)}")
        logger.exception(e)
        if not isinstance(e, (socket.gaierror, socket.timeout, SoftTimeLimitExceeded)):
            raise


@shared_task(
    bind=True, max_retries=3, default_retry_delay=60, soft_time_limit=30, time_limit=60
)
def send_password_reset_email_task(self, user_id):
    """
    Celery task to send password reset email to newly created users.

    Args:
        user_id: The ID of the newly created user
    """
    from apps.accounts.user.models import User, UserToken

    try:
        with transaction.atomic():
            try:
                user = User.objects.select_for_update().get(pk=user_id)
            except User.DoesNotExist:
                logger.error(
                    f"Failed to send password reset email: User {user_id} does not exist"
                )
                return

            if not user.email:
                logger.warning(
                    f"User {user_id} has no email address, skipping password reset email"
                )
                return

            token = default_token_generator.make_token(user)
            UserToken.objects.update_or_create(user=user, defaults={"token": token})

            uid = urlsafe_base64_encode(force_bytes(user.pk))
            base_url = ROLE_BASE_URLS.get(user.role, ROLE_BASE_URLS["member"])

            context = {
                "user": user,
                "token": token,
                "uid": uid,
                "reset_url": f"{base_url}/auth/reset-password?token={token}",
            }

            subject = _("Reset Your Password")
            text_content = render_to_string("auth/password_reset_email.txt", context)
            html_content = render_to_string("auth/password_reset_email.html", context)

            try:
                send_sendgrid_email(
                    to_emails=user.email,
                    subject=subject,
                    text_content=text_content,
                    html_content=html_content,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                )
                logger.info(f"Password reset email sent successfully to {user.email}")
            except Exception as e:
                logger.error(
                    f"Failed to send password reset email to {user.email}: {str(e)}"
                )
                raise

    except (socket.gaierror, socket.timeout) as e:
        logger.error(f"Network error while sending password reset email: {str(e)}")
        retry_countdown = self.request.retries * 60
        raise self.retry(exc=e, countdown=retry_countdown, max_retries=3)
    except SoftTimeLimitExceeded:
        logger.error("Timeout while sending password reset email")
        self.request.kwargs["soft_time_limit"] = (
            self.request.kwargs.get("soft_time_limit", 30) + 30
        )
        raise self.retry(countdown=1)
    except Exception as e:
        logger.error(f"Error sending password reset email: {str(e)}")
        logger.exception(e)
        if not isinstance(e, (socket.gaierror, socket.timeout, SoftTimeLimitExceeded)):
            raise
