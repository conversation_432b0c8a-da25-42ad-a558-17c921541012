from django.test import TestCase
from django.utils import timezone
from datetime import timedelta
import uuid

from django.contrib.auth import get_user_model
from apps.accounts.user.filters import UserExportFilter

User = get_user_model()


class UserExportFilterTests(TestCase):
    """Test suite for UserExportFilter functionality"""

    def setUp(self):
        """Set up test data"""

        self.admin_user = User.objects.create_user(
            email="<EMAIL>",
            password="adminpassword",
            username="admin",
            role="admin",
            is_staff=True,
            is_superuser=True,
        )

        self.staff_user = User.objects.create_user(
            email="<EMAIL>",
            password="staffpassword",
            username="staffuser",
            role="member",
            is_staff=True,
            is_superuser=False,
        )

        self.regular_user = User.objects.create_user(
            email="<EMAIL>",
            password="userpassword",
            username="regularuser",
            role="member",
            is_staff=False,
        )

        self.inactive_user = User.objects.create_user(
            email="<EMAIL>",
            password="inactivepassword",
            username="inactiveuser",
            role="member",
            is_staff=False,
            is_active=False,
        )

        self.super_user = User.objects.create_user(
            email="<EMAIL>",
            password="superpassword",
            username="superuser",
            role="admin",
            is_staff=True,
            is_superuser=True,
        )

        self.logged_in_user = User.objects.create_user(
            email="<EMAIL>",
            password="loggedpassword",
            username="loggeduser",
            role="member",
        )

        User.objects.filter(pk=self.logged_in_user.pk).update(
            last_login=timezone.now() - timedelta(days=1)
        )
        self.logged_in_user.refresh_from_db()

        self.old_user = User.objects.create_user(
            email="<EMAIL>",
            password="oldpassword",
            username="olduser",
            role="member",
        )

        User.objects.filter(pk=self.old_user.pk).update(
            created=timezone.now() - timedelta(days=60)
        )
        self.old_user.refresh_from_db()

        self.last_month_user = User.objects.create_user(
            email="<EMAIL>",
            password="lastmonthpassword",
            username="lastmonthuser",
            role="member",
        )

        today = timezone.now()
        last_month = today.replace(month=today.month - 1 if today.month > 1 else 12)
        User.objects.filter(pk=self.last_month_user.pk).update(created=last_month)
        self.last_month_user.refresh_from_db()

        self.queryset = User.objects.all()

    def test_exclude_staff(self):
        """Test excluding staff users"""

        filtered = UserExportFilter(
            {"exclude_staff": "true"}, queryset=self.queryset
        ).qs

        self.assertEqual(filtered.count(), 5)

        usernames = [user.username for user in filtered]
        self.assertNotIn("admin", usernames)
        self.assertNotIn("staffuser", usernames)
        self.assertNotIn("superuser", usernames)

        filtered = UserExportFilter(
            {"exclude_staff": "false"}, queryset=self.queryset
        ).qs

        self.assertEqual(filtered.count(), 8)

    def test_exclude_superusers(self):
        """Test excluding superusers"""

        filtered = UserExportFilter(
            {"exclude_superusers": "true"}, queryset=self.queryset
        ).qs

        self.assertEqual(filtered.count(), 6)

        usernames = [user.username for user in filtered]
        self.assertNotIn("admin", usernames)
        self.assertNotIn("superuser", usernames)

        filtered = UserExportFilter(
            {"exclude_superusers": "false"}, queryset=self.queryset
        ).qs

        self.assertEqual(filtered.count(), 8)

    def test_inactive_only(self):
        """Test filtering for inactive users only"""

        filtered = UserExportFilter(
            {"inactive_only": "true"}, queryset=self.queryset
        ).qs

        self.assertEqual(filtered.count(), 1)
        self.assertEqual(filtered.first().username, "inactiveuser")

        filtered = UserExportFilter(
            {"inactive_only": "false"}, queryset=self.queryset
        ).qs

        self.assertEqual(filtered.count(), 8)

    def test_never_logged_in(self):
        """Test filtering for users who never logged in"""

        filtered = UserExportFilter(
            {"never_logged_in": "true"}, queryset=self.queryset
        ).qs

        self.assertEqual(filtered.count(), 7)

        usernames = [user.username for user in filtered]
        self.assertNotIn("loggeduser", usernames)

        filtered = UserExportFilter(
            {"never_logged_in": "false"}, queryset=self.queryset
        ).qs

        self.assertEqual(filtered.count(), 8)

    def test_created_this_month(self):
        """Test filtering for users created this month"""

        filtered = UserExportFilter(
            {"created_this_month": "true"}, queryset=self.queryset
        ).qs

        self.assertEqual(filtered.count(), 6)

        usernames = [user.username for user in filtered]
        self.assertNotIn("olduser", usernames)
        self.assertNotIn("lastmonthuser", usernames)

        filtered = UserExportFilter(
            {"created_this_month": "false"}, queryset=self.queryset
        ).qs

        self.assertEqual(filtered.count(), 8)

    def test_created_this_year(self):
        """Test filtering for users created this year"""

        filtered = UserExportFilter(
            {"created_this_year": "true"}, queryset=self.queryset
        ).qs

        self.assertEqual(filtered.count(), 8)

        filtered = UserExportFilter(
            {"created_this_year": "false"}, queryset=self.queryset
        ).qs

        self.assertEqual(filtered.count(), 8)

    def test_combine_export_filters(self):
        """Test combining multiple export-specific filters"""

        filtered = UserExportFilter(
            {
                "exclude_staff": "true",
                "exclude_superusers": "true",
                "never_logged_in": "true",
            },
            queryset=self.queryset,
        ).qs

        self.assertEqual(filtered.count(), 4)

        usernames = [user.username for user in filtered]
        self.assertNotIn("admin", usernames)
        self.assertNotIn("staffuser", usernames)
        self.assertNotIn("superuser", usernames)
        self.assertNotIn("loggeduser", usernames)

        self.assertIn("regularuser", usernames)
        self.assertIn("inactiveuser", usernames)
        self.assertIn("olduser", usernames)
        self.assertIn("lastmonthuser", usernames)

    def test_combine_with_standard_filters(self):
        """Test combining export filters with standard filters"""

        filtered = UserExportFilter(
            {"exclude_staff": "true", "role": "member", "is_active": "true"},
            queryset=self.queryset,
        ).qs

        self.assertEqual(filtered.count(), 4)

        usernames = [user.username for user in filtered]
        self.assertNotIn("admin", usernames)
        self.assertNotIn("staffuser", usernames)
        self.assertNotIn("superuser", usernames)
        self.assertNotIn("inactiveuser", usernames)

        self.assertIn("regularuser", usernames)
        self.assertIn("loggeduser", usernames)
        self.assertIn("olduser", usernames)
        self.assertIn("lastmonthuser", usernames)
