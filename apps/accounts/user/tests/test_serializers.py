from django.test import TestCase
from apps.accounts.user.models import User
from apps.accounts.user.serializers.user import UserSerializer
from apps.accounts.user.serializers.create_user import CreateUserSerializer
from apps.accounts.user.serializers.update_user import UpdateUserSerializer

from apps.countries.models import Country


class UserSerializerTest(TestCase):
    def setUp(self):
        self.user_data = {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "username": "testuser",
            "fullname": "Test User",
            "phone_number": "**********",
            "location": "Test Location",
            "company_information": "Test Company",
            "role": "member",
        }
        self.user = User.objects.create_user(**self.user_data)
        self.country = Country.objects.create(
            name="Test Country",
            iso3="TST",
            iso2="TS",
            phone_code="123",
            capital="Test Capital",
            region="Test Region",
            subregion="Test Subregion",
        )

    def test_user_serializer(self):
        serializer = UserSerializer(self.user)
        data = serializer.data

        self.assertEqual(data["email"], self.user_data["email"])
        self.assertEqual(data["username"], self.user_data["username"])
        self.assertEqual(data["fullname"], self.user_data["fullname"])
        self.assertEqual(data["phone_number"], self.user_data["phone_number"])
        self.assertEqual(data["location"], self.user_data["location"])
        self.assertEqual(
            data["company_information"], self.user_data["company_information"]
        )
        self.assertEqual(data["role"], self.user_data["role"])
        self.assertIsNone(data["country"])
        self.assertIsNone(data["country_name"])

    def test_user_serializer_with_country(self):
        self.user.country = self.country
        self.user.save()

        serializer = UserSerializer(self.user)
        data = serializer.data

        self.assertEqual(str(data["country"]), str(self.country.id))
        self.assertEqual(data["country_name"], self.country.name)

    def test_create_user_serializer(self):
        data = {
            "email": "<EMAIL>",
            "fullname": "New User",
            "phone_number": "0987654321",
            "location": "New Location",
            "company_information": "New Company",
            "role": "member",
            "country": self.country.id,
        }

        serializer = CreateUserSerializer(data=data)
        self.assertTrue(serializer.is_valid())

        user = serializer.save()
        self.assertEqual(user.email, data["email"])
        self.assertEqual(user.fullname, data["fullname"])
        self.assertEqual(user.phone_number, data["phone_number"])
        self.assertEqual(user.location, data["location"])
        self.assertEqual(user.company_information, data["company_information"])
        self.assertEqual(user.role, data["role"])
        self.assertEqual(user.country, self.country)

    def test_update_user_serializer(self):
        data = {
            "fullname": "Updated User",
            "phone_number": "5555555555",
            "location": "Updated Location",
            "company_information": "Updated Company",
            "country": self.country.id,
        }

        serializer = UpdateUserSerializer(self.user, data=data, partial=True)
        self.assertTrue(serializer.is_valid())

        user = serializer.save()
        self.assertEqual(user.fullname, data["fullname"])
        self.assertEqual(user.phone_number, data["phone_number"])
        self.assertEqual(user.location, data["location"])
        self.assertEqual(user.company_information, data["company_information"])
        self.assertEqual(user.country, self.country)
