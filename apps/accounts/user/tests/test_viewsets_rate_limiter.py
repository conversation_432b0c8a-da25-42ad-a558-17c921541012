from django.test import TestCase, override_settings
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from apps.accounts.user.models import User
from rest_framework_simplejwt.tokens import RefreshToken
from django.core.cache import cache
import json
import unittest


@override_settings(RATE_LIMITER_ENABLED=False)
class UserViewsetsRateLimiterTests(TestCase):
    def setUp(self):
        """Set up test data and authenticate"""

        cache.clear()

        self.client = APIClient()

        self.admin = User.objects.create_superuser(
            email="<EMAIL>", password="adminpass123", username="admin"
        )

        self.user = User.objects.create_user(
            email="<EMAIL>", password="userpass123", username="testuser"
        )

        refresh = RefreshToken.for_user(self.admin)
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {str(refresh.access_token)}")  # type: ignore

    @unittest.skip("Rate limiting is disabled in test environment")
    def test_create_user_rate_limit(self):
        """Test rate limiting for user creation"""
        url = reverse("accounts:create-user")
        data = {
            "email": "test{}@example.com",
            "password": "testpass123",
            "username": "testuser{}",
            "fullname": "Test User",
        }

        for i in range(5):
            response = self.client.post(
                url,
                {
                    "email": data["email"].format(i),
                    "password": data["password"],
                    "username": data["username"].format(i),
                    "fullname": data["fullname"],
                },
            )
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        response = self.client.post(
            url,
            {
                "email": data["email"].format(5),
                "password": data["password"],
                "username": data["username"].format(5),
                "fullname": data["fullname"],
            },
        )
        self.assertEqual(response.status_code, status.HTTP_429_TOO_MANY_REQUESTS)

    @unittest.skip("Rate limiting is disabled in test environment")
    def test_update_user_rate_limit(self):
        """Test rate limiting for user updates"""
        url = reverse("accounts:update-user", kwargs={"pk": self.user.pk})
        data = json.dumps(
            {
                "email": self.user.email,
                "username": self.user.username,
                "fullname": "Updated Name",
                "role": self.user.role,
                "password": "testpass123",
            }
        )

        for _ in range(5):
            response = self.client.patch(url, data, content_type="application/json")
            if response.status_code != status.HTTP_200_OK:
                print(f"Update user error: {response.content}")
            self.assertEqual(response.status_code, status.HTTP_200_OK)

        response = self.client.patch(url, data, content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_429_TOO_MANY_REQUESTS)

    @unittest.skip("Rate limiting is disabled in test environment")
    def test_list_users_rate_limit(self):
        """Test rate limiting for user listing"""
        url = reverse("accounts:list-user")

        for _ in range(5):
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_200_OK)

        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_429_TOO_MANY_REQUESTS)

    @unittest.skip("Rate limiting is disabled in test environment")
    def test_delete_user_rate_limit(self):
        """Test rate limiting for user deletion"""

        users = []
        for i in range(6):
            user = User.objects.create_user(
                email=f"delete{i}@example.com",
                password="testpass123",
                username=f"deleteuser{i}",
            )
            users.append(user)

        for i in range(5):
            url = reverse("accounts:delete-user", kwargs={"pk": users[i].pk})
            response = self.client.delete(url)
            self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        url = reverse("accounts:delete-user", kwargs={"pk": users[5].pk})
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_429_TOO_MANY_REQUESTS)

    @unittest.skip("Rate limiting is disabled in test environment")
    def test_update_role_rate_limit(self):
        """Test rate limiting for role updates"""
        url = reverse("accounts:update-role", kwargs={"pk": self.user.pk})
        data = json.dumps({"role": "club_manager"})

        for _ in range(5):
            response = self.client.patch(url, data, content_type="application/json")
            if response.status_code != status.HTTP_200_OK:
                print(f"Update role error: {response.content}")
            self.assertEqual(response.status_code, status.HTTP_200_OK)

        response = self.client.patch(url, data, content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_429_TOO_MANY_REQUESTS)

    def test_rate_limiter_disabled(self):
        """Test that rate limiting is bypassed when disabled"""

        url = reverse("accounts:create-user")
        for i in range(10):
            response = self.client.post(
                url,
                {
                    "email": f"test{i}@example.com",
                    "password": "testpass123",
                    "username": f"testuser{i}",
                    "fullname": "Test User",
                },
            )
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        url = reverse("accounts:update-user", kwargs={"pk": self.user.pk})
        data = json.dumps(
            {
                "email": self.user.email,
                "username": self.user.username,
                "fullname": "Updated Name",
                "role": self.user.role,
                "password": "testpass123",
            }
        )
        for _ in range(10):
            response = self.client.patch(url, data, content_type="application/json")
            self.assertEqual(response.status_code, status.HTTP_200_OK)

        url = reverse("accounts:list-user")
        for _ in range(10):
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_200_OK)

        url = reverse("accounts:update-role", kwargs={"pk": self.user.pk})
        data = json.dumps({"role": "club_manager"})
        for _ in range(10):
            response = self.client.patch(url, data, content_type="application/json")
            self.assertEqual(response.status_code, status.HTTP_200_OK)
