from django.test import TestCase, override_settings
from rest_framework import status
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model

from apps.accounts.user.tests.constants import (
    CREATE_USER_URL,
    UPDATE_USER_URL,
    DELETE_USER_URL,
    LIST_USER_URL,
    USER_EMAIL,
    USER_USERNAME,
    USER_PASSWORD,
    ADMIN_EMAIL,
    ADMIN_PASSWORD,
    ADMIN_USERNAME,
)
from core.utilities import tprint

User = get_user_model()


class UserTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.admin_user = User.objects.create_superuser(
            email=ADMIN_EMAIL, password=ADMIN_PASSWORD, username=ADMIN_USERNAME
        )
        self.client.force_authenticate(user=self.admin_user)  # type: ignore

    def test_create_success(self):
        data = {
            "email": "<EMAIL>",
            "username": "newuser",
            "password": "newpassword123",
            "fullname": "New User",
        }
        response = self.client.post(CREATE_USER_URL, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_update_user_success(self):
        user = User.objects.create(
            email=USER_EMAIL, password=USER_PASSWORD, username=USER_USERNAME
        )
        data = {"fullname": "Updated User"}
        response = self.client.patch(UPDATE_USER_URL(str(user.id)), data)  # type: ignore
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        user.refresh_from_db()
        self.assertEqual(user.fullname, "Updated User")  # type: ignore

    def test_delete_user_success(self):
        user = User.objects.create(
            email=USER_EMAIL, password=USER_PASSWORD, username=USER_USERNAME
        )
        response = self.client.delete(DELETE_USER_URL(str(user.id)))  # type: ignore
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(User.objects.filter(id=str(user.id)).exists())  # type: ignore

    @override_settings(RATE_LIMITER_ENABLED=False)
    def test_list_users_success(self):
        User.objects.create(
            email=USER_EMAIL, password=USER_PASSWORD, username=USER_USERNAME
        )
        response = self.client.get(LIST_USER_URL)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(len(response.json()) > 0)

    def test_update_role_success(self):
        user = User.objects.create(
            email=USER_EMAIL, password=USER_PASSWORD, username=USER_USERNAME
        )
        data = {"role": "club_manager"}
        response = self.client.patch(UPDATE_USER_URL(str(user.id)) + "update-role/", data)  # type: ignore
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        user.refresh_from_db()
        self.assertEqual(user.role, "club_manager")  # type: ignore
