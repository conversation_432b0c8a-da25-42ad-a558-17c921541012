from django.test import TestCase
from apps.accounts.user.models import User, VerificationCode
from apps.countries.models import Country


class UserModelTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpassword123",
            username="testuser",
            is_email_verified=True,
        )
        self.country = Country.objects.create(
            name="Test Country",
            iso3="TST",
            iso2="TS",
            phone_code="123",
            capital="Test Capital",
            region="Test Region",
            subregion="Test Subregion",
        )

    def test_user_creation(self):
        self.assertEqual(self.user.email, "<EMAIL>")
        self.assertEqual(self.user.username, "testuser")
        self.assertTrue(self.user.is_email_verified)
        self.assertFalse(self.user.is_staff)
        self.assertFalse(self.user.is_superuser)

    def test_user_with_country(self):
        self.user.country = self.country
        self.user.save()

        self.assertEqual(self.user.country, self.country)
        self.assertEqual(self.user.country.name, "Test Country")
        self.assertEqual(self.user.country.iso3, "TST")


class VerificationCodeModelTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpassword123",
            username="testuser",
            is_email_verified=True,
        )

    def test_verification_code_creation(self):
        code = VerificationCode.objects.create(user=self.user)
        self.assertEqual(len(code.code), 6)
        self.assertFalse(code.is_used)
        self.assertEqual(code.user, self.user)

    def test_verification_code_generation(self):
        code1 = VerificationCode.generate_code()
        code2 = VerificationCode.generate_code()
        self.assertEqual(len(code1), 6)
        self.assertEqual(len(code2), 6)
        self.assertNotEqual(code1, code2)
