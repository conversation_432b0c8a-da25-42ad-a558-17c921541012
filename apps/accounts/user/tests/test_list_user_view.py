from django.test import TestCase
from django.urls import reverse


from rest_framework.test import APIClient  # noqa
from rest_framework import status  # noqa
from django.utils import timezone
from datetime import timedelta
import uuid
import json
import csv
from io import StringIO

from django.contrib.auth import get_user_model

User = get_user_model()

# Note: The linter may incorrectly flag APIClient.force_authenticate as an unknown attribute,


class ListUserViewTests(TestCase):
    """Test suite for admin user listing API"""

    def setUp(self):
        """Set up test data"""

        self.admin_user = User.objects.create_user(
            email="<EMAIL>",
            password="adminpassword",
            username="admin",
            role="admin",
            is_staff=True,
            is_superuser=True,
        )

        self.manager_user = User.objects.create_user(
            email="<EMAIL>",
            password="managerpassword",
            username="manager",
            role="club_manager",
            is_staff=False,
        )

        self.regular_user = User.objects.create_user(
            email="<EMAIL>",
            password="userpassword",
            username="regularuser",
            role="member",
            is_staff=False,
        )

        self.inactive_user = User.objects.create_user(
            email="<EMAIL>",
            password="inactivepassword",
            username="inactiveuser",
            role="member",
            is_staff=False,
            is_active=False,
        )

        self.gmail_user = User.objects.create_user(
            email="<EMAIL>",
            password="gmailpassword",
            username="gmailuser",
            role="member",
        )

        self.old_user = User.objects.create_user(
            email="<EMAIL>",
            password="oldpassword",
            username="olduser",
            role="member",
        )
        User.objects.filter(pk=self.old_user.pk).update(
            created=timezone.now() - timedelta(days=7)
        )
        self.old_user.refresh_from_db()

        self.client = APIClient()

        self.url = reverse("accounts:list-user")
        self.export_url = reverse("accounts:export-users")

    def test_admin_can_access_user_list(self):
        """Test that admin users can access the user list API"""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)

        self.assertGreater(len(results), 0)

    def test_non_admin_cannot_access_user_list(self):
        """Test that non-admin users cannot access the user list API"""

        self.client.force_authenticate(user=self.manager_user)

        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        self.client.force_authenticate(user=self.regular_user)

        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_filter_by_role(self):
        """Test filtering users by role"""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(f"{self.url}?role=admin")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["role"], "admin")

        response = self.client.get(f"{self.url}?role=club_manager")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["role"], "club_manager")

        response = self.client.get(f"{self.url}?role=member")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)

        self.assertEqual(len(results), 4)

    def test_filter_by_email(self):
        """Test filtering users by email"""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(f"{self.url}?email=gmail")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["email"], "<EMAIL>")

        response = self.client.get(f"{self.url}?email_exact=<EMAIL>")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["email"], "<EMAIL>")

        response = self.client.get(f"{self.url}?email_domain=gmail.com")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["email"], "<EMAIL>")

    def test_filter_by_username(self):
        """Test filtering users by username"""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(f"{self.url}?username=admin")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["username"], "admin")

        response = self.client.get(f"{self.url}?username_exact=admin")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["username"], "admin")

    def test_filter_by_active_status(self):
        """Test filtering users by active status"""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(f"{self.url}?is_active=true")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)

        self.assertEqual(len(results), 5)

        response = self.client.get(f"{self.url}?is_active=false")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["username"], "inactiveuser")

    def test_filter_by_staff_status(self):
        """Test filtering users by staff status"""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(f"{self.url}?is_staff=true")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["username"], "admin")

        response = self.client.get(f"{self.url}?is_staff=false")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 5)

    def test_filter_by_creation_date(self):
        """Test filtering users by creation date"""
        self.client.force_authenticate(user=self.admin_user)

        today = timezone.now().date().isoformat()
        three_days_ago = (timezone.now() - timedelta(days=3)).date().isoformat()

        response = self.client.get(f"{self.url}?created_after={three_days_ago}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertGreaterEqual(len(results), 1)

        response = self.client.get(f"{self.url}?created_before={today}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertGreaterEqual(len(results), 1)

    def test_search_functionality(self):
        """Test searching users"""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(f"{self.url}?search=admin")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["username"], "admin")

        response = self.client.get(f"{self.url}?search=gmail")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["email"], "<EMAIL>")

    def test_ordering(self):
        """Test ordering users"""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(f"{self.url}?ordering=username")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)

        usernames = [result["username"] for result in results]
        sorted_usernames = sorted(usernames)
        self.assertEqual(usernames, sorted_usernames)

        response = self.client.get(f"{self.url}?ordering=-username")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)

        usernames = [result["username"] for result in results]
        sorted_usernames = sorted(usernames, reverse=True)
        self.assertEqual(usernames, sorted_usernames)

    def test_combined_filters(self):
        """Test combining multiple filters"""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(f"{self.url}?role=member&is_active=true")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = self._get_results_from_response(response)
        self.assertEqual(len(results), 3)

        usernames = [result["username"] for result in results]
        self.assertIn("regularuser", usernames)
        self.assertIn("gmailuser", usernames)
        self.assertIn("olduser", usernames)
        self.assertNotIn("inactiveuser", usernames)

    def test_csv_export(self):
        """Test exporting users as CSV"""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(f"{self.url}?format_type=csv")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response["Content-Type"], "text/csv")
        self.assertEqual(
            response["Content-Disposition"], 'attachment; filename="users.csv"'
        )

        content = response.content.decode("utf-8")
        csv_reader = csv.reader(StringIO(content))
        rows = list(csv_reader)

        self.assertEqual(rows[0][0], "ID")
        self.assertEqual(rows[0][1], "Username")
        self.assertEqual(rows[0][2], "Email")

        self.assertGreater(len(rows), 1)

    def test_json_export(self):
        """Test exporting users as JSON"""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(f"{self.url}?format_type=json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response["Content-Type"], "application/json")
        self.assertEqual(
            response["Content-Disposition"], 'attachment; filename="users.json"'
        )

        content = response.content.decode("utf-8")
        data = json.loads(content)

        self.assertIsInstance(data, list)
        self.assertGreater(len(data), 0)

    def test_export_users_action(self):
        """Test the export_users action with additional filters"""
        self.client.force_authenticate(user=self.admin_user)

        export_url = self.export_url

        try:
            response = self.client.get(
                f"{export_url}?format=csv&role=member&is_active=true"
            )
            if response.status_code == 404:

                export_url = self.url
                response = self.client.get(
                    f"{export_url}?format_type=csv&role=member&is_active=true"
                )
        except Exception:

            export_url = self.url
            response = self.client.get(
                f"{export_url}?format_type=csv&role=member&is_active=true"
            )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response["Content-Type"], "text/csv")

        content = response.content.decode("utf-8")
        csv_reader = csv.reader(StringIO(content))
        rows = list(csv_reader)

        self.assertGreater(len(rows), 1)

        try:
            response = self.client.get(
                f"{self.export_url}?format=json&role=member&is_active=true"
            )
            if response.status_code == 404:

                response = self.client.get(
                    f"{self.url}?format_type=json&role=member&is_active=true"
                )
        except Exception:

            response = self.client.get(
                f"{self.url}?format_type=json&role=member&is_active=true"
            )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response["Content-Type"], "application/json")

        content = response.content.decode("utf-8")
        data = json.loads(content)

        self.assertIsInstance(data, list)
        self.assertGreater(len(data), 0)

    def test_pagination(self):
        """Test pagination of user list"""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(f"{self.url}?limit=2")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertIsInstance(response.data, dict)
        self.assertIn("meta", response.data)
        self.assertIn("results", response.data)
        self.assertIn("count", response.data["meta"])

        results = response.data["results"]
        self.assertEqual(len(results), 2)

        response = self.client.get(f"{self.url}?limit=2&page=2")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = response.data["results"]
        self.assertGreaterEqual(len(results), 1)

        self.assertEqual(response.data["meta"]["current_page_number"], 2)

    def _get_results_from_response(self, response):
        """Helper method to extract results from paginated response"""
        if (
            hasattr(response, "data")
            and isinstance(response.data, dict)
            and "results" in response.data
        ):
            return response.data["results"]
        return response.data
