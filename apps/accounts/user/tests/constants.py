from django.urls import reverse

CREATE_USER_URL = reverse("accounts:create-user")
UPDATE_USER_URL = lambda pk: reverse("accounts:update-user", kwargs={"pk": pk})
DELETE_USER_URL = lambda pk: reverse("accounts:delete-user", kwargs={"pk": pk})
LIST_USER_URL = reverse("accounts:list-user")
EXPORT_USERS_URL = reverse("accounts:export-users")

USER_EMAIL = "<EMAIL>"
USER_USERNAME = "testuser"
USER_PASSWORD = "testpassword123"
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "adminpassword123"
ADMIN_USERNAME = "adminuser"
