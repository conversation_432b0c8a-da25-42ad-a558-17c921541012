import json
import csv
from io import <PERSON><PERSON>
from datetime import datetime, timedelta
from django.test import TestCase, override_settings
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model
from django.utils import timezone
from apps.accounts.user.tests.constants import (
    EXPORT_USERS_URL,
    ADMIN_EMAIL,
    ADMIN_PASSWORD,
    ADMIN_USERNAME,
)

User = get_user_model()


class ExportFiltersTests(TestCase):
    """Tests for the export users filters functionality"""

    def setUp(self):
        """Set up test data"""
        self.client = APIClient()

        self.admin = User.objects.create_user(
            email="<EMAIL>",
            password="adminpass",
            username="admin_user",
            is_staff=True,
            is_superuser=True,
            role="admin",
        )
        self.client.force_authenticate(user=self.admin)

        self.regular = User.objects.create_user(
            email="<EMAIL>",
            password="userpass",
            username="regular_user",
            role="member",
        )

        self.regular.last_login = timezone.now()
        self.regular.save()

        self.club_manager = User.objects.create_user(
            email="<EMAIL>",
            password="managerpass",
            username="manager_user",
            role="club_manager",
        )

        self.club_manager.last_login = timezone.now()
        self.club_manager.save()

        self.club_manager2 = User.objects.create_user(
            email="<EMAIL>",
            password="managerpass2",
            username="manager_user2",
            role="club_manager",
        )

        self.staff = User.objects.create_user(
            email="<EMAIL>",
            password="staffpass",
            username="staff_user",
            is_staff=True,
            role="member",
        )

        self.inactive = User.objects.create_user(
            email="<EMAIL>",
            password="inactivepass",
            username="inactive_user",
            role="member",
        )
        self.inactive.is_active = False
        self.inactive.save()

        self.user1 = User.objects.create_user(
            email="<EMAIL>",
            password="user1pass",
            username="gmail_user",
            role="member",
        )

    def _parse_csv_response(self, response):
        """Helper method to parse CSV response"""
        content = response.content.decode("utf-8")
        csv_reader = csv.reader(StringIO(content))
        return list(csv_reader)

    def _parse_json_response(self, response):
        """Helper method to parse JSON response"""
        content = response.content.decode("utf-8")
        return json.loads(content)

    def _get_emails_from_csv(self, rows):
        """Helper method to extract emails from CSV rows"""

        return [row[2] for row in rows[1:]]

    def _get_emails_from_json(self, data):
        """Helper method to extract emails from JSON data"""
        return [user["email"] for user in data]

    @override_settings(RATE_LIMITER_ENABLED=False)
    def test_filter_by_role(self):
        """Test filtering users by role"""
        response = self.client.get(f"{EXPORT_USERS_URL}?role=club_manager")

        self.assertEqual(response.status_code, 200)
        rows = self._parse_csv_response(response)
        emails = self._get_emails_from_csv(rows)

        self.assertEqual(len(emails), 2)
        self.assertIn("<EMAIL>", emails)
        self.assertIn("<EMAIL>", emails)
        self.assertNotIn("<EMAIL>", emails)
        self.assertNotIn("<EMAIL>", emails)

    @override_settings(RATE_LIMITER_ENABLED=False)
    def test_filter_by_email_contains(self):
        """Test filtering users by email contains"""
        response = self.client.get(f"{EXPORT_USERS_URL}?email=manager")

        self.assertEqual(response.status_code, 200)
        rows = self._parse_csv_response(response)
        emails = self._get_emails_from_csv(rows)

        self.assertEqual(len(emails), 2)
        self.assertIn("<EMAIL>", emails)
        self.assertIn("<EMAIL>", emails)

    @override_settings(RATE_LIMITER_ENABLED=False)
    def test_filter_by_email_exact(self):
        """Test filtering users by exact email"""
        response = self.client.get(f"{EXPORT_USERS_URL}?email_exact=<EMAIL>")

        self.assertEqual(response.status_code, 200)
        rows = self._parse_csv_response(response)
        emails = self._get_emails_from_csv(rows)

        self.assertEqual(len(emails), 1)
        self.assertIn("<EMAIL>", emails)

    @override_settings(RATE_LIMITER_ENABLED=False)
    def test_filter_by_email_domain(self):
        """Test filtering users by email domain"""
        response = self.client.get(f"{EXPORT_USERS_URL}?email_domain=gmail.com")

        self.assertEqual(response.status_code, 200)
        rows = self._parse_csv_response(response)
        emails = self._get_emails_from_csv(rows)

        self.assertEqual(len(emails), 1)
        self.assertIn("<EMAIL>", emails)
        self.assertNotIn("<EMAIL>", emails)

    @override_settings(RATE_LIMITER_ENABLED=False)
    def test_filter_by_is_active(self):
        """Test filtering users by active status"""
        response = self.client.get(f"{EXPORT_USERS_URL}?is_active=false")

        self.assertEqual(response.status_code, 200)
        rows = self._parse_csv_response(response)
        emails = self._get_emails_from_csv(rows)

        self.assertIn("<EMAIL>", emails)
        self.assertNotIn("<EMAIL>", emails)
        self.assertNotIn("<EMAIL>", emails)
        self.assertNotIn("<EMAIL>", emails)
        self.assertNotIn("<EMAIL>", emails)

    @override_settings(RATE_LIMITER_ENABLED=False)
    def test_filter_by_username(self):
        """Test filtering users by username"""
        response = self.client.get(f"{EXPORT_USERS_URL}?username=staff")

        self.assertEqual(response.status_code, 200)
        rows = self._parse_csv_response(response)
        emails = self._get_emails_from_csv(rows)

        self.assertEqual(len(emails), 1)
        self.assertIn("<EMAIL>", emails)

    @override_settings(RATE_LIMITER_ENABLED=False)
    def test_filter_exclude_staff(self):
        """Test filtering to exclude staff users"""
        response = self.client.get(f"{EXPORT_USERS_URL}?exclude_staff=true")

        self.assertEqual(response.status_code, 200)
        rows = self._parse_csv_response(response)
        emails = self._get_emails_from_csv(rows)

        self.assertNotIn("<EMAIL>", emails)
        self.assertIn("<EMAIL>", emails)
        self.assertIn("<EMAIL>", emails)

    @override_settings(RATE_LIMITER_ENABLED=False)
    def test_filter_inactive_only(self):
        """Test filtering to include only inactive users"""
        response = self.client.get(f"{EXPORT_USERS_URL}?inactive_only=true")

        self.assertEqual(response.status_code, 200)
        rows = self._parse_csv_response(response)
        emails = self._get_emails_from_csv(rows)

        self.assertIn("<EMAIL>", emails)
        self.assertNotIn("<EMAIL>", emails)
        self.assertNotIn("<EMAIL>", emails)
        self.assertNotIn("<EMAIL>", emails)
        self.assertNotIn("<EMAIL>", emails)

    @override_settings(RATE_LIMITER_ENABLED=False)
    def test_filter_never_logged_in(self):
        """Test filtering users who have never logged in"""
        response = self.client.get(f"{EXPORT_USERS_URL}?never_logged_in=true")

        self.assertEqual(response.status_code, 200)
        rows = self._parse_csv_response(response)
        emails = self._get_emails_from_csv(rows)

        self.assertIn("<EMAIL>", emails)
        self.assertIn("<EMAIL>", emails)
        self.assertIn("<EMAIL>", emails)

        self.assertNotIn("<EMAIL>", emails)
        self.assertNotIn("<EMAIL>", emails)

    @override_settings(RATE_LIMITER_ENABLED=False)
    def test_filter_has_logged_in(self):
        """Test filtering users who have logged in"""
        response = self.client.get(f"{EXPORT_USERS_URL}?has_logged_in=true")

        self.assertEqual(response.status_code, 200)
        rows = self._parse_csv_response(response)
        emails = self._get_emails_from_csv(rows)

        self.assertIn("<EMAIL>", emails)
        self.assertIn("<EMAIL>", emails)

    @override_settings(RATE_LIMITER_ENABLED=False)
    def test_filter_multiple_roles(self):
        """Test filtering users by multiple roles"""
        response = self.client.get(
            f"{EXPORT_USERS_URL}?multiple_roles=club_manager&multiple_roles=member"
        )

        self.assertEqual(response.status_code, 200)
        rows = self._parse_csv_response(response)
        emails = self._get_emails_from_csv(rows)

        self.assertIn("<EMAIL>", emails)
        self.assertIn("<EMAIL>", emails)
        self.assertIn("<EMAIL>", emails)

        self.assertNotIn("<EMAIL>", emails)

    @override_settings(RATE_LIMITER_ENABLED=False)
    def test_filter_search(self):
        """Test filtering users by search term"""
        response = self.client.get(f"{EXPORT_USERS_URL}?search=gmail")

        self.assertEqual(response.status_code, 200)
        rows = self._parse_csv_response(response)
        emails = self._get_emails_from_csv(rows)

        self.assertIn("<EMAIL>", emails)
        self.assertNotIn("<EMAIL>", emails)

    @override_settings(RATE_LIMITER_ENABLED=False)
    def test_filter_combination(self):
        """Test filtering users by multiple criteria"""

        response = self.client.get(
            f"{EXPORT_USERS_URL}?role=member&never_logged_in=true"
        )

        self.assertEqual(response.status_code, 200)
        rows = self._parse_csv_response(response)
        emails = self._get_emails_from_csv(rows)

        self.assertIn("<EMAIL>", emails)
        self.assertIn("<EMAIL>", emails)

        self.assertNotIn("<EMAIL>", emails)

        self.assertNotIn("<EMAIL>", emails)
        self.assertNotIn("<EMAIL>", emails)

    @override_settings(RATE_LIMITER_ENABLED=False)
    def test_filter_json_format(self):
        """Test filtering with JSON format"""
        response = self.client.get(f"{EXPORT_USERS_URL}?format=json&role=club_manager")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response["Content-Type"], "application/json")

        data = self._parse_json_response(response)
        emails = self._get_emails_from_json(data)

        self.assertEqual(len(emails), 2)
        self.assertIn("<EMAIL>", emails)
        self.assertIn("<EMAIL>", emails)

    @override_settings(RATE_LIMITER_ENABLED=False)
    def test_filter_complex_combination(self):
        """Test filtering users by complex combination of criteria"""

        complex_user = User.objects.create_user(
            email="<EMAIL>",
            password="password123",
            username="complexuser",
            role="member",
        )

        complex_user.last_login = timezone.now()
        complex_user.save()

        response = self.client.get(
            f"{EXPORT_USERS_URL}?role=member&email_domain=gmail.com&format=json"
        )

        self.assertEqual(response.status_code, 200)
        data = self._parse_json_response(response)
        emails = self._get_emails_from_json(data)

        self.assertEqual(len(emails), 2)
        self.assertIn("<EMAIL>", emails)
        self.assertIn("<EMAIL>", emails)
        self.assertNotIn("<EMAIL>", emails)
