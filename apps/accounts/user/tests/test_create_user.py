from django.test import TestCase, override_settings
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
import time
from datetime import datetime
from unittest.mock import patch, MagicMock
import logging
from django.conf import settings
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode
from django.utils.encoding import force_bytes

from apps.accounts.user.models import User


@override_settings(TESTING=False)
class CreateUserViewTest(TestCase):
    """Test cases for the CreateUserViewSet."""

    def setUp(self):
        """Set up for tests."""
        self.client = APIClient()

        self.admin_user = User.objects.create_user(
            username="adminuser",
            email="<EMAIL>",
            password="adminpass123",
            role="admin",
            is_staff=True,
            is_superuser=True,
        )

        self.regular_user = User.objects.create_user(
            username="regularuser",
            email="<EMAIL>",
            password="userpass123",
            role="member",
        )

        self.url = reverse("accounts:create-user")

    @patch("apps.accounts.user.tasks.send_welcome_email_task.delay")
    @patch("apps.accounts.user.tasks.send_password_reset_email_task.delay")
    def test_create_user_with_phone_number(self, mock_reset_task, mock_welcome_task):
        """Test creating a user with phone number and the exact format specified."""

        self.client.force_authenticate(user=self.admin_user)

        data = {
            "email": "<EMAIL>",
            "fullname": "Selmi",
            "phone_number": "+*********16",
            "role": "club_manager",
        }

        response = self.client.post(self.url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data["email"], "<EMAIL>")
        self.assertEqual(response.data["fullname"], "Selmi")
        self.assertEqual(response.data["phone_number"], "+*********16")
        self.assertEqual(response.data["role"], "club_manager")

        user = User.objects.get(email="<EMAIL>")
        self.assertEqual(user.fullname, "Selmi")
        self.assertEqual(user.phone_number, "+*********16")
        self.assertEqual(user.role, "club_manager")

        mock_welcome_task.assert_called_once_with(user.id)
        mock_reset_task.assert_called_once_with(user.id)

    @patch("apps.accounts.user.tasks.send_welcome_email_task.delay")
    @patch("apps.accounts.user.tasks.send_password_reset_email_task.delay")
    def test_create_user_response_time(self, mock_reset_task, mock_welcome_task):
        """Test response time specifically for the format causing timeouts."""

        self.client.force_authenticate(user=self.admin_user)

        data = {
            "email": "<EMAIL>",
            "fullname": "Selmi",
            "phone_number": "+*********16",
            "role": "club_manager",
        }

        start_time = time.time()
        response = self.client.post(self.url, data, format="json")
        end_time = time.time()

        creation_time = end_time - start_time

        logging.info(f"Response time for specific format: {creation_time} seconds")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        max_allowed_time = 1.0
        self.assertTrue(
            creation_time < max_allowed_time,
            f"User creation took too long: {creation_time} seconds",
        )

        user = User.objects.get(email="<EMAIL>")
        mock_welcome_task.assert_called_once_with(user.id)
        mock_reset_task.assert_called_once_with(user.id)

    @override_settings(EMAIL_BACKEND="django.core.mail.backends.locmem.EmailBackend")
    @patch("apps.accounts.user.tasks.send_welcome_email_task.delay")
    @patch("apps.accounts.user.tasks.send_password_reset_email_task.delay")
    def test_create_user_performance(self, mock_reset_task, mock_welcome_task):
        """Test performance of creating multiple users in succession."""

        self.client.force_authenticate(user=self.admin_user)

        num_users = 5

        start_time = time.time()

        creation_times = []

        for i in range(num_users):
            user_data = {
                "email": f"testperformance{i}@example.com",
                "fullname": f"Test User {i}",
                "phone_number": f"+*********{i}0",
                "role": "club_manager",
            }

            user_start = time.time()
            response = self.client.post(self.url, user_data, format="json")
            user_end = time.time()

            creation_time = user_end - user_start
            creation_times.append(creation_time)

            self.assertEqual(
                response.status_code,
                status.HTTP_201_CREATED,
                f"Failed to create user {i} - response: {response.data}",
            )

        end_time = time.time()
        total_time = end_time - start_time

        logging.info(f"Total time to create {num_users} users: {total_time} seconds")
        logging.info(f"Average time per user: {total_time/num_users} seconds")
        logging.info(f"Longest creation time: {max(creation_times)} seconds")

        max_allowed_time = 0.5
        self.assertTrue(
            max(creation_times) < max_allowed_time,
            f"User creation took too long: {max(creation_times)} seconds",
        )

        self.assertEqual(mock_welcome_task.call_count, num_users)
        self.assertEqual(mock_reset_task.call_count, num_users)

    @patch("apps.accounts.user.tasks.send_welcome_email_task.delay")
    @patch("apps.accounts.user.tasks.send_password_reset_email_task.delay")
    def test_create_user_with_task_failure(self, mock_reset_task, mock_welcome_task):
        """Test that user creation succeeds even if task queuing fails."""

        mock_reset_task.side_effect = Exception("Simulated task queuing failure")
        mock_welcome_task.side_effect = Exception("Simulated task queuing failure")

        self.client.force_authenticate(user=self.admin_user)

        data = {
            "email": "<EMAIL>",
            "fullname": "Task Failure Test",
            "phone_number": "+*********99",
            "role": "club_manager",
        }

        response = self.client.post(self.url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        user = User.objects.get(email="<EMAIL>")
        self.assertEqual(user.fullname, "Task Failure Test")
        self.assertEqual(user.role, "club_manager")

        self.assertTrue(User.objects.filter(email="<EMAIL>").exists())

        mock_welcome_task.assert_called_once_with(user.id)

        mock_reset_task.assert_not_called()

    @patch("apps.accounts.user.tasks.send_welcome_email_task.delay")
    @patch("apps.accounts.user.tasks.send_password_reset_email_task.delay")
    def test_create_user_with_role(self, mock_reset_task, mock_welcome_task):
        """Test creating a user with a specified role."""

        self.client.force_authenticate(user=self.admin_user)

        data = {
            "email": "<EMAIL>",
            "fullname": "Club Manager",
            "role": "club_manager",
        }

        response = self.client.post(self.url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data["role"], "club_manager")

        user = User.objects.get(email="<EMAIL>")
        self.assertEqual(user.role, "club_manager")

        mock_welcome_task.assert_called_once_with(user.id)
        mock_reset_task.assert_called_once_with(user.id)

    @patch("apps.accounts.user.tasks.send_welcome_email_task.delay")
    @patch("apps.accounts.user.tasks.send_password_reset_email_task.delay")
    def test_create_user_without_role(self, mock_reset_task, mock_welcome_task):
        """Test creating a user without specifying a role (should default to 'member')."""

        self.client.force_authenticate(user=self.admin_user)

        data = {
            "email": "<EMAIL>",
            "fullname": "New Member",
        }

        response = self.client.post(self.url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data["role"], "member")

        user = User.objects.get(email="<EMAIL>")
        self.assertEqual(user.role, "member")

        mock_welcome_task.assert_called_once_with(user.id)
        mock_reset_task.assert_called_once_with(user.id)

    def test_permission_denied_for_non_admin(self):
        """Test that non-admin users cannot create new users."""

        self.client.force_authenticate(user=self.regular_user)

        data = {
            "email": "<EMAIL>",
            "fullname": "New User",
            "username": "newuser",
            "role": "member",
        }

        response = self.client.post(self.url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertFalse(User.objects.filter(email="<EMAIL>").exists())

    def test_create_user_with_email_failure(self):
        """Test that user creation still succeeds even if email sending fails."""
        with patch("django.core.mail.EmailMultiAlternatives.send") as mock_email_send:
            mock_email_send.side_effect = Exception("Simulated email sending failure")

            self.client.force_authenticate(user=self.admin_user)

            data = {
                "email": "<EMAIL>",
                "fullname": "Email Failure Test",
                "phone_number": "+*********99",
                "role": "club_manager",
            }

            try:
                response = self.client.post(self.url, data, format="json")

                self.assertEqual(response.status_code, status.HTTP_201_CREATED)

                user = User.objects.get(email="<EMAIL>")
                self.assertEqual(user.fullname, "Email Failure Test")
                self.assertEqual(user.role, "club_manager")

                self.assertTrue(
                    User.objects.filter(email="<EMAIL>").exists()
                )

            except Exception as e:
                self.fail(f"User creation failed when email sending failed: {str(e)}")
