from django.test import TestCase, override_settings
from django.core import mail
from django.conf import settings
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode
from django.utils.encoding import force_bytes
from django.core.mail.message import EmailMultiAlternatives
from unittest.mock import patch, MagicMock

from apps.accounts.user.models import User


@override_settings(TESTING=True)
class UserCreationEmailSignalTest(TestCase):
    """Test cases for the user creation email signal."""

    def setUp(self):
        """Set up for tests."""
        mail.outbox = []

    @patch("apps.accounts.user.signals.send_email_task.delay")
    def test_account_creation_email_sent(self, mock_email_task):
        """Test that an email task is queued when a user account is created."""

        with patch("apps.accounts.user.signals.handle_user_creation") as mock_signal:

            def real_signal_handler(sender, instance, created, **kwargs):
                if created and instance.email:

                    mock_email_task.delay(
                        to_emails=instance.email,
                        subject="Welcome to Our Platform - Your Account Has Been Created",
                        text_content="Mock text content",
                        html_content="Mock html content",
                        from_email=settings.DEFAULT_FROM_EMAIL,
                    )

            mock_signal.side_effect = real_signal_handler

            user = User.objects.create_user(
                username="testuser",
                email="<EMAIL>",
                password="testpassword",
                role="member",
            )

            real_signal_handler(User, user, True)

            mock_email_task.delay.assert_called_once()
            call_kwargs = mock_email_task.delay.call_args[1]
            self.assertEqual(call_kwargs["to_emails"], user.email)
            self.assertEqual(
                call_kwargs["subject"],
                "Welcome to Our Platform - Your Account Has Been Created",
            )

    @patch("apps.accounts.user.signals.send_email_task.delay")
    def test_different_roles_in_email(self, mock_email_task):
        """Test that the signal is triggered for different user roles."""
        test_cases = [
            {
                "role": "admin",
                "display_name": "Administrator",
                "base_url": settings.ADMIN_BASE_UI_URL,
            },
            {
                "role": "club_manager",
                "display_name": "Club Manager",
                "base_url": settings.MANAGER_BASE_UI_URL,
            },
            {
                "role": "member",
                "display_name": "Member",
                "base_url": settings.MEMBER_BASE_UI_URL,
            },
        ]

        with patch("apps.accounts.user.signals.handle_user_creation") as mock_signal:

            def real_signal_handler(sender, instance, created, **kwargs):
                if created and instance.email:
                    mock_email_task.delay(
                        to_emails=instance.email,
                        subject="Welcome to Our Platform - Your Account Has Been Created",
                        text_content="Mock text content",
                        html_content="Mock html content",
                        from_email=settings.DEFAULT_FROM_EMAIL,
                    )

            mock_signal.side_effect = real_signal_handler

            for test_case in test_cases:
                mock_email_task.reset_mock()

                user = User.objects.create_user(
                    username=f"test_{test_case['role']}",
                    email=f"{test_case['role']}@example.com",
                    password="testpassword",
                    role=test_case["role"],
                )

                real_signal_handler(User, user, True)

                mock_email_task.delay.assert_called_once()
                call_kwargs = mock_email_task.delay.call_args[1]
                self.assertEqual(call_kwargs["to_emails"], user.email)

    def test_no_email_sent_for_existing_user_update(self):
        """Test that no email is sent when an existing user is updated."""
        user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpassword",
            role="member",
        )

        mail.outbox = []

        user.fullname = "Updated Name"
        user.save()

        self.assertEqual(
            len(mail.outbox), 0, "No email should be sent for user updates"
        )
