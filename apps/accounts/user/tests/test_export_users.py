import json
import csv
from io import String<PERSON>
from django.test import TestCase, override_settings
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model
from apps.accounts.user.tests.constants import (
    EXPORT_USERS_URL,
    ADMIN_EMAIL,
    ADMIN_PASSWORD,
    ADMIN_USERNAME,
    USER_EMAIL,
    USER_USERNAME,
    USER_PASSWORD,
)

User = get_user_model()


class ExportUsersTests(TestCase):
    """Tests for the export users functionality"""

    def setUp(self):
        self.client = APIClient()

        self.admin_user = User.objects.create_superuser(
            email=ADMIN_EMAIL, password=ADMIN_PASSWORD, username=ADMIN_USERNAME
        )

        self.regular_user = User.objects.create_user(
            email=USER_EMAIL, password=USER_PASSWORD, username=USER_USERNAME
        )
        self.club_manager_user = User.objects.create_user(
            email="<EMAIL>",
            password="managerpass123",
            username="manageruser",
            role="club_manager",
        )
        self.club_manager2_user = User.objects.create_user(
            email="<EMAIL>",
            password="manager2pass123",
            username="manager2user",
            role="club_manager",
        )

        self.client.force_authenticate(user=self.admin_user)

    @override_settings(RATE_LIMITER_ENABLED=False)
    def test_export_users_csv_format(self):
        """Test exporting users in CSV format"""
        response = self.client.get(EXPORT_USERS_URL)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response["Content-Type"], "text/csv")
        self.assertTrue(
            'attachment; filename="users.csv"' in response["Content-Disposition"]
        )

        content = response.content.decode("utf-8")
        csv_reader = csv.reader(StringIO(content))
        rows = list(csv_reader)

        self.assertEqual(
            rows[0],
            [
                "ID",
                "Username",
                "Email",
                "Full Name",
                "Role",
                "Active",
                "Staff",
                "Created At",
                "Phone Number",
            ],
        )

        self.assertEqual(len(rows), 5)

        emails = [row[2] for row in rows[1:]]
        self.assertIn(ADMIN_EMAIL, emails)
        self.assertIn(USER_EMAIL, emails)
        self.assertIn("<EMAIL>", emails)
        self.assertIn("<EMAIL>", emails)

    @override_settings(RATE_LIMITER_ENABLED=False)
    def test_export_users_json_format(self):
        """Test exporting users in JSON format"""
        response = self.client.get(f"{EXPORT_USERS_URL}?format=json")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response["Content-Type"], "application/json")
        self.assertTrue(
            'attachment; filename="users.json"' in response["Content-Disposition"]
        )

        content = response.content.decode("utf-8")
        users_data = json.loads(content)

        self.assertEqual(len(users_data), 4)

        emails = [user["email"] for user in users_data]
        self.assertIn(ADMIN_EMAIL, emails)
        self.assertIn(USER_EMAIL, emails)
        self.assertIn("<EMAIL>", emails)
        self.assertIn("<EMAIL>", emails)

    @override_settings(RATE_LIMITER_ENABLED=False)
    def test_export_users_filtered_by_role(self):
        """Test exporting users filtered by role"""
        response = self.client.get(f"{EXPORT_USERS_URL}?role=club_manager")

        self.assertEqual(response.status_code, 200)

        content = response.content.decode("utf-8")
        csv_reader = csv.reader(StringIO(content))
        rows = list(csv_reader)

        self.assertEqual(len(rows), 3)

        header = rows[0]
        self.assertIn("Email", header)
        self.assertIn("Role", header)

        for row in rows[1:]:
            self.assertEqual(row[4], "club_manager")

    @override_settings(RATE_LIMITER_ENABLED=False)
    def test_export_users_search_by_email(self):
        """Test exporting users filtered by search term"""
        response = self.client.get(f"{EXPORT_USERS_URL}?search=<EMAIL>")

        self.assertEqual(response.status_code, 200)

        content = response.content.decode("utf-8")
        csv_reader = csv.reader(StringIO(content))
        rows = list(csv_reader)

        self.assertEqual(len(rows), 2)

        emails = [row[2] for row in rows[1:]]
        self.assertIn("<EMAIL>", emails)
        self.assertNotIn(ADMIN_EMAIL, emails)
        self.assertNotIn(USER_EMAIL, emails)
        self.assertNotIn("<EMAIL>", emails)

    @override_settings(RATE_LIMITER_ENABLED=False)
    def test_export_users_unauthorized(self):
        """Test that non-admin users cannot export users"""

        self.client.force_authenticate(user=self.regular_user)

        response = self.client.get(EXPORT_USERS_URL)

        self.assertEqual(response.status_code, 403)

    @override_settings(RATE_LIMITER_ENABLED=False)
    def test_export_users_unauthenticated(self):
        """Test that unauthenticated users cannot export users"""

        self.client.force_authenticate(user=None)

        response = self.client.get(EXPORT_USERS_URL)

        self.assertEqual(response.status_code, 401)
