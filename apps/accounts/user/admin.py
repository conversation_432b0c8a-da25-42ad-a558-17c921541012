from django.contrib import admin

from .models import User, VerificationCode


class VerificationCodeAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "user",
        "code",
    ]
    search_fields = ["id", "user", "code"]
    readonly_fields = ["id"]


class UserAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "email",
        "username",
        "is_staff",
        "is_superuser",
        "is_email_verified",
        "phone_number",
        "location",
        "company_information",
        "role",
    ]
    search_fields = ["id", "email", "username", "phone_number", "location", "role"]
    list_filter = ["is_staff", "is_superuser", "is_email_verified", "role"]
    readonly_fields = ["id"]

    def save_model(self, request, obj, form, change):
        if "password" in form.changed_data:
            obj.set_password(form.cleaned_data["password"])
        obj.save()


admin.site.register(User, UserAdmin)
admin.site.register(VerificationCode, VerificationCodeAdmin)
