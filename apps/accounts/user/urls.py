from django.urls import path

from apps.accounts.user.views.base_user import BaseUserViewSet
from apps.accounts.user.views.create_user import CreateUSerViewSet
from apps.accounts.user.views.update_user import UpdateUserViewSet
from apps.accounts.user.views.delete_user import DeleteUserViewSet
from apps.accounts.user.views.list_user import ListUserViewSet

app_name = "accounts"

urlpatterns = [
    path("create/", CreateUSerViewSet.as_view(), name="create-user"),
    path("<uuid:pk>/", UpdateUserViewSet.as_view(), name="update-user"),
    path("delete/<uuid:pk>/", DeleteUserViewSet.as_view(), name="delete-user"),
    path("", ListUserViewSet.as_view({"get": "list"}), name="list-user"),
    path(
        "<uuid:pk>/update-role/",
        BaseUserViewSet.as_view({"patch": "update_role"}),
        name="update-role",
    ),
    path(
        "export/",
        ListUserViewSet.as_view({"get": "export_users"}),
        name="export-users",
    ),
]
