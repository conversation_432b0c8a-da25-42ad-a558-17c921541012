from django.db.models.signals import post_save
from django.dispatch import receiver
from django.conf import settings
from django.template.loader import render_to_string
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode
from django.utils.encoding import force_bytes
import logging

from apps.accounts.user.models import User
from core.tasks.emails import send_email_task

logger = logging.getLogger(__name__)


@receiver(post_save, sender=User)
def handle_user_creation(sender, instance, created, **kwargs):
    """
    Signal handler for user creation.
    Sends welcome email to newly created users.
    """
    return
    if created and instance.email:
        try:
            token = default_token_generator.make_token(instance)
            base_url = settings.MEMBER_BASE_UI_URL
            if instance.role == "admin":
                base_url = settings.ADMIN_BASE_UI_URL
            elif instance.role == "club_manager":
                base_url = settings.MANAGER_BASE_UI_URL

            uid = urlsafe_base64_encode(force_bytes(instance.pk))
            reset_url = f"{base_url}/auth/reset-password?uid={uid}&token={token}"

            role_display = {
                "admin": "Administrator",
                "club_manager": "Club Manager",
                "member": "Member",
            }.get(instance.role, "Member")

            context = {
                "user": instance,
                "reset_password_url": reset_url,
                "role": instance.role,
                "role_display": role_display,
                "frontend_url": getattr(
                    settings, "FRONTEND_URL", "http://localhost:3000"
                ),
            }

            subject = "Welcome to Our Platform - Your Account Has Been Created"
            html_content = render_to_string(
                "accounts/emails/user_created.html", context
            )
            text_content = render_to_string("accounts/emails/user_created.txt", context)

            send_email_task.delay(
                to_emails=instance.email,
                subject=subject,
                text_content=text_content,
                html_content=html_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
            )

            logger.info(f"Welcome email queued for {instance.email}")
        except Exception as e:
            logger.error(
                f"Failed to queue welcome email for {instance.email}: {str(e)}"
            )
            logger.exception(e)
