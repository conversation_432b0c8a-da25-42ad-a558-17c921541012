from rest_framework.permissions import Is<PERSON><PERSON><PERSON>icated, IsAdminUser
from core.abstract.viewsets import AbstractGenericViewSet
from drf_spectacular.utils import extend_schema
from rest_framework.generics import CreateAPIView
from django.utils.translation import gettext_lazy as _
from rest_framework.response import Response
from rest_framework import status
from django.db import transaction
import logging

from core.decorators.error_handler import api_error_handler
from apps.accounts.user.serializers.create_user import CreateUserSerializer
from apps.accounts.user.models import User
from apps.accounts.user.tasks import (
    send_welcome_email_task,
    send_password_reset_email_task,
)
from core.ratelimiter import dynamic_rate_limit

logger = logging.getLogger(__name__)


@extend_schema(tags=["Admin"])
class CreateUSerViewSet(AbstractGenericViewSet, CreateAPIView):
    serializer_class = CreateUserSerializer
    queryset = User.objects.all()
    permission_classes = (IsAuthenticated, IsAdminUser)

    @extend_schema(
        description="""
        Create a new user with specified role.
        
        If no role is provided, 'member' will be assigned by default.
        
        Available roles:
        - member: Individual member user on the Tej platform
        - club_manager: Club Manager on the Tej platform
        - admin: Has administrative privileges
        """,
        request=CreateUserSerializer,
        responses={
            201: CreateUserSerializer,
            400: _("Bad Request"),
        },
    )
    @api_error_handler
    @dynamic_rate_limit()
    def create(self, request, *args, **kwargs):
        with transaction.atomic():
            serializer = self.get_serializer(data=request.data)
            if serializer.is_valid():
                if not serializer.validated_data.get("role"):
                    serializer.validated_data["role"] = "member"

                try:

                    user = serializer.save()

                    try:

                        send_welcome_email_task.delay(user.id)

                        send_password_reset_email_task.delay(user.id)
                    except Exception as e:
                        logger.error(f"Failed to queue email tasks: {str(e)}")
                        logger.exception(e)

                        pass

                    return Response(
                        serializer.data,
                        status=status.HTTP_201_CREATED,
                    )
                except Exception as e:
                    logger.error(f"Failed to create user: {str(e)}")
                    logger.exception(e)
                    return Response(
                        {"error": _("Failed to create user. Please try again.")},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    )

            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
