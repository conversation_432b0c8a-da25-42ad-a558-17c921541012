from rest_framework.permissions import IsA<PERSON><PERSON>icated, IsAdminUser
from core.abstract.viewsets import AbstractViewSet
from drf_spectacular.utils import extend_schema
from django.utils.translation import gettext_lazy as _
from rest_framework.response import Response
from rest_framework import status
from rest_framework.decorators import action

from apps.accounts.user.models import User
from core.ratelimiter import dynamic_rate_limit


@extend_schema(tags=["Admin"])
class BaseUserViewSet(AbstractViewSet):
    permission_classes = (
        IsAuthenticated,
        IsAdminUser,
    )

    def get_queryset(self):
        if self.request.user.is_superuser:
            return User.objects.all()
        return User.objects.exclude(is_superuser=True)

    def get_object(self):
        obj = User.objects.get(pk=self.kwargs["pk"])
        self.check_object_permissions(self.request, obj)
        return obj

    @extend_schema(
        description="""
        Update user role to a new role.
        
        Available roles:
        - member: Individual member user on the Tej platform
        - club_manager: Club Manager on the Tej platform
        - admin: Has administrative privileges
        """,
        request={"application/json": {"example": {"role": "member"}}},
        responses={
            200: {
                "description": "Role updated successfully",
                "content": {
                    "application/json": {
                        "example": {"status": "Role updated to member"}
                    }
                },
            },
            400: {
                "description": "Invalid role provided",
                "content": {"application/json": {"example": {"error": _("Invalid role")}}},
            },
        },
    )
    @action(detail=True, methods=["patch"], permission_classes=[IsAdminUser])
    @dynamic_rate_limit()
    def update_role(self, request, pk=None):
        user = self.get_object()
        new_role = request.data.get("role")
        if new_role in dict(User.ROLE_CHOICES):
            user.role = new_role
            user.save()
            return Response(
                {"status": _(f"Role updated to {new_role}")}, status=status.HTTP_200_OK
            )
        return Response({"error": _("Invalid role")}, status=status.HTTP_400_BAD_REQUEST)
