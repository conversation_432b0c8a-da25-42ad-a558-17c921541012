from rest_framework.permissions import IsAuth<PERSON>icated, IsAdminUser
from core.abstract.viewsets import AbstractGenericViewSet
from drf_spectacular.utils import extend_schema
from rest_framework.generics import (
    DestroyAPIView,
)
from django.utils.translation import gettext_lazy as _
from rest_framework.response import Response
from rest_framework import status

from core.decorators.error_handler import api_error_handler
from apps.accounts.user.serializers.user import (
    UserSerializer,
)
from apps.accounts.user.models import User
from core.ratelimiter import dynamic_rate_limit


@extend_schema(tags=["Admin"])
class DeleteUserViewSet(AbstractGenericViewSet, DestroyAPIView):
    serializer_class = UserSerializer
    permission_classes = (
        IsAuthenticated,
        IsAdminUser,
    )
    queryset = User.objects.all()

    @extend_schema(
        description="Delete a user",
        responses={204: _("No Content"), 404: _("Not Found")},
    )
    @api_error_handler
    @dynamic_rate_limit()
    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
