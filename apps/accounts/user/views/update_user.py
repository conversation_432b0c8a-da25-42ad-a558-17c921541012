from rest_framework.permissions import Is<PERSON><PERSON><PERSON>icated, IsAdminUser
from core.abstract.viewsets import AbstractGenericViewSet
from drf_spectacular.utils import extend_schema
from rest_framework.generics import UpdateAPIView
from django.utils.translation import gettext_lazy as _
from rest_framework.response import Response
from rest_framework import status
from core.ratelimiter import dynamic_rate_limit

from core.decorators.error_handler import api_error_handler
from apps.accounts.user.serializers.user import (
    UserSerializer,
)
from apps.accounts.user.serializers.update_user import (
    UpdateUserSerializer,
)
from apps.accounts.user.models import User


@extend_schema(tags=["Admin"])
class UpdateUserViewSet(AbstractGenericViewSet, UpdateAPIView):
    serializer_class = UpdateUserSerializer
    permission_classes = (
        IsAuthenticated,
        IsAdminUser,
    )
    queryset = User.objects.all()

    @extend_schema(
        description="""
        Update user information including role.
        
        Available roles:
        - member: Individual member user on the Tej platform
        - club_manager: Club Manager on the Tej platform 
        - admin: Has administrative privileges
        """,
        request=UpdateUserSerializer,
        responses={200: UserSerializer, 400: _("Bad Request")},
    )
    @api_error_handler
    @dynamic_rate_limit()
    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @extend_schema(
        description="""
        Partially update user information including role.
        
        Available roles:
        - member: Individual member user on the Tej platform
        - club_manager: Club Manager on the Tej platform 
        - admin: Has administrative privileges
        """,
        request=UpdateUserSerializer,
        responses={200: UserSerializer, 400: _("Bad Request")},
    )
    @api_error_handler
    @dynamic_rate_limit()
    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
