from rest_framework.permissions import <PERSON><PERSON><PERSON><PERSON>icated, IsAdminUser
from drf_spectacular.utils import extend_schema
from rest_framework.generics import (
    ListAPIView,
)
from django.utils.translation import gettext_lazy as _
from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import OpenApiParameter
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from rest_framework.viewsets import GenericViewSet
from rest_framework.decorators import action
from django.http import HttpResponse
import csv
import json
import uuid

from core.abstract.paginations import MetaPageNumberPagination
from apps.accounts.user.serializers.user import (
    UserSerializer,
)
from apps.accounts.user.models import User
from apps.accounts.user.filters import UserFilter, UserExportFilter
from core.ratelimiter import dynamic_rate_limit


@extend_schema(tags=["Admin"])
class ListUserViewSet(GenericViewSet, ListAPIView):
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]
    pagination_class = MetaPageNumberPagination
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_class = UserFilter
    search_fields = [
        "username",
        "email",
        "fullname",
        "role",
    ]
    ordering_fields = ["username", "email", "created", "role"]
    ordering = ["-created"]

    def get_queryset(self):
        return User.objects.all()

    @extend_schema(
        description="List all users with filtering and search options",
        parameters=[
            OpenApiParameter(
                name="search",
                description="Search by username, email, fullname, or role",
                required=False,
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
            ),
            OpenApiParameter(
                name="role",
                description="Filter by user role",
                required=False,
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
            ),
            OpenApiParameter(
                name="format_type",
                description="Export format ('csv' or 'json')",
                required=False,
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
            ),
        ],
        responses={200: UserSerializer(many=True), 400: _("Bad Request")},
    )
    @dynamic_rate_limit()
    def list(self, request, *args, **kwargs):
        format_type = request.query_params.get("format_type")
        if format_type == "csv":
            return self.export_csv(request)
        elif format_type == "json":
            return self.export_json(request)

        return super().list(request, *args, **kwargs)

    def export_csv(self, request):
        """Export user data as CSV"""
        queryset = self.filter_queryset(self.get_queryset())

        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = 'attachment; filename="users.csv"'

        writer = csv.writer(response)
        writer.writerow(
            [
                "ID",
                "Username",
                "Email",
                "Full Name",
                "Role",
                "Active",
                "Staff",
                "Created At",
                "Phone Number",
            ]
        )

        for user in queryset:
            writer.writerow(
                [
                    user.id,
                    user.username,
                    user.email,
                    user.fullname,
                    user.role,
                    user.is_active,
                    user.is_staff,
                    user.created,
                    user.phone_number,
                ]
            )

        return response

    def export_json(self, request):
        """Export user data as JSON"""
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)

        class CustomJSONEncoder(json.JSONEncoder):
            def default(self, obj):
                if isinstance(obj, uuid.UUID):
                    return str(obj)
                elif hasattr(obj, "isoformat"):
                    return obj.isoformat()
                return super().default(obj)

        response = HttpResponse(
            json.dumps(serializer.data, indent=4, cls=CustomJSONEncoder),
            content_type="application/json",
        )
        response["Content-Disposition"] = 'attachment; filename="users.json"'

        return response

    @extend_schema(
        description="Export users data as CSV or JSON",
        parameters=[
            OpenApiParameter(
                name="format",
                description="Export format ('csv' or 'json'), defaults to 'csv'",
                required=False,
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
            ),
            OpenApiParameter(
                name="search",
                description="Search by username, email, fullname, or role",
                required=False,
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
            ),
            OpenApiParameter(
                name="role",
                description="Filter by user role",
                required=False,
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
            ),
            OpenApiParameter(
                name="email",
                description="Filter by email (contains)",
                required=False,
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
            ),
            OpenApiParameter(
                name="email_exact",
                description="Filter by exact email",
                required=False,
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
            ),
            OpenApiParameter(
                name="email_domain",
                description="Filter by email domain (e.g., 'gmail.com')",
                required=False,
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
            ),
            OpenApiParameter(
                name="username",
                description="Filter by username (contains)",
                required=False,
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
            ),
            OpenApiParameter(
                name="is_active",
                description="Filter by active status (true/false)",
                required=False,
                type=OpenApiTypes.BOOL,
                location=OpenApiParameter.QUERY,
            ),
            OpenApiParameter(
                name="created_after",
                description="Filter users created after date (YYYY-MM-DD)",
                required=False,
                type=OpenApiTypes.DATE,
                location=OpenApiParameter.QUERY,
            ),
            OpenApiParameter(
                name="created_before",
                description="Filter users created before date (YYYY-MM-DD)",
                required=False,
                type=OpenApiTypes.DATE,
                location=OpenApiParameter.QUERY,
            ),
            OpenApiParameter(
                name="exclude_staff",
                description="Exclude staff users (true/false)",
                required=False,
                type=OpenApiTypes.BOOL,
                location=OpenApiParameter.QUERY,
            ),
            OpenApiParameter(
                name="exclude_superusers",
                description="Exclude superuser accounts (true/false)",
                required=False,
                type=OpenApiTypes.BOOL,
                location=OpenApiParameter.QUERY,
            ),
            OpenApiParameter(
                name="inactive_only",
                description="Only include inactive users (true/false)",
                required=False,
                type=OpenApiTypes.BOOL,
                location=OpenApiParameter.QUERY,
            ),
            OpenApiParameter(
                name="never_logged_in",
                description="Only include users who never logged in (true/false)",
                required=False,
                type=OpenApiTypes.BOOL,
                location=OpenApiParameter.QUERY,
            ),
            OpenApiParameter(
                name="created_this_month",
                description="Only include users created this month (true/false)",
                required=False,
                type=OpenApiTypes.BOOL,
                location=OpenApiParameter.QUERY,
            ),
            OpenApiParameter(
                name="created_this_year",
                description="Only include users created this year (true/false)",
                required=False,
                type=OpenApiTypes.BOOL,
                location=OpenApiParameter.QUERY,
            ),
        ],
        responses={
            200: {
                "description": "Users data exported successfully",
                "content": {
                    "text/csv": {"schema": {"type": "string", "format": "binary"}},
                    "application/json": {
                        "schema": {"type": "string", "format": "binary"}
                    },
                },
            },
            403: {"description": "Permission denied"},
        },
    )
    @action(
        detail=False, methods=["get"], permission_classes=[IsAuthenticated, IsAdminUser]
    )
    @dynamic_rate_limit()
    def export_users(self, request):
        """Export users data in CSV or JSON format with extensive filtering options"""

        filterset = UserExportFilter(request.query_params, queryset=self.get_queryset())
        filtered_queryset = filterset.qs

        format_type = request.query_params.get("format", "csv").lower()

        if format_type == "json":
            return self._export_json(request, filtered_queryset)
        else:
            return self._export_csv(request, filtered_queryset)

    def _export_csv(self, request, queryset=None):
        """Export user data as CSV with optional filtered queryset"""
        if queryset is None:
            queryset = self.filter_queryset(self.get_queryset())

        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = 'attachment; filename="users.csv"'

        writer = csv.writer(response)
        writer.writerow(
            [
                "ID",
                "Username",
                "Email",
                "Full Name",
                "Role",
                "Active",
                "Staff",
                "Created At",
                "Phone Number",
            ]
        )

        for user in queryset:
            writer.writerow(
                [
                    user.id,
                    user.username,
                    user.email,
                    user.fullname,
                    user.role,
                    user.is_active,
                    user.is_staff,
                    user.created,
                ]
            )

        return response

    def _export_json(self, request, queryset=None):
        """Export user data as JSON with optional filtered queryset"""
        if queryset is None:
            queryset = self.filter_queryset(self.get_queryset())

        serializer = self.get_serializer(queryset, many=True)

        class CustomJSONEncoder(json.JSONEncoder):
            def default(self, obj):
                if isinstance(obj, uuid.UUID):
                    return str(obj)
                elif hasattr(obj, "isoformat"):
                    return obj.isoformat()
                return super().default(obj)

        response = HttpResponse(
            json.dumps(serializer.data, indent=4, cls=CustomJSONEncoder),
            content_type="application/json",
        )
        response["Content-Disposition"] = 'attachment; filename="users.json"'

        return response
