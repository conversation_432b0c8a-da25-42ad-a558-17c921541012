from apps.missions.models import WeeklyMission
from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from django.conf import settings

from core.abstract.serializers import AbstractSerializer
from apps.accounts.user.models import User
from apps.okrs.models import SixWeekPeriod


class ProfileSerializer(AbstractSerializer):
    country_name = serializers.SerializerMethodField()
    has_all_missions = serializers.SerializerMethodField()
    avatar = serializers.SerializerMethodField()

    def get_country_name(self, obj):
        """Return the country name if the user has a country."""
        if obj.country:
            return obj.country.name
        return None

    def get_has_all_missions(self, obj):
        """
        Return True if the user has all six weekly missions created, False otherwise.
        """
        mission_count = WeeklyMission.objects.filter(user=obj).count()
        return mission_count >= 6

    def get_avatar(self, obj):
        """
        Return the absolute URL for the user's avatar.
        Falls back to relative URL if request context is not available.
        """
        if not obj.avatar:
            return None

        try:

            request = self.context.get("request")
            if request:
                return request.build_absolute_uri(obj.avatar.url)
        except (AttributeError, ValueError) as e:

            pass

        try:

            if hasattr(settings, "SITE_URL") and settings.SITE_URL:
                return f"{settings.SITE_URL}{obj.avatar.url}"
            else:

                return obj.avatar.url
        except (AttributeError, ValueError):

            return None

    class Meta:
        model = User
        fields = [
            "_id",
            "id",
            "username",
            "avatar",
            "email",
            "is_superuser",
            "created",
            "updated",
            "fullname",
            "phone_number",
            "location",
            "role",
            "company_information",
            "company_name",
            "account_type",
            "country",
            "country_name",
            "google_calendar_synced",
            "has_all_missions",
        ]
        read_only_fields = [
            "id",
            "created",
            "updated",
            "username",
            "email",
            "is_superuser",
            "role",
        ]

    def to_representation(self, instance):
        """
        Customize the representation to ensure consistent data format.
        """
        data = super().to_representation(instance)

        if "avatar" not in data:
            data["avatar"] = self.get_avatar(instance)

        return data
