from rest_framework.serializers import ValidationError
from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from apps.accounts.user.models import User
from apps.accounts.profile.serializers.profile import ProfileSerializer


class UpdateProfileSerializer(serializers.ModelSerializer):
    """Serializer for updating user profile with avatar support."""

    avatar = serializers.ImageField(required=False, write_only=True)
    country_name = serializers.CharField(read_only=True, source="country.name")

    class Meta:
        model = User
        fields = [
            "id",
            "avatar",
            "fullname",
            "phone_number",
            "location",
            "company_information",
            "company_name",
            "account_type",
            "country",
            "country_name",
        ]
        read_only_fields = ["id", "country_name"]

    def validate_avatar(self, value):
        """Validate avatar file size and format."""
        if value:

            if value.size > 1024 * 1024:
                raise ValidationError(_("File size too large. Maximum size is 1MB."))

            allowed_formats = ["image/jpeg", "image/jpg", "image/png", "image/gif"]
            if (
                hasattr(value, "content_type")
                and value.content_type not in allowed_formats
            ):
                raise ValidationError(
                    _("Unsupported file format. Please use JPEG, PNG, or GIF.")
                )

        return value

    def validate_fullname(self, value):
        """Validate fullname is not empty."""
        if value is not None and value.strip() == "":
            raise ValidationError(_("Fullname cannot be empty."))
        return value

    def validate(self, attrs):
        """Validate the entire set of attributes."""

        if hasattr(self, "initial_data"):
            submitted_fields = set(self.initial_data.keys())
            allowed_fields = {
                "avatar",
                "fullname",
                "phone_number",
                "location",
                "company_information",
                "company_name",
                "account_type",
                "country",
            }

            disallowed_fields = submitted_fields - allowed_fields
            if disallowed_fields:
                errors = {}
                for field in disallowed_fields:
                    errors[field] = [_("This field is not allowed for update.")]
                raise ValidationError(errors)

        return attrs

    def update(self, instance, validated_data):
        """Update the user instance with validated data."""

        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        instance.save()
        return instance

    def to_representation(self, instance):
        """Return the updated profile using ProfileSerializer format."""

        return ProfileSerializer(instance, context=self.context).data
