from django.test import TestCase
from rest_framework import status
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model
from core.utilities import tprint
from django.urls import reverse

from apps.accounts.profile.tests.constants import (
    GET_ME_URL,
    UPDATE_ME_URL,
    USER_EMAIL,
    USER_PASSWORD,
    NEW_USERNAME,
    NEW_FULLNAME,
    NEW_ORGANIZATION,
    PROFILE_PICTURE,
)

User = get_user_model()


class ProfileEndpointsTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create(
            email=USER_EMAIL,
            password=USER_PASSWORD,
            username="testuser",
            fullname="Test User",
        )
        self.user.set_password(USER_PASSWORD)
        self.user.save()
        self.client.force_authenticate(user=self.user)

    def test_get_me_success(self):
        response = self.client.get(reverse("get-me"))
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_update_profile_success(self):
        data = {"fullname": NEW_FULLNAME, "location": "New Location"}
        response = self.client.put(UPDATE_ME_URL, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.user.refresh_from_db()
        self.assertEqual(self.user.username, "testuser")
        self.assertEqual(self.user.fullname, NEW_FULLNAME)
        self.assertEqual(self.user.location, "New Location")

    def test_partial_update_profile_success(self):
        data = {"fullname": "Updated User"}
        response = self.client.patch(reverse("update-me"), data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.user.refresh_from_db()
        self.assertEqual(self.user.fullname, "Updated User")
