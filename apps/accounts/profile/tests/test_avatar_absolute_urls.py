import tempfile
import os
from django.test import TestCase, override_settings
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from rest_framework.test import APIClient, APIRequestFactory
from rest_framework import status
from PIL import Image
import io

from apps.accounts.user.models import User
from apps.accounts.profile.serializers.profile import ProfileSerializer


class AvatarAbsoluteURLTest(TestCase):
    """Test cases specifically for avatar absolute URL handling."""

    def setUp(self):
        """Set up test data."""
        self.factory = APIRequestFactory()
        self.client = APIClient()

        self.user = User.objects.create_user(
            email="<EMAIL>",
            username="testuser",
            password="testpass123",
            fullname="Test User",
        )

        self.client.force_authenticate(user=self.user)

    def create_test_image(self):
        """Create a small test image."""
        image = Image.new("RGB", (50, 50), color="blue")
        image_file = io.BytesIO()
        image.save(image_file, format="JPEG")
        image_file.seek(0)
        return SimpleUploadedFile(
            "test_avatar.jpg", image_file.getvalue(), content_type="image/jpeg"
        )

    def test_avatar_absolute_url_with_request_context(self):
        """Test that avatar URLs are absolute when request context is provided."""

        avatar = self.create_test_image()
        self.user.avatar = avatar
        self.user.save()

        request = self.factory.get("/api/v1/profile/me/")
        request.user = self.user

        serializer = ProfileSerializer(self.user, context={"request": request})
        data = serializer.data

        self.assertIsNotNone(data["avatar"])
        self.assertTrue(data["avatar"].startswith("http://"))
        self.assertIn("test_avatar", data["avatar"])

    def test_profile_endpoint_returns_absolute_avatar_url(self):
        """Test that the profile endpoint returns absolute avatar URLs."""

        avatar = self.create_test_image()
        self.user.avatar = avatar
        self.user.save()

        url = reverse("get-me")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertIsNotNone(data["avatar"])
        self.assertTrue(data["avatar"].startswith("http://testserver"))
        self.assertIn("/media/", data["avatar"])

    def test_profile_no_avatar(self):
        """Test profile serialization when user has no avatar."""
        request = self.factory.get("/api/v1/profile/me/")
        request.user = self.user

        serializer = ProfileSerializer(self.user, context={"request": request})
        data = serializer.data

        self.assertIsNone(data["avatar"])

    def test_profile_endpoint_no_avatar(self):
        """Test profile endpoint when user has no avatar."""
        url = reverse("get-me")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertIsNone(data["avatar"])

    @override_settings(MEDIA_ROOT=tempfile.mkdtemp())
    def tearDown(self):
        """Clean up test files."""
        if self.user.avatar:
            try:
                if hasattr(self.user.avatar, "path"):
                    os.remove(self.user.avatar.path)
            except (OSError, FileNotFoundError):
                pass
