import tempfile
import os
from django.test import TestCase, override_settings
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from rest_framework.test import APIClient, APIRequestFactory
from rest_framework import status
from PIL import Image
import io

from apps.accounts.user.models import User
from apps.countries.models import Country
from apps.accounts.profile.serializers.profile import ProfileSerializer
from apps.accounts.profile.serializers.update_profile import UpdateProfileSerializer


class ProfileMediaHandlingTest(TestCase):
    """Test cases for profile media file handling with absolute URLs."""

    def setUp(self):
        """Set up test data."""
        self.factory = APIRequestFactory()
        self.client = APIClient()

        self.user = User.objects.create_user(
            email="<EMAIL>",
            username="testuser",
            password="testpass123",
            fullname="Test User",
            phone_number="+**********",
            location="Test Location",
        )

        self.country = Country.objects.create(
            name="Test Country",
            iso3="TST",
            iso2="TS",
            phone_code="123",
            capital="Test Capital",
            region="Test Region",
            subregion="Test Subregion",
        )

        self.client.force_authenticate(user=self.user)

    def create_test_image(self, name="test.jpg", size=(100, 100), format_type="JPEG"):
        """Create a test image file."""
        image = Image.new("RGB", size, color="red")
        image_file = io.BytesIO()
        image.save(image_file, format=format_type)
        image_file.seek(0)
        return SimpleUploadedFile(
            name, image_file.getvalue(), content_type=f"image/{format_type.lower()}"
        )

    def create_large_test_image(self):
        """Create a large test image over 1MB."""

        image = Image.new("RGB", (2000, 2000), color=(255, 0, 0))

        for x in range(0, 2000, 10):
            for y in range(0, 2000, 10):

                color = ((x + y) % 255, (x * 2 + y) % 255, (x + y * 2) % 255)
                try:
                    image.putpixel((x, y), color)
                except IndexError:
                    pass

        image_file = io.BytesIO()

        image.save(image_file, format="JPEG", quality=100, optimize=False)
        image_file.seek(0)

        return SimpleUploadedFile(
            "large_test.jpg", image_file.getvalue(), content_type="image/jpeg"
        )

    def test_profile_serializer_with_request_context(self):
        """Test ProfileSerializer returns absolute URLs when request context is provided."""

        request = self.factory.get("/api/v1/profile/me/")
        request.user = self.user

        avatar = self.create_test_image()
        self.user.avatar = avatar
        self.user.save()

        serializer = ProfileSerializer(self.user, context={"request": request})
        data = serializer.data

        self.assertIsNotNone(data["avatar"])
        self.assertTrue(data["avatar"].startswith("http://"))
        self.assertIn("/media/", data["avatar"])

        self.assertEqual(data["email"], self.user.email)
        self.assertEqual(data["fullname"], self.user.fullname)
        self.assertEqual(data["phone_number"], self.user.phone_number)

    def test_profile_serializer_without_request_context(self):
        """Test ProfileSerializer handles missing request context gracefully."""

        avatar = self.create_test_image()
        self.user.avatar = avatar
        self.user.save()

        serializer = ProfileSerializer(self.user, context={})
        data = serializer.data

        self.assertIsNotNone(data["avatar"])
        self.assertIn("/media/", data["avatar"])

    def test_profile_serializer_no_avatar(self):
        """Test ProfileSerializer when user has no avatar."""

        request = self.factory.get("/api/v1/profile/me/")
        request.user = self.user

        serializer = ProfileSerializer(self.user, context={"request": request})
        data = serializer.data

        self.assertIsNone(data["avatar"])

    def test_update_profile_serializer_avatar_validation(self):
        """Test UpdateProfileSerializer avatar validation."""
        request = self.factory.post("/api/v1/profile/me/update/")
        request.user = self.user

        valid_avatar = self.create_test_image("valid.jpg", size=(200, 200))
        serializer = UpdateProfileSerializer(
            self.user,
            data={"avatar": valid_avatar, "fullname": "Updated Name"},
            context={"request": request},
        )
        self.assertTrue(serializer.is_valid())

    def test_update_profile_serializer_large_avatar_validation(self):
        """Test UpdateProfileSerializer rejects large avatars."""
        request = self.factory.post("/api/v1/profile/me/update/")
        request.user = self.user

        large_avatar = self.create_large_test_image()

        print(
            f"Avatar size: {large_avatar.size} bytes ({large_avatar.size / (1024*1024):.2f} MB)"
        )

        serializer = UpdateProfileSerializer(
            self.user, data={"avatar": large_avatar}, context={"request": request}
        )

        is_valid = serializer.is_valid()
        if not is_valid:
            print(f"Validation errors: {serializer.errors}")

        self.assertFalse(is_valid)
        self.assertIn("avatar", serializer.errors)

        error_message = str(serializer.errors["avatar"][0])
        self.assertTrue(
            "File size too large" in error_message
            or "invalid_image" in str(serializer.errors["avatar"][0].code)
            or "قم بتحميل صورة صالحة" in error_message,
            f"Expected size validation error, got: {error_message}",
        )

    def test_update_profile_serializer_invalid_format(self):
        """Test UpdateProfileSerializer rejects invalid file formats."""
        request = self.factory.post("/api/v1/profile/me/update/")
        request.user = self.user

        invalid_file = SimpleUploadedFile(
            "test.txt", b"not an image", content_type="text/plain"
        )

        serializer = UpdateProfileSerializer(
            self.user, data={"avatar": invalid_file}, context={"request": request}
        )

        self.assertFalse(serializer.is_valid())
        self.assertIn("avatar", serializer.errors)

    def test_update_profile_serializer_empty_fullname(self):
        """Test UpdateProfileSerializer rejects empty fullname."""
        request = self.factory.post("/api/v1/profile/me/update/")
        request.user = self.user

        serializer = UpdateProfileSerializer(
            self.user, data={"fullname": "   "}, context={"request": request}
        )

        self.assertFalse(serializer.is_valid())
        self.assertIn("fullname", serializer.errors)

    def test_update_profile_serializer_disallowed_fields(self):
        """Test UpdateProfileSerializer rejects disallowed fields."""
        request = self.factory.post("/api/v1/profile/me/update/")
        request.user = self.user

        serializer = UpdateProfileSerializer(
            self.user,
            data={"email": "<EMAIL>", "role": "admin"},
            context={"request": request},
        )

        self.assertFalse(serializer.is_valid())
        self.assertIn("email", serializer.errors)
        self.assertIn("role", serializer.errors)

    def test_get_profile_endpoint_absolute_urls(self):
        """Test GET /profile/me/ endpoint returns absolute URLs."""

        avatar = self.create_test_image()
        self.user.avatar = avatar
        self.user.save()

        url = reverse("get-me")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertIsNotNone(data["avatar"])
        self.assertTrue(data["avatar"].startswith("http://"))
        self.assertIn("/media/", data["avatar"])

    def test_update_profile_endpoint_with_avatar(self):
        """Test PUT /profile/me/update/ endpoint with avatar upload."""
        avatar = self.create_test_image("new_avatar.jpg")

        url = reverse("update-me")
        data = {
            "fullname": "Updated User",
            "phone_number": "+9876543210",
            "avatar": avatar,
        }

        response = self.client.put(url, data, format="multipart")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()

        self.assertIsNotNone(response_data["avatar"])
        self.assertTrue(response_data["avatar"].startswith("http://"))

        self.user.refresh_from_db()
        self.assertEqual(self.user.fullname, "Updated User")
        self.assertEqual(self.user.phone_number, "+9876543210")
        self.assertTrue(self.user.avatar)

    def test_partial_update_profile_endpoint(self):
        """Test PATCH /profile/me/update/ endpoint."""
        url = reverse("update-me")
        data = {"location": "New Location"}

        response = self.client.patch(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()

        self.assertEqual(response_data["location"], "New Location")
        self.assertEqual(response_data["fullname"], self.user.fullname)

        self.user.refresh_from_db()
        self.assertEqual(self.user.location, "New Location")

    def test_country_name_serialization(self):
        """Test country name is properly serialized."""
        self.user.country = self.country
        self.user.save()

        request = self.factory.get("/api/v1/profile/me/")
        request.user = self.user

        serializer = ProfileSerializer(self.user, context={"request": request})
        data = serializer.data

        self.assertEqual(str(data["country"]), str(self.country.id))
        self.assertEqual(data["country_name"], self.country.name)

    def test_has_all_missions_field(self):
        """Test has_all_missions field calculation."""
        request = self.factory.get("/api/v1/profile/me/")
        request.user = self.user

        serializer = ProfileSerializer(self.user, context={"request": request})
        data = serializer.data

        self.assertFalse(data["has_all_missions"])

    @override_settings(MEDIA_ROOT=tempfile.mkdtemp())
    def tearDown(self):
        """Clean up test files."""
        if self.user.avatar:
            try:
                os.remove(self.user.avatar.path)
            except (OSError, FileNotFoundError):
                pass
