from django.test import TestCase
from apps.accounts.user.models import User
from apps.countries.models import Country

from apps.accounts.profile.serializers.profile import ProfileSerializer
from apps.accounts.profile.serializers.update_profile import UpdateProfileSerializer


class ProfileSerializerTest(TestCase):
    def setUp(self):
        self.user_data = {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "username": "testuser",
            "fullname": "Test User",
            "phone_number": "**********",
            "location": "Test Location",
            "company_information": "Test Company",
            "role": "applicant",
        }
        self.user = User.objects.create_user(**self.user_data)
        self.country = Country.objects.create(
            name="Test Country",
            iso3="TST",
            iso2="TS",
            phone_code="123",
            capital="Test Capital",
            region="Test Region",
            subregion="Test Subregion",
        )

    def test_profile_serializer(self):
        serializer = ProfileSerializer(self.user)
        data = serializer.data

        self.assertEqual(data["email"], self.user_data["email"])
        self.assertEqual(data["username"], self.user_data["username"])
        self.assertEqual(data["fullname"], self.user_data["fullname"])
        self.assertEqual(data["phone_number"], self.user_data["phone_number"])
        self.assertEqual(data["location"], self.user_data["location"])
        self.assertEqual(
            data["company_information"], self.user_data["company_information"]
        )
        self.assertEqual(data["role"], self.user_data["role"])
        self.assertIsNone(data["country"])
        self.assertIsNone(data["country_name"])

    def test_profile_serializer_with_country(self):
        self.user.country = self.country
        self.user.save()

        serializer = ProfileSerializer(self.user)
        data = serializer.data

        self.assertEqual(str(data["country"]), str(self.country.id))
        self.assertEqual(data["country_name"], self.country.name)

    def test_update_profile_serializer(self):
        data = {
            "fullname": "Updated User",
            "phone_number": "5555555555",
            "location": "Updated Location",
            "company_information": "Updated Company",
            "country": self.country.id,
        }

        serializer = UpdateProfileSerializer(self.user, data=data, partial=True)
        self.assertTrue(serializer.is_valid())

        user = serializer.save()
        self.assertEqual(user.fullname, data["fullname"])
        self.assertEqual(user.phone_number, data["phone_number"])
        self.assertEqual(user.location, data["location"])
        self.assertEqual(user.company_information, data["company_information"])
        self.assertEqual(user.country, self.country)
