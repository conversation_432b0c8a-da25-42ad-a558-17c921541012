from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema
from django.utils.translation import gettext_lazy as _

from core.decorators.error_handler import api_error_handler
from apps.accounts.profile.serializers.profile import (
    ProfileSerializer,
)
from apps.accounts.profile.serializers.update_profile import UpdateProfileSerializer


@extend_schema(tags=["Profile"])
class UpdateMyProfileViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = UpdateProfileSerializer

    @extend_schema(
        description="Update the current user profile",
        request=UpdateProfileSerializer,
        responses={200: ProfileSerializer, 400: _("Bad Request")},
    )
    @api_error_handler
    def update(self, request: Request, *args, **kwargs):
        """
        Update the current user profile.
        """
        serializer = UpdateProfileSerializer(
            request.user, data=request.data, context={"request": request}
        )
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @extend_schema(
        description="Partially update the current user profile",
        request=UpdateProfileSerializer,
        responses={200: ProfileSerializer, 400: _("Bad Request")},
    )
    @api_error_handler
    def partial_update(self, request: Request, *args, **kwargs):
        """
        Partially update the current user profile.
        """
        serializer = self.serializer_class(
            request.user,
            data=request.data,
            partial=True,
            context={"request": request},
        )
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
