from rest_framework import viewsets, status
from rest_framework.generics import DestroyAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema
from django.utils.translation import gettext_lazy as _

from core.decorators.error_handler import api_error_handler
from apps.accounts.profile.serializers.profile import (
    ProfileSerializer,
)


@extend_schema(tags=["Profile"])
class DeleteMyProfilePicView(DestroyAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = ProfileSerializer

    @extend_schema(
        description="Delete the current user profile picture",
        responses={204: _("No Content"), 400: _("Bad Request")},
    )
    @api_error_handler
    def delete(self, request: Request, *args, **kwargs):
        """
        Delete the current user profile picture.
        """
        user = request.user
        user.avatar.delete()
        user.avatar_url = None
        user.save()
        return Response(status=status.HTTP_204_NO_CONTENT)
