from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema
from django.utils.translation import gettext_lazy as _
from rest_framework.decorators import action

from core.decorators.error_handler import api_error_handler
from apps.accounts.profile.serializers.profile import (
    ProfileSerializer,
)
from apps.accounts.profile.serializers.update_profile import UpdateProfileSerializer


@extend_schema(tags=["Profile"])
class ProfileViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]

    @extend_schema(
        description="Get current user profile", responses={200: ProfileSerializer}
    )
    def retrieve(self, request):
        """Retrieve the current user's profile with absolute media URLs."""
        serializer = ProfileSerializer(request.user, context={"request": request})
        return Response(serializer.data)

    @extend_schema(
        description="Update current user profile",
        request=UpdateProfileSerializer,
        responses={200: ProfileSerializer, 400: _("Bad Request")},
    )
    @api_error_handler
    def update(self, request):
        """Update the current user's profile completely."""
        serializer = UpdateProfileSerializer(
            request.user, data=request.data, partial=False, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        updated_user = serializer.save()

        response_serializer = ProfileSerializer(
            updated_user, context={"request": request}
        )
        return Response(response_serializer.data)

    @extend_schema(
        description="Partially update current user profile",
        request=UpdateProfileSerializer,
        responses={200: ProfileSerializer, 400: _("Bad Request")},
    )
    @action(detail=False, methods=["patch"])
    @api_error_handler
    def partial_update(self, request):
        """Partially update the current user's profile."""
        serializer = UpdateProfileSerializer(
            request.user, data=request.data, partial=True, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        updated_user = serializer.save()

        response_serializer = ProfileSerializer(
            updated_user, context={"request": request}
        )
        return Response(response_serializer.data)
