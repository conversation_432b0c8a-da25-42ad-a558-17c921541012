from django.urls import path


from apps.accounts.profile.views.profile import ProfileViewSet
from apps.accounts.profile.views.delete_my_profile_pic import DeleteMyProfilePicView
from apps.accounts.profile.views.update_profile import UpdateMyProfileViewSet

urlpatterns = [
    path("me/", ProfileViewSet.as_view({"get": "retrieve"}), name="get-me"),
    path(
        "me/update/",
        ProfileViewSet.as_view({"put": "update", "patch": "partial_update"}),
        name="update-me",
    ),
    path(
        "me/delete-profile-pic/",
        DeleteMyProfilePicView.as_view(),
        name="delete-my-profile-pic",
    ),
]
