from rest_framework.response import Response
from rest_framework_simplejwt.views import TokenRefreshView
from rest_framework.permissions import AllowAny
from rest_framework import status
from rest_framework import serializers
from rest_framework import viewsets
from drf_spectacular.utils import extend_schema
from rest_framework_simplejwt.exceptions import TokenError, InvalidToken
from rest_framework.exceptions import APIException
from django.utils.translation import gettext as _
from django_ratelimit.decorators import ratelimit
from django.utils.decorators import method_decorator

from core.decorators.error_handler import api_error_handler


class ValidationError400(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = _("Invalid input.")
    default_code = "invalid"


class ValidationError404(APIException):
    status_code = status.HTTP_404_NOT_FOUND
    default_detail = _("Invalid input.")
    default_code = "invalid"


class RefreshViewSet(viewsets.ViewSet, TokenRefreshView):
    permission_classes = (AllowAny,)
    http_method_names = ["post"]

    @api_error_handler
    @extend_schema(
        tags=["auth"],
        responses={
            200: {"description": _("Token refresh successful.")},
            401: {"description": _("Token is invalid or expired")},
        },
    )
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except TokenError as e:
            raise ValidationError400(str(e.args[0]))

        return Response(serializer.validated_data, status=status.HTTP_200_OK)
