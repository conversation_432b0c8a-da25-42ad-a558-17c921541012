from rest_framework_simplejwt.tokens import RefreshToken, TokenError
from rest_framework import viewsets, status
from rest_framework.exceptions import ValidationError
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.utils.translation import gettext_lazy as _
from drf_spectacular.utils import extend_schema
from django_ratelimit.decorators import ratelimit
from django.utils.decorators import method_decorator

from apps.accounts.auth.serializers.logout import LogoutSerializer
from core.decorators.error_handler import api_error_handler


class LogoutViewSet(viewsets.ViewSet):
    permission_classes = (IsAuthenticated,)
    http_method_names = ["post"]
    serializer_class = LogoutSerializer

    @api_error_handler
    @extend_schema(
        tags=["auth"],
        request=LogoutSerializer,
        responses={204: {"description": "Logout successful."}},
    )
    def create(self, request, *args, **kwargs):
        refresh = request.data.get("refresh")
        if refresh is None:
            raise ValidationError({"error": _("A refresh token is required.")})

        try:
            token = RefreshToken(request.data.get("refresh"))
            token.blacklist()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except TokenError:
            raise ValidationError({"error": _("The refresh token is invalid.")})
