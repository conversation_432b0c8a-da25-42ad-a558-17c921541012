import os
import logging
from rest_framework.viewsets import ViewSet
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from django.utils.translation import gettext_lazy as _
from drf_spectacular.utils import extend_schema
from django.template.exceptions import TemplateDoesNotExist
from django_ratelimit.decorators import ratelimit
from django.utils.decorators import method_decorator

from core.decorators.error_handler import api_error_handler
from apps.accounts.auth.serializers import (
    ChangePasswordSerializer,
)
from apps.accounts.auth.models import User

UI_BASE_URL = os.environ.get("UI_BASE_URL", "http://localhost:3000")
logger = logging.getLogger(__name__)


class ChangePasswordViewSet(ViewSet):
    permission_classes = (IsAuthenticated,)
    http_method_names = ["post"]
    serializer_class = ChangePasswordSerializer

    @api_error_handler
    @extend_schema(
        tags=["auth"],
        request=ChangePasswordSerializer,
        responses={200: {"description": "Password has been changed successfully."}},
    )
    def create(self, request, *args, **kwargs):
        try:
            serializer = self.serializer_class(
                data=request.data, context={"request": request}
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(
                {"error": _("Password has been changed successfully.")},
                status=status.HTTP_200_OK,
            )
        except TemplateDoesNotExist as e:
            logger.error(e)
            return Response(
                {"error": _("Email template does not exist.")},
                status=status.HTTP_400_BAD_REQUEST,
            )
