from rest_framework.viewsets import ViewSet
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from rest_framework import status
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from drf_spectacular.utils import extend_schema
from django_ratelimit.decorators import ratelimit
from django.utils.decorators import method_decorator
from django.db import transaction

from core.decorators.error_handler import api_error_handler
from ..serializers import SetNewPasswordSerializer

User = get_user_model()


class ResetPasswordViewSet(ViewSet):
    serializer_class = SetNewPasswordSerializer
    permission_classes = (AllowAny,)
    http_method_names = ["post"]

    @api_error_handler
    @method_decorator(ratelimit(key="ip", rate="5/m", method=["POST"]))
    @extend_schema(
        tags=["auth"],
        request=SetNewPasswordSerializer,
        responses={
            200: {"description": "Password has been reset successfully"},
            400: {"description": "Invalid input or expired token"},
            429: {"description": "Too many attempts. Please try again later."},
        },
    )
    def create(self, request, *args, **kwargs):
        """
        Reset user password using a valid token.
        Rate limited to 5 attempts per minute per IP address.
        """
        with transaction.atomic():
            serializer = self.serializer_class(
                data=request.data, context={"request": request}
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()

            return Response(
                {"detail": _("Password has been reset successfully")},
                status=status.HTTP_200_OK,
            )
