from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from rest_framework.decorators import action
from django.core.mail import send_mail
from rest_framework_simplejwt.tokens import RefreshToken
from django.utils.translation import gettext as _
from django.utils import timezone
from drf_spectacular.utils import extend_schema
from django_ratelimit.decorators import ratelimit
from django.utils.decorators import method_decorator
import twilio

from apps.accounts.user.models import User
from apps.accounts.auth.models import OTP
from apps.accounts.auth.serializers import (
    LoginOTPSerializer,
    VerifyOTPSerializer,
)
from apps.accounts.user.serializers.user import UserSerializer

from apps.accounts.auth.utils import generate_otp, twilio_verify_send
from core.decorators.error_handler import api_error_handler
from apps.accounts.auth.utils import send_sms
from core.tasks import send_activation_email


class LoginOTPViewSet(viewsets.ViewSet):
    permission_classes = [AllowAny]

    @extend_schema(
        tags=["auth"],
        request=LoginOTPSerializer,
        responses={200: {"description": "OTP sent successfully."}},
    )
    @action(methods=["post"], detail=False)
    @api_error_handler
    def login_otp(self, request):
        serializer = LoginOTPSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        email = serializer.validated_data["email"]  # type: ignore
        otp_code = generate_otp()

        email = email.lower()
        user = User.objects.filter(email=email).first()
        otp_message = _(f"Your OTP is: {otp_code}")

        send_activation_email(
            _("Login OTP"), otp_message, email, html_content=otp_message
        )
        OTP.objects.create(
            user=user,
            code=otp_code,
            expires_at=timezone.now() + timezone.timedelta(minutes=10),
        )

        return Response(status=status.HTTP_200_OK)

    @extend_schema(
        tags=["auth"],
        request=VerifyOTPSerializer,
        responses={200: {"description": "OTP verified successfully."}},
    )
    @action(methods=["post"], detail=False)
    @api_error_handler
    def verify_otp(self, request):
        serializer = VerifyOTPSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.validated_data["user"]  # type: ignore
        tokens = RefreshToken.for_user(user)
        user_Serializer = UserSerializer(user)

        return Response(
            {
                "refresh": str(tokens),
                "access": str(tokens.access_token),
                "user": user_Serializer.data,
            },
            status=status.HTTP_200_OK,
        )
