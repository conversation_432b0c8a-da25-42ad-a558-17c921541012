import re
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet
from rest_framework.permissions import AllowAny
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import get_user_model
from drf_spectacular.utils import extend_schema
from django.utils.translation import gettext_lazy as _
from django_ratelimit.decorators import ratelimit
from django.utils.decorators import method_decorator

from apps.accounts.auth.serializers import (
    RegisterSerializer,
    EmailVerificationSerializer,
)
from apps.accounts.auth.serializers.register import (
    ResendVerificationCodeSerializer,
)
from apps.accounts.user.models import VerificationCode
from apps.accounts.user.serializers.user import UserSerializer
from core.decorators.error_handler import api_error_handler

User = get_user_model()


class RegisterViewSet(ViewSet):
    permission_classes = (AllowAny,)
    http_method_names = ["post"]

    @extend_schema(
        tags=["auth"],
        request=RegisterSerializer,
        responses={201: RegisterSerializer},
    )
    @api_error_handler
    def create(self, request, *args, **kwargs):
        """
        Create a new user account.

        This endpoint validates the registration data, creates a new user,
        and sends a verification code to the user's email.

        The api_error_handler decorator handles various exceptions including
        validation errors and database integrity errors.
        """

        serializer = RegisterSerializer(data=request.data, context={"request": request})
        serializer.is_valid(raise_exception=True)

        user = serializer.save()

        message = _(
            "Account created successfully. Please check your email for verification code."
        )
        return Response(
            {
                "message": message,
                "user": UserSerializer(user, context={"request": request}).data,
            },
            status=status.HTTP_201_CREATED,
        )


class ValidateEmailViewSet(ViewSet):
    http_method_names = ["post"]

    @extend_schema(
        tags=["auth"],
        request=EmailVerificationSerializer,
        responses={200: RegisterSerializer},
    )
    @api_error_handler
    def create(self, request):
        serializer = EmailVerificationSerializer(data=request.data)
        if serializer.is_valid(raise_exception=True):
            code = request.data.get("code")
            email = request.data.get("email")
            verification_code = VerificationCode.objects.filter(code=code).first()
            if not verification_code:
                return Response(
                    {"error": {"code": _("Invalid verification code")}},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            user = verification_code.user
            verification_code.delete()
            refresh = RefreshToken.for_user(user)
            return Response(
                {
                    "message": _("Account verified successfully"),
                    "user": UserSerializer(user, context={"request": request}).data,
                    "refresh": str(refresh),
                    "access": str(refresh.access_token),
                },
                status=status.HTTP_200_OK,
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ResendVerificationCodeViewSet(ViewSet):
    http_method_names = ["post"]

    @extend_schema(
        tags=["auth"],
        request=ResendVerificationCodeSerializer,
        responses={200: UserSerializer},
    )
    @api_error_handler
    def create(self, request):
        serializer = ResendVerificationCodeSerializer(data=request.data)
        if serializer.is_valid(raise_exception=True):
            user = serializer.save()
            return Response(
                {"message": _("Verification code sent successfully")},
                status=status.HTTP_200_OK,
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
