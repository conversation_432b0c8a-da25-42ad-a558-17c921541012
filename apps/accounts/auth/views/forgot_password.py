import os
from rest_framework.viewsets import ViewSet
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from rest_framework import status
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from drf_spectacular.utils import extend_schema
from django_ratelimit.decorators import ratelimit
from django.utils.decorators import method_decorator

from core.decorators.error_handler import api_error_handler
from apps.accounts.auth.serializers.forgot_password_serializer import (
    ForgotPasswordSerializer,
)


User = get_user_model()
UI_BASE_URL = os.environ.get("UI_BASE_URL", "http://localhost:3000")


class ForgotPasswordViewSet(ViewSet):
    permission_classes = (AllowAny,)
    http_method_names = ["post"]
    serializer_class = ForgotPasswordSerializer

    @api_error_handler
    @extend_schema(
        tags=["auth"],
        request=ForgotPasswordSerializer,
        responses={200: {"description": "Password reset e-mail has been sent."}},
    )
    def create(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(
            {"detail": _("Password reset e-mail has been sent.")},
            status=status.HTTP_200_OK,
        )
