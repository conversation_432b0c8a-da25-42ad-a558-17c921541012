from functools import wraps
import re
import vonage
from rest_framework.response import Response
from rest_framework import status
from django.utils.translation import gettext_lazy as _
from twilio.rest import Client
from typing import List
from django.conf import settings
import traceback
import logging
from apps.accounts.user.models import User
import random
from twilio.base.exceptions import TwilioRestException


logger = logging.getLogger(__name__)


def generate_otp():
    return "".join(random.choices("**********!@#$%^ASDFGHJJLoiuytreqzxcvnn", k=8))


def user_with_this_email_should_exist(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        request = args[1]
        email = request.data.get("email")
        user = User.objects.filter(email=email).first()
        if not user:
            return Response(
                {"error": {"email": _("User with this email does not exist")}},
                status=status.HTTP_400_BAD_REQUEST,
            )
        return func(*args, **kwargs)

    return wrapper


def send_sms(message: str, numbers: list, testing: bool = False) -> bool:
    try:
        if testing:
            client = Client(settings.TWILIO_TEST_SID, settings.TWLIO_TEST_AUTH_TOKEN)
            for recipient in numbers:
                if recipient:
                    client.messages.create(
                        to=recipient, from_=settings.TWILIO_NUMBER, body=message
                    )
            return True
        else:
            client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)
            for recipient in numbers:
                if recipient:
                    res = client.messages.create(
                        to=recipient,
                        messaging_service_sid=settings.TWILIO_MESSAGING_SID,
                        body=message,
                    )
                    logger.error(res.__dict__)
            return True
    except Exception as e:
        logger.error(f"Error while sending SMS: {str(e)}")
        logger.error(traceback.format_exc())
        return False


def send_sms_vonage(message: str, numbers: List[str], testing: bool = False) -> bool:
    try:
        client = vonage.Client(
            key=settings.VONAGE_API_KEY, secret=settings.VONAGE_API_SECRET
        )
        sms = vonage.Sms(client)
        responseData = sms.send_message(
            {
                "from": settings.VONAGE_FROM,
                "to": numbers[0],
                "text": message,
            }
        )

        if responseData["messages"][0]["status"] == "0":
            logger.info("Message sent successfully.")
            return True
        else:
            logger.error(
                f"Message failed with error: {responseData['messages'][0]['error-text']}"
            )
            return False
    except Exception as e:
        logger.error(f"Error while sending SMS: {str(e)}")
        logger.error(traceback.format_exc())
        return False


def twilio_verify_send(phone: str) -> bool:
    try:
        client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)
        verify = client.verify.v2.services(settings.TWILIO_VERIFY_SERVICE_SID)
        verify.verifications.create(to=phone, channel="sms")
        return True
    except Exception as e:
        logger.error(f"Error while sending SMS: {str(e)}")
        logger.error(traceback.format_exc())
        return False


def twilio_verify_check(phone: str, code: str):
    try:
        client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)
        verify = client.verify.v2.services(settings.TWILIO_VERIFY_SERVICE_SID)
        result = verify.verification_checks.create(to=phone, code=code)
    except TwilioRestException:
        return False
    return result.status == "approved"
