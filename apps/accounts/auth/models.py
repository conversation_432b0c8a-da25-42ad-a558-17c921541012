from django.db import models
from django.utils import timezone
from django.conf import settings
from apps.accounts.user.models import User

EXPIRATION = getattr(settings, "OTP_EXPIRATION_MINS", 10)


def default_expiration():
    return timezone.now() + timezone.timedelta(minutes=EXPIRATION)


class OTP(models.Model):
    user = models.ForeignKey(User, related_name="otps", on_delete=models.CASCADE)
    code = models.CharField(max_length=8)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(default=default_expiration)

    def is_valid(self):
        return timezone.now() < self.expires_at

    def __str__(self):
        return f"{self.user.email} - {self.code}"
