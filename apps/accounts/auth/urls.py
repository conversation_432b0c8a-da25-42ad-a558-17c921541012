from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer

from apps.accounts.auth.views import (
    RegisterViewSet,
    LoginViewSet,
    RefreshViewSet,
    LogoutViewSet,
    ForgotPasswordViewSet,
    ResetPasswordViewSet,
    ValidateEmailViewSet,
    ResendVerificationCodeViewSet,
    LoginOTPViewSet,
    ChangePasswordViewSet,
)

router = DefaultRouter()
router.register(r"", LoginOTPViewSet, basename="auth")


urlpatterns = [
    path(
        "",
        include(
            router.urls,
        ),
    ),
    path(
        "register/",
        RegisterViewSet.as_view({"post": "create"}),
        name="auth-register",
    ),
    path(
        "validate-email/",
        ValidateEmailViewSet.as_view({"post": "create"}),
        name="auth-validate-email",
    ),
    path("login/", LoginViewSet.as_view({"post": "create"}), name="auth-login"),
    path(
        "refresh/",
        RefreshViewSet.as_view({"post": "create"}),
        name="auth-refresh",
    ),
    path(
        "logout/",
        LogoutViewSet.as_view({"post": "create"}),
        name="auth-logout",
    ),
    path(
        "forgot-password/",
        ForgotPasswordViewSet.as_view({"post": "create"}),
        name="auth-forgot-password",
    ),
    path(
        "reset-password/",
        ResetPasswordViewSet.as_view({"post": "create"}),
        name="auth-reset-password",
    ),
    path(
        "resend-verification-code/",
        ResendVerificationCodeViewSet.as_view({"post": "create"}),
        name="auth-resend-verification-code",
    ),
    path(
        "change-password/",
        ChangePasswordViewSet.as_view({"post": "create"}),
        name="auth-change-password",
    ),
]
