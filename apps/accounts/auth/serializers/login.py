from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework_simplejwt.settings import api_settings
from django.contrib.auth.models import update_last_login
from django.utils.translation import gettext as _
from rest_framework import serializers, status
from rest_framework.exceptions import AuthenticationFailed

from apps.accounts.user.serializers.user import UserSerializer
from apps.accounts.user.models import User


class LoginSerializer(TokenObtainPairSerializer):
    email = serializers.EmailField(required=True)
    password = serializers.CharField(required=True)

    def get_user(self, email):
        """Retrieve user by email or phone number."""
        user = User.objects.filter(email=email).first()

        if not user:
            raise AuthenticationFailed(
                _("User does not exist."),
                code=status.HTTP_404_NOT_FOUND,
            )
        return user

    def validate(self, attrs):
        user = self.get_user(attrs.get("email"))
        if not user.check_password(attrs.get("password")):
            raise AuthenticationFailed(
                _("Invalid credentials."), code=status.HTTP_401_UNAUTHORIZED
            )

        request = self.context.get("request")

        if (
            request
            and request.query_params.get("only_admins", "false").lower() == "true"
        ):
            if not user.is_staff and user.role != "admin":
                raise AuthenticationFailed(
                    _("Only admin users are allowed to log in."),
                    code=status.HTTP_403_FORBIDDEN,
                )

        if request and (user_type := request.query_params.get("user_type")):
            if user_type == "admin" and user.role != "admin":
                raise AuthenticationFailed(
                    _("Only admin users are allowed to log in."),
                    code=status.HTTP_403_FORBIDDEN,
                )
            elif user_type == "club_manager" and user.role != "club_manager":
                raise AuthenticationFailed(
                    _("Only club managers are allowed to log in."),
                    code=status.HTTP_403_FORBIDDEN,
                )
            elif user_type == "member" and user.role != "member":
                raise AuthenticationFailed(
                    _("Only members are allowed to log in."),
                    code=status.HTTP_403_FORBIDDEN,
                )

        refresh = self.get_token(user)
        data = {
            "user": UserSerializer(user, context=self.context).data,
            "refresh": str(refresh),
            "access": str(refresh.access_token),
        }

        if api_settings.UPDATE_LAST_LOGIN:
            update_last_login(None, user)

        return data
