from django.template.loader import render_to_string
from django.utils.translation import gettext_lazy as _
from rest_framework import status
from rest_framework import serializers
from rest_framework.exceptions import APIException
from django.contrib.auth import get_user_model
from django.contrib.auth.tokens import default_token_generator
from rest_framework.exceptions import NotFound
from django.conf import settings
import logging

from core.tasks.emails import send_sendgrid_email
from apps.accounts.user.models import UserToken

User = get_user_model()

logger = logging.getLogger(__name__)


class ValidationError400(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = _("Invalid input.")
    default_code = "invalid"


class SetNewPasswordSerializer(serializers.Serializer):
    password = serializers.CharField(
        style={"input_type": "password"}, required=True, min_length=8, write_only=True
    )
    token = serializers.CharField(required=True, write_only=True)

    def validate_password(self, value):
        """
        Validate password complexity.
        NOTE: Basic validation only here. Token validation and complex password rules
        are handled in validate() to ensure proper error precedence.
        """

        return value

    def validate(self, data):
        """
        Validate the password reset token and new password.
        Token validation takes precedence over password complexity validation.
        """
        token = data["token"]

        try:
            user_token = UserToken.objects.get(token=token)
            user = user_token.user
        except UserToken.DoesNotExist:
            raise ValidationError400(_("Invalid or expired token"))

        password = data["password"]
        if len(password) < 8:
            raise ValidationError400(_("Password must be at least 8 characters long"))
        if not any(char.isdigit() for char in password):
            raise ValidationError400(_("Password must contain at least one number"))
        if not any(char.isupper() for char in password):
            raise ValidationError400(
                _("Password must contain at least one uppercase letter")
            )

        if user.check_password(password):
            raise ValidationError400(_("You can't use your current password"))

        self.context["user"] = user
        self.context["user_token"] = user_token

        return data

    def save(self, **kwargs):
        """
        Set the new password and send confirmation email.
        """
        user = self.context["user"]
        user_token = self.context["user_token"]

        user.set_password(self.validated_data["password"])
        user.save()

        user_token.delete()

        subject = _("Password Reset Confirmation")
        text_content = render_to_string(
            "auth/password_reset_confirmation_email.txt", {"user": user}
        )
        html_content = render_to_string(
            "auth/password_reset_confirmation_email.html", {"user": user}
        )

        try:
            send_sendgrid_email(
                to_emails=user.email,
                subject=subject,
                text_content=text_content,
                html_content=html_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
            )
            logger.info(f"Password reset confirmation email sent to {user.email}")
        except Exception as e:

            logger.error(f"Failed to send password reset confirmation email: {str(e)}")

        return user
