from rest_framework import serializers, status
from rest_framework.exceptions import AuthenticationFailed, ValidationError
from django.utils.translation import gettext as _
from django.utils import timezone

from apps.accounts.user.models import User
from apps.accounts.auth.models import OTP


class UserExistsMixin:
    """Mixin to check if a user exists by email or phone number."""

    def validate_email(self, value):
        if not User.objects.filter(email=value).exists():
            raise AuthenticationFailed({"email": _("User does not exist.")})
        return value


class LoginOTPSerializer(UserExistsMixin, serializers.Serializer):
    email = serializers.EmailField(required=True)


class VerifyOTPSerializer(UserExistsMixin, serializers.Serializer):
    email = serializers.EmailField(required=True)
    otp = serializers.CharField(max_length=8)

    def validate(self, data):
        email = data.get("email")
        otp = data.get("otp")
        try:
            user = User.objects.filter(email=email).first()
            if not OTP.objects.filter(
                user=user, code=otp, expires_at__gt=timezone.now()
            ).exists():
                raise ValidationError({"otp": _("Invalid or expired OTP.")})
            OTP.objects.filter(user=user).delete()

        except User.DoesNotExist:
            raise ValidationError({"email": _("User does not exist.")})

        data["user"] = user
        return data
