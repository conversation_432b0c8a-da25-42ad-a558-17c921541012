import os
from django.template.loader import render_to_string
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers, status
from rest_framework.exceptions import AuthenticationFailed
from django.contrib.auth import get_user_model
from django.contrib.auth.tokens import PasswordResetTokenGenerator
from django.conf import settings

from core.tasks import send_activation_email
from apps.accounts.user.models import UserToken
from core.tasks.sms import send_sms_task

User = get_user_model()
UI_BASE_URL = os.environ.get("UI_BASE_URL", "http://localhost:3000")


class ForgotPasswordSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    testing = serializers.BooleanField(required=False, default=False)

    def validate_email(self, value):
        if not User.objects.filter(email=value).exists():
            raise AuthenticationFailed(
                {"email": _("User does not exist.")},
                code=status.HTTP_404_NOT_FOUND,
            )
        return value

    def save(self, **kwargs):
        email = self.validated_data["email"]
        user = User.objects.get(email=email)

        token = PasswordResetTokenGenerator().make_token(user)

        if user.role == "manager":
            reset_link = f"{settings.MANAGER_RESET_PASSWORD_URL}{token}"
        else:
            reset_link = f"{settings.MEMBER_RESET_PASSWORD_URL}{token}"

        UserToken.objects.get_or_create(user=user, token=token)
        text_content = _(f"Click the link below to reset your password:\n{reset_link}")
        html_content = render_to_string(
            "forgot_password_email.html", {"reset_link": reset_link}
        )
        if "@" in email:
            send_activation_email(
                _("Password Reset Request"),
                text_content,
                email,
                html_content=html_content,
            )
        else:
            send_sms_task.delay(text_content, [email])
