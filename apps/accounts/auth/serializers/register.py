from apps.accounts.user.serializers.user import UserSerializer
from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.template.loader import render_to_string
from django.db import models, IntegrityError
from django.apps import apps
from apps.accounts.user.models import User, VerificationCode
from core.tasks import send_activation_email


Country = apps.get_model("countries", "Country")


class RegisterSerializer(serializers.ModelSerializer):
    ACCOUNT_TYPE_CHOICES = [
        ("free", "Free"),
        ("paid", "Paid"),
    ]

    ROLE_CHOICES = [
        ("admin", "Admin"),
        ("club_manager", "Club Manager"),
        ("member", "Member"),
    ]

    password = serializers.CharField(
        write_only=True, required=True, min_length=8, max_length=128
    )
    email = serializers.EmailField(required=True)
    phone_number = serializers.CharField(required=False, allow_blank=True)
    location = serializers.Char<PERSON>ield(required=False, allow_blank=True)
    city = serializers.CharField(required=False, allow_blank=True)
    company_information = serializers.CharField(required=False, allow_blank=True)
    account_type = serializers.ChoiceField(
        choices=ACCOUNT_TYPE_CHOICES, default="free", required=False
    )
    role = serializers.ChoiceField(
        choices=ROLE_CHOICES, default="member", required=False
    )
    country = serializers.PrimaryKeyRelatedField(
        queryset=Country.objects.all(), required=False, allow_null=True
    )

    class Meta:
        model = User
        fields = [
            "id",
            "email",
            "fullname",
            "password",
            "phone_number",
            "location",
            "city",
            "company_information",
            "account_type",
            "role",
            "country",
        ]

    def validate(self, data):
        """
        Validate the registration data.

        Checks for uniqueness of email, phone_number, and potential username conflicts.
        """

        email = data.pop("email", None)
        if not email:
            raise serializers.ValidationError({"email": _("Email is required.")})

        email = email.lower()

        if User.objects.filter(email__iexact=email).exists():
            raise serializers.ValidationError(
                {"email": _("User with this email already exists.")}
            )
        data["email"] = email

        phone_number = data.get("phone_number")
        if phone_number and User.objects.filter(phone_number=phone_number).exists():
            raise serializers.ValidationError(
                {"phone_number": _("User with this phone number already exists.")}
            )

        fullname = data.get("fullname")
        potential_username = None

        if fullname:
            potential_username = fullname.strip()
        else:
            potential_username = email

        if (
            potential_username
            and User.objects.filter(username=potential_username).exists()
        ):
            raise serializers.ValidationError(
                {
                    "fullname": _(
                        "A user with this name already exists. Please use a different name."
                    )
                }
            )

        return data

    def create(self, validated_data):
        """
        Create a new user with the validated data.

        This method creates a user, generates a verification code, and sends an activation email.
        It includes additional error handling for potential database integrity errors.
        """
        try:

            user = User.objects.create_user(**validated_data)

            verification_code = VerificationCode.objects.create(
                user=user, code=VerificationCode.generate_code()
            )

            text_content = _(f"Your verification code is: {verification_code.code}")
            html_content = render_to_string(
                "auth/emails/verification_code.html", {"code": verification_code.code}
            )

            send_activation_email.delay(
                subject=_("Verify Your Email"),
                text_content=text_content,
                html_content=html_content,
                to_email=user.email,
            )

            return user

        except IntegrityError as e:

            import logging

            logger = logging.getLogger(__name__)
            logger.error(f"IntegrityError during user creation: {str(e)}")

            raise

    def to_representation(self, instance):
        rep = super().to_representation(instance)
        try:
            rep["avatar"] = (
                self.context["request"].build_absolute_uri(instance.avatar.url)
                if instance.avatar
                else None
            )
        except (AttributeError, ValueError, KeyError):
            rep["avatar"] = None
        return rep


class EmailVerificationSerializer(serializers.Serializer):
    code = serializers.CharField(max_length=6, min_length=6)
    email = serializers.EmailField()

    def validate(self, data):
        code = data.get("code")
        try:
            verification_code = VerificationCode.objects.get(code=code)
            user = verification_code.user
            user.is_email_verified = True
            user.save()
            return data
        except User.DoesNotExist:
            raise serializers.ValidationError({"email": _("User does not exist")})
        except VerificationCode.DoesNotExist:
            raise serializers.ValidationError({"code": _("Invalid verification code")})


class ResendVerificationCodeSerializer(serializers.Serializer):
    email = serializers.EmailField()

    def create(self, validated_data):
        email = validated_data.pop("email")
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            raise serializers.ValidationError({"email": _("User does not exist.")})

        verification_code = VerificationCode.objects.filter(user=user).first()
        if not verification_code:
            verification_code = VerificationCode.objects.create(
                user=user, code=VerificationCode.generate_code()
            )

        text_content = _(f"Your verification code is: {verification_code.code}")
        html_content = render_to_string(
            "auth/emails/verification_code.html", {"code": verification_code.code}
        )

        send_activation_email.delay(
            subject=_("Verify Your Email"),
            text_content=text_content,
            html_content=html_content,
            to_email=user.email,
        )

        return user
