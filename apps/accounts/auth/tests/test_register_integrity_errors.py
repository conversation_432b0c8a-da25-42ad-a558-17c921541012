import pytest
import json
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model
from django.apps import apps

User = get_user_model()
Country = apps.get_model("countries", "Country")

REGISTER_URL = "/api/v1/auth/register/"


@pytest.mark.django_db
class TestRegisterIntegrityErrors:
    """Test registration endpoint with various integrity error cases."""

    def setup_method(self):
        """Set up test data."""
        self.client = APIClient()

        self.test_country = Country.objects.create(
            name="Test Country", iso2="TC", iso3="TCY"
        )

        self.existing_user = User.objects.create_user(
            email="<EMAIL>",
            password="password123",
            fullname="Existing User",
            username="existinguser",
            phone_number="1234567890",
        )

    def test_register_with_duplicate_email(self):
        """Test registration with an email that already exists."""
        data = {
            "email": "<EMAIL>",
            "password": "newpassword123",
            "fullname": "New User",
            "country": self.test_country.id,
        }

        response = self.client.post(REGISTER_URL, data)

        assert response.status_code == status.HTTP_400_BAD_REQUEST

        response_data = json.loads(response.content)

        assert "error" in response_data
        assert "email" in response_data["error"]

        assert response_data["error"]["email"]

        assert User.objects.count() == 1

    def test_register_with_duplicate_phone_number(self):
        """Test registration with a phone number that already exists."""
        data = {
            "email": "<EMAIL>",
            "password": "password123",
            "fullname": "New User",
            "phone_number": "1234567890",
            "country": self.test_country.id,
        }

        response = self.client.post(REGISTER_URL, data)

        assert response.status_code == status.HTTP_400_BAD_REQUEST

        response_data = json.loads(response.content)

        assert "error" in response_data
        assert "phone_number" in response_data["error"]

        assert response_data["error"]["phone_number"]

    def test_register_with_duplicate_username(self):
        """Test registration with a fullname that would generate a duplicate username."""

        User.objects.create_user(
            email="<EMAIL>",
            password="password123",
            fullname="Different Name",
            username="specificusername",
        )

        data = {
            "email": "<EMAIL>",
            "password": "password123",
            "fullname": "specificusername",
            "country": self.test_country.id,
        }

        response = self.client.post(REGISTER_URL, data)

        assert response.status_code == status.HTTP_400_BAD_REQUEST

        response_data = json.loads(response.content)

        assert "error" in response_data
        assert (
            "fullname" in response_data["error"] or "username" in response_data["error"]
        )

    def test_register_with_empty_email(self):
        """Test registration with an empty email."""
        data = {
            "email": "",
            "password": "password123",
            "fullname": "New User",
            "country": self.test_country.id,
        }

        response = self.client.post(REGISTER_URL, data)

        assert response.status_code == status.HTTP_400_BAD_REQUEST

        response_data = json.loads(response.content)

        assert "error" in response_data
        assert "email" in response_data["error"]

        assert User.objects.count() == 1

    def test_register_with_invalid_email_format(self):
        """Test registration with an invalid email format."""
        data = {
            "email": "not-an-email",
            "password": "password123",
            "fullname": "New User",
            "country": self.test_country.id,
        }

        response = self.client.post(REGISTER_URL, data)

        assert response.status_code == status.HTTP_400_BAD_REQUEST

        response_data = json.loads(response.content)

        assert "error" in response_data
        assert "email" in response_data["error"]

        assert User.objects.count() == 1

    def test_register_with_short_password(self):
        """Test registration with a password that's too short."""
        data = {
            "email": "<EMAIL>",
            "password": "short",
            "fullname": "New User",
            "country": self.test_country.id,
        }

        response = self.client.post(REGISTER_URL, data)

        assert response.status_code == status.HTTP_400_BAD_REQUEST

        response_data = json.loads(response.content)

        assert "error" in response_data
        assert "password" in response_data["error"]

        assert User.objects.count() == 1

    def test_register_success_after_validation(self):
        """Test successful registration after all validations pass."""
        data = {
            "email": "<EMAIL>",
            "password": "password123",
            "fullname": "Success User",
            "phone_number": "9876543210",
            "country": self.test_country.id,
        }

        response = self.client.post(REGISTER_URL, data)

        assert response.status_code == status.HTTP_201_CREATED

        response_data = json.loads(response.content)

        assert "message" in response_data
        assert "user" in response_data
        assert response_data["user"]["email"] == "<EMAIL>"

        assert User.objects.count() == 2

        new_user = User.objects.get(email="<EMAIL>")
        assert new_user.fullname == "Success User"
        assert new_user.phone_number == "9876543210"
        assert new_user.country == self.test_country

    def test_register_with_same_email_different_case(self):
        """Test registration with the same email but different case."""

        test_email = "<EMAIL>"

        User.objects.filter(email__iexact=test_email).delete()

        test_user = User.objects.create_user(
            email=test_email,
            password="password123",
            fullname="Case Test User",
            username="casetestuser",
        )

        assert User.objects.filter(email=test_email).exists()

        user_count_before = User.objects.count()

        data = {
            "email": "<EMAIL>",
            "password": "password123",
            "fullname": "New User",
            "country": self.test_country.id,
        }

        response = self.client.post(REGISTER_URL, data)

        print(f"Response status code: {response.status_code}")
        print(f"Response content: {response.content.decode('utf-8')}")

        assert response.status_code == status.HTTP_400_BAD_REQUEST

        response_data = json.loads(response.content)

        assert "error" in response_data
        assert "email" in response_data["error"]

    def test_concurrent_registrations_with_same_email(self):
        """
        Test handling of concurrent registrations with the same email.

        This is a simplified simulation of a race condition where two requests
        try to register with the same email at nearly the same time.
        """

        data1 = {
            "email": "<EMAIL>",
            "password": "password123",
            "fullname": "First User",
            "country": self.test_country.id,
        }

        response1 = self.client.post(REGISTER_URL, data1)
        assert response1.status_code == status.HTTP_201_CREATED

        response1_data = json.loads(response1.content)
        assert "message" in response1_data

        data2 = {
            "email": "<EMAIL>",
            "password": "password456",
            "fullname": "Second User",
            "country": self.test_country.id,
        }

        response2 = self.client.post(REGISTER_URL, data2)
        assert response2.status_code == status.HTTP_400_BAD_REQUEST

        response2_data = json.loads(response2.content)

        assert "error" in response2_data
        assert "email" in response2_data["error"]
