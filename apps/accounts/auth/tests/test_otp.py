from typing import Any
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase
from django.utils import timezone
from django.test import override_settings
from apps.accounts.user.models import User
from apps.accounts.auth.models import OTP
from apps.accounts.auth.utils import generate_otp
from core.utilities import tprint


class OTPLoginTests(APITestCase):
    def setUp(self):
        self.user = User.objects.create(
            email="<EMAIL>",
            username="testuser",
            password="testpassword",
            phone_number="+***********",
            is_email_verified=True,
        )
        self.user.set_password("testpassword")
        self.user.save()
        self.login_otp_url = reverse("auth-login-otp")
        self.verify_otp_url = reverse("auth-verify-otp")

    def test_send_otp_success(self):
        response: Any = self.client.post(
            self.login_otp_url, {"email": "<EMAIL>"}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(OTP.objects.filter(user=self.user).exists())

    def test_send_otp_invalid_email(self):
        response: Any = self.client.post(
            self.login_otp_url, {"email": "<EMAIL>"}
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_verify_otp_success(self):
        with override_settings(RATELIMIT_ENABLE=False):
            otp_code = generate_otp()
            OTP.objects.create(
                user=self.user,
                code=otp_code,
                expires_at=timezone.now() + timezone.timedelta(minutes=10),
            )

            response: Any = self.client.post(
                self.verify_otp_url,
                {"email": "<EMAIL>", "otp": otp_code},
            )
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertIn("access", response.data)
            self.assertIn("refresh", response.data)
            self.assertIn("user", response.data)

    def test_verify_otp_invalid_otp(self):
        otp_code = generate_otp()
        OTP.objects.create(
            user=self.user,
            code=otp_code,
            expires_at=timezone.now() + timezone.timedelta(minutes=10),
        )

        response: Any = self.client.post(
            self.verify_otp_url,
            {"email": "<EMAIL>", "otp": "wrongotp"},
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_verify_otp_expired_otp(self):
        otp_code = generate_otp()
        OTP.objects.create(
            user=self.user,
            code=otp_code,
            expires_at=timezone.now() - timezone.timedelta(minutes=1),
        )

        response: Any = self.client.post(
            self.verify_otp_url,
            {"email": "<EMAIL>", "otp": otp_code},
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_verify_otp_invalid_email(self):
        otp_code = generate_otp()
        OTP.objects.create(
            user=self.user,
            code=otp_code,
            expires_at=timezone.now() + timezone.timedelta(minutes=10),
        )

        response: Any = self.client.post(
            self.verify_otp_url,
            {"email": "<EMAIL>", "otp": otp_code},
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        self.assertEqual(
            response.json(),
            {"error": {"email": "المستخدم الذي لديه هذا البريد الإلكتروني غير موجود."}},
        )
