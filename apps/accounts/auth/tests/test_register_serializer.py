import pytest
from django.contrib.auth import get_user_model
from django.apps import apps
from rest_framework.test import APIClient

from apps.accounts.auth.serializers.register import RegisterSerializer

User = get_user_model()
Country = apps.get_model("countries", "Country")


@pytest.mark.django_db
class TestRegisterSerializer:
    """Test the RegisterSerializer"""

    def setup_method(self):
        """Set up test data."""
        self.client = APIClient()
        self.test_country = Country.objects.create(
            name="Test Country", iso2="TC", iso3="TCY"
        )

    def test_register_serializer_valid_data(self):
        """Test that the RegisterSerializer accepts valid data."""
        data = {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "fullname": "Test User",
            "country": self.test_country.id,
            "phone_number": "**********",
            "location": "Test Location",
            "city": "Test City",
            "company_information": "Test Company Info",
            "account_type": "free",
            "role": "member",
        }
        serializer = RegisterSerializer(data=data)
        assert serializer.is_valid()

    def test_register_serializer_company_name_removed(self):
        """Test that the RegisterSerializer does not accept company_name."""
        data = {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "fullname": "Test User",
            "country": self.test_country.id,
            "company_name": "Test Company",
        }
        serializer = RegisterSerializer(data=data)
        assert serializer.is_valid()

        assert "company_name" not in serializer.validated_data

    def test_register_serializer_creates_user_without_company_name(self):
        """Test that the RegisterSerializer creates a user without company_name."""
        data = {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "fullname": "Test User",
            "country": self.test_country.id,
            "company_information": "Test Company Info",
        }
        serializer = RegisterSerializer(data=data)
        assert serializer.is_valid()
        user = serializer.save()

        assert user.email == "<EMAIL>"
        assert user.fullname == "Test User"
        assert user.country == self.test_country
        assert user.company_information == "Test Company Info"

        assert user.company_name is None

    def test_register_api_endpoint(self):
        """Test the register API endpoint."""
        data = {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "fullname": "API Test User",
            "country": self.test_country.id,
            "company_information": "API Test Company Info",
        }
        response = self.client.post("/api/v1/auth/register/", data)
        assert response.status_code == 201

        user = User.objects.get(email="<EMAIL>")
        assert user.fullname == "API Test User"
        assert user.country == self.test_country
        assert user.company_information == "API Test Company Info"

        assert user.company_name is None

    def test_register_api_endpoint_with_company_name(self):
        """Test the register API endpoint with company_name (should be ignored)."""
        data = {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "fullname": "Company Test User",
            "country": self.test_country.id,
            "company_name": "This Should Be Ignored",
        }
        response = self.client.post("/api/v1/auth/register/", data)
        assert response.status_code == 201

        user = User.objects.get(email="<EMAIL>")
        assert user.fullname == "Company Test User"

        assert user.company_name is None
