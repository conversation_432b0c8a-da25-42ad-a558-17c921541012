from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase
from django.contrib.auth import get_user_model
from django.test import override_settings
from unittest.mock import patch
from typing import Union
from django.utils.translation import activate

from core.utilities import tprint

User = get_user_model()


@override_settings(LANGUAGE_CODE="ar")
class AuthErrorsTest(APITestCase):
    def setUp(self):
        activate("ar")
        self.client.defaults["HTTP_ACCEPT_LANGUAGE"] = "ar"

    def test_invalid_login(self):
        with override_settings(RATELIMIT_ENABLE=False):
            url = reverse("auth-login")
            data = {"email": "<EMAIL>", "password": "invalidpassword"}
            response = self.client.post(url, data)

            self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
            self.assertEqual(
                response.json(),
                {"error": "المستخدم الذي لديه هذا البريد الإلكتروني غير موجود."},
            )

    def test_short_password_register(self):
        url = reverse("auth-register")
        data = {"email": "<EMAIL>", "password": "short"}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json(),
            {"error": {"password": ["تأكد ان الحقل 8 محرف على الاقل."]}},
        )

    def test_invalid_refresh_token(self):
        url = reverse("auth-refresh")
        data = {"refresh": "invalidtoken"}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertEqual(response.json(), {"error": "الرمز غير صالح أو منتهي الصلاحية"})

    def test_invalid_forgot_password(self):
        url = reverse("auth-forgot-password")
        data = {"email": "<EMAIL>"}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertEqual(
            response.json(),
            {"error": {"email": "المستخدم الذي لديه هذا البريد الإلكتروني غير موجود."}},
        )

    def test_invalid_reset_password(self):
        url = reverse("auth-reset-password")
        data = {"token": "invalidtoken", "password": "newpassword"}
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json(), {"error": "رمز مميز غير صالح أو منتهي الصلاحية"}
        )

    @patch("core.tasks.send_activation_email")
    def test_invalid_otp(self, mock_send_email):
        user = User.objects.create_user(
            email="<EMAIL>", password="password"
        )  # type: ignore
        url = "/api/v1/auth/login_otp/"
        data = {"email": "<EMAIL>"}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        url = "/api/v1/auth/verify_otp/"
        data = {"email": "<EMAIL>", "otp": "invalidotp"}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json(),
            {"error": {"otp": ["تأكد ان الحقل لا يزيد عن 8 محرف."]}},
        )

    def test_invalid_email_verification(self):
        url = reverse("auth-validate-email")
        data = {"code": "123456"}
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json(), {"error": {"email": ["هذا الحقل مطلوب."]}})

    def test_invalid_resend_verification_code(self):
        url = reverse("auth-resend-verification-code")
        data = {"email": "<EMAIL>"}
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json(),
            {"error": {"email": "المستخدم الذي لديه هذا البريد الإلكتروني غير موجود."}},
        )

    def test_invalid_change_password_old_password(self):
        """Test error message when old password is incorrect for change password"""
        pass

    def test_invalid_change_password_same_password(self):
        """Test error message when new password is the same as old password"""
        pass

    def test_invalid_register_existing_email(self):
        """Test error message when trying to register with an existing email"""
        pass

    def test_expired_refresh_token(self):
        """Test error message for expired refresh token"""
        pass

    def test_invalid_token_format(self):
        """Test error message for malformed token format"""
        pass

    def test_social_auth_errors(self):
        """Test error messages for social authentication failures"""
        pass
