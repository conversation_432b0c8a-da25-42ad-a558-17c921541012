from django.test import TestCase
from rest_framework import status
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model
from django.core.files.uploadedfile import SimpleUploadedFile
from PIL import Image
import io

from apps.accounts.profile.tests.constants import (
    GET_ME_URL,
    UPDATE_ME_URL,
    USER_EMAIL,
    USER_PASSWORD,
    NEW_FULLNAME,
    DELETE_MY_PROFILE_AVATAR_URL,
    USER_USERNAME,
    USER_FULLNAME,
)

User = get_user_model()


def create_test_image():
    """Create a test image in memory"""
    file = io.BytesIO()
    image = Image.new("RGB", (100, 100), "red")
    image.save(file, "PNG")
    file.name = "test.png"
    file.seek(0)
    return file


class AvatarProfileTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            email=USER_EMAIL,
            password=USER_PASSWORD,
            username=USER_USERNAME,
            fullname=USER_FULLNAME,
        )
        self.user.set_password(USER_PASSWORD)

        test_image = create_test_image()
        self.test_image = SimpleUploadedFile(
            name="test.png", content=test_image.read(), content_type="image/png"
        )

        self.user.avatar = self.test_image
        self.user.save()

        self.client.force_authenticate(user=self.user)

    def test_get_profile_with_avatar(self):
        response = self.client.get(GET_ME_URL)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsNotNone(response.json()["avatar"])

    def test_get_profile_without_avatar(self):
        self.user.avatar = None
        self.user.save()

        response = self.client.get(GET_ME_URL)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsNone(response.json()["avatar"])

    def test_update_profile_with_avatar(self):
        test_image = create_test_image()
        data = {
            "fullname": NEW_FULLNAME,
            "avatar": SimpleUploadedFile(
                name="new_test.png", content=test_image.read(), content_type="image/png"
            ),
        }
        response = self.client.put(UPDATE_ME_URL, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.user.refresh_from_db()
        self.assertEqual(self.user.fullname, NEW_FULLNAME)
        self.assertIsNotNone(self.user.avatar)

    def test_partial_update_profile_with_avatar(self):
        self.user.avatar = None
        self.user.save()

        test_image = create_test_image()
        data = {
            "avatar": SimpleUploadedFile(
                name="new_test.png", content=test_image.read(), content_type="image/png"
            )
        }
        response = self.client.patch(UPDATE_ME_URL, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.user.refresh_from_db()
        self.assertIsNotNone(self.user.avatar)

    def test_delete_profile_avatar(self):
        response = self.client.delete(DELETE_MY_PROFILE_AVATAR_URL)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.user.refresh_from_db()
        self.assertEqual(self.user.avatar.name, "")

    def tearDown(self):
        if self.user.avatar:
            self.user.avatar.delete()
