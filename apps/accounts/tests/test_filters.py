from django.test import TestCase, override_settings
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework.response import Response
from rest_framework import status
from apps.accounts.user.models import User
from apps.accounts.filters import UserFilter
from typing import Set, Dict, Any, cast


class UserFilterTests(TestCase):
    def setUp(self):
        self.admin = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="admin",
            is_staff=True,
            is_superuser=True,
        )

        self.club_manager1 = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="club_manager"
        )

        self.club_manager2 = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="club_manager"
        )

        self.member1 = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="member",
            fullname="<PERSON>",
        )

        self.member2 = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="member",
            fullname="<PERSON>",
        )

        self.club_manager3 = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="club_manager"
        )

    def test_filter_by_role(self):
        """Test filtering users by role"""
        all_users = User.objects.all()

        filterset = UserFilter({"role": "club_manager"}, queryset=all_users)
        self.assertEqual(filterset.qs.count(), 3)
        self.assertTrue(all(user.role == "club_manager" for user in filterset.qs))

        filterset = UserFilter({"role": "member"}, queryset=all_users)
        self.assertEqual(filterset.qs.count(), 2)
        self.assertTrue(all(user.role == "member" for user in filterset.qs))

        filterset = UserFilter({"role": "admin"}, queryset=all_users)
        self.assertEqual(filterset.qs.count(), 1)
        self.assertTrue(all(user.role == "admin" for user in filterset.qs))

    def test_filter_by_email(self):
        """Test filtering users by email"""
        all_users = User.objects.all()

        filterset = UserFilter({"email": "manager"}, queryset=all_users)
        self.assertEqual(filterset.qs.count(), 3)

        filterset = UserFilter({"email": "<EMAIL>"}, queryset=all_users)
        self.assertEqual(filterset.qs.count(), 1)

    def test_filter_by_fullname(self):
        """Test filtering users by fullname"""
        all_users = User.objects.all()

        filterset = UserFilter({"fullname": "John"}, queryset=all_users)
        self.assertEqual(filterset.qs.count(), 1)
        self.assertEqual(filterset.qs.first(), self.member1)

        filterset = UserFilter({"fullname": "Smith"}, queryset=all_users)
        self.assertEqual(filterset.qs.count(), 1)
        self.assertEqual(filterset.qs.first(), self.member2)

    def test_filter_by_is_staff(self):
        """Test filtering users by is_staff"""
        all_users = User.objects.all()

        filterset = UserFilter({"is_staff": True}, queryset=all_users)
        self.assertEqual(filterset.qs.count(), 1)
        self.assertEqual(filterset.qs.first(), self.admin)

        filterset = UserFilter({"is_staff": False}, queryset=all_users)
        self.assertEqual(filterset.qs.count(), 5)
        self.assertNotIn(self.admin, filterset.qs)

    def test_combined_filters(self):
        """Test combining multiple filters"""
        all_users = User.objects.all()

        filterset = UserFilter(
            {"role": "member", "fullname": "John"}, queryset=all_users
        )
        self.assertEqual(filterset.qs.count(), 1)
        self.assertEqual(filterset.qs.first(), self.member1)

        filterset = UserFilter({"role": "admin", "is_staff": True}, queryset=all_users)
        self.assertEqual(filterset.qs.count(), 1)
        self.assertEqual(filterset.qs.first(), self.admin)

        filterset = UserFilter(
            {"role": "club_manager", "fullname": "John"}, queryset=all_users
        )
        self.assertEqual(filterset.qs.count(), 0)


class UserFilterAPITests(APITestCase):
    client: APIClient

    def setUp(self):
        self.admin = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="admin",
            is_staff=True,
            is_superuser=True,
        )

        self.club_manager1 = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="club_manager"
        )

        self.club_manager2 = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="club_manager"
        )

        self.member1 = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="member",
            fullname="John Doe",
        )

        self.member2 = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="member",
            fullname="Jane Smith",
        )

        self.club_manager3 = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="club_manager"
        )

        self.url = reverse("accounts:list-user")

        self.client = APIClient()
        self.client.force_authenticate(user=self.admin)

    def test_api_filter_by_role(self):
        """Test API filtering users by role"""

        response = cast(Response, self.client.get(f"{self.url}?role=club_manager"))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = cast(Dict[str, Any], response.data)
        self.assertEqual(data["meta"]["count"], 3)

        response = cast(Response, self.client.get(f"{self.url}?role=member"))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = cast(Dict[str, Any], response.data)
        self.assertEqual(data["meta"]["count"], 2)

        response = cast(Response, self.client.get(f"{self.url}?role=admin"))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = cast(Dict[str, Any], response.data)
        self.assertEqual(data["meta"]["count"], 1)

    @override_settings(RATE_LIMITER_ENABLED=False)
    def test_api_search(self):
        """Test API search functionality"""

        response = cast(Response, self.client.get(f"{self.url}?search=manager"))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = cast(Dict[str, Any], response.data)

        self.assertEqual(data["meta"]["count"], 3)

        response = cast(Response, self.client.get(f"{self.url}?search=member1"))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = cast(Dict[str, Any], response.data)

        self.assertEqual(data["meta"]["count"], 1)

    def test_unauthorized_access(self):
        """Test that non-admin users cannot access the user list"""

        self.client.force_authenticate(user=self.member1)

        response = cast(Response, self.client.get(self.url))
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
