from django.contrib import admin
from .models import Plan, Payment, Transaction


class PlanAdmin(admin.ModelAdmin):
    list_display = [
        "title",
        "price",
        "number_of_coupons",
        "number_of_quizzes",
        "number_of_reviews",
    ]
    search_fields = ["title"]
    list_filter = ["price"]


class PaymentAdmin(admin.ModelAdmin):
    list_display = [
        "user",
        "plan",
        "amount",
        "status",
        "payment_id",
        "created",
    ]
    search_fields = ["user__username", "plan__title", "status", "payment_id", "created"]
    list_filter = ["status", "created"]


class TransactionAdmin(admin.ModelAdmin):
    list_display = ["payment", "transaction_id", "status", "amount", "created"]
    search_fields = [
        "payment__user__username",
        "transaction_id",
        "status",
        "amount",
        "created",
    ]
    list_filter = ["status", "created"]


admin.site.register(Plan, PlanAdmin)
admin.site.register(Payment, PaymentAdmin)
admin.site.register(Transaction, TransactionAdmin)
