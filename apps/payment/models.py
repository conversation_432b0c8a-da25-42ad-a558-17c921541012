from django.db import models
from decimal import Decimal
from django.utils.translation import gettext_lazy as _

from core.abstract.models import AbstractAutoIncrementModel
from apps.accounts.user.models import User


class Plan(AbstractAutoIncrementModel):
    title = models.CharField(max_length=255)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    number_of_coupons = models.IntegerField()
    number_of_quizzes = models.IntegerField()
    number_of_reviews = models.IntegerField()

    def __str__(self):
        return self.title

    class Meta:
        verbose_name = _("Plan")
        verbose_name_plural = _("Plans")


class Payment(AbstractAutoIncrementModel):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="payments")
    plan = models.ForeignKey(Plan, on_delete=models.CASCADE, related_name="payments")
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=50)
    payment_id = models.CharField(max_length=255)

    def __str__(self):
        return f"{self.user} - {self.plan} - {self.status}"

    class Meta:
        verbose_name = _("Payment")
        verbose_name_plural = _("Payments")


class Transaction(AbstractAutoIncrementModel):
    STATUS_CHOICES = [
        ("initiated", _("Initiated")),
        ("paid", _("Paid")),
        ("failed", _("Failed")),
        ("refunded", _("Refunded")),
        ("voided", _("Voided")),
    ]

    payment = models.ForeignKey(
        Payment, on_delete=models.CASCADE, related_name="transactions"
    )
    transaction_id = models.UUIDField(unique=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    fee = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal("0"))
    currency = models.CharField(max_length=3, default="SAR")
    refunded = models.DecimalField(
        max_digits=10, decimal_places=2, default=Decimal("0")
    )
    refunded_at = models.DateTimeField(null=True, blank=True)
    captured = models.DecimalField(
        max_digits=10, decimal_places=2, default=Decimal("0")
    )
    captured_at = models.DateTimeField(null=True, blank=True)
    voided_at = models.DateTimeField(null=True, blank=True)
    description = models.TextField(blank=True)
    invoice_id = models.CharField(max_length=255, null=True, blank=True)
    ip = models.GenericIPAddressField(null=True, blank=True)
    callback_url = models.URLField(max_length=500, null=True, blank=True)
    metadata = models.JSONField(null=True, blank=True)
    source = models.JSONField()

    @property
    def amount_format(self):
        return f"{self.amount:.2f} {self.currency}"

    @property
    def fee_format(self):
        return f"{self.fee:.2f} {self.currency}"

    @property
    def refunded_format(self):
        return f"{self.refunded:.2f} {self.currency}"

    @property
    def captured_format(self):
        return f"{self.captured:.2f} {self.currency}"

    def __str__(self):
        return f"{self.payment} - {self.transaction_id} - {self.status}"

    class Meta:
        verbose_name = _("Transaction")
        verbose_name_plural = _("Transactions")
        ordering = ["-created"]
