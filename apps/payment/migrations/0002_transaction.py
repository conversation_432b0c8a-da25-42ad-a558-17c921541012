# Generated by Django 4.2.9 on 2025-02-13 20:26

from decimal import Decimal
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    dependencies = [
        ("payment", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Transaction",
            fields=[
                ("created", models.DateTimeField(auto_now_add=True)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("transaction_id", models.UUIDField(unique=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("initiated", "Initiated"),
                            ("paid", "Paid"),
                            ("failed", "Failed"),
                            ("refunded", "Refunded"),
                            ("voided", "Voided"),
                        ],
                        max_length=20,
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "fee",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0"), max_digits=10
                    ),
                ),
                ("currency", models.CharField(default="SAR", max_length=3)),
                (
                    "refunded",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0"), max_digits=10
                    ),
                ),
                ("refunded_at", models.DateTimeField(blank=True, null=True)),
                (
                    "captured",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0"), max_digits=10
                    ),
                ),
                ("captured_at", models.DateTimeField(blank=True, null=True)),
                ("voided_at", models.DateTimeField(blank=True, null=True)),
                ("description", models.TextField(blank=True)),
                ("invoice_id", models.CharField(blank=True, max_length=255, null=True)),
                ("ip", models.GenericIPAddressField(blank=True, null=True)),
                (
                    "callback_url",
                    models.URLField(blank=True, max_length=500, null=True),
                ),
                ("metadata", models.JSONField(blank=True, null=True)),
                ("source", models.JSONField()),
                (
                    "payment",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transactions",
                        to="payment.payment",
                    ),
                ),
            ],
            options={
                "verbose_name": "Transaction",
                "verbose_name_plural": "Transactions",
                "ordering": ["-created"],
            },
        ),
    ]
