import requests
from django.conf import settings
from rest_framework import generics, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema
from django.utils.translation import gettext as _
import logging
from time import sleep
from django.db import transaction
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from requests.exceptions import RequestException, ConnectionError, Timeout
from urllib3.exceptions import MaxRetryError
from decimal import Decimal
import uuid

from apps.coupons.models import Coupon
from apps.coupons.serializers import CouponReadSerializer
from core.decorators.error_handler import api_error_handler
from apps.payment.models import Plan, Payment, Transaction
from apps.payment.serializers.payment import PaymentSerializer
from apps.payment.serializers.payment_create import PaymentCreateSerializer

from apps.payment.tests.mock import MockResponse

logger = logging.getLogger(__name__)


class CreatePaymentView(generics.CreateAPIView):
    serializer_class = PaymentSerializer
    permission_classes = [IsAuthenticated]
    MAX_COUPON_WAIT_TIME = 60
    COUPON_CHECK_INTERVAL = 5
    MAX_API_RETRIES = 3
    API_RETRY_DELAY = 5

    def get_serializer_class(self):
        if self.request.method == "POST":
            return PaymentCreateSerializer
        return PaymentSerializer

    def validate_payment_id(self, payment_id, is_testing=False):
        """
        Validate payment ID with retry logic for API connection issues.

        Args:
            payment_id: The payment ID to validate
            is_testing: Whether this is a test request

        Returns:
            Response object from Moyasar API or mock response
        """
        if is_testing:
            mock_data = {"status": "paid", "payment_id": payment_id}
            return MockResponse(json_data=mock_data, status_code=201)

        url = f"https://api.moyasar.com/v1/payments/{payment_id}"

        for attempt in range(self.MAX_API_RETRIES):
            try:
                response = requests.get(
                    url,
                    auth=(settings.MOYASSAR_API_KEY, ""),
                    headers={"Content-Type": "application/json"},
                    timeout=10,
                )
                if response.ok:
                    payment_data = response.json()
                    self._create_transaction(payment_data)
                return response

            except (ConnectionError, Timeout, MaxRetryError) as e:
                logger.warning(
                    f"API connection attempt {attempt + 1}/{self.MAX_API_RETRIES} failed: {str(e)}"
                )
                if attempt < self.MAX_API_RETRIES - 1:
                    logger.info(f"Retrying in {self.API_RETRY_DELAY} seconds...")
                    sleep(self.API_RETRY_DELAY)
                else:
                    logger.error("All API connection attempts failed")
                    raise ConnectionError(
                        _(
                            "Unable to connect to payment service. Please try again later."
                        )
                    )
            except RequestException as e:
                logger.error(f"Unexpected API error: {str(e)}")
                raise ConnectionError(
                    _(
                        "An error occurred while validating the payment. Please try again later."
                    )
                )

    def _create_transaction(self, payment_data):
        """Create a transaction record from payment data."""
        try:
            payment = Payment.objects.get(payment_id=payment_data["id"])
            transaction_data = {
                "payment": payment,
                "transaction_id": uuid.UUID(payment_data["id"]),
                "status": payment_data["status"],
                "amount": Decimal(str(payment_data["amount"] / 100)),
                "fee": Decimal(str(payment_data.get("fee", 0) / 100)),
                "currency": payment_data.get("currency", "SAR"),
                "refunded": Decimal(str(payment_data.get("refunded", 0) / 100)),
                "refunded_at": payment_data.get("refunded_at"),
                "captured": Decimal(str(payment_data.get("captured", 0) / 100)),
                "captured_at": payment_data.get("captured_at"),
                "voided_at": payment_data.get("voided_at"),
                "description": payment_data.get("description", ""),
                "invoice_id": payment_data.get("invoice_id"),
                "ip": payment_data.get("ip"),
                "callback_url": payment_data.get("callback_url"),
                "metadata": payment_data.get("metadata"),
                "source": payment_data.get("source", {}),
            }

            Transaction.objects.create(**transaction_data)
            logger.info(f"Created transaction record for payment {payment_data['id']}")
        except Exception as e:
            logger.error(f"Error creating transaction record: {str(e)}")
            logger.error("Payment data: %s", payment_data)

    def wait_for_coupon(self, user, payment_id):
        """
        Wait for coupon to be created with improved retry logic and error handling.

        Args:
            user: The user to check coupons for
            payment_id: The payment ID associated with the coupon

        Returns:
            Coupon object if found, None otherwise
        """
        logger.info(
            f"Waiting for coupon creation - User: {user.email}, Payment: {payment_id}"
        )

        start_time = timezone.now()
        end_time = start_time + timedelta(seconds=self.MAX_COUPON_WAIT_TIME)

        attempt = 1
        while timezone.now() < end_time:
            try:
                with transaction.atomic():
                    coupon = (
                        Coupon.objects.select_for_update(skip_locked=True)
                        .filter(
                            created_to=user,
                            created__gte=timezone.now() - timedelta(hours=1),
                            type="PAID",
                        )
                        .order_by("-created")
                        .first()
                    )

                    if coupon:
                        logger.info(
                            f"Found coupon on attempt {attempt} - "
                            f"Code: {coupon.code}, "
                            f"Created: {coupon.created}, "
                            f"Type: {coupon.type}"
                        )
                        return coupon

                    payment = Payment.objects.filter(payment_id=payment_id).first()
                    if payment and payment.status in ["failed", "refunded", "voided"]:
                        logger.warning(
                            f"Payment {payment_id} is in terminal state: {payment.status}. "
                            "Stopping coupon wait."
                        )
                        return None

            except Exception as e:
                logger.error(
                    f"Error checking for coupon on attempt {attempt}: {str(e)}"
                )
                logger.error("Stack trace:", exc_info=True)

            time_remaining = (end_time - timezone.now()).seconds
            if time_remaining <= 0:
                break

            sleep_time = min(self.COUPON_CHECK_INTERVAL, time_remaining)
            attempt += 1
            logger.info(
                f"Coupon not found on attempt {attempt}, "
                f"waiting {sleep_time}s... "
                f"Time remaining: {time_remaining}s"
            )
            sleep(sleep_time)

        logger.warning(
            f"No coupon found after {attempt} attempts "
            f"({self.MAX_COUPON_WAIT_TIME} seconds) - "
            f"User: {user.email}, Payment: {payment_id}"
        )
        return None

    @extend_schema(
        tags=["Payments"],
        request=PaymentCreateSerializer,
        responses={201: PaymentSerializer},
    )
    @api_error_handler
    def post(self, request, *args, **kwargs):
        try:
            plan_id = request.data.get("plan")
            try:
                plan = Plan.objects.get(id=plan_id)
            except Plan.DoesNotExist:
                return Response(
                    {"error": _("Plan not found")},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            payment_id = request.data.get("payment_id")
            is_testing = request.data.get("is_testing", False)
            payment_response = self.validate_payment_id(payment_id, is_testing)

            if payment_response.status_code == status.HTTP_404_NOT_FOUND:  # type: ignore
                return Response(
                    {"error": _("Invalid payment ID")},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            payment_exists = Payment.objects.filter(payment_id=payment_id).exists()
            if payment_exists:
                payment = Payment.objects.get(payment_id=payment_id)
                coupon = self.wait_for_coupon(request.user, payment_id)
                if coupon:
                    return Response(
                        {
                            "message": _("Payment already exists"),
                            "payment": PaymentSerializer(payment).data,
                            "coupon": CouponReadSerializer(coupon).data,
                        },
                        status=status.HTTP_200_OK,
                    )
                else:
                    return Response(
                        {
                            "message": _("Payment exists but coupon not found"),
                            "payment": PaymentSerializer(payment).data,
                        },
                        status=status.HTTP_200_OK,
                    )

            serializer = self.get_serializer(
                data={
                    "plan": plan_id,
                    "status": "pending",
                    "payment_id": payment_id,
                }
            )
            serializer.is_valid(raise_exception=True)
            payment = serializer.save(user=request.user, amount=plan.price)

            coupon = self.wait_for_coupon(request.user, payment_id)

            response_data = {
                "payment": PaymentSerializer(payment).data,
            }

            if coupon:
                response_data["coupon"] = CouponReadSerializer(coupon).data
            else:
                logger.warning(
                    f"No coupon found for payment {payment_id} - "
                    f"User: {request.user.email}, "
                    f"Plan: {plan.title}"
                )
                response_data["message"] = _("Payment created but coupon not found yet")

            return Response(response_data, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"Error processing payment: {str(e)}")
            logger.error("Stack trace:", exc_info=True)
            return Response(
                {
                    "error": _(
                        "An error occurred while processing your payment. Please try again later."
                    )
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
