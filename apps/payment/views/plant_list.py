import requests
from django.conf import settings
from rest_framework import generics, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema
from django.utils.translation import gettext as _
import logging
from time import sleep
from django.db import transaction
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from requests.exceptions import RequestException, ConnectionError, Timeout
from urllib3.exceptions import MaxRetryError
from decimal import Decimal
import uuid

from apps.coupons.models import Coupon
from apps.coupons.serializers import CouponReadSerializer
from core.decorators.error_handler import api_error_handler
from apps.payment.models import Plan, Payment, Transaction
from apps.payment.serializers.plan import PlanSerializer

from apps.payment.tests.mock import MockResponse

logger = logging.getLogger(__name__)


class PlanListView(generics.ListAPIView):
    queryset = Plan.objects.all()
    serializer_class = PlanSerializer
    permission_classes = [IsAuthenticated]

    @extend_schema(tags=["Plans"], responses={200: PlanSerializer(many=True)})
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
