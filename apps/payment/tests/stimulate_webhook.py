import requests
import hmac
import hashlib
import json
import os
import sys
from datetime import datetime
from pathlib import Path


project_root = str(Path(__file__).resolve().parent.parent.parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.development")
django.setup()

from django.conf import settings


def generate_moyasar_signature(payload, secret):
    """Generate HMAC signature for Moyasar webhook payload."""
    payload_bytes = json.dumps(payload).encode("utf-8")
    return hmac.new(secret.encode("utf-8"), payload_bytes, hashlib.sha256).hexdigest()


def create_test_payment(payment_id):
    """Create a test payment in the database."""
    from apps.payment.models import Payment, Plan
    from apps.accounts.user.models import User

    try:
        plan = Plan.objects.first()
        if not plan:
            plan = Plan.objects.create(
                title="Test Plan", price=1000, number_of_coupons=1
            )

        user = User.objects.filter(email="<EMAIL>").first()
        if not user:
            user = User.objects.create_user(
                email="<EMAIL>", password="admin12345"
            )

        payment = Payment.objects.create(
            payment_id=payment_id,
            user=user,
            plan=plan,
            amount=plan.price,
            status="pending",
        )
        print(f"Created test payment: {payment.payment_id}")
        return payment
    except Exception as e:
        print(f"Error creating test payment: {str(e)}")
        return None


def test_webhook(event_type="payment_paid", payment_id="test_payment_id"):
    """
    Test Moyasar webhook with different event types.

    Args:
        event_type (str): Type of payment event ('payment_paid', 'payment_failed', 'payment_refunded', 'payment_voided')
        payment_id (str): Payment ID to test with
    """

    payment = create_test_payment(payment_id)
    if not payment:
        print("Failed to create test payment. Webhook test may fail.")

    host = os.getenv("API_HOST", "api")
    port = os.getenv("API_PORT", "8139")
    ngrok_url = os.getenv("NGROK_URL")

    if ngrok_url:
        webhook_url = f"{ngrok_url.rstrip('/')}/api/v1/payments/moyasar-webhook/"
    else:
        webhook_url = f"http://{host}:{port}/api/v1/payments/moyasar-webhook/"

    print(f"\nTest Configuration:")
    print(f"Event Type: {event_type}")
    print(f"Payment ID: {payment_id}")
    print(f"Webhook URL: {webhook_url}")
    print(f"Host: {host}")
    print(f"Port: {port}")

    payment_data = {
        "id": payment_id,
        "status": event_type.replace("payment_", ""),
        "amount": 1000,
        "fee": 10,
        "currency": "SAR",
        "refunded": 0,
        "captured": 0,
        "captured_at": None,
        "voided_at": None,
        "description": "Test Payment",
        "amount_format": "10.00 SAR",
        "fee_format": "0.10 SAR",
        "refunded_format": "0.00 SAR",
        "captured_format": "0.00 SAR",
        "invoice_id": None,
        "ip": "127.0.0.1",
        "callback_url": "https://example.com/callback",
        "created_at": datetime.utcnow().isoformat(),
        "updated_at": datetime.utcnow().isoformat(),
        "metadata": {},
        "source": {
            "type": "creditcard",
            "company": "visa",
            "name": "Test User",
            "number": "XXXX-XXXX-XXXX-1234",
            "message": "Succeeded!",
            "transaction_url": "https://api.moyasar.com/v1/transactions/123",
        },
    }

    if event_type == "payment_failed":
        payment_data["status"] = "failed"
        payment_data["source"]["message"] = "Failed!"
        payment_data["error"] = {
            "message": "Insufficient funds",
            "type": "payment_failed",
        }
    elif event_type == "payment_refunded":
        payment_data["status"] = "refunded"
        payment_data["refunded"] = payment_data["amount"]
        payment_data["refunded_format"] = payment_data["amount_format"]
    elif event_type == "payment_voided":
        payment_data["status"] = "voided"
        payment_data["voided_at"] = datetime.utcnow().isoformat()

    payload = {
        "id": payment_data["id"],
        "type": event_type,
        "created_at": datetime.utcnow().isoformat(),
        "data": payment_data,
    }

    try:
        secret = settings.MOYASSAR_SECRET_KEY
        if not secret:
            print("Error: MOYASSAR_SECRET_KEY not found in settings")
            return False

        signature = generate_moyasar_signature(payload, secret)

        headers = {"Content-Type": "application/json", "X-Moyasar-Signature": signature}

        print(f"\nSending webhook request...")
        print(f"Headers:")
        for key, value in headers.items():
            print(f"  {key}: {value}")
        print(f"\nPayload:")
        print(json.dumps(payload, indent=2))

        response = requests.post(webhook_url, json=payload, headers=headers, timeout=10)

        print(f"\nResponse:")
        print(f"Status Code: {response.status_code}")
        print(f"Response Body: {response.text}")

        if response.status_code != 200:
            print(f"Warning: Unexpected status code {response.status_code}")

        return response.status_code == 200

    except requests.exceptions.ConnectionError as e:
        print(f"\nConnection Error: Could not connect to {webhook_url}")
        print(f"Make sure:")
        print(f"1. The Django server is running and accessible")
        print(f"2. If using Docker, the service name '{host}' is correct")
        print(f"3. The port {port} is correct and exposed")
        print(f"4. If testing external webhooks, ngrok is running and NGROK_URL is set")
        print(f"\nError details: {str(e)}")
        return False
    except Exception as e:
        print(f"\nError sending webhook: {str(e)}")
        return False


if __name__ == "__main__":
    print("\nMoyasar Webhook Test Tool")
    print("=" * 80)
    print("\nEnvironment:")
    print(f"DJANGO_SETTINGS_MODULE: {os.getenv('DJANGO_SETTINGS_MODULE')}")
    print(f"API_HOST: {os.getenv('API_HOST')}")
    print(f"API_PORT: {os.getenv('API_PORT')}")
    print(f"NGROK_URL: {os.getenv('NGROK_URL', 'Not set')}")
    print("=" * 80)

    events = ["payment_paid", "payment_failed", "payment_refunded", "payment_voided"]
    for event in events:
        print(f"\nTesting {event} webhook:")
        success = test_webhook(event_type=event, payment_id=f"test_{event}_id")
        print(f"Test {'succeeded' if success else 'failed'}")
        print("=" * 80)
