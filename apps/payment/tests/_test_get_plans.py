from rest_framework import status
from rest_framework.test import APITestCase
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model

from apps.payment.tests.constants import (
    GET_PLANS,
    CREATE_PAYMENT,
    USER_EMAIL,
    USER_PASSWORD,
)
from apps.payment.models import Plan
from apps.accounts.user.models import VerificationCode
from core.utilities import tprint

User = get_user_model()


class PlansTests(APITestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create(
            email=USER_EMAIL, password=USER_PASSWORD, is_email_verified=True
        )
        self.user.set_password(USER_PASSWORD)
        self.user.save()
        self.client.force_authenticate(user=self.user)

        Plan.objects.create(
            title="Basic Plan",
            price=9.99,
            number_of_coupons=5,
            number_of_quizzes=3,
            number_of_reviews=8,
        )
        Plan.objects.create(
            title="Premium Plan",
            price=19.99,
            number_of_coupons=2,
            number_of_quizzes=3,
            number_of_reviews=1,
        )

    def test_get_plans_success(self):
        response = self.client.get(GET_PLANS)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["count"], 2)
