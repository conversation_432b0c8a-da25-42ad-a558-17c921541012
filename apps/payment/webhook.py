import json
import logging
from django.http import JsonResponse
from rest_framework.views import APIView
from django.utils.translation import gettext as _
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings

from core.utilities import tprint

logger = logging.getLogger(__name__)


class MoyasarWebhookView(APIView):
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        try:
            secret_token = request.headers.get("X-Event-Secret")
            if not secret_token or secret_token != settings.MOYASSAR_SECRET_KEY:
                return Response(
                    {"error": _("Invalid secret token")},
                    status=status.HTTP_403_FORBIDDEN,
                )

            event_data = json.loads(request.body)
            event_data_paid_example = {
                "id": "b75457ba-4508-4020-8b18-8813d5349da3",
                "type": "payment_paid",
                "created_at": "2024-09-07T20:38:02+00:00",
                "secret_token": "",
                "account_name": None,
                "live": False,
                "data": {
                    "id": "633b329a-21f0-4d57-be00-6f26bc1fca81",
                    "status": "paid",
                    "amount": 49000,
                    "fee": 1665,
                    "currency": "SAR",
                    "refunded": 0,
                    "refunded_at": None,
                    "captured": 0,
                    "captured_at": None,
                    "voided_at": None,
                    "description": "Coupon Order",
                    "amount_format": "490.00 SAR",
                    "fee_format": "16.65 SAR",
                    "refunded_format": "0.00 SAR",
                    "captured_format": "0.00 SAR",
                    "invoice_id": None,
                    "ip": "**************",
                    "callback_url": "http://localhost:3000/payment/success?planId=42d667b6-0458-44ef-8f22-9d5af0388f30&quizId=ea4a3f4a-8e97-4401-9696-a1e29fd1fea6",
                    "created_at": "2024-09-07T20:37:57.773Z",
                    "updated_at": "2024-09-07T20:38:02.813Z",
                    "metadata": None,
                    "source": {
                        "type": "creditcard",
                        "company": "visa",
                        "name": "sel wr",
                        "number": "4242-42XX-XXXX-4242",
                        "gateway_id": "moyasar_cc_E2VK5uGcgT9Pz9W2kR1tCCS",
                        "reference_number": None,
                        "token": None,
                        "message": "APPROVED",
                        "transaction_url": "https://api.moyasar.com/v1/transaction_auths/696a6c98-caf5-40eb-99be-b44002fe2b31/form",
                        "response_code": None,
                        "authorization_code": None,
                        "issuer_name": "STRIPE PAYMENTS UK LIMITED",
                        "issuer_country": "GB",
                        "issuer_card_type": "credit",
                        "issuer_card_category": "CLASSIC",
                    },
                },
            }

            logger.info(f"Received Moyasar Webhook: {event_data}")

            event_type = event_data.get("event")
            if event_type == "payment_paid":
                self.handle_payment_paid(event_data)
            elif event_type == "payment_failed":
                self.handle_payment_failed(event_data)

            else:
                return Response(
                    {"message": _("Unhandled event type")},
                    status=status.HTTP_200_OK,
                )

            return Response(
                {"message": _("Webhook received successfully")},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logger.error(f"Error processing webhook: {str(e)}")
            tprint.tprint(f"Error processing webhook: {str(e)}")
            return Response(
                {"error": _("Failed to process webhook")},
                status=status.HTTP_400_BAD_REQUEST,
            )

    def handle_payment_paid(self, event_data):
        payment_id = event_data.get("id")
        amount = event_data.get("amount")

        logger.info(f"Payment {payment_id} succeeded with amount {amount}")

    def handle_payment_failed(self, event_data):
        payment_id = event_data.get("id")
        logger.warning(f"Payment {payment_id} failed")
