import json
import logging
from django.http import JsonResponse
from rest_framework.views import APIView
from django.utils.translation import gettext as _
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings
from django.utils import timezone
from time import sleep
from decimal import Decimal

from apps.coupons.models import Coupon
from core.utilities import tprint
from .models import Payment, Plan, Transaction
from core.tasks import send_email_with_html

logger = logging.getLogger(__name__)


class MoyasarWebhookView(APIView):
    permission_classes = [AllowAny]

    def verify_moyasar_signature(self, request):
        """Verify that the webhook request came from Moyasar using the X-Event-Secret header."""
        try:
            event_secret = request.headers.get("X-Event-Secret")
            logger.info(f"Received Moyasar webhook request")
            logger.info(f"Headers: {dict(request.headers)}")
            logger.info(f"Body: {request.body.decode('utf-8')}")

            if not event_secret:
                logger.error("No X-Event-Secret header found")
                return False

            expected_secret = settings.MOYASSAR_SECRET_KEY
            if not expected_secret:
                logger.error("MOYASSAR_SECRET_KEY not configured")
                return False

            is_valid = event_secret == expected_secret
            logger.info(f"Secret verification result: {is_valid}")

            return is_valid
        except Exception as e:
            logger.error(f"Error verifying Moyasar secret: {str(e)}")
            logger.error(f"Stack trace:", exc_info=True)
            return False

    def handle_successful_payment(self, data):
        """Handle successful payment webhook event."""
        try:
            payment_id = data.get("id")
            amount = data.get("amount")

            payment = Payment.objects.filter(payment_id=payment_id).first()
            if not payment:
                logger.error(f"Payment {payment_id} not found in our system")
                return False

            payment.status = "success"
            payment.save()

            try:
                Transaction.objects.create(
                    payment=payment,
                    transaction_id=data.get("id"),
                    status=data.get("status"),
                    amount=Decimal(str(data.get("amount", 0) / 100)),
                    fee=Decimal(str(data.get("fee", 0) / 100)),
                    currency=data.get("currency", "SAR"),
                    refunded=Decimal(str(data.get("refunded", 0) / 100)),
                    refunded_at=data.get("refunded_at"),
                    captured=Decimal(str(data.get("captured", 0) / 100)),
                    captured_at=data.get("captured_at"),
                    voided_at=data.get("voided_at"),
                    description=data.get("description", ""),
                    invoice_id=data.get("invoice_id"),
                    ip=data.get("ip"),
                    callback_url=data.get("callback_url"),
                    metadata=data.get("metadata"),
                    source=data.get("source", {}),
                )
                logger.info(f"Created transaction record for payment {payment_id}")
            except Exception as e:
                logger.error(f"Error creating transaction record: {str(e)}")
                logger.error(f"Payment data: {data}")

            plan = payment.plan

            coupons = []
            for __ in range(plan.number_of_coupons):
                coupon = Coupon.objects.create(
                    created_by=payment.user, created_to=payment.user, type="PAID"
                )
                coupons.append(coupon.code)

            coupon_list = "\n".join([f"- {code}" for code in coupons])
            send_email_with_html.delay(
                subject=_("Payment Successful"),
                text_content=_("Thank you for your payment."),
                from_email=settings.EMAIL_HOST_USER,
                to_email=payment.user.email,
                html_content=_(
                    f"<p>Thank you for your payment of {amount/100} SAR for the {plan.title} plan.</p>"
                    f"<p>Your coupon codes:</p><pre>{coupon_list}</pre>"
                ),
            )

            return True
        except Exception as e:
            logger.error(f"Error handling successful payment: {str(e)}")
            return False

    def handle_failed_payment(self, data):
        """Handle failed payment webhook event."""
        try:
            payment_id = data.get("id")
            payment = Payment.objects.filter(payment_id=payment_id).first()
            if payment:
                payment.status = "failed"
                payment.save()

                send_email_with_html.delay(
                    subject=_("Payment Failed"),
                    text_content=_("Your payment has failed."),
                    from_email=settings.EMAIL_HOST_USER,
                    to_email=payment.user.email,
                    html_content=_(
                        "<p>Your payment has failed. Please try again or contact support.</p>"
                    ),
                )
            return True
        except Exception as e:
            logger.error(f"Error handling failed payment: {str(e)}")
            return False

    def handle_refunded_payment(self, data):
        """Handle refunded payment webhook event."""
        try:
            payment_id = data.get("id")
            payment = Payment.objects.filter(payment_id=payment_id).first()
            if payment:
                payment.status = "refunded"
                payment.save()

                now = timezone.now()
                Coupon.objects.filter(
                    created_by=payment.user,
                    created_to=payment.user,
                    used_at__isnull=True,
                ).update(used_at=now, used_by=payment.user)

                send_email_with_html.delay(
                    subject=_("Payment Refunded"),
                    text_content=_("Your payment has been refunded."),
                    from_email=settings.EMAIL_HOST_USER,
                    to_email=payment.user.email,
                    html_content=_("<p>Your payment has been refunded.</p>"),
                )
            return True
        except Exception as e:
            logger.error(f"Error handling refunded payment: {str(e)}")
            return False

    def handle_voided_payment(self, data):
        """Handle voided payment webhook event."""
        try:
            payment_id = data.get("id")
            payment = Payment.objects.filter(payment_id=payment_id).first()
            if payment:
                payment.status = "voided"
                payment.save()

                now = timezone.now()
                Coupon.objects.filter(
                    created_by=payment.user,
                    created_to=payment.user,
                    used_at__isnull=True,
                ).update(used_at=now, used_by=payment.user)
            return True
        except Exception as e:
            logger.error(f"Error handling voided payment: {str(e)}")
            return False

    def post(self, request, *args, **kwargs):
        """Handle webhook notifications from Moyasar."""
        logger.info("Received webhook request")
        logger.info(f"Request headers: {dict(request.headers)}")
        logger.info(f"Request body: {request.body.decode('utf-8')}")

        if not self.verify_moyasar_signature(request):
            logger.error("Invalid Moyasar signature")
            logger.error(
                f"Expected signature from header: {request.headers.get('X-Event-Secret')}"
            )
            logger.error(
                f"Request body used for signature: {request.body.decode('utf-8')}"
            )
            return Response(
                {"error": "Invalid signature"}, status=status.HTTP_403_FORBIDDEN
            )

        try:
            data = json.loads(request.body)
            logger.info(f"Parsed webhook data: {json.dumps(data, indent=2)}")

            event = data.get("type")
            payment_data = data.get("data", {})
            tprint.tprint(payment_data)

            payment_id = None
            if payment_data.get("id"):
                payment_id = payment_data.get("id")
            elif payment_data.get("payment", {}).get("id"):
                payment_id = payment_data.get("payment", {}).get("id")

            logger.info(f"Processing webhook: event={event}, payment_id={payment_id}")
            logger.info(f"Full payment data: {json.dumps(payment_data, indent=2)}")

            if not payment_id:
                logger.error("No payment ID in webhook data")
                return Response(
                    {"status": "error", "message": "No payment ID provided"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            for attempt in range(3):
                payment = Payment.objects.filter(payment_id=payment_id).first()
                if payment:
                    break
                logger.info(
                    f"Payment {payment_id} not found, attempt {attempt + 1}/3. Waiting..."
                )
                sleep(1)

            if not payment:
                logger.error(
                    f"Payment {payment_id} not found in database after 3 attempts"
                )
                logger.error(
                    f"Available payment IDs: {list(Payment.objects.values_list('payment_id', flat=True))}"
                )
                return Response(
                    {"status": "error", "message": f"Payment {payment_id} not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            if event == "payment_paid":
                tprint.tprint("payment_paid")
                amount = payment_data.get("amount")
                currency = payment_data.get("currency")
                source = payment_data.get("source", {})
                transaction_url = source.get("transaction_url")

                logger.info(
                    f"Payment {payment_id} successful: {amount} {currency}"
                    f" (Transaction URL: {transaction_url})"
                )

                if self.handle_successful_payment(payment_data):
                    return Response(
                        {
                            "status": "success",
                            "message": _("Payment processed successfully"),
                        }
                    )
                return Response(
                    {"status": "error", "message": _("Failed to process payment")},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

            elif event == "payment_failed":
                tprint.tprint("payment_failed")
                error = payment_data.get("error", {})
                error_message = error.get("message", "Unknown error")

                logger.warning(f"Payment {payment_id} failed: {error_message}")

                if self.handle_failed_payment(payment_data):
                    return Response(
                        {
                            "status": "failed",
                            "message": _("Payment failed"),
                            "error": error_message,
                        }
                    )
                return Response(
                    {
                        "status": "error",
                        "message": _("Failed to handle payment failure"),
                    },
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

            elif event == "payment_refunded":
                refunded_amount = payment_data.get("refunded")
                logger.info(
                    f"Payment {payment_id} refunded: {refunded_amount} {payment_data.get('currency')}"
                )

                if self.handle_refunded_payment(payment_data):
                    return Response(
                        {"status": "success", "message": _("Payment refunded")}
                    )
                return Response(
                    {"status": "error", "message": _("Failed to handle refund")},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

            elif event == "payment_voided":
                tprint.tprint("payment_voided")
                logger.info(f"Payment {payment_id} voided")

                if self.handle_voided_payment(payment_data):
                    return Response(
                        {"status": "success", "message": _("Payment voided")}
                    )
                return Response(
                    {"status": "error", "message": _("Failed to handle void")},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

            else:
                tprint.tprint(f"Unhandled event type: {event}")
                logger.warning(f"Unhandled Moyasar event type: {event}")
                return Response(
                    {"status": "error", "message": _("Unhandled event type")},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        except json.JSONDecodeError:
            logger.error("Failed to parse JSON from Moyasar webhook")
            return Response(
                {"status": "error", "message": _("Invalid JSON")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        except Exception as e:
            logger.error(f"Error handling Moyasar webhook: {str(e)}")
            return Response(
                {"status": "error", "message": _("Internal server error")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
