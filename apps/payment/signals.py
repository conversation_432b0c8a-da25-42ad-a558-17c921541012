from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils.translation import gettext as _
import logging

from .models import Payment, Transaction
from apps.notifications.utils import create_notification

logger = logging.getLogger(__name__)


@receiver(post_save, sender=Payment)
def payment_notification(sender, instance, created, **kwargs):
    """Send notification when payment status changes"""
    if created:
        create_notification(
            message=_("Payment initiated for {plan_name}").format(
                plan_name=instance.plan.name
            ),
            notification_type="Process",
            target=instance.user,
            description=_(
                "Your payment of {amount} for {plan_name} has been initiated."
            ).format(amount=instance.amount, plan_name=instance.plan.name),
        )
    elif instance.status == "success":
        create_notification(
            message=_("Payment successful for {plan_name}").format(
                plan_name=instance.plan.name
            ),
            notification_type="Completed",
            target=instance.user,
            description=_(
                "Your payment of {amount} for {plan_name} has been successfully processed."
            ).format(amount=instance.amount, plan_name=instance.plan.name),
        )
    elif instance.status == "failed":
        create_notification(
            message=_("Payment failed for {plan_name}").format(
                plan_name=instance.plan.name
            ),
            notification_type="Failed",
            target=instance.user,
            description=_(
                "Your payment of {amount} for {plan_name} has failed. Please try again."
            ).format(amount=instance.amount, plan_name=instance.plan.name),
        )


@receiver(post_save, sender=Transaction)
def transaction_notification(sender, instance, created, **kwargs):
    """Send notification for transaction events"""
    if created:

        if instance.status == "refunded":
            create_notification(
                message=_("Refund processed for {plan_name}").format(
                    plan_name=instance.payment.plan.name
                ),
                notification_type="Process",
                target=instance.payment.user,
                description=_(
                    "Your refund of {refunded_amount} for {plan_name} has been processed."
                ).format(
                    refunded_amount=instance.refunded_format,
                    plan_name=instance.payment.plan.name,
                ),
            )
        elif instance.status == "voided":
            create_notification(
                message=_("Payment voided for {plan_name}").format(
                    plan_name=instance.payment.plan.name
                ),
                notification_type="Process",
                target=instance.payment.user,
                description=_(
                    "Your payment of {amount} for {plan_name} has been voided."
                ).format(
                    amount=instance.amount_format, plan_name=instance.payment.plan.name
                ),
            )
