from rest_framework import serializers

from apps.accounts.user.serializers import UserSerializer

from apps.payment.models import Plan, Payment, Transaction


class TransactionSerializer(serializers.ModelSerializer):
    amount_format = serializers.CharField(read_only=True)
    fee_format = serializers.CharField(read_only=True)
    refunded_format = serializers.CharField(read_only=True)
    captured_format = serializers.CharField(read_only=True)

    class Meta:
        model = Transaction
        fields = [
            "id",
            "payment",
            "transaction_id",
            "status",
            "amount",
            "fee",
            "currency",
            "refunded",
            "refunded_at",
            "captured",
            "captured_at",
            "voided_at",
            "description",
            "amount_format",
            "fee_format",
            "refunded_format",
            "captured_format",
            "invoice_id",
            "ip",
            "callback_url",
            "created",
            "updated",
            "metadata",
            "source",
        ]
        read_only_fields = ["id", "created", "updated"]
