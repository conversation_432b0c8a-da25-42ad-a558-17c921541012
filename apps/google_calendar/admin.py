from django.contrib import admin

from .models import GoogleCalendarCredentials, GoogleCalendarSubscription


class GoogleCalendarCredentialsAdmin(admin.ModelAdmin):
    list_display = ["id", "user"]
    search_fields = ["user"]
    list_filter = ["user", "credentials"]
    readonly_fields = ["user", "credentials"]


admin.site.register(
    GoogleCalendarCredentials,
    GoogleCalendarCredentialsAdmin,
)

from django.contrib import admin

from .models import GoogleCalendarCredentials


class GoogleCalendarSubscriptionAdmin(admin.ModelAdmin):
    list_display = ["id", "user", "channel_id", "resource_id", "expiration"]
    search_fields = ["user"]
    list_filter = ["user"]
    readonly_fields = ["user"]


admin.site.register(
    GoogleCalendarSubscription,
    GoogleCalendarSubscriptionAdmin,
)
