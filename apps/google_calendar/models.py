from django.contrib.auth.models import User
import json

from django.conf import settings
from django.db import models
from google.oauth2.credentials import Credentials


class GoogleCalendarCredentials(models.Model):
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    credentials = models.TextField()

    def set_credentials(self, credentials):
        self.credentials = credentials.to_json()
        self.save()

    def get_credentials(self):
        return Credentials.from_authorized_user_info(json.loads(self.credentials))


class GoogleCalendarSubscription(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    channel_id = models.CharField(max_length=255, unique=True)
    resource_id = models.CharField(max_length=255)
    expiration = models.DateTimeField()

    def __str__(self):
        return f"Subscription for {self.user.email}"
