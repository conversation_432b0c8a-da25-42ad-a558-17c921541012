from celery import shared_task
import logging

from apps.google_calendar.services import (
    create_or_update_google_event,
    delete_google_event,
)
from apps.clubs.meetings.models import ClubMeeting
from apps.accounts.user.models import User

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def schedule_google_calendar_event(self, meeting_id):
    """
    Celery task to create or update a Google Calendar event for a meeting.
    """
    try:
        meeting = ClubMeeting.objects.get(pk=meeting_id)
        logger.info(f"Task: Processing Google Calendar event for meeting {meeting_id}")

        google_event_id, meet_link = create_or_update_google_event(meeting)

        if google_event_id is not None:
            ClubMeeting.objects.filter(pk=meeting_id).update(
                google_calendar_event_id=google_event_id, meeting_link=meet_link
            )
            logger.info(
                f"Task: Successfully updated meeting {meeting_id} with Google event ID {google_event_id} and link {meet_link}"
            )
        else:
            logger.error(
                f"Task: Failed to create or update Google Calendar event for meeting {meeting_id}"
            )

    except ClubMeeting.DoesNotExist:
        logger.error(f"Task: ClubMeeting with id {meeting_id} not found.")
    except Exception as e:
        logger.error(f"Task: Unexpected error processing meeting {meeting_id}: {e}")

        self.retry(exc=e)


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def delete_google_calendar_event_task(self, google_event_id, user_id):
    """
    Celery task to delete a Google Calendar event.
    """
    try:
        user = User.objects.get(pk=user_id)
        logger.info(
            f"Task: Processing deletion for Google Calendar event {google_event_id} by user {user_id}"
        )

        success = delete_google_event(google_event_id, user)

        if success:
            logger.info(
                f"Task: Successfully processed deletion for event {google_event_id}"
            )
        else:
            logger.error(
                f"Task: Failed to delete Google Calendar event {google_event_id}"
            )

    except User.DoesNotExist:
        logger.error(
            f"Task: User with id {user_id} not found for event deletion {google_event_id}."
        )
    except Exception as e:
        logger.error(f"Task: Unexpected error deleting event {google_event_id}: {e}")

        self.retry(exc=e)
