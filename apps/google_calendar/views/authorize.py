import os

from django.conf import settings
from drf_spectacular.utils import extend_schema
from google_auth_oauthlib.flow import Flow
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

os.environ["OAUTHLIB_INSECURE_TRANSPORT"] = "1"


GOOGLE_CALENDAR_SCOPES = [
    "https://www.googleapis.com/auth/calendar.readonly",
    "https://www.googleapis.com/auth/calendar.events",
    "https://www.googleapis.com/auth/userinfo.email",
    "openid",
]


class AuthorizeView(APIView):
    @extend_schema(
        tags=["google-calendar"],
        description="Authorize the user with Google Calendar.",
        responses={302: "Redirects to Google OAuth2 authorization URL."},
    )
    def get(self, request):
        client_config = {
            "web": {
                "client_id": settings.SOCIAL_AUTH_GOOGLE_OAUTH2_KEY,
                "client_secret": settings.SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET,
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token",
                "redirect_uris": [settings.GOOGLE_OAUTH2_REDIRECT_URI],
            }
        }

        flow = Flow.from_client_config(
            client_config,
            scopes=GOOGLE_CALENDAR_SCOPES,
            redirect_uri=settings.GOOGLE_OAUTH2_REDIRECT_URI,
        )
        authorization_url, state = flow.authorization_url(
            access_type="offline", include_granted_scopes="true", prompt="consent"
        )
        print(authorization_url, "AUTH URL\n\n\n")
        request.session["state"] = state

        return Response(
            {"detail": authorization_url},
            status=status.HTTP_200_OK,
        )
