import os

from django.conf import settings
from drf_spectacular.utils import extend_schema
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from apps.google_calendar.models import (
    GoogleCalendarCredentials,
)
from apps.accounts.user.models import User
from .authorize import GOOGLE_CALENDAR_SCOPES

os.environ["OAUTHLIB_INSECURE_TRANSPORT"] = "1"


class OAuth2CallbackView(APIView):
    @extend_schema(
        tags=["google-calendar"],
        description="Handle the OAuth2 callback and fetch user credentials.",
        responses={302: "Redirects to the calendar sync page."},
    )
    def get(self, request):
        state = request.session.get("state")
        if not state:
            return Response(
                {"detail": "Missing state in session."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        client_config = {
            "web": {
                "client_id": settings.SOCIAL_AUTH_GOOGLE_OAUTH2_KEY,
                "client_secret": settings.SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET,
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token",
                "redirect_uris": [settings.GOOGLE_OAUTH2_REDIRECT_URI],
            }
        }

        flow = Flow.from_client_config(
            client_config,
            scopes=GOOGLE_CALENDAR_SCOPES,
            state=state,
            redirect_uri=settings.GOOGLE_OAUTH2_REDIRECT_URI,
        )

        try:
            flow.fetch_token(authorization_response=request.build_absolute_uri())
        except Exception as e:
            return Response(
                {"detail": f"Error fetching token: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        credentials = flow.credentials

        try:
            service = build("oauth2", "v2", credentials=credentials)
            user_info = service.userinfo().get().execute()
            user_email = user_info.get("email")

            user = User.objects.filter(email=user_email).first()
            if not user:
                return Response(
                    {"detail": "User not found."},
                    status=status.HTTP_404_NOT_FOUND,
                )

            creds, _ = GoogleCalendarCredentials.objects.get_or_create(user=user)
            creds.set_credentials(credentials)

            credentials = creds.get_credentials()

            if credentials.expired and credentials.refresh_token:
                credentials.refresh(Request())
                creds.set_credentials(credentials)

            service = build("calendar", "v3", credentials=credentials)

            user.google_calendar_synced = True
            user.save()

            return Response(
                {
                    "message": "Calendar synced successfully!",
                }
            )

        except Exception as e:
            return Response(
                {"detail": f"Error fetching user info: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )
