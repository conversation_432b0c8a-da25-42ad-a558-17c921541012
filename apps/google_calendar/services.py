import uuid
import logging
from datetime import datetime, timedelta

from django.conf import settings
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from .models import GoogleCalendarCredentials


logger = logging.getLogger(__name__)


def get_user_credentials(user):
    """
    Retrieve stored Google credentials for a user, refreshing if necessary.
    Returns None if credentials are not found or cannot be refreshed.
    """
    try:
        creds_model = GoogleCalendarCredentials.objects.get(user=user)
        credentials = creds_model.get_credentials()

        if not credentials or not credentials.valid:
            if credentials and credentials.expired and credentials.refresh_token:
                try:
                    credentials.refresh(Request())

                    creds_model.set_credentials(credentials)
                    logger.info(
                        f"Refreshed Google Calendar credentials for user {user.id}"
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to refresh credentials for user {user.id}: {e}"
                    )

                    user.google_calendar_synced = False
                    user.save(update_fields=["google_calendar_synced"])
                    return None
            else:
                logger.warning(
                    f"No valid credentials or refresh token for user {user.id}. Requires re-authentication."
                )
                user.google_calendar_synced = False
                user.save(update_fields=["google_calendar_synced"])
                return None
        return credentials
    except GoogleCalendarCredentials.DoesNotExist:
        logger.warning(f"No GoogleCalendarCredentials found for user {user.id}")
        user.google_calendar_synced = False
        user.save(update_fields=["google_calendar_synced"])
        return None
    except Exception as e:
        logger.error(f"Error retrieving credentials for user {user.id}: {e}")
        return None


def build_calendar_service(credentials):
    """Builds the Google Calendar service object."""
    if not credentials:
        return None
    try:
        service = build("calendar", "v3", credentials=credentials)
        return service
    except Exception as e:
        logger.error(f"Failed to build Google Calendar service: {e}")
        return None


def create_or_update_google_event(meeting):
    """
    Creates or updates a Google Calendar event for the given ClubMeeting instance.
    Includes a Google Meet link.
    Returns the Google Calendar event ID and Google Meet link, or (None, None) on failure.
    """
    organizer = meeting.organizer
    if not organizer or not organizer.google_calendar_synced:
        logger.warning(
            f"Meeting {meeting.id} organizer {organizer.id if organizer else 'None'} has not synced Google Calendar."
        )
        return None, None

    credentials = get_user_credentials(organizer)
    if not credentials:
        logger.error(
            f"Could not retrieve valid credentials for organizer {organizer.id}"
        )
        return None, None

    service = build_calendar_service(credentials)
    if not service:
        logger.error(f"Could not build calendar service for organizer {organizer.id}")
        return None, None

    start_time_iso = meeting.start_time.isoformat()
    end_time_iso = meeting.end_time.isoformat()

    attendees = [{"email": organizer.email}]
    # TODO: Add meeting.attendees if needed, ensure they have emails

    event_body = {
        "summary": meeting.title,
        "description": meeting.description or "",
        "start": {
            "dateTime": start_time_iso,
            "timeZone": (
                str(meeting.start_time.tzinfo)
                if meeting.start_time.tzinfo
                else settings.TIME_ZONE
            ),
        },
        "end": {
            "dateTime": end_time_iso,
            "timeZone": (
                str(meeting.end_time.tzinfo)
                if meeting.end_time.tzinfo
                else settings.TIME_ZONE
            ),
        },
        "attendees": attendees,
        "reminders": {
            "useDefault": False,
            "overrides": [
                {"method": "email", "minutes": 60},
                {"method": "popup", "minutes": 15},
            ],
        },
        "conferenceData": {
            "createRequest": {
                "requestId": f"{meeting.id}-{uuid.uuid4()}",
                "conferenceSolutionKey": {"type": "hangoutsMeet"},
            }
        },
    }

    google_event_id = meeting.google_calendar_event_id
    meet_link = None
    new_event_id = None

    try:
        if google_event_id:
            logger.info(
                f"Updating Google Calendar event {google_event_id} for meeting {meeting.id}"
            )
            updated_event = (
                service.events()
                .update(
                    calendarId="primary",
                    eventId=google_event_id,
                    body=event_body,
                    conferenceDataVersion=1,
                    sendNotifications=True,
                )
                .execute()
            )
            meet_link = updated_event.get("hangoutLink")
            new_event_id = updated_event.get("id")
            logger.info(
                f"Successfully updated event {new_event_id}. Meet link: {meet_link}"
            )
        else:
            logger.info(f"Creating new Google Calendar event for meeting {meeting.id}")
            created_event = (
                service.events()
                .insert(
                    calendarId="primary",
                    body=event_body,
                    conferenceDataVersion=1,
                    sendNotifications=True,
                )
                .execute()
            )
            meet_link = created_event.get("hangoutLink")
            new_event_id = created_event.get("id")
            logger.info(
                f"Successfully created event {new_event_id}. Meet link: {meet_link}"
            )

        return new_event_id, meet_link

    except HttpError as error:
        logger.error(
            f"HTTP error interacting with Google Calendar API for meeting {meeting.id}: {error}"
        )

        if error.resp.status == 404 and google_event_id:
            logger.warning(
                f"Google event {google_event_id} not found for update. Will attempt to create a new one."
            )

            return None, None

        elif error.resp.status == 403:
            logger.error(
                f"Permission denied for Google Calendar API for user {organizer.id}. Marking as not synced."
            )
            organizer.google_calendar_synced = False
            organizer.save(update_fields=["google_calendar_synced"])
            return None, None
        return None, None
    except Exception as e:
        logger.error(
            f"An unexpected error occurred with Google Calendar API for meeting {meeting.id}: {e}"
        )
        return None, None


def delete_google_event(google_event_id, user):
    """Deletes a Google Calendar event."""
    if not google_event_id:
        logger.warning("No Google Calendar event ID provided for deletion.")
        return False

    credentials = get_user_credentials(user)
    if not credentials:
        logger.error(
            f"Could not retrieve valid credentials for user {user.id} to delete event {google_event_id}"
        )
        return False

    service = build_calendar_service(credentials)
    if not service:
        logger.error(
            f"Could not build calendar service for user {user.id} to delete event {google_event_id}"
        )
        return False

    try:
        logger.info(
            f"Attempting to delete Google Calendar event {google_event_id} for user {user.id}"
        )
        service.events().delete(
            calendarId="primary", eventId=google_event_id, sendNotifications=True
        ).execute()
        logger.info(f"Successfully deleted Google Calendar event {google_event_id}")
        return True
    except HttpError as error:
        if error.resp.status in [404, 410]:
            logger.warning(
                f"Google Calendar event {google_event_id} already deleted or not found."
            )
            return True
        logger.error(
            f"HTTP error deleting Google Calendar event {google_event_id}: {error}"
        )

        if error.resp.status == 403:
            logger.error(
                f"Permission denied to delete Google Calendar event {google_event_id} for user {user.id}."
            )

        return False
    except Exception as e:
        logger.error(
            f"An unexpected error occurred deleting Google Calendar event {google_event_id}: {e}"
        )
        return False
