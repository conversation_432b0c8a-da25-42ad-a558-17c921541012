from googleapiclient.discovery import build
from googleapiclient.errors import HttpError


def create_google_calendar_event(event, credentials):
    """
    Create an event in Google Calendar.
    """
    try:
        service = build("calendar", "v3", credentials=credentials)

        created_event = (
            service.events().insert(calendarId="primary", body=event).execute()
        )
        print(f"Event created: {created_event.get('htmlLink')}")
        return created_event.get("id")

    except HttpError as error:
        print(f"An error occurred: {error}")
