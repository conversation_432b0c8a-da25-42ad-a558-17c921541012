# Google Calendar Integration

This Django app integrates Google Calendar with Taj's mission reminder system, allowing users to sync their mission practice reminders with their Google Calendar.

## Features

- OAuth2 authentication with Google Calendar API
- Automatic event creation for mission practice reminders
- Secure credential storage
- Seamless integration with mission scheduling

## How It Works

### 1. User Authorization Flow

1. User clicks "Connect Google Calendar" in the UI
2. They are redirected to `/api/v1/google-calendar/authorize/`
3. Google OAuth consent screen appears
4. After granting access, user is redirected to `/api/v1/google-calendar/oauth2callback/`
5. Credentials are securely stored in `GoogleCalendarCredentials` model

```python
class GoogleCalendarCredentials(models.Model):
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    credentials = models.TextField()  # Encrypted OAuth2 credentials
```

### 2. Mission Reminder Integration

When a new mission is created with practice reminders:

1. The mission signal handler checks for Google Calendar credentials
2. If credentials exist, creates calendar events for each practice time
3. Events include:
   - Mission details
   - 30-minute duration
   - 10-minute popup reminder
   - UTC timezone handling

```python
# In signals.py
if credentials:
    event = {
        "summary": f"Mission: {instance.id}",
        "description": "Practice mission:",
        "start": {"dateTime": scheduled_datetime.isoformat(), "timeZone": "UTC"},
        "end": {"dateTime": event_end_time.isoformat(), "timeZone": "UTC"},
        "reminders": {
            "useDefault": False,
            "overrides": [{"method": "popup", "minutes": 10}]
        }
    }
    create_google_calendar_event(event, credentials)
```

## Setup

1. Configure Google OAuth2 credentials in `.env`:
```
GOOGLE_OAUTH2_CLIENT_ID = 'your-client-id'
GOOGLE_OAUTH2_CLIENT_SECRET = 'your-client-secret'
GOOGLE_OAUTH2_REDIRECT_URI = 'your-redirect-uri'
```

2. Add to `INSTALLED_APPS`:
```python
INSTALLED_APPS = [
    ...
    'apps.google_calendar',
]
```

3. Include URLs:
```python
urlpatterns = [
    ...
    path('google-calendar/', include('apps.google_calendar.urls')),
]
```

## Flow Diagram

```
┌──────────┐    ┌──────────┐    ┌──────────────┐
│  User    │    │  Google  │    │ Taj Backend  │
│          │    │  OAuth   │    │              │
└────┬─────┘    └────┬─────┘    └──────┬───────┘
     │               │                  │
     │   Authorize   │                  │
     │──────────────>│                  │
     │               │                  │
     │   Consent    │                  │
     │<──────────────│                  │
     │               │                  │
     │    Grant     │                  │
     │──────────────>│                  │
     │               │                  │
     │    Token     │   Save Token    │
     │───────────────────────────────>│
     │               │                  │
     │               │  Create Events  │
     │               │<─────────────────│
     │               │                  │
```

## Dependencies

- google-auth-oauthlib==1.2.1
- Django REST Framework
- Celery (for async event creation)
