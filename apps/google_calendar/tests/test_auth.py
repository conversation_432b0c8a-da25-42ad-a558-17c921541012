import json
import base64
from unittest.mock import patch, MagicMock

from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase, APIClient

from apps.accounts.user.models import User
from apps.google_calendar.models import GoogleCalendarCredentials


class GoogleCalendarAuthTests(APITestCase):
    def setUp(self):

        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="password123",
            username="testuser",
            fullname="Test User",
            role="club_manager",
        )
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

        self.authorize_url = reverse("authorize")
        self.callback_url = reverse("oauth2callback")

    def test_authorize_view_returns_url(self):
        """Test that the authorize view returns an authorization URL"""

        with patch("apps.google_calendar.views.authorize.Flow") as mock_flow:
            mock_flow_instance = MagicMock()
            mock_flow.from_client_config.return_value = mock_flow_instance
            mock_flow_instance.authorization_url.return_value = (
                "https://accounts.google.com/o/oauth2/auth?test=1",
                "test_state",
            )

            response = self.client.get(self.authorize_url)

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(
                response.data["detail"],
                "https://accounts.google.com/o/oauth2/auth?test=1",
            )

    def test_oauth2_callback_updates_user_synced_status(self):
        """Test that the OAuth2 callback updates the user's google_calendar_synced status"""

        self.assertFalse(self.user.google_calendar_synced)

        GoogleCalendarCredentials.objects.create(
            user=self.user,
            credentials='{"token": "fake_token", "refresh_token": "fake_refresh_token"}',
        )

        self.user.google_calendar_synced = True
        self.user.save()

        self.user.refresh_from_db()
        self.assertTrue(self.user.google_calendar_synced)
        self.assertTrue(
            GoogleCalendarCredentials.objects.filter(user=self.user).exists()
        )

    def test_profile_includes_google_calendar_synced(self):
        """Test that the profile serializer includes the google_calendar_synced field"""
        url = reverse("get-me")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("google_calendar_synced", response.data)
        self.assertFalse(response.data["google_calendar_synced"])

        self.user.google_calendar_synced = True
        self.user.save()

        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["google_calendar_synced"])
