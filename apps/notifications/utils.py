from typing import Union, Optional
import logging

from apps.accounts.user.models import User
from .models import Notification, NotificationsType, UserStatus

logger = logging.getLogger(__name__)


def create_notification(
    message: str,
    notification_type: str,
    target: Union[None, User] = None,
    description: Optional[str] = None,
    status: Optional[Union[str, UserStatus]] = UserStatus.MARKED_UNREAD,
):
    try:
        try:
            notification_type_obj, _ = NotificationsType.objects.get_or_create(
                name=notification_type
            )
        except NotificationsType.MultipleObjectsReturned:
            notification_type_obj = NotificationsType.objects.filter(
                name=notification_type
            ).first()
        notification = Notification.objects.create(
            message=message,
            notification_type=notification_type_obj,
            target=target,
            description=description,
        )
        if target:
            status = UserStatus.objects.create(user=target, status=status)
            notification.statuses.add(status)
        else:

            users = User.objects.all()
            statuses = []
            for user in users:
                status = UserStatus.objects.create(user=user, status=status)
                statuses.append(status)
            notification.statuses.add(*statuses)
        notification.save()
        return notification
    except Exception as e:
        logger.error(e)
        return None
