from django.contrib import admin

from .models import Notification, NotificationsType, UserStatus


class NotificationAdmin(admin.ModelAdmin):
    list_display = ("message", "target", "created", "updated")
    list_filter = ("message", "target", "created", "updated")


class NotificationsTypeAdmin(admin.ModelAdmin):
    list_display = ("name", "icon", "created", "updated")
    list_filter = ("name", "icon", "created", "updated")


class UserStatusAdmin(admin.ModelAdmin):
    list_display = ("status", "user", "created", "updated")
    list_filter = ("status", "user", "created", "updated")


admin.site.register(NotificationsType, NotificationsTypeAdmin)
admin.site.register(Notification, NotificationAdmin)
admin.site.register(UserStatus, UserStatusAdmin)
