# Generated by Django 4.2.4 on 2025-02-18 10:51

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="UserStatus",
            fields=[
                ("created", models.DateTimeField(auto_now_add=True)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("u", "unread"),
                            ("r", "read"),
                            ("d", "deleted"),
                            ("x", "disabled"),
                        ],
                        default="u",
                        max_length=1,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="status",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Status",
                "verbose_name_plural": "User Status",
            },
        ),
        migrations.CreateModel(
            name="NotificationsType",
            fields=[
                ("created", models.DateTimeField(auto_now_add=True)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                (
                    "icon",
                    models.ImageField(
                        blank=True, null=True, upload_to="notifications/icons/"
                    ),
                ),
            ],
            options={
                "verbose_name": "Notification Type",
                "verbose_name_plural": "Notification Types",
                "indexes": [
                    models.Index(
                        fields=["created"], name="notificatio_created_ec261c_idx"
                    )
                ],
            },
        ),
        migrations.CreateModel(
            name="Notification",
            fields=[
                ("created", models.DateTimeField(auto_now_add=True)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("message", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "notification_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to="notifications.notificationstype",
                    ),
                ),
                (
                    "statuses",
                    models.ManyToManyField(
                        related_name="notifications", to="notifications.userstatus"
                    ),
                ),
                (
                    "target",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Notification",
                "verbose_name_plural": "Notifications",
                "ordering": ["-created"],
                "indexes": [
                    models.Index(
                        fields=["created"], name="notificatio_created_5feed0_idx"
                    )
                ],
            },
        ),
    ]
