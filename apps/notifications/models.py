from django.db import models
from django.utils.translation import gettext_lazy as _

from core.abstract.models import AbstractModel
from apps.accounts.user.models import User


class NotificationManager(models.Manager):
    def for_user(self, user):
        return (
            super()
            .get_queryset()
            .exclude(
                statuses__in=UserStatus.objects.filter(
                    user=user, status=UserStatus.DELETED
                )
            )
        )


class NotificationsType(AbstractModel):
    name = models.CharField(max_length=100)
    icon = models.ImageField(upload_to="notifications/icons/", null=True, blank=True)

    def __str__(self):
        return f"{self.name}"

    class Meta:
        verbose_name = _("Notification Type")
        verbose_name_plural = _("Notification Types")
        indexes = [models.Index(fields=["created"])]


class UserStatus(AbstractModel):
    MARKED_READ = "r"
    MARKED_UNREAD = "u"
    DELETED = "d"
    DISABLED = "x"
    CHOICES = (
        (MARKED_UNREAD, _("unread")),
        (MARKED_READ, _("read")),
        (DELETED, _("deleted")),
        (DISABLED, _("disabled")),
    )

    status = models.CharField(max_length=1, choices=CHOICES, default=MARKED_UNREAD)
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="status",
    )

    def __str__(self):
        return f"{self.user} - {self.status}"

    class Meta:
        verbose_name = _("User Status")
        verbose_name_plural = _("User Status")


class Notification(AbstractModel):
    MARKED_READ = "r"
    MARKED_UNREAD = "u"
    DELETED = "d"
    message = models.CharField(max_length=100)
    description = models.TextField(null=True, blank=True)
    target = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="notifications",
        null=True,
        blank=True,
    )
    notification_type = models.ForeignKey(
        NotificationsType,
        on_delete=models.CASCADE,
        related_name="notifications",
    )
    statuses = models.ManyToManyField(UserStatus, related_name="notifications")

    def __str__(self):
        return f"{self.message}"

    objects = NotificationManager()

    class Meta:
        ordering = ["-created"]
        verbose_name = _("Notification")
        verbose_name_plural = _("Notifications")
        indexes = [models.Index(fields=["created"])]
