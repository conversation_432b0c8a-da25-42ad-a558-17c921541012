import json
from channels.generic.websocket import AsyncWebsocketConsumer
import logging
from asgiref.sync import sync_to_async
from django.utils.translation import gettext_lazy as _

logger = logging.getLogger(__name__)


class NotificationConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.user = self.scope["user"]
        self.group_name = "notifications"
        self.private_group_name = (
            f"user_{self.user.id}" if self.user.is_authenticated else None
        )
        logger.info(f"User {self.user} connected to notifications")

        if self.channel_layer:

            await self.channel_layer.group_add(self.group_name, self.channel_name)

            if self.private_group_name:
                await self.channel_layer.group_add(
                    self.private_group_name, self.channel_name
                )

            await self.accept()

            await self.send(
                text_data=json.dumps(
                    {
                        "type": "connection_established",
                        "message": _("Connected to notification service"),
                        "is_authenticated": self.user.is_authenticated,
                    }
                )
            )
        else:
            logger.error("Channel layer is None")
            await self.close()

    async def disconnect(self, close_code):
        if self.channel_layer is not None:
            await self.channel_layer.group_discard(self.group_name, self.channel_name)
            if self.private_group_name:
                await self.channel_layer.group_discard(
                    self.private_group_name, self.channel_name
                )
            logger.info(f"User {self.user} disconnected from notifications")
        else:
            logger.error("Channel layer is None")

    async def receive(self, text_data):
        try:
            text_data_json = json.loads(text_data)
            message = text_data_json.get("message", "")

            if not message:
                await self.send(
                    text_data=json.dumps(
                        {"type": "error", "message": _("Message content is required")}
                    )
                )
                return

            if self.channel_layer:

                await self.channel_layer.group_send(
                    self.group_name,
                    {
                        "type": "notification_message",
                        "message": message,
                        "sender_id": (
                            str(self.user.id) if self.user.is_authenticated else None
                        ),
                    },
                )

                if self.private_group_name:
                    await self.channel_layer.group_send(
                        self.private_group_name,
                        {
                            "type": "private_notification_message",
                            "message": message,
                        },
                    )

                await self.send(
                    text_data=json.dumps(
                        {
                            "type": "message_received",
                            "message": _("Message received and broadcast"),
                        }
                    )
                )
        except json.JSONDecodeError:
            await self.send(
                text_data=json.dumps(
                    {"type": "error", "message": _("Invalid JSON format")}
                )
            )
        except Exception as e:
            logger.error(f"Error in receive: {str(e)}")
            await self.send(
                text_data=json.dumps(
                    {
                        "type": "error",
                        "message": _("An error occurred processing your message"),
                    }
                )
            )

    async def notification_message(self, event):
        message = event["message"]
        await self.send(
            text_data=json.dumps(
                {
                    "type": "notification",
                    "message": message,
                    "sender_id": event.get("sender_id"),
                }
            )
        )

    async def private_notification_message(self, event):
        message = event["message"]
        await self.send(
            text_data=json.dumps({"type": "private_notification", "message": message})
        )

    async def send_notification(self, event):
        message = event["message"]
        await self.send(
            text_data=json.dumps({"type": "notification", "message": message})
        )
