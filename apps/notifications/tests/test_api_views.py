from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

from apps.accounts.user.models import User as CustomUser
from apps.notifications.models import (
    Notification,
    UserStatus,
    NotificationsType,
)


class NotificationAPITests(APITestCase):
    def setUp(self):
        self.user = CustomUser.objects.create(
            username="testuser",
            password="password",
            email="<EMAIL>",
        )
        self.user.set_password("password")
        self.user.save()

        self.admin = CustomUser.objects.create_superuser(
            username="adminuser", password="password", email="<EMAIL>"
        )
        self.admin.set_password("password")
        self.admin.save()

        self.other_user = CustomUser.objects.create(
            username="otheruser",
            password="password",
            email="<EMAIL>",
        )
        self.other_user.set_password("password")
        self.other_user.save()

        self.notification_type = NotificationsType.objects.create(name="Test Type")
        self.notification = Notification.objects.create(
            message="Test Notification",
            target=self.user,
            notification_type=self.notification_type,
        )
        self.notification.statuses.add(
            UserStatus.objects.create(user=self.user, status=UserStatus.MARKED_UNREAD)
        )

        self.other_notification = Notification.objects.create(
            message="Other User Notification",
            target=self.other_user,
            notification_type=self.notification_type,
        )
        self.other_notification.statuses.add(
            UserStatus.objects.create(
                user=self.other_user, status=UserStatus.MARKED_UNREAD
            )
        )

        self.public_notification = Notification.objects.create(
            message="Public Notification",
            target=None,
            notification_type=self.notification_type,
            description="This is a public notification",
        )

        for user in [self.user, self.other_user, self.admin]:
            self.public_notification.statuses.add(
                UserStatus.objects.create(user=user, status=UserStatus.MARKED_UNREAD)
            )

        self.client.force_authenticate(user=self.user)

    def test_list_notifications(self):
        url = reverse("notification-list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        messages = [n["message"] for n in response.json()["results"]]
        self.assertIn("Test Notification", messages)
        self.assertIn("Public Notification", messages)

    def test_list_notifications_with_filters(self):
        url = reverse("notification-list")

        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        private_found = False
        for notification in response.json()["results"]:
            if notification["message"] == "Test Notification":
                private_found = True
                break
        self.assertTrue(private_found)

        public_found = False
        for notification in response.json()["results"]:
            if notification["message"] == "Public Notification":
                public_found = True
                break
        self.assertTrue(public_found)

    def test_update_notification_status(self):
        url = reverse("update-notification-status")
        data = {
            "notification_ids": [self.notification.pk],
            "action": UserStatus.MARKED_READ,
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        status_obj = UserStatus.objects.get(
            user=self.user, notifications=self.notification
        )
        self.assertEqual(status_obj.status, UserStatus.MARKED_READ)

    def test_update_notification_status_invalid_action(self):
        url = reverse("update-notification-status")
        data = {
            "notification_ids": [self.notification.pk],
            "action": "invalid_action",
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_update_notification_status_other_user_notification(self):
        url = reverse("update-notification-status")
        data = {
            "notification_ids": [self.other_notification.pk],
            "action": UserStatus.MARKED_READ,
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        other_user_status = UserStatus.objects.get(
            user=self.other_user, notifications=self.other_notification
        )
        self.assertEqual(other_user_status.status, UserStatus.MARKED_UNREAD)

    def test_update_all_notification_status(self):
        url = reverse("update-all-notification-status")
        data = {"action": UserStatus.MARKED_READ}
        response = self.client.put(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        user_status = UserStatus.objects.get(
            user=self.user, notifications=self.notification
        )
        self.assertEqual(user_status.status, UserStatus.MARKED_READ)

        other_user_status = UserStatus.objects.get(
            user=self.other_user, notifications=self.other_notification
        )
        self.assertEqual(other_user_status.status, UserStatus.MARKED_UNREAD)

    def test_update_all_notification_status_invalid_action(self):
        url = reverse("update-all-notification-status")
        data = {"action": "invalid_action"}
        response = self.client.put(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_list_notifications_admin(self):
        self.client.force_authenticate(user=self.admin)
        url = reverse("notification-list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        admin_test_notifs = [
            n
            for n in response.json()["results"]
            if n["message"] == "Public Notification"
        ]
        self.assertTrue(len(admin_test_notifs) > 0)

    def test_notification_access(self):

        url = reverse("notification-list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.client.force_authenticate(user=self.other_user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        private_found = False
        for notification in response.json()["results"]:
            if notification["message"] == "Test Notification":
                private_found = True
                break
        self.assertFalse(private_found)
