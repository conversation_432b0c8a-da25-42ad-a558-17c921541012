from channels.testing import WebsocketCommunicator
from config.asgi import application
from django.test import TestCase
from rest_framework.authtoken.models import Token
from channels.db import database_sync_to_async
import json

from apps.accounts.user.models import User
from apps.notifications.models import Notification, NotificationsType


class NotificationConsumerTest(TestCase):
    @database_sync_to_async
    def create_user(self, username, email, password):
        user = User.objects.create(username=username, email=email)
        user.set_password(password)
        user.save()
        return user

    @database_sync_to_async
    def create_token(self, user):
        return Token.objects.create(user=user)

    @database_sync_to_async
    def create_notification(self, message, user=None, description=None):
        notification_type, _ = NotificationsType.objects.get_or_create(name="Test Type")
        notification = Notification.objects.create(
            message=message,
            notification_type=notification_type,
            target=user,
            description=description,
        )
        return notification

    async def test_connect_with_token(self):
        """Test that a user can connect to the websocket with a valid token."""
        user = await self.create_user("testuser", "<EMAIL>", "testpass")
        token = await self.create_token(user)
        communicator = WebsocketCommunicator(
            application, f"/ws/notifications/?token={token}"
        )
        try:
            connected, subprotocol = await communicator.connect()

            self.assertTrue(connected)
        finally:
            await communicator.disconnect()

    async def test_connect_without_token(self):
        """Test connection attempt without a token."""
        communicator = WebsocketCommunicator(application, f"/ws/notifications/")
        try:
            connected, subprotocol = await communicator.connect()

            self.assertTrue(connected)
        finally:
            await communicator.disconnect()

    async def test_send_message(self):
        """Test sending a message through the websocket."""
        user = await self.create_user("testuser2", "<EMAIL>", "testpass")
        token = await self.create_token(user)
        communicator = WebsocketCommunicator(
            application, f"/ws/notifications/?token={token}"
        )
        try:
            connected, subprotocol = await communicator.connect()
            self.assertTrue(connected)

            message_data = {"message": "Test message"}
            await communicator.send_json_to(message_data)

        finally:
            await communicator.disconnect()

    async def test_connection_established_message(self):
        """Test that the consumer sends a connection confirmation message."""
        user = await self.create_user("testuser3", "<EMAIL>", "testpass")
        token = await self.create_token(user)
        communicator = WebsocketCommunicator(
            application, f"/ws/notifications/?token={token}"
        )
        try:
            connected, subprotocol = await communicator.connect()
            self.assertTrue(connected)

            response = await communicator.receive_json_from()
            self.assertEqual(response["type"], "connection_established")
            self.assertEqual(response["message"], "Connected to notification service")
            # Note: auth may not work in test environment, so we don't assert on its value
        finally:
            await communicator.disconnect()
