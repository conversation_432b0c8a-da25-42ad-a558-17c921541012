from django.test import TestCase

from apps.accounts.user.models import User
from apps.notifications.models import (
    Notification,
    UserStatus,
    NotificationsType,
)
from apps.notifications.utils import create_notification


class NotificationUtilsTest(TestCase):
    def setUp(self):
        self.user = User.objects.create(
            username="testuser",
            email="<EMAIL>",
        )
        self.user.set_password("password")
        self.user.save()

        self.other_user = User.objects.create(
            username="otheruser",
            email="<EMAIL>",
        )
        self.other_user.set_password("password")
        self.other_user.save()

    def test_create_notification_for_user(self):
        notification = create_notification(
            message="Test User Notification",
            notification_type="Test Type",
            target=self.user,
            description="This is a test notification",
        )

        self.assertIsNotNone(notification)
        self.assertEqual(notification.message, "Test User Notification")
        self.assertEqual(notification.description, "This is a test notification")
        self.assertEqual(notification.target, self.user)

        notification_type = NotificationsType.objects.get(name="Test Type")
        self.assertEqual(notification.notification_type, notification_type)

        user_status = UserStatus.objects.get(user=self.user, notifications=notification)
        self.assertEqual(user_status.status, UserStatus.MARKED_UNREAD)

    def test_create_notification_public(self):
        notification = create_notification(
            message="Test Public Notification",
            notification_type="Public Type",
            target=None,
            description="This is a public notification",
        )

        self.assertIsNotNone(notification)
        self.assertEqual(notification.message, "Test Public Notification")
        self.assertEqual(notification.description, "This is a public notification")
        self.assertIsNone(notification.target)

        for user in [self.user, self.other_user]:

            status_exists = UserStatus.objects.filter(
                user=user, notifications=notification
            ).exists()
            self.assertTrue(status_exists)

    def test_create_notification_with_custom_status(self):
        notification = create_notification(
            message="Test Status Notification",
            notification_type="Status Type",
            target=self.user,
            description="This is a notification with custom status",
            status=UserStatus.MARKED_READ,
        )

        self.assertIsNotNone(notification)

        user_status = UserStatus.objects.get(user=self.user, notifications=notification)
        self.assertEqual(user_status.status, UserStatus.MARKED_READ)

    def test_create_notification_with_existing_type(self):

        notification_type = NotificationsType.objects.create(name="Existing Type")

        notification = create_notification(
            message="Test Existing Type",
            notification_type="Existing Type",
            target=self.user,
        )

        self.assertIsNotNone(notification)
        self.assertEqual(notification.notification_type, notification_type)

        self.assertEqual(
            NotificationsType.objects.filter(name="Existing Type").count(), 1
        )

    def test_create_notification_with_invalid_data(self):

        non_existent_user = type("obj", (object,), {"id": 99999})

        notification = create_notification(
            message="Test Invalid Data",
            notification_type="Invalid Type",
            target=non_existent_user,
        )

        self.assertIsNone(notification)
