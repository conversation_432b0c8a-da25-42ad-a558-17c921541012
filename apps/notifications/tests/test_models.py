from django.test import TestCase

from apps.accounts.user.models import User
from apps.notifications.models import (
    Notification,
    UserStatus,
    NotificationsType,
)


class NotificationModelsTest(TestCase):
    def setUp(self):
        self.user = User.objects.create(
            username="testuser",
            email="<EMAIL>",
        )
        self.user.set_password("password")
        self.user.save()

        self.other_user = User.objects.create(
            username="otheruser",
            email="<EMAIL>",
        )

        self.notification_type = NotificationsType.objects.create(name="Test Type")

        self.notification = Notification.objects.create(
            message="Test Notification",
            notification_type=self.notification_type,
            target=self.user,
            description="Test Description",
        )

        self.status = UserStatus.objects.create(
            user=self.user, status=UserStatus.MARKED_UNREAD
        )

        self.notification.statuses.add(self.status)

    def test_notification_type_str(self):
        self.assertEqual(str(self.notification_type), "Test Type")

    def test_user_status_str(self):
        self.assertEqual(str(self.status), f"{self.user} - {UserStatus.MARKED_UNREAD}")

    def test_notification_str(self):
        self.assertEqual(str(self.notification), "Test Notification")

    def test_notification_manager_for_user(self):

        deleted_notification = Notification.objects.create(
            message="Deleted Notification",
            notification_type=self.notification_type,
            target=self.user,
        )
        deleted_status = UserStatus.objects.create(
            user=self.user, status=UserStatus.DELETED
        )
        deleted_notification.statuses.add(deleted_status)

        user_notifications = Notification.objects.for_user(self.user)
        self.assertIn(self.notification, user_notifications)
        self.assertNotIn(deleted_notification, user_notifications)

    def test_notification_statuses_relationship(self):

        other_status = UserStatus.objects.create(
            user=self.other_user, status=UserStatus.MARKED_READ
        )
        self.notification.statuses.add(other_status)

        self.assertEqual(self.notification.statuses.count(), 2)
        self.assertIn(self.status, self.notification.statuses.all())
        self.assertIn(other_status, self.notification.statuses.all())

    def test_user_status_choices(self):

        for status_choice in [
            UserStatus.MARKED_READ,
            UserStatus.MARKED_UNREAD,
            UserStatus.DELETED,
            UserStatus.DISABLED,
        ]:
            status = UserStatus.objects.create(user=self.user, status=status_choice)
            self.assertEqual(status.status, status_choice)

    def test_notification_ordering(self):

        new_notification = Notification.objects.create(
            message="Newer Notification",
            notification_type=self.notification_type,
            target=self.user,
        )
        new_status = UserStatus.objects.create(
            user=self.user, status=UserStatus.MARKED_UNREAD
        )
        new_notification.statuses.add(new_status)

        notifications = Notification.objects.filter(
            statuses__user=self.user,
            message__in=["Test Notification", "Newer Notification"],
        ).order_by("-created")

        self.assertEqual(notifications.first().message, "Newer Notification")
        self.assertEqual(notifications.last().message, "Test Notification")
