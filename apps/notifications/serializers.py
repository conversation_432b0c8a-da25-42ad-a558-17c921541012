from apps.accounts.user.serializers.user import UserSerializer
from rest_framework import serializers
from decouple import config
from django.utils.translation import gettext as _
import logging

from .models import Notification, NotificationsType, UserStatus

logger = logging.getLogger(__name__)


class NotificationTypeSerializer(serializers.ModelSerializer):
    icon = serializers.SerializerMethodField()

    class Meta:
        model = NotificationsType
        fields = ["name", "icon"]

    def get_icon(self, obj):
        try:
            return (
                self.context["request"].build_absolute_uri(obj.icon.url)
                if obj.icon
                else None
            )
        except:
            return config("BASE_URL") + obj.icon.url if obj.icon else None


class UserStatusSerializer(serializers.ModelSerializer):
    user = UserSerializer()

    class Meta:
        model = UserStatus
        fields = ["status", "user", "created"]
        read_only_fields = ["user"]


class NotificationSerializer(serializers.ModelSerializer):
    notification_type = NotificationTypeSerializer()
    description = serializers.Char<PERSON>ield(required=False)
    status = serializers.SerializerMethodField()
    private = serializers.SerializerMethodField()

    class Meta:
        model = Notification
        fields = [
            "id",
            "message",
            "description",
            "notification_type",
            "status",
            "private",
            "created",
        ]
        read_only_fields = [
            "id",
            "message",
            "description",
            "notification_type",
        ]

    def get_status(self, obj):
        try:
            return (
                obj.statuses.filter(user=self.context["request"].user).first().status
                or "u"
            )
        except Exception as e:
            logger.error(f"An error occurred: {e}")
            return "u"

    def get_private(self, obj):
        return True if obj.target else False


class NotificationSocketSerializer(serializers.ModelSerializer):
    notification_type = NotificationTypeSerializer()
    description = serializers.CharField(required=False)
    status = serializers.SerializerMethodField()
    private = serializers.SerializerMethodField()

    class Meta:
        model = Notification
        fields = [
            "message",
            "description",
            "notification_type",
            "status",
            "private",
            "created",
        ]
        read_only_fields = [
            "message",
            "description",
            "notification_type",
        ]

    def get_status(self, obj):
        try:
            return (
                obj.statuses.filter(user=self.context["request"].user).first().status
                or "u"
            )
        except Exception as e:
            return "u"

    def get_private(self, obj):
        return True if obj.target else False


class UpdateNotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserStatus
        fields = ["status"]


class UpdateOneNotificationSerializer(serializers.Serializer):
    status = serializers.CharField(max_length=1)

    def validate_status(self, value):
        if value not in [
            UserStatus.MARKED_READ,
            UserStatus.MARKED_UNREAD,
            UserStatus.DELETED,
        ]:
            raise serializers.ValidationError(_("Invalid status"))
        return value


class BulkUpdateSelectedNotificationsSerializer(serializers.Serializer):
    notification_ids = serializers.ListField(
        child=serializers.UUIDField(), required=True
    )
    action = serializers.ChoiceField(
        choices=[
            UserStatus.MARKED_READ,
            UserStatus.MARKED_UNREAD,
            UserStatus.DELETED,
        ],
        required=True,
    )


class ActionSerializer(serializers.Serializer):
    action = serializers.ChoiceField(
        choices=[
            UserStatus.MARKED_READ,
            UserStatus.MARKED_UNREAD,
            UserStatus.DELETED,
        ],
        required=True,
    )
