from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from django.db.models.signals import post_save
from django.dispatch import receiver

from apps.notifications.serializers import NotificationSocketSerializer
from .models import Notification


@receiver(post_save, sender=Notification)
def notification_created(sender, instance, created, **kwargs):
    channel_layer = get_channel_layer()
    if created:
        group_name = "notifications"
        target_user_id = f"user_{instance.target.id}" if instance.target else None

        if channel_layer:
            if target_user_id:
                async_to_sync(channel_layer.group_send)(
                    target_user_id,
                    {
                        "type": "private_notification_message",
                        "message": (NotificationSocketSerializer(instance).data),
                    },
                )
            else:
                async_to_sync(channel_layer.group_send)(
                    group_name,
                    {
                        "type": "send_notification",
                        "message": (NotificationSocketSerializer(instance).data),
                    },
                )
