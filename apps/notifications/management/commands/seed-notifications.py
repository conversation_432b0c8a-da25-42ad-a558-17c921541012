import threading
from django.core.management.base import BaseCommand
from django.core.files import File
from django.db import connection
from apps.notifications.models import NotificationsType


class SeedNotificationsThread(threading.Thread):
    def __init__(self, stdout, style):
        threading.Thread.__init__(self)
        self.stdout = stdout
        self.style = style
        self.notification_types = [
            {
                "name": "Feedback Reminder",
                "icon": "public/icons/IconButton.png",
            },
            {
                "name": "Critical",
                "icon": "public/icons/IconButton.png",
            },
            {
                "name": "Invitation",
                "icon": "public/icons/IconButton.png",
            },
            {
                "name": "Process",
                "icon": "public/icons/IconButton.png",
            },
            {
                "name": "Settings",
                "icon": "public/icons/IconButton.png",
            },
            {
                "name": "Appointment Reminder",
                "icon": "public/icons/IconButton.png",
            },
            {
                "name": "Completed",
                "icon": "public/icons/IconButton.png",
            },
            {
                "name": "Failed",
                "icon": "public/icons/IconButton.png",
            },
            {
                "name": "Task Assignment",
                "icon": "public/icons/IconButton.png",
            },
            {
                "name": "Comment",
                "icon": "public/icons/IconButton.png",
            },
            {
                "name": "New Chat Message",
                "icon": "public/icons/IconButton.png",
            },
            {
                "name": "New Chat Room",
                "icon": "public/icons/IconButton.png",
            },
        ]
        self._stop_event = threading.Event()

    def write(self, message, style_func=None):
        if style_func:
            self.stdout.write(style_func(message))
        else:
            self.stdout.write(message)

    def stop(self):
        self._stop_event.set()

    def stopped(self):
        return self._stop_event.is_set()

    def run(self):

        connection.close()

        try:
            self.write("Starting notification types seeding...")
            total = len(self.notification_types)

            for index, notif_type in enumerate(self.notification_types, 1):
                if self.stopped():
                    self.write("Seeding process cancelled.", self.style.WARNING)
                    return

                try:
                    if not NotificationsType.objects.filter(
                        name=notif_type["name"]
                    ).exists():
                        with open(notif_type["icon"], "rb") as icon_file:
                            NotificationsType.objects.get_or_create(
                                name=notif_type["name"],
                                icon=File(icon_file),
                            )
                        self.write(
                            f"Created notification type: {notif_type['name']} ({index}/{total})"
                        )
                    else:
                        self.write(
                            f"Notification type already exists: {notif_type['name']} ({index}/{total})"
                        )
                except Exception as e:
                    self.write(
                        f"Error processing notification type {notif_type['name']}: {str(e)}",
                        self.style.ERROR,
                    )

            self.write("Successfully seeded notification types.", self.style.SUCCESS)

        except Exception as e:
            self.write(f"Error seeding notification types: {str(e)}", self.style.ERROR)
        finally:

            connection.close()


class Command(BaseCommand):
    help = "Seed the most used notification types"

    def handle(self, *args, **kwargs):
        self.stdout.write("Starting notification types seeding in background...")

        seeder = SeedNotificationsThread(self.stdout, self.style)
        seeder.start()

        self.stdout.write(
            self.style.SUCCESS(
                "Seeding process started in background. Check logs for progress."
            )
        )
