from rest_framework import generics, status
from rest_framework.permissions import IsAdminUser, IsAuthenticated
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema
from drf_spectacular.types import OpenApiTypes
from drf_spectacular.openapi import OpenApiParameter  # type: ignore
from django.utils.translation import gettext_lazy as _
from django.db.models import Q

from core.decorators.error_handler import api_error_handler
from .models import Notification, UserStatus
from .serializers import (
    ActionSerializer,
    NotificationSerializer,
    UpdateNotificationSerializer,
    BulkUpdateSelectedNotificationsSerializer,
)
from .permissions import NotificationOwner
from core.abstract.paginations import MetaPageNumberPagination


class NotificationUpdateView(generics.UpdateAPIView):
    queryset = Notification.objects.all()
    serializer_class = UpdateNotificationSerializer
    permission_classes = [IsAuthenticated, NotificationOwner]

    @api_error_handler
    @extend_schema(
        tags=["Notifications"],
        summary="Update notification",
        description="Update a notification for the authenticated user.",
    )
    def put(self, request, *args, **kwargs):
        if request.user == self.get_object().target or request.user.is_superuser:
            super().put(request, *args, **kwargs)
            instance = self.get_object()
            serializer = NotificationSerializer(instance, context={"request": request})
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(
            {"error": _("You do not have permission to perform this action.")},
            status=status.HTTP_403_FORBIDDEN,
        )

    @api_error_handler
    @extend_schema(
        tags=["Notifications"],
        summary="Update notification",
        description="Update a notification for the authenticated user.",
    )
    def patch(self, request, *args, **kwargs):
        if request.user == self.get_object().target or request.user.is_superuser:
            super().patch(request, *args, **kwargs)
            instance = self.get_object()
            serializer = NotificationSerializer(instance, context={"request": request})
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(
            {"error": _("You do not have permission to perform this action.")},
            status=status.HTTP_403_FORBIDDEN,
        )


class NotificationListView(generics.ListAPIView):
    queryset = Notification.objects.all()
    serializer_class = NotificationSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = MetaPageNumberPagination

    def get_queryset(self):
        user_notifications = self.queryset.filter(
            Q(target=self.request.user) | Q(target=None),
            statuses__status__in=[
                UserStatus.MARKED_UNREAD,
                UserStatus.MARKED_READ,
            ],
        )
        return user_notifications

    @api_error_handler
    @extend_schema(
        tags=["Notifications"],
        summary="List notifications",
        description="List all notifications for the authenticated user.",
        parameters=[
            OpenApiParameter(
                name="public",
                type=OpenApiTypes.BOOL,
                location=OpenApiParameter.QUERY,
                required=False,
                description="Include public notifications.",
            ),
            OpenApiParameter(
                name="private",
                type=OpenApiTypes.BOOL,
                location=OpenApiParameter.QUERY,
                required=False,
                description="Include private notifications.",
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        public = request.query_params.get("public", False)
        private = request.query_params.get("private", False)
        queryset = self.get_queryset()
        if private:
            queryset = queryset.exclude(target=None)
        if public:
            queryset = queryset.exclude(target=request.user)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class NotificationDeleteView(generics.DestroyAPIView):
    queryset = Notification.objects.all()
    serializer_class = NotificationSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]

    @api_error_handler
    @extend_schema(
        tags=["Notifications"],
        summary="Delete notification",
        description="Delete a notification for the authenticated user.",
    )
    def delete(self, request, *args, **kwargs):
        if request.user == self.get_object().target:
            return super().delete(request, *args, **kwargs)
        return Response(
            {"error": _("You do not have permission to perform this action.")},
            status=status.HTTP_403_FORBIDDEN,
        )


class UpdateNotificationStatusView(generics.GenericAPIView):
    queryset = Notification.objects.all()
    serializer_class = BulkUpdateSelectedNotificationsSerializer
    permission_classes = [IsAuthenticated]

    @api_error_handler
    @extend_schema(
        tags=["Notifications"],
        summary="Update notifications status",
        description="Update notifications status (read/unread/deleted) for the authenticated user.",
        request=BulkUpdateSelectedNotificationsSerializer,
    )
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        action = request.data.get("action")
        notification_ids = serializer.validated_data.get("notification_ids")

        if action not in [
            UserStatus.MARKED_READ,
            UserStatus.MARKED_UNREAD,
            UserStatus.DELETED,
        ]:
            return Response(
                {"error": _("Invalid action specified.")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        user_statuses = UserStatus.objects.filter(
            user=request.user, notifications__id__in=notification_ids
        )
        user_statuses.update(status=action)

        return Response(
            {"error": _(f"Selected notifications marked as {action}.")},
            status=status.HTTP_200_OK,
        )


class UpdateAllNotificationStatusView(generics.GenericAPIView):
    queryset = Notification.objects.all()
    serializer_class = BulkUpdateSelectedNotificationsSerializer
    permission_classes = [IsAuthenticated]

    @api_error_handler
    @extend_schema(
        tags=["Notifications"],
        summary="Mark all notifications as read or unread",
        description="Mark all notifications as read or unread for the authenticated user.",
        request=ActionSerializer,
    )
    def put(self, request, *args, **kwargs):
        action = request.data.get("action")

        if action not in [
            UserStatus.MARKED_READ,
            UserStatus.MARKED_UNREAD,
            UserStatus.DELETED,
        ]:
            return Response(
                {"error": _("Invalid action specified.")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        user_statuses = UserStatus.objects.filter(user=request.user)
        user_statuses.update(status=action)

        return Response(
            {"error": _(f"All notifications marked as {action}.")},
            status=status.HTTP_200_OK,
        )
