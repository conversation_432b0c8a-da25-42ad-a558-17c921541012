from django.db import models
from django.conf import settings
from django.core.validators import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MaxValueValidator
from django.utils import timezone
from core.abstract.models import AbstractModel
from apps.domains.models import Domain
from apps.okrs.models import SixWeekPeriod
from apps.okrs.constants import (
    MONDAY,
    TUESDAY,
    WEDNESDAY,
    THURSDAY,
    FRIDAY,
    SATURDAY,
    SUNDAY,
)


class Goal(AbstractModel):
    PRIORITY_HIGH = "high"
    PRIORITY_LOW = "low"
    PRIORITY_CHOICES = [
        (PRIORITY_HIGH, "High"),
        (PRIORITY_LOW, "Low"),
    ]

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="goals",
    )
    six_week_period = models.ForeignKey(
        SixWeekPeriod, on_delete=models.CASCADE, related_name="goals"
    )
    title = models.TextField()
    priority = models.CharField(
        max_length=4, choices=PRIORITY_CHOICES, default=PRIORITY_LOW
    )

    class Meta:
        ordering = ["priority", "-created"]
        constraints = [
            models.UniqueConstraint(
                fields=["six_week_period", "title"],
                name="unique_period_goal",
            ),
        ]

    def __str__(self):
        return f"{self.title} ({self.six_week_period.title})"

    def set_priority(self, priority):
        if priority in [self.PRIORITY_HIGH, self.PRIORITY_LOW]:
            self.priority = priority
            self.save()
            return True
        return False


class DailyGoal(AbstractModel):
    PRIORITY_HIGH = "high"
    PRIORITY_LOW = "low"
    PRIORITY_CHOICES = [
        (PRIORITY_HIGH, "High"),
        (PRIORITY_LOW, "Low"),
    ]

    weekly_mission = models.ForeignKey(
        "WeeklyMission", on_delete=models.CASCADE, related_name="daily_goals"
    )
    title = models.TextField()
    priority = models.CharField(
        max_length=4, choices=PRIORITY_CHOICES, default=PRIORITY_LOW
    )
    completion_date = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ["priority", "-created"]
        constraints = [
            models.UniqueConstraint(
                fields=["weekly_mission", "title"],
                name="unique_mission_daily_goal",
            ),
        ]

    def __str__(self):
        return f"{self.title} (Week {self.weekly_mission.week_number})"

    def is_completed(self):
        if not self.weekly_mission or not self.weekly_mission.practice_days:
            return False
        progress_entries = self.daily_progress.filter(completed=True).count()
        total_entries = self.daily_progress.count()
        return progress_entries == total_entries and total_entries > 0

    def mark_completed(self):
        if not self.is_completed():
            self.completion_date = timezone.now()
            self.save()
            return True
        return False

    def mark_incomplete(self):
        if self.is_completed():
            self.completion_date = None
            self.save()
            return True
        return False

    def set_priority(self, priority):
        if priority in [self.PRIORITY_HIGH, self.PRIORITY_LOW]:
            if priority == self.PRIORITY_HIGH:
                day_numbers = list(
                    self.daily_progress.values_list("day_number", flat=True)
                )

                if day_numbers:
                    for day_number in day_numbers:
                        other_goals = DailyGoal.objects.filter(
                            weekly_mission=self.weekly_mission,
                            daily_progress__day_number=day_number,
                            priority=self.PRIORITY_HIGH,
                        ).exclude(id=self.id)

                        for goal in other_goals:
                            goal.priority = self.PRIORITY_LOW
                            goal.save(update_fields=["priority"])

            self.priority = priority
            self.save(update_fields=["priority"] if self.id else None)
            return True
        return False

    def get_progress_for_day(self, day_number):
        return self.daily_progress.filter(day_number=day_number).first()

    def get_overall_completion_percentage(self):
        progress_entries = self.daily_progress.filter(completed=True).count()
        total_entries = self.daily_progress.count()
        if total_entries == 0:
            return 0
        return (progress_entries / total_entries) * 100

    def create_daily_progress_entries(self):
        if self.weekly_mission and self.weekly_mission.practice_days:
            existing_days = set(
                self.daily_progress.values_list("day_number", flat=True)
            )

            for day_number in self.weekly_mission.practice_days:
                if day_number not in existing_days:
                    DailyGoalProgress.objects.create(
                        daily_goal=self, day_number=day_number
                    )


class DailyGoalProgress(AbstractModel):
    daily_goal = models.ForeignKey(
        DailyGoal, on_delete=models.CASCADE, related_name="daily_progress"
    )
    day_number = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(7)]
    )
    completed = models.BooleanField(default=False)
    completion_date = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ["day_number"]
        constraints = [
            models.UniqueConstraint(
                fields=["daily_goal", "day_number"],
                name="unique_daily_goal_day",
            ),
            models.CheckConstraint(
                check=models.Q(day_number__gte=1) & models.Q(day_number__lte=7),
                name="valid_day_number",
            ),
        ]

    def __str__(self):
        return f"{self.daily_goal.title} (Day {self.day_number})"

    def mark_completed(self):
        if not self.completed:
            self.completed = True
            self.completion_date = timezone.now()
            self.save()
            return True
        return False

    def mark_incomplete(self):
        if self.completed:
            self.completed = False
            self.completion_date = None
            self.save()
            return True
        return False


class WeeklyMission(AbstractModel):
    DAYS_CHOICES = [MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY]

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="weekly_missions",
    )
    six_week_period = models.ForeignKey(
        SixWeekPeriod, on_delete=models.CASCADE, related_name="missions"
    )
    domain = models.ForeignKey(
        Domain, on_delete=models.CASCADE, related_name="weekly_missions"
    )
    week_number = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(6)]
    )
    goals = models.ManyToManyField(Goal, related_name="missions")
    practice_days = models.JSONField(help_text="List of weekday numbers (1-7)")
    daily_reminder = models.BooleanField(default=False)
    reminder_time = models.TimeField(null=True, blank=True)
    completion_date = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ["week_number", "-created"]
        constraints = [
            models.CheckConstraint(
                check=models.Q(week_number__gte=1) & models.Q(week_number__lte=6),
                name="valid_week_number",
            ),
            models.CheckConstraint(
                check=models.Q(
                    models.Q(daily_reminder=False)
                    | models.Q(daily_reminder=True, reminder_time__isnull=False)
                ),
                name="reminder_time_required",
            ),
        ]

    def __str__(self):
        return f"Week {self.week_number} Mission ({self.six_week_period.title})"

    def clean(self):
        from django.core.exceptions import ValidationError

        if (
            self.user != self.six_week_period.user
            and self.user not in self.six_week_period.shared_with.all()
        ):
            raise ValidationError(
                "You can only create missions for your own periods or periods shared with you"
            )

        if self.six_week_period.status not in [
            SixWeekPeriod.STATUS_DRAFT,
            SixWeekPeriod.STATUS_IN_PROGRESS,
        ]:
            raise ValidationError(
                "Cannot add missions to completed or archived periods"
            )

    def is_completed(self):
        return self.completion_date is not None

    def mark_completed(self):
        if not self.is_completed():
            self.completion_date = timezone.now()
            self.save()
            return True
        return False

    def mark_incomplete(self):
        if self.is_completed():
            self.completion_date = None
            self.save()
            return True
        return False


class WeeklySuccessRating(AbstractModel):
    """
    Model to track user's self-reported weekly success rating.
    Users can rate how successful they felt their week was on a scale of 1-10.
    Only the most recent rating per week is considered valid.
    """

    weekly_mission = models.ForeignKey(
        WeeklyMission, on_delete=models.CASCADE, related_name="success_ratings"
    )
    rating = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(10)]
    )

    class Meta:
        ordering = ["-created"]

    def __str__(self):
        return f"Rating: {self.rating}/10 for {self.weekly_mission}"
