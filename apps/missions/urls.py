from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from apps.missions.views.weekly_mission import WeeklyMissionViewSet
from apps.missions.views.goals import (
    GoalListCreateView,
    GoalDetailView,
    GoalTogglePriorityView,
    DailyGoalListCreateView,
    DailyGoalDetailView,
    DailyGoalTogglePriorityView,
    DailyGoalCompleteView,
    DailyGoalIncompleteView,
    DailyGoalUpdateProgressView,
    DailyGoalProgressByDayView,
    DailyGoalBulkCreateView,
)
from apps.missions.views.goals.daily_goals_bulk_create import DailyGoalsBulkCreateView

app_name = "missions"


router = DefaultRouter()
router.register(r"weekly-missions", WeeklyMissionViewSet, basename="weekly-mission")

period_mission_patterns = [
    path(
        "periods/<uuid:period_pk>/missions/",
        WeeklyMissionViewSet.as_view({"get": "by_period"}),
        name="period-missions",
    ),
    path(
        "periods/<uuid:period_pk>/current-week/",
        WeeklyMissionViewSet.as_view({"get": "current_week"}),
        name="period-current-week",
    ),
]


goal_patterns = [
    path("goals/", GoalListCreateView.as_view(), name="goal-list-create"),
    path("goals/<uuid:pk>/", GoalDetailView.as_view(), name="goal-detail"),
    path(
        "goals/<uuid:pk>/toggle-priority/",
        GoalTogglePriorityView.as_view(),
        name="goal-toggle-priority",
    ),
]


daily_goal_patterns = [
    path(
        "daily-goals/", DailyGoalListCreateView.as_view(), name="daily-goal-list-create"
    ),
    path(
        "daily-goals/bulk-create/",
        DailyGoalBulkCreateView.as_view(),
        name="daily-goal-bulk-create",
    ),
    path(
        "daily-goals/bulk_create/",
        DailyGoalsBulkCreateView.as_view(),
        name="daily-goals-bulk-create-alt",
    ),
    path(
        "daily-goals/<uuid:pk>/",
        DailyGoalDetailView.as_view(),
        name="daily-goal-detail",
    ),
    path(
        "daily-goals/<uuid:pk>/toggle-priority/",
        DailyGoalTogglePriorityView.as_view(),
        name="daily-goal-toggle-priority",
    ),
    path(
        "daily-goals/<uuid:pk>/complete/<int:day_number>/",
        DailyGoalCompleteView.as_view(),
        name="daily-goal-complete",
    ),
    path(
        "daily-goals/<uuid:pk>/incomplete/<int:day_number>/",
        DailyGoalIncompleteView.as_view(),
        name="daily-goal-incomplete",
    ),
    path(
        "daily-goals/<uuid:pk>/update-progress/",
        DailyGoalUpdateProgressView.as_view(),
        name="daily-goal-update-progress",
    ),
    path(
        "daily-goals/<uuid:pk>/progress-by-day/",
        DailyGoalProgressByDayView.as_view(),
        name="daily-goal-progress-by-day",
    ),
]

urlpatterns = [
    path("", include(router.urls)),
    path("", include(period_mission_patterns)),
    path("", include(goal_patterns)),
    path("", include(daily_goal_patterns)),
]
