from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from .models import WeeklyMission, Goal
from apps.okrs.models import SixWeekPeriod
from apps.okrs.constants import (
    MONDAY,
    TUESDAY,
    WEDNESDAY,
    THURSDAY,
    FRIDAY,
    SATURDAY,
    SUNDAY,
)
from drf_spectacular.utils import extend_schema_field
from drf_spectacular.types import OpenApiTypes


class GoalSerializer(serializers.ModelSerializer):
    class Meta:
        model = Goal
        fields = [
            "id",
            "user",
            "six_week_period",
            "title",
            "priority",
            "updated",
        ]
        read_only_fields = ["user"]
        extra_kwargs = {"updated": {"read_only": True}}

    def validate_six_week_period(self, value):
        user = self.context["request"].user
        if value.user != user and user not in value.shared_with.all():
            raise serializers.ValidationError(
                _(
                    "You can only create goals for your own periods or periods shared with you"
                )
            )

        if value.status not in [
            SixWeekPeriod.STATUS_DRAFT,
            SixWeekPeriod.STATUS_IN_PROGRESS,
        ]:
            raise serializers.ValidationError(
                _("Cannot add goals to completed or archived periods")
            )
        return value

    def create(self, validated_data):
        validated_data["user"] = self.context["request"].user
        return super().create(validated_data)


class WeeklyMissionSerializer(serializers.ModelSerializer):
    goals = GoalSerializer(many=True, read_only=True)
    goal_ids = serializers.PrimaryKeyRelatedField(
        queryset=Goal.objects.all(), write_only=True, many=True, source="goals"
    )
    is_completed = serializers.BooleanField(read_only=True)
    practice_days_display = serializers.SerializerMethodField()

    class Meta:
        model = WeeklyMission
        fields = [
            "id",
            "user",
            "six_week_period",
            "domain",
            "week_number",
            "goals",
            "goal_ids",
            "practice_days",
            "practice_days_display",
            "daily_reminder",
            "reminder_time",
            "is_completed",
            "completion_date",
            "updated",
        ]
        read_only_fields = [
            "user",
            "completion_date",
            "is_completed",
            "practice_days_display",
        ]
        extra_kwargs = {
            "updated": {"read_only": True},
            "practice_days": {
                "help_text": 'List of weekday numbers (1-7) or comma-separated string of numbers. Example: [1,3,5] or "1,3,5"'
            },
        }

    @extend_schema_field(OpenApiTypes.OBJECT)
    def get_practice_days_display(self, obj):
        """Return the translated day names for the practice days."""
        day_mapping = {
            1: _("Monday"),
            2: _("Tuesday"),
            3: _("Wednesday"),
            4: _("Thursday"),
            5: _("Friday"),
            6: _("Saturday"),
            7: _("Sunday"),
        }

        result = []
        if hasattr(obj, "practice_days") and obj.practice_days:
            for day_number in obj.practice_days:
                if day_number in day_mapping:
                    day_name = day_mapping[day_number]
                    result.append((day_number, day_name))

        return result

    def validate_practice_days(self, value):
        """
        Validate that practice_days is a list of integers between 1 and 7 or a comma-separated string of integers.
        """

        if isinstance(value, str):
            try:
                value = [int(day.strip()) for day in value.split(",") if day.strip()]
            except ValueError:
                raise serializers.ValidationError(
                    _(
                        "Practice days must be a comma-separated list of integers between 1 and 7"
                    )
                )

        if not isinstance(value, list):
            raise serializers.ValidationError(
                _(
                    "Practice days must be a list or a comma-separated string of integers"
                )
            )

        if not value:
            raise serializers.ValidationError(
                _("At least one practice day is required")
            )

        valid_days = set(range(1, 8))

        for day in value:
            if not isinstance(day, int) or day not in valid_days:
                raise serializers.ValidationError(
                    _("Practice days must be integers between 1 and 7")
                )

        return value

    def validate_reminder_time(self, value):
        """Validate that reminder_time is in HH:MM:SS format."""
        if value and not isinstance(value, str):
            try:
                return value
            except (ValueError, TypeError):
                raise serializers.ValidationError(
                    _("Reminder time must be in HH:MM:SS format")
                )
        return value

    def validate_six_week_period(self, value):
        user = self.context["request"].user
        if value.user != user and user not in value.shared_with.all():
            raise serializers.ValidationError(
                _(
                    "You can only create missions for your own periods or periods shared with you"
                )
            )

        if value.status not in [
            SixWeekPeriod.STATUS_DRAFT,
            SixWeekPeriod.STATUS_IN_PROGRESS,
        ]:
            raise serializers.ValidationError(
                _("Cannot add missions to completed or archived periods")
            )
        return value

    def validate_goal_ids(self, goals):
        if not goals:
            raise serializers.ValidationError(_("At least one goal is required"))

        if self.initial_data and isinstance(self.initial_data, dict):
            six_week_period = self.initial_data.get("six_week_period")
            week_number = self.initial_data.get("week_number")

            if week_number == 1 and len(goals) != 1:
                raise serializers.ValidationError(
                    _("Exactly one goal is required for week 1")
                )

            if six_week_period:
                for goal in goals:
                    if str(goal.six_week_period.id) != six_week_period:
                        raise serializers.ValidationError(
                            _("All goals must belong to the specified period")
                        )

        return goals

    def validate(self, data):
        if data.get("daily_reminder") and not data.get("reminder_time"):
            raise serializers.ValidationError(
                _("Reminder time is required when daily reminder is enabled")
            )

        six_week_period = data.get("six_week_period")
        week_number = data.get("week_number")

        if self.instance is None:
            existing_mission = WeeklyMission.objects.filter(
                user=self.context["request"].user,
                six_week_period=six_week_period,
                week_number=week_number,
            ).first()

            if existing_mission:
                raise serializers.ValidationError(
                    _("A mission for week {0} already exists in this period").format(
                        week_number
                    )
                )

        return data

    def create(self, validated_data):
        validated_data["user"] = self.context["request"].user
        return super().create(validated_data)
