# Missions System

This module implements the weekly missions component of the OKRs system, allowing users to define and track specific goals for each week.

## Overview

Weekly missions are specific tasks or goals that users want to accomplish within a particular week of a 6-week period. They help break down larger objectives into manageable chunks and provide a clear focus for each week.

## Features

- Create missions for specific weeks
- Assign missions to different domains (work, personal, health, etc.)
- Set priority levels (high or low)
- Mark missions as completed
- Filter missions by week, period, or search terms

## Model

### WeeklyMission

The main model in this app represents a weekly mission with the following attributes:

- **Goal**: A description of what needs to be accomplished
- **Domain**: The area of life or work this mission belongs to
- **Week Number**: The specific week (1-6) within the 6-week period
- **Six Week Period**: The parent period this mission belongs to
- **Priority**: High or low priority
- **Completed**: Whether the mission has been completed
- **User**: The owner of the mission
- **Created/Updated**: Timestamps

## API Endpoints

### Main Endpoints

- `GET /api/v1/missions/`: List all missions
- `POST /api/v1/missions/`: Create a new mission
- `GET /api/v1/missions/{id}/`: Retrieve a specific mission
- `PUT /api/v1/missions/{id}/`: Update a mission
- `PATCH /api/v1/missions/{id}/`: Partially update a mission
- `DELETE /api/v1/missions/{id}/`: Delete a mission

### Action Endpoints

- `POST /api/v1/missions/{id}/toggle_completion/`: Toggle mission completion status
- `POST /api/v1/missions/{id}/toggle_priority/`: Toggle mission priority

### Filter Endpoints

- `GET /api/v1/missions/by_week/?week=1&period=123`: Filter missions by week
- `GET /api/v1/missions/search/?q=project`: Search missions
- `GET /api/v1/missions/by_period/{period_id}/`: Get missions by period
- `GET /api/v1/missions/current_week/{period_id}/`: Get missions for the current week of a period

## Workflow

1. Create a 6-week period in the OKRs app
2. Add missions for each week of the period
3. Start the period
4. Mark missions as completed as you accomplish them
5. Adjust priorities as needed
6. Filter missions to focus on the current week

## Permissions

- Users can only view and modify their own missions
- Users can view missions for periods that are shared with them
- Only the owner of a mission can toggle its completion status or priority

## Integration with OKRs

The missions app is tightly integrated with the OKRs app:

- Missions are associated with a specific 6-week period
- The OKRs app provides endpoints to view missions for a specific week or the current week
- Mission completion contributes to the overall progress of the period

## Best Practices

- Create 3-5 missions per week for optimal focus
- Balance missions across different domains
- Use high priority for the most important missions (limit to 1-2 per week)
- Review and adjust missions at the beginning of each week
- Mark missions as completed as soon as they are accomplished 