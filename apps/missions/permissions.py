from rest_framework import permissions
from django.db.models import Q


class MissionRoleBasedPermission(permissions.BasePermission):
    """
    Permission class that enforces role-based filtering for mission-related views:
    - Admin users can see all data
    - Club managers can see data from their club members
    - Regular members can only see their own data or data shared with them
    """

    def has_permission(self, request, view):

        return request.user and request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        user = request.user

        if user.is_staff or user.role == "admin":
            return True

        if hasattr(obj, "user"):

            if user.role == "club_manager":
                return (
                    obj.user == user
                    or obj.user.club_members.filter(manager=user).exists()
                )

            if hasattr(obj, "six_week_period") and hasattr(
                obj.six_week_period, "shared_with"
            ):
                return obj.user == user or user in obj.six_week_period.shared_with.all()

            return obj.user == user

        if hasattr(obj, "weekly_mission") and hasattr(obj.weekly_mission, "user"):

            if user.role == "club_manager":
                return (
                    obj.weekly_mission.user == user
                    or obj.weekly_mission.user.club_members.filter(
                        manager=user
                    ).exists()
                )

            if hasattr(obj.weekly_mission, "six_week_period") and hasattr(
                obj.weekly_mission.six_week_period, "shared_with"
            ):
                return (
                    obj.weekly_mission.user == user
                    or user in obj.weekly_mission.six_week_period.shared_with.all()
                )

            return obj.weekly_mission.user == user

        return False
