from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from django.utils import timezone
from apps.domains.models import Domain
from apps.okrs.models import SixWeekPeriod
from apps.missions.models import WeeklyMission, Goal

User = get_user_model()


class WeeklyMissionUpdateTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            username="testuser",
        )
        self.client.force_authenticate(user=self.user)

        self.domain = Domain.objects.create(
            name="Test Domain", description="Test Domain Description"
        )

        self.new_domain = Domain.objects.create(
            name="New Domain", description="New Domain Description"
        )

        self.start_date = timezone.now().date()
        self.end_date = self.start_date + timezone.timedelta(days=42)

        self.period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            description="Test Description",
            start_date=self.start_date,
            end_date=self.end_date,
        )

        self.goal = Goal.objects.create(
            user=self.user,
            six_week_period=self.period,
            title="Test Goal",
            priority=Goal.PRIORITY_HIGH,
        )

        self.goal2 = Goal.objects.create(
            user=self.user,
            six_week_period=self.period,
            title="Second Goal",
            priority=Goal.PRIORITY_LOW,
        )

        self.mission = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=2,
            practice_days=[1, 3, 5],
        )
        self.mission.goals.add(self.goal)

    def test_update_weekly_mission_basic_fields(self):
        """Test updating basic fields of a weekly mission"""
        url = reverse("missions:weekly-mission-detail", args=[self.mission.id])
        data = {
            "practice_days": [2, 4, 6],
            "daily_reminder": True,
            "reminder_time": "09:00:00",
        }

        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.mission.refresh_from_db()
        self.assertEqual(self.mission.practice_days, [2, 4, 6])
        self.assertTrue(self.mission.daily_reminder)
        self.assertEqual(str(self.mission.reminder_time), "09:00:00")

    def test_update_weekly_mission_domain(self):
        """Test updating the domain of a weekly mission"""
        url = reverse("missions:weekly-mission-detail", args=[self.mission.id])
        data = {
            "domain": self.new_domain.id,
        }

        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.mission.refresh_from_db()
        self.assertEqual(self.mission.domain.id, self.new_domain.id)

    def test_update_weekly_mission_week_number(self):
        """Test updating the week number of a weekly mission"""
        url = reverse("missions:weekly-mission-detail", args=[self.mission.id])
        data = {
            "week_number": 3,
        }

        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.mission.refresh_from_db()
        self.assertEqual(self.mission.week_number, 3)

    def test_update_weekly_mission_goals(self):
        """Test updating the goals of a weekly mission"""
        url = reverse("missions:weekly-mission-detail", args=[self.mission.id])
        data = {
            "goal_ids": [self.goal2.id],
        }

        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.mission.refresh_from_db()
        self.assertEqual(self.mission.goals.count(), 1)
        self.assertEqual(self.mission.goals.first().id, self.goal2.id)

    def test_update_weekly_mission_weekly_goal(self):
        """Test updating the first goal title through weekly_goal field"""
        url = reverse("missions:weekly-mission-detail", args=[self.mission.id])
        updated_goal_title = "Updated Weekly Goal Title"
        data = {
            "weekly_goal": updated_goal_title,
        }

        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.goal.refresh_from_db()
        self.assertEqual(self.goal.title, updated_goal_title)

    def test_update_weekly_mission_all_fields(self):
        """Test updating all fields of a weekly mission at once"""
        url = reverse("missions:weekly-mission-detail", args=[self.mission.id])
        updated_goal_title = "Completely Updated Goal"
        data = {
            "domain": self.new_domain.id,
            "week_number": 4,
            "goal_ids": [self.goal2.id],
            "practice_days": [1, 2, 3, 4],
            "daily_reminder": True,
            "reminder_time": "10:00:00",
            "weekly_goal": updated_goal_title,
        }

        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.mission.refresh_from_db()
        self.goal2.refresh_from_db()

        self.assertEqual(self.mission.domain.id, self.new_domain.id)
        self.assertEqual(self.mission.week_number, 4)
        self.assertEqual(self.mission.goals.count(), 1)
        self.assertEqual(self.mission.goals.first().id, self.goal2.id)
        self.assertEqual(self.mission.practice_days, [1, 2, 3, 4])
        self.assertTrue(self.mission.daily_reminder)
        self.assertEqual(str(self.mission.reminder_time), "10:00:00")
        self.assertEqual(self.mission.goals.first().title, updated_goal_title)

    def test_week_number_validation(self):
        """Test validation of the week number field"""
        url = reverse("missions:weekly-mission-detail", args=[self.mission.id])
        data = {
            "week_number": 7,
        }

        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("error", response.data)

    def test_week_1_validation(self):
        """Test validation of week 1 requiring exactly one goal"""
        url = reverse("missions:weekly-mission-detail", args=[self.mission.id])
        data = {
            "week_number": 1,
            "goal_ids": [self.goal.id, self.goal2.id],
        }

        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("error", response.data)
        self.assertTrue(
            "Exactly one goal is required for week 1" in str(response.data["error"])
            or "مطلوب هدف واحد بالضبط للأسبوع الأول" in str(response.data["error"])
        )
