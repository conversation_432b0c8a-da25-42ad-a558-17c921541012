from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone

from apps.domains.models import Domain
from apps.okrs.models import SixWeekPeriod
from apps.missions.models import WeeklyMission, Goal, DailyGoal, DailyGoalProgress

User = get_user_model()


class DailyGoalPriorityTest(TestCase):
    """Test daily goal priority management."""

    def setUp(self):
        """Set up test environment."""

        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            fullname="Test User",
            username="testuser",
        )

        self.domain = Domain.objects.create(
            name="Test Domain", description="Test Domain Description"
        )

        self.start_date = timezone.now().date()
        self.end_date = self.start_date + timezone.timedelta(days=42)

        self.period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            description="Test Description",
            start_date=self.start_date,
            end_date=self.end_date,
        )

        self.goal = Goal.objects.create(
            user=self.user,
            six_week_period=self.period,
            title="Test Goal",
            priority=Goal.PRIORITY_HIGH,
        )

        self.mission1 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=1,
            practice_days=[1, 3, 5],
        )
        self.mission1.goals.add(self.goal)

        self.mission2 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=2,
            practice_days=[2, 4, 6],
        )
        self.mission2.goals.add(self.goal)

    def create_daily_goals_with_progress(self, mission, count=3):
        """Helper method to create daily goals with progress entries."""
        goals = []
        for i in range(count):
            goal = DailyGoal.objects.create(
                weekly_mission=mission,
                title=f"Daily Goal {mission.week_number}-{i+1}",
                priority=DailyGoal.PRIORITY_LOW,
            )
            goals.append(goal)

            for day_number in mission.practice_days:
                DailyGoalProgress.objects.create(daily_goal=goal, day_number=day_number)

        return goals

    def test_set_priority_high_makes_others_low(self):
        """Test that setting a daily goal to high priority makes other goals for the same day low priority."""

        goals_m1 = self.create_daily_goals_with_progress(self.mission1, 3)

        goals_m1[0].set_priority(DailyGoal.PRIORITY_HIGH)

        goals_m1[0].refresh_from_db()
        self.assertEqual(goals_m1[0].priority, DailyGoal.PRIORITY_HIGH)

        goals_m1[1].set_priority(DailyGoal.PRIORITY_HIGH)

        goals_m1[0].refresh_from_db()
        goals_m1[1].refresh_from_db()
        self.assertEqual(goals_m1[0].priority, DailyGoal.PRIORITY_LOW)
        self.assertEqual(goals_m1[1].priority, DailyGoal.PRIORITY_HIGH)

        goals_m1[2].refresh_from_db()
        self.assertEqual(goals_m1[2].priority, DailyGoal.PRIORITY_LOW)

    def test_priority_independent_across_missions(self):
        """Test that priority settings are independent across different missions."""

        goals_m1 = self.create_daily_goals_with_progress(self.mission1, 2)
        goals_m2 = self.create_daily_goals_with_progress(self.mission2, 2)

        goals_m1[0].set_priority(DailyGoal.PRIORITY_HIGH)

        goals_m2[0].set_priority(DailyGoal.PRIORITY_HIGH)

        goals_m1[0].refresh_from_db()
        goals_m2[0].refresh_from_db()

        self.assertEqual(goals_m1[0].priority, DailyGoal.PRIORITY_HIGH)
        self.assertEqual(goals_m2[0].priority, DailyGoal.PRIORITY_HIGH)

    def test_priority_management_by_day(self):
        """Test that priority management works on a per-day basis."""

        goals = self.create_daily_goals_with_progress(self.mission1, 3)

        goals[0].set_priority(DailyGoal.PRIORITY_HIGH)
        goals[0].refresh_from_db()
        self.assertEqual(goals[0].priority, DailyGoal.PRIORITY_HIGH)

        goals[1].set_priority(DailyGoal.PRIORITY_HIGH)
        goals[0].refresh_from_db()
        goals[1].refresh_from_db()
        self.assertEqual(goals[0].priority, DailyGoal.PRIORITY_LOW)
        self.assertEqual(goals[1].priority, DailyGoal.PRIORITY_HIGH)

        goals[2].set_priority(DailyGoal.PRIORITY_HIGH)
        goals[0].refresh_from_db()
        goals[1].refresh_from_db()
        goals[2].refresh_from_db()
        self.assertEqual(goals[0].priority, DailyGoal.PRIORITY_LOW)
        self.assertEqual(goals[1].priority, DailyGoal.PRIORITY_LOW)
        self.assertEqual(goals[2].priority, DailyGoal.PRIORITY_HIGH)

    def test_toggle_priority_endpoint(self):
        """Test that the toggle_priority endpoint correctly manages priorities."""
        from rest_framework.test import APIClient
        from django.urls import reverse
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.user)

        goals = self.create_daily_goals_with_progress(self.mission1, 2)

        url = reverse("missions:daily-goal-toggle-priority", args=[goals[0].id])
        response = client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        goals[0].refresh_from_db()
        self.assertEqual(goals[0].priority, DailyGoal.PRIORITY_HIGH)

        url = reverse("missions:daily-goal-toggle-priority", args=[goals[1].id])
        response = client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        goals[0].refresh_from_db()
        goals[1].refresh_from_db()
        self.assertEqual(goals[0].priority, DailyGoal.PRIORITY_LOW)
        self.assertEqual(goals[1].priority, DailyGoal.PRIORITY_HIGH)
