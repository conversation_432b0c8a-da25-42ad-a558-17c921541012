from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from django.utils import timezone
import unittest
from apps.domains.models import Domain
from apps.okrs.models import SixWeekPeriod
from apps.missions.models import WeeklyMission, Goal, DailyGoal, DailyGoalProgress
from datetime import date, timedelta, time
import uuid

User = get_user_model()


class WeeklyMissionViewTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpassword"
        )
        self.domain = Domain.objects.create(
            name="Test Domain", description="Test Domain Description"
        )
        self.client.force_authenticate(user=self.user)

        start_date = date(2024, 1, 1)
        end_date = start_date + timedelta(days=42)
        self.period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            start_date=start_date,
            end_date=end_date,
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
        )

        self.goal = Goal.objects.create(
            user=self.user,
            six_week_period=self.period,
            title="Test Goal",
            priority=Goal.PRIORITY_HIGH,
        )
        self.goal2 = Goal.objects.create(
            user=self.user,
            six_week_period=self.period,
            title="Second Goal",
            priority=Goal.PRIORITY_LOW,
        )
        self.goal3 = Goal.objects.create(
            user=self.user,
            six_week_period=self.period,
            title="Third Goal",
            priority=Goal.PRIORITY_LOW,
        )

        self.user2 = User.objects.create_user(
            username="shareduser", email="<EMAIL>", password="sharedpassword"
        )
        shared_start_date = date(2024, 1, 1)
        shared_end_date = shared_start_date + timedelta(days=42)
        self.shared_period = SixWeekPeriod.objects.create(
            user=self.user2,
            title="Shared Period",
            start_date=shared_start_date,
            end_date=shared_end_date,
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
        )
        self.shared_period.shared_with.add(self.user)

        self.shared_goal = Goal.objects.create(
            user=self.user2,
            six_week_period=self.shared_period,
            title="Shared Goal",
            priority=Goal.PRIORITY_HIGH,
        )

        self.unauthorized_user = User.objects.create_user(
            username="unauthorized",
            email="<EMAIL>",
            password="unauthorized",
        )
        unauth_start_date = date(2024, 1, 1)
        unauth_end_date = unauth_start_date + timedelta(days=42)
        self.unauthorized_period = SixWeekPeriod.objects.create(
            user=self.unauthorized_user,
            title="Unauthorized Period",
            start_date=unauth_start_date,
            end_date=unauth_end_date,
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
        )

    def test_create_mission(self):
        url = reverse("missions:weekly-mission-list")
        data = {
            "six_week_period": self.period.id,
            "domain": self.domain.id,
            "week_number": 1,
            "goal_ids": [self.goal.id],
            "practice_days": [1],
            "daily_reminder": False,
            "reminder_time": None,
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(WeeklyMission.objects.count(), 1)
        self.assertEqual(WeeklyMission.objects.first().week_number, 1)

    def test_create_mission_with_multiple_goals(self):
        url = reverse("missions:weekly-mission-list")
        data = {
            "six_week_period": self.period.id,
            "domain": self.domain.id,
            "week_number": 2,
            "goal_ids": [self.goal.id, self.goal2.id],
            "practice_days": [1, 3, 5],
            "daily_reminder": True,
            "reminder_time": "08:00:00",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        mission = WeeklyMission.objects.first()
        self.assertEqual(mission.goals.count(), 2)
        self.assertEqual(mission.practice_days, [1, 3, 5])

    def test_create_goal(self):
        initial_count = Goal.objects.count()
        url = reverse("missions:goal-list-create")
        data = {
            "six_week_period": self.period.id,
            "title": "New Goal",
            "priority": Goal.PRIORITY_HIGH,
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Goal.objects.count(), initial_count + 1)
        self.assertEqual(response.data["title"], "New Goal")

    def test_toggle_goal_priority(self):
        url = reverse("missions:goal-toggle-priority", args=[self.goal.id])
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.goal.refresh_from_db()
        self.assertEqual(self.goal.priority, Goal.PRIORITY_LOW)

        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.goal.refresh_from_db()
        self.assertEqual(self.goal.priority, Goal.PRIORITY_HIGH)

    def test_list_missions(self):
        mission = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=1,
            practice_days=[1],
        )
        mission.goals.add(self.goal)

        url = reverse("missions:weekly-mission-list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        print(f"Response data type: {type(response.data)}")
        print(f"Response data: {response.data}")

        self.assertTrue(response.status_code == status.HTTP_200_OK)

    @unittest.skip("URL path issues need to be resolved")
    def test_filter_by_period(self):
        mission1 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=1,
            practice_days=[1],
        )
        mission1.goals.add(self.goal)

        mission2 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.shared_period,
            domain=self.domain,
            week_number=1,
            practice_days=[1],
        )
        mission2.goals.add(self.shared_goal)

        period_pk = self.period.pk
        url = f"/api/v1/missions/periods/{period_pk}/missions/"
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]["id"], str(mission1.id))

    def test_filter_by_week(self):
        mission1 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=1,
            practice_days=[1],
        )
        mission1.goals.add(self.goal)

        mission2 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=2,
            practice_days=[2, 4],
        )
        mission2.goals.add(self.goal2)

        url = reverse("missions:weekly-mission-by-week")
        response = self.client.get(url, {"week": 1})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["id"], str(mission1.id))

        url = reverse("missions:weekly-mission-by-week")
        response = self.client.get(url, {"week": 2})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["id"], str(mission2.id))

    @unittest.skip("URL path issues need to be resolved")
    def test_filter_by_period_and_week(self):
        mission1 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=1,
            practice_days=[1],
        )
        mission1.goals.add(self.goal)

        mission2 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.shared_period,
            domain=self.domain,
            week_number=1,
            practice_days=[2],
        )
        mission2.goals.add(self.shared_goal)

        period_id = int(str(self.period.id).replace("-", "")[:8], 16)
        url = reverse("missions:period-missions", args=[period_id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]["id"], str(mission1.id))

    def test_toggle_completion(self):
        mission = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=1,
            practice_days=[1],
        )
        mission.goals.add(self.goal)

        self.assertFalse(mission.is_completed())

        url = reverse("missions:weekly-mission-toggle-completion", args=[mission.id])
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        mission.refresh_from_db()
        self.assertTrue(mission.is_completed())

        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        mission.refresh_from_db()
        self.assertFalse(mission.is_completed())

    def test_search_missions(self):
        mission1 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=1,
            practice_days=[1],
        )
        mission1.goals.add(self.goal)

        mission2 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=2,
            practice_days=[2],
        )
        mission2.goals.add(self.goal2)

        url = reverse("missions:weekly-mission-search")
        response = self.client.get(url, {"q": "Test"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        mission_ids = [m["id"] for m in response.data["results"]]
        self.assertIn(str(mission1.id), mission_ids)

        url = reverse("missions:weekly-mission-search")
        response = self.client.get(url, {"q": "Second"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        mission_ids = [m["id"] for m in response.data["results"]]
        self.assertIn(str(mission2.id), mission_ids)

    @unittest.skip("URL path issues need to be resolved")
    def test_period_missions(self):
        mission1 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=1,
            practice_days=[1],
        )
        mission1.goals.add(self.goal)

        mission2 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.shared_period,
            domain=self.domain,
            week_number=1,
            practice_days=[1],
        )
        mission2.goals.add(self.shared_goal)

        period_pk = self.period.pk
        url = f"/api/v1/missions/periods/{period_pk}/missions/"
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]["id"], str(mission1.id))

    @unittest.skip("URL path issues need to be resolved")
    def test_current_week_missions(self):
        self.period.status = SixWeekPeriod.STATUS_IN_PROGRESS
        self.period.save()

        mission1 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=1,
            practice_days=[1],
        )
        mission1.goals.add(self.goal)

        mission2 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=2,
            practice_days=[2],
        )
        mission2.goals.add(self.goal)
        mission2.goals.add(self.goal2)

        period_pk = self.period.pk
        url = f"/api/v1/missions/periods/{period_pk}/missions/current-week/"
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]["id"], str(mission1.id))

    def test_shared_period_access(self):
        """Test that users with access to shared periods can view missions."""

        mission = WeeklyMission.objects.create(
            user=self.user2,
            six_week_period=self.shared_period,
            domain=self.domain,
            week_number=1,
            practice_days=[1],
        )
        mission.goals.add(self.shared_goal)

        self.client.force_authenticate(user=self.user2)
        url = reverse("missions:weekly-mission-detail", args=[mission.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.client.force_authenticate(user=self.user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_unauthorized_period_access(self):
        mission = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=1,
            practice_days=[1],
        )
        mission.goals.add(self.goal)

        self.client.force_authenticate(user=self.unauthorized_user)

        url = reverse("missions:weekly-mission-detail", args=[mission.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_create_mission_validation_errors(self):
        url = reverse("missions:weekly-mission-list")

        data = {
            "six_week_period": self.period.id,
            "domain": self.domain.id,
            "week_number": 7,
            "goal_ids": [self.goal.id],
            "practice_days": [1],
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        data = {
            "six_week_period": self.period.id,
            "domain": self.domain.id,
            "week_number": 1,
            "goal_ids": [self.goal.id],
            "practice_days": [8],
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        data = {
            "six_week_period": self.period.id,
            "domain": self.domain.id,
            "week_number": 1,
            "goal_ids": [self.goal.id],
            "practice_days": [1],
            "daily_reminder": True,
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_goal_validation_errors(self):
        url = reverse("missions:goal-list-create")
        data = {
            "six_week_period": self.shared_period.id,
            "title": "",
            "priority": "invalid_priority",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_practice_days_string_format(self):
        """Test that practice_days accepts a comma-separated string format."""
        url = reverse("missions:weekly-mission-list")
        data = {
            "six_week_period": self.period.id,
            "domain": self.domain.id,
            "week_number": 2,
            "goal_ids": [self.goal.id],
            "practice_days": "1,3,5",
            "daily_reminder": True,
            "reminder_time": "08:00:00",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        mission = WeeklyMission.objects.last()
        self.assertEqual(mission.practice_days, [1, 3, 5])

        self.assertEqual(response.data["practice_days"], [1, 3, 5])

    def test_practice_days_list_format(self):
        """Test that practice_days accepts a list format."""
        url = reverse("missions:weekly-mission-list")
        data = {
            "six_week_period": self.period.id,
            "domain": self.domain.id,
            "week_number": 2,
            "goal_ids": [self.goal.id],
            "practice_days": [2, 4, 6],
            "daily_reminder": True,
            "reminder_time": "08:00:00",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        mission = WeeklyMission.objects.last()
        self.assertEqual(mission.practice_days, [2, 4, 6])

        self.assertEqual(response.data["practice_days"], [2, 4, 6])

    def test_practice_days_display_format(self):
        """Test the practice_days_display field in the response."""

        mission = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=1,
            practice_days=[1, 3, 5],
        )
        mission.goals.add(self.goal)

        url = reverse("missions:weekly-mission-detail", args=[mission.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        practice_days_display = response.data["practice_days_display"]
        self.assertEqual(len(practice_days_display), 3)

        day_numbers = [day[0] for day in practice_days_display]
        self.assertEqual(day_numbers, [1, 3, 5])

        self.assertTrue(all(len(day) == 2 for day in practice_days_display))

    def test_invalid_practice_days_format(self):
        """Test validation for invalid practice_days formats."""
        url = reverse("missions:weekly-mission-list")

        data = {
            "six_week_period": self.period.id,
            "domain": self.domain.id,
            "week_number": 2,
            "goal_ids": [self.goal.id],
            "practice_days": "monday,tuesday",
            "daily_reminder": True,
            "reminder_time": "08:00:00",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        data = {
            "six_week_period": self.period.id,
            "domain": self.domain.id,
            "week_number": 2,
            "goal_ids": [self.goal.id],
            "practice_days": [0, 8],
            "daily_reminder": True,
            "reminder_time": "08:00:00",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        data = {
            "six_week_period": self.period.id,
            "domain": self.domain.id,
            "week_number": 2,
            "goal_ids": [self.goal.id],
            "practice_days": [],
            "daily_reminder": True,
            "reminder_time": "08:00:00",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_duplicate_week_validation(self):
        """Test that duplicate missions for the same week are not allowed."""

        mission1 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=2,
            practice_days=[1, 3],
        )
        mission1.goals.add(self.goal, self.goal2)

        url = reverse("missions:weekly-mission-list")
        data = {
            "six_week_period": self.period.id,
            "domain": self.domain.id,
            "week_number": 2,
            "goal_ids": [self.goal.id],
            "practice_days": [2, 4],
            "daily_reminder": True,
            "reminder_time": "08:00:00",
        }
        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        self.assertIn("موجودة بالفعل", str(response.data))

        data["week_number"] = 3
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        other_user_period = self.shared_period

        other_user_goal = Goal.objects.create(
            user=self.user2,
            six_week_period=other_user_period,
            title="Other User Goal",
            priority=Goal.PRIORITY_HIGH,
        )

        mission2 = WeeklyMission.objects.create(
            user=self.user2,
            six_week_period=other_user_period,
            domain=self.domain,
            week_number=2,
            practice_days=[1, 3],
        )
        mission2.goals.add(other_user_goal)

        self.client.force_authenticate(user=self.user)
        url = reverse("missions:weekly-mission-list")
        data = {
            "six_week_period": other_user_period.id,
            "domain": self.domain.id,
            "week_number": 2,
            "goal_ids": [self.shared_goal.id],
            "practice_days": [2, 4],
            "daily_reminder": False,
            "reminder_time": None,
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_week_1_single_goal_validation(self):
        """Test that week 1 only allows exactly one goal."""
        url = reverse("missions:weekly-mission-list")

        data = {
            "six_week_period": self.period.id,
            "domain": self.domain.id,
            "week_number": 1,
            "goal_ids": [],
            "practice_days": [],
            "daily_reminder": True,
            "reminder_time": "08:00:00",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        response_data = str(response.data)
        is_valid_error = (
            "At least one goal is required" in response_data
            or "Exactly one goal is required for week 1" in response_data
            or "مطلوب هدف واحد على الأقل" in response_data
            or "مطلوب هدف واحد بالضبط للأسبوع الأول" in response_data
        )
        self.assertTrue(
            is_valid_error,
            f"Expected goal validation error message, got: {response_data}",
        )

        data["goal_ids"] = [self.goal.id, self.goal2.id]
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        data["goal_ids"] = [self.goal.id]
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_weeks_2_to_6_multiple_goals_validation(self):
        """Test that weeks 2-6 allow multiple goals but at least one is required."""
        url = reverse("missions:weekly-mission-list")

        data = {
            "six_week_period": self.period.id,
            "domain": self.domain.id,
            "week_number": 2,
            "goal_ids": [],
            "practice_days": [1],
            "daily_reminder": True,
            "reminder_time": "08:00:00",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        response_data = str(response.data)
        self.assertTrue(
            "At least one goal is required" in response_data
            or "مطلوب هدف واحد على الأقل" in response_data,
            f"Expected goal validation error message, got: {response_data}",
        )

        data["goal_ids"] = [self.goal.id]
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        data = {
            "six_week_period": self.period.id,
            "domain": self.domain.id,
            "week_number": 3,
            "goal_ids": [self.goal.id, self.goal2.id],
            "practice_days": [1],
            "daily_reminder": True,
            "reminder_time": "08:00:00",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        mission = WeeklyMission.objects.get(week_number=3)
        self.assertEqual(mission.goals.count(), 2)

    def test_bulk_create_daily_goals(self):
        """Test the bulk creation of daily goals for a weekly mission."""

        mission = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=2,
            practice_days=[1, 3, 5],
        )
        mission.goals.add(self.goal)

        url = reverse("missions:daily-goal-bulk-create")
        data = {
            "weekly_mission": mission.id,
            "goals": [
                {"title": "Daily Goal 1", "priority": "high"},
                {"title": "Daily Goal 2", "priority": "low"},
                {"title": "Daily Goal 3", "priority": "high"},
            ],
        }

        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        self.assertEqual(len(response.data["created"]), 3)
        self.assertEqual(len(response.data["errors"]), 0)

        from apps.missions.models import DailyGoal

        daily_goals = DailyGoal.objects.filter(weekly_mission=mission)
        self.assertEqual(daily_goals.count(), 3)

        high_priority_goals = daily_goals.filter(priority="high")
        self.assertEqual(high_priority_goals.count(), 2)

    def test_bulk_create_daily_goals_validation_errors(self):
        """Test validation errors during bulk creation of daily goals."""

        mission = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=2,
            practice_days=[1, 3, 5],
        )
        mission.goals.add(self.goal)

        week1_mission = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=1,
            practice_days=[1, 3, 5],
        )
        week1_mission.goals.add(self.goal)

        url = reverse("missions:daily-goal-bulk-create")

        data = {"goals": [{"title": "Daily Goal 1", "priority": "high"}]}
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        error_response = str(response.data)
        is_valid_error = (
            "Weekly mission ID is required" in error_response
            or "هذه الخانة مطلوبه" in error_response
            or "معرف المهمة الأسبوعية مطلوب" in error_response
        )
        self.assertTrue(
            is_valid_error,
            f"Expected missing weekly mission error, got: {error_response}",
        )

        data = {"weekly_mission": mission.id}
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        error_response = str(response.data)
        is_valid_error = (
            "A list of goals is required" in error_response
            or "هذه الخانة مطلوبه" in error_response
            or "قائمة الأهداف مطلوبة" in error_response
        )
        self.assertTrue(
            is_valid_error, f"Expected missing goals list error, got: {error_response}"
        )

        data = {"weekly_mission": mission.id, "goals": []}
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        error_response = str(response.data)
        is_valid_error = (
            "A list of goals is required" in error_response
            or "هذه القائمة فارغة" in error_response
            or "قائمة الأهداف مطلوبة" in error_response
        )
        self.assertTrue(
            is_valid_error, f"Expected empty goals list error, got: {error_response}"
        )

        data = {
            "weekly_mission": "00000000-0000-0000-0000-000000000000",
            "goals": [{"title": "Daily Goal 1", "priority": "high"}],
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        error_response = str(response.data)
        is_valid_error = (
            "Weekly mission not found" in error_response
            or "لم يتم العثور على المهمة الأسبوعية" in error_response
        )
        self.assertTrue(
            is_valid_error, f"Expected not found error, got: {error_response}"
        )

        data = {
            "weekly_mission": week1_mission.id,
            "goals": [{"title": "Daily Goal 1", "priority": "high"}],
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        error_response = str(response.data)
        is_valid_error = (
            "Daily goals are not allowed for week 1" in error_response
            or "لا يسمح بالأهداف اليومية للأسبوع الأول" in error_response
        )
        self.assertTrue(
            is_valid_error, f"Expected week 1 validation error, got: {error_response}"
        )

    def test_bulk_create_daily_goals_permissions(self):
        """Test permission checks for bulk creation of daily goals."""

        mission = WeeklyMission.objects.create(
            user=self.user2,
            six_week_period=self.shared_period,
            domain=self.domain,
            week_number=2,
            practice_days=[1, 3, 5],
        )
        mission.goals.add(self.shared_goal)

        self.client.force_authenticate(user=self.unauthorized_user)
        url = reverse("missions:daily-goal-bulk-create")
        data = {
            "weekly_mission": mission.id,
            "goals": [
                {"title": "Test Daily Goal 1", "priority": DailyGoal.PRIORITY_HIGH},
                {"title": "Test Daily Goal 2", "priority": DailyGoal.PRIORITY_LOW},
            ],
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        self.client.force_authenticate(user=self.user)
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(len(response.data["created"]), 2)

    def test_api_endpoints_permissions(self):
        """Test permission checks for API endpoints"""
        pass

    def test_weekly_mission_serialization(self):
        """Test proper serialization of weekly mission data"""
        pass

    def test_goal_update(self):
        """Test updating an existing goal"""
        pass

    def test_mission_deletion(self):
        """Test deleting a weekly mission"""
        pass

    def test_goal_deletion(self):
        """Test deleting a goal"""
        pass

    def test_invalid_practice_days_format_messages(self):
        """Test error messages for invalid practice days format"""
        pass

    def test_mission_filtering_options(self):
        """Test various filtering options for missions list"""
        pass

    def test_mission_status_transition(self):
        """Test transitioning between mission statuses"""
        pass


class DailyGoalViewSetTest(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpassword"
        )
        self.client.force_authenticate(user=self.user)

        self.domain = Domain.objects.create(
            name="Test Domain", description="Test Domain Description"
        )
        start_date = date(2024, 1, 1)
        end_date = start_date + timedelta(days=42)
        self.period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            start_date=start_date,
            end_date=end_date,
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
        )
        self.goal = Goal.objects.create(
            user=self.user,
            six_week_period=self.period,
            title="Test Goal",
        )
        self.mission = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=2,
            practice_days=[1, 2, 3],
        )
        self.mission.goals.add(self.goal)
        self.daily_goal = DailyGoal.objects.create(
            weekly_mission=self.mission,
            title="Test Daily Goal",
        )
        self.daily_goal.create_daily_progress_entries()

    def test_mark_daily_goal_complete(self):
        url = reverse("missions:daily-goal-complete", args=[self.daily_goal.id, 1])
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        progress = self.daily_goal.get_progress_for_day(1)
        self.assertTrue(progress.completed)

    def test_mark_daily_goal_incomplete(self):

        progress = self.daily_goal.get_progress_for_day(1)
        progress.mark_completed()

        url = reverse("missions:daily-goal-incomplete", args=[self.daily_goal.id, 1])
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        progress = self.daily_goal.get_progress_for_day(1)
        self.assertFalse(progress.completed)

    def test_mark_daily_goal_complete_invalid_day(self):
        url = f"/api/missions/daily-goals/{self.daily_goal.pk}/complete/8/"
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_mark_daily_goal_missing_path_day(self):
        url = f"/api/missions/daily-goals/{self.daily_goal.pk}/complete/"
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_daily_goal_completion_status(self):
        progress = self.daily_goal.get_progress_for_day(1)
        progress.mark_completed()

        self.assertFalse(self.daily_goal.is_completed())

        progress = self.daily_goal.get_progress_for_day(2)
        progress.mark_completed()

        self.assertFalse(self.daily_goal.is_completed())

        progress = self.daily_goal.get_progress_for_day(3)
        progress.mark_completed()

        self.assertTrue(self.daily_goal.is_completed())

    def test_get_daily_goal_progress(self):
        progress = self.daily_goal.get_progress_for_day(1)
        self.assertIsNotNone(progress)
        self.assertEqual(progress.day_number, 1)

        progress = self.daily_goal.get_progress_for_day(8)
        self.assertIsNone(progress)

    def test_get_overall_completion_percentage(self):
        self.assertEqual(self.daily_goal.get_overall_completion_percentage(), 0)

        progress = self.daily_goal.get_progress_for_day(1)
        progress.mark_completed()

        self.assertAlmostEqual(
            self.daily_goal.get_overall_completion_percentage(), 33.33333333333333
        )
