import pytest
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from apps.missions.models import Goal, WeeklyMission, DailyGoal
from apps.domains.models import Domain
from apps.okrs.models import SixWeekPeriod

User = get_user_model()


@pytest.mark.django_db
class TestMetaPagination:
    @pytest.fixture(autouse=True)
    def setup(self):

        self.user = User.objects.create_user(
            email="<EMAIL>", password="testpassword", username="testuser"
        )
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

        self.domain = Domain.objects.create(name="Health")
        self.start_date = timezone.now().date()
        self.period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            start_date=self.start_date,
            end_date=self.start_date + timedelta(days=42),
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
        )

        self.mission = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=2,
            practice_days=[1, 3, 5],
        )

        for i in range(10):
            Goal.objects.create(
                user=self.user,
                six_week_period=self.period,
                title=f"Test Goal {i+1}",
                priority=Goal.PRIORITY_HIGH if i % 2 == 0 else Goal.PRIORITY_LOW,
            )

        for i in range(10):
            DailyGoal.objects.create(
                weekly_mission=self.mission,
                title=f"Test Daily Goal {i+1}",
                priority=(
                    DailyGoal.PRIORITY_HIGH if i % 2 == 0 else DailyGoal.PRIORITY_LOW
                ),
            )

        self.goal_list_url = reverse("missions:goal-list-create")
        self.daily_goal_list_url = reverse("missions:daily-goal-list-create")
        self.weekly_mission_list_url = reverse("missions:weekly-mission-list")

    def test_weekly_goal_pagination(self):
        """Test that WeeklyGoalFilter uses MetaPageNumberPagination correctly."""

        response = self.client.get(self.goal_list_url)
        assert response.status_code == status.HTTP_200_OK

        assert "meta" in response.data
        assert "results" in response.data

        meta = response.data["meta"]
        assert "count" in meta
        assert "current_page_number" in meta
        assert "limit" in meta
        assert "next" in meta
        assert "previous" in meta
        assert "next_page_number" in meta
        assert "previous_page_number" in meta
        assert "total_pages" in meta

        assert meta["count"] == 10
        assert meta["current_page_number"] == 1
        assert meta["previous"] is None
        assert meta["previous_page_number"] is None

        response = self.client.get(f"{self.goal_list_url}?limit=3")
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data["results"]) == 3
        assert response.data["meta"]["limit"] == 3
        assert response.data["meta"]["total_pages"] == 4
        assert response.data["meta"]["next_page_number"] == 2

        response = self.client.get(f"{self.goal_list_url}?priority=high&limit=2")
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data["results"]) == 2
        assert response.data["meta"]["limit"] == 2
        assert all(g["priority"] == "high" for g in response.data["results"])

    def test_daily_goal_pagination(self):
        """Test that DailyGoalFilter uses MetaPageNumberPagination correctly."""
        response = self.client.get(self.daily_goal_list_url)
        assert response.status_code == status.HTTP_200_OK

        assert "meta" in response.data
        assert "results" in response.data

        meta = response.data["meta"]
        assert meta["count"] == 10
        assert meta["current_page_number"] == 1

        response = self.client.get(f"{self.daily_goal_list_url}?page=2&limit=3")
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data["results"]) == 3
        assert response.data["meta"]["current_page_number"] == 2
        assert response.data["meta"]["previous_page_number"] == 1

    def test_weekly_mission_pagination(self):
        """Test that WeeklyMissionFilter uses MetaPageNumberPagination correctly."""

        valid_weeks = [1, 3, 4, 5, 6]
        for i, week in enumerate(valid_weeks):
            WeeklyMission.objects.create(
                user=self.user,
                six_week_period=self.period,
                domain=self.domain,
                week_number=week,
                practice_days=[2, 4, 6],
            )

        response = self.client.get(f"{self.weekly_mission_list_url}?limit=2")
        assert response.status_code == status.HTTP_200_OK

        assert "meta" in response.data
        assert "results" in response.data

        meta = response.data["meta"]
        assert meta["count"] == 6
        assert meta["current_page_number"] == 1
        assert meta["limit"] == 2
        assert meta["total_pages"] == 3
        assert len(response.data["results"]) == 2
