from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone, translation
from django.utils.translation import gettext as _
from rest_framework.test import APIClient
from rest_framework import status
import json
from datetime import date, timedelta
from apps.domains.models import Domain
from apps.missions.models import WeeklyMission, DailyGoal, DailyGoalProgress
from apps.okrs.models import SixWeekPeriod
from unittest.mock import patch

User = get_user_model()


class WeekdaysEndpointTests(TestCase):
    def setUp(self):
        self.client = APIClient()

        self.user = User.objects.create_user(
            email="<EMAIL>", password="password123", username="testuser"
        )
        self.client.force_authenticate(user=self.user)

        self.start_date = date.today()
        self.end_date = self.start_date + timedelta(days=42)

        self.period = SixWeekPeriod.objects.create(
            title="Test Period",
            user=self.user,
            start_date=self.start_date,
            end_date=self.end_date,
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
        )

        self.domain = Domain.objects.create(name="Test Domain", category="personal")

        self.mission = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=2,
            practice_days=[1, 3, 5],
        )

        self.daily_goal1 = DailyGoal.objects.create(
            weekly_mission=self.mission,
            title="Test Goal 1",
            priority=DailyGoal.PRIORITY_HIGH,
        )
        self.daily_goal2 = DailyGoal.objects.create(
            weekly_mission=self.mission,
            title="Test Goal 2",
            priority=DailyGoal.PRIORITY_LOW,
        )

        DailyGoalProgress.objects.create(
            daily_goal=self.daily_goal1,
            day_number=1,
            completed=True,
            completion_date=timezone.now(),
        )
        DailyGoalProgress.objects.create(
            daily_goal=self.daily_goal2, day_number=1, completed=False
        )
        DailyGoalProgress.objects.create(
            daily_goal=self.daily_goal1,
            day_number=3,
            completed=True,
            completion_date=timezone.now(),
        )

        self.weekdays_url = reverse(
            "missions:weekly-mission-weekdays", args=[self.mission.id]
        )

    def test_weekdays_endpoint_returns_correct_structure(self):
        """Test that the weekdays endpoint returns the correct structure for all 7 days."""
        response = self.client.get(self.weekdays_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()
        self.assertEqual(len(data), 7)

        for day_data in data:
            self.assertIn("day_number", day_data)
            self.assertIn("day_name", day_data)
            self.assertIn("date", day_data)
            self.assertIn("state", day_data)
            self.assertIn("is_practice_day", day_data)
            self.assertIn("completion_percentage", day_data)
            self.assertIn("daily_goals", day_data)

            self.assertTrue(1 <= day_data["day_number"] <= 7)

    def test_practice_days_are_properly_marked(self):
        """Test that practice days are properly marked as enabled."""
        response = self.client.get(self.weekdays_url)
        data = response.json()

        for day_data in data:
            day_number = day_data["day_number"]

            if day_number in [1, 3, 5]:
                self.assertEqual(day_data["state"], "enabled")
                self.assertTrue(day_data["is_practice_day"])
            else:
                self.assertEqual(day_data["state"], "disabled")
                self.assertFalse(day_data["is_practice_day"])

    def test_daily_goals_included_for_practice_days_only(self):
        """Test that daily goals are included only for practice days."""
        response = self.client.get(self.weekdays_url)
        data = response.json()

        for day_data in data:
            if day_data["is_practice_day"]:
                self.assertEqual(len(day_data["daily_goals"]), 2)
                self.assertTrue(
                    any(g["title"] == "Test Goal 1" for g in day_data["daily_goals"])
                )
                self.assertTrue(
                    any(g["title"] == "Test Goal 2" for g in day_data["daily_goals"])
                )
            else:
                self.assertEqual(len(day_data["daily_goals"]), 0)

    def test_completion_status_is_correct(self):
        """Test that completion status is correctly calculated."""
        response = self.client.get(self.weekdays_url)
        data = response.json()

        monday_data = next(d for d in data if d["day_number"] == 1)

        self.assertEqual(monday_data["completion_percentage"], 50.0)

        wednesday_data = next(d for d in data if d["day_number"] == 3)

        self.assertEqual(wednesday_data["completion_percentage"], 50.0)

        friday_data = next(d for d in data if d["day_number"] == 5)

        self.assertEqual(friday_data["completion_percentage"], 0.0)

    def test_individual_goals_completion_status(self):
        """Test that individual goals show correct completion status."""
        response = self.client.get(self.weekdays_url)
        data = response.json()

        monday_data = next(d for d in data if d["day_number"] == 1)

        goal1_data = next(
            g for g in monday_data["daily_goals"] if g["title"] == "Test Goal 1"
        )
        goal2_data = next(
            g for g in monday_data["daily_goals"] if g["title"] == "Test Goal 2"
        )

        self.assertTrue(goal1_data["completed"])
        self.assertIsNotNone(goal1_data["completion_date"])

        self.assertFalse(goal2_data["completed"])
        self.assertIsNone(goal2_data["completion_date"])

    def test_day_name_translation(self):
        """Test that day names are translated correctly."""

        response = self.client.get(self.weekdays_url)
        data = response.json()

        day_names = {d["day_number"]: d["day_name"] for d in data}
        self.assertEqual(day_names[1], "الاثنين")
        self.assertEqual(day_names[2], "الثلاثاء")
        self.assertEqual(day_names[3], "الأربعاء")
        self.assertEqual(day_names[4], "الخميس")
        self.assertEqual(day_names[5], "الجمعة")
        self.assertEqual(day_names[6], "السبت")
        self.assertEqual(day_names[7], "الأحد")

    def test_weekdays_permission_check(self):
        """Test that only authorized users can access mission weekdays."""

        other_user = User.objects.create_user(
            email="<EMAIL>", password="password123", username="otheruser"
        )

        self.client.force_authenticate(user=other_user)

        response = self.client.get(self.weekdays_url)

        self.assertIn(
            response.status_code, [status.HTTP_403_FORBIDDEN, status.HTTP_404_NOT_FOUND]
        )

    def test_weekdays_correct_dates(self):
        """Test that the weekdays endpoint returns correct dates based on week number."""
        response = self.client.get(self.weekdays_url)
        data = response.json()

        expected_week_start = self.start_date + timedelta(days=7)

        dates = sorted([(d["day_number"], d["date"]) for d in data], key=lambda x: x[0])

        for i, (day_number, date_str) in enumerate(dates):
            expected_date = expected_week_start + timedelta(days=i)
            self.assertEqual(date_str, expected_date.isoformat())
