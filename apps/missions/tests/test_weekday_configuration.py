from datetime import date ,timedelta 
from django .test import TestCase ,override_settings 
from django .contrib .auth import get_user_model 
from rest_framework .test import APIClient 
from rest_framework import status 
from apps .missions .models import WeeklyMission ,DailyGoal ,DailyGoalProgress 
from apps .missions .views .weekly_mission import get_weekday_mapping ,get_week_start_date 
from apps .okrs .models import SixWeekPeriod 
from apps .domains .models import Domain 
from apps .clubs .models import Club ,ClubType 

User =get_user_model ()


class WeekdayConfigurationTest (TestCase ):
    """Test suite for weekday configuration functionality."""

    def setUp (self ):
        """Set up test data."""
        self .client =APIClient ()
        self .client .defaults ["HTTP_ACCEPT_LANGUAGE"]="en"


        self .user =User .objects .create_user (
        username ="testuser",
        email ="<EMAIL>",
        password ="testpass123",
        fullname ="Test User",
        )
        self .client .force_authenticate (user =self .user )


        self .club_type =ClubType .objects .create (name ="Test Club Type")
        self .club =Club .objects .create (
        name ="Test Club",
        type =self .club_type ,
        manager =self .user ,
        )
        self .club .members .add (self .user )


        self .domain =Domain .objects .create (
        name ="Test Domain",
        category ="education",
        )


        start_date =date (2025 ,5 ,26 )
        self .period =SixWeekPeriod .objects .create (
        title ="Test Period",
        user =self .user ,
        start_date =start_date ,
        end_date =start_date +timedelta (days =42 ),
        status ="in_progress",
        )


        self .weekly_mission =WeeklyMission .objects .create (
        user =self .user ,
        six_week_period =self .period ,
        domain =self .domain ,
        week_number =1 ,
        practice_days =[1 ,3 ,5 ],
        )


        self .daily_goal =DailyGoal .objects .create (
        weekly_mission =self .weekly_mission ,
        title ="Test Daily Goal",
        priority ="high",
        )

    def test_get_weekday_mapping_sunday_start (self ):
        """Test weekday mapping when week starts on Sunday."""
        day_names_en ,day_names_ar =get_weekday_mapping ("sunday")


        self .assertEqual (day_names_en [1 ],"Sunday")
        self .assertEqual (day_names_ar [1 ],"الأحد")


        self .assertEqual (day_names_en [2 ],"Monday")
        self .assertEqual (day_names_ar [2 ],"الاثنين")


        self .assertEqual (day_names_en [7 ],"Saturday")
        self .assertEqual (day_names_ar [7 ],"السبت")

    def test_get_weekday_mapping_monday_start (self ):
        """Test weekday mapping when week starts on Monday."""
        day_names_en ,day_names_ar =get_weekday_mapping ("monday")


        self .assertEqual (day_names_en [1 ],"Monday")
        self .assertEqual (day_names_ar [1 ],"الاثنين")


        self .assertEqual (day_names_en [2 ],"Tuesday")
        self .assertEqual (day_names_ar [2 ],"الثلاثاء")


        self .assertEqual (day_names_en [7 ],"Sunday")
        self .assertEqual (day_names_ar [7 ],"الأحد")

    def test_get_week_start_date_sunday_start (self ):
        """Test week start date calculation when week starts on Sunday."""

        period_start =date (2025 ,5 ,26 )


        week1_start =get_week_start_date (period_start ,1 ,"sunday")
        expected_week1_start =date (2025 ,5 ,25 )
        self .assertEqual (week1_start ,expected_week1_start )


        week2_start =get_week_start_date (period_start ,2 ,"sunday")
        expected_week2_start =date (2025 ,6 ,1 )
        self .assertEqual (week2_start ,expected_week2_start )

    def test_get_week_start_date_monday_start (self ):
        """Test week start date calculation when week starts on Monday."""

        period_start =date (2025 ,5 ,26 )


        week1_start =get_week_start_date (period_start ,1 ,"monday")
        expected_week1_start =date (2025 ,5 ,26 )
        self .assertEqual (week1_start ,expected_week1_start )


        week2_start =get_week_start_date (period_start ,2 ,"monday")
        expected_week2_start =date (2025 ,6 ,2 )
        self .assertEqual (week2_start ,expected_week2_start )

    @override_settings (WEEKDAY_START_FROM ="sunday")
    def test_weekdays_api_sunday_start_english (self ):
        """Test weekdays API endpoint with Sunday start setting in English."""
        url =f"/api/v1/missions/weekly-missions/{self.weekly_mission.id}/weekdays/"

        response =self .client .get (url )

        self .assertEqual (response .status_code ,status .HTTP_200_OK )
        weekdays_data =response .json ()


        self .assertEqual (len (weekdays_data ),7 )


        day1 =weekdays_data [0 ]
        self .assertEqual (day1 ["day_number"],1 )
        self .assertEqual (day1 ["day_name"],"Sunday")
        self .assertEqual (day1 ["date"],"2025-05-25")


        day2 =weekdays_data [1 ]
        self .assertEqual (day2 ["day_number"],2 )
        self .assertEqual (day2 ["day_name"],"Monday")
        self .assertEqual (day2 ["date"],"2025-05-26")


        day7 =weekdays_data [6 ]
        self .assertEqual (day7 ["day_number"],7 )
        self .assertEqual (day7 ["day_name"],"Saturday")
        self .assertEqual (day7 ["date"],"2025-05-31")

    @override_settings (WEEKDAY_START_FROM ="monday")
    def test_weekdays_api_monday_start_english (self ):
        """Test weekdays API endpoint with Monday start setting in English."""
        url =f"/api/v1/missions/weekly-missions/{self.weekly_mission.id}/weekdays/"

        response =self .client .get (url )

        self .assertEqual (response .status_code ,status .HTTP_200_OK )
        weekdays_data =response .json ()


        self .assertEqual (len (weekdays_data ),7 )


        day1 =weekdays_data [0 ]
        self .assertEqual (day1 ["day_number"],1 )
        self .assertEqual (day1 ["day_name"],"Monday")
        self .assertEqual (day1 ["date"],"2025-05-26")


        day2 =weekdays_data [1 ]
        self .assertEqual (day2 ["day_number"],2 )
        self .assertEqual (day2 ["day_name"],"Tuesday")
        self .assertEqual (day2 ["date"],"2025-05-27")


        day7 =weekdays_data [6 ]
        self .assertEqual (day7 ["day_number"],7 )
        self .assertEqual (day7 ["day_name"],"Sunday")
        self .assertEqual (day7 ["date"],"2025-06-01")

    @override_settings (WEEKDAY_START_FROM ="sunday")
    def test_weekdays_api_sunday_start_arabic (self ):
        """Test weekdays API endpoint with Sunday start setting in Arabic."""
        self .client .defaults ["HTTP_ACCEPT_LANGUAGE"]="ar"

        url =f"/api/v1/missions/weekly-missions/{self.weekly_mission.id}/weekdays/"

        response =self .client .get (url )

        self .assertEqual (response .status_code ,status .HTTP_200_OK )
        weekdays_data =response .json ()


        self .assertEqual (len (weekdays_data ),7 )


        day1 =weekdays_data [0 ]
        self .assertEqual (day1 ["day_number"],1 )
        self .assertEqual (day1 ["day_name"],"الأحد")
        self .assertEqual (day1 ["date"],"2025-05-25")


        day2 =weekdays_data [1 ]
        self .assertEqual (day2 ["day_number"],2 )
        self .assertEqual (day2 ["day_name"],"الاثنين")
        self .assertEqual (day2 ["date"],"2025-05-26")


        day7 =weekdays_data [6 ]
        self .assertEqual (day7 ["day_number"],7 )
        self .assertEqual (day7 ["day_name"],"السبت")
        self .assertEqual (day7 ["date"],"2025-05-31")

    @override_settings (WEEKDAY_START_FROM ="monday")
    def test_weekdays_api_monday_start_arabic (self ):
        """Test weekdays API endpoint with Monday start setting in Arabic."""
        self .client .defaults ["HTTP_ACCEPT_LANGUAGE"]="ar"

        url =f"/api/v1/missions/weekly-missions/{self.weekly_mission.id}/weekdays/"

        response =self .client .get (url )

        self .assertEqual (response .status_code ,status .HTTP_200_OK )
        weekdays_data =response .json ()


        self .assertEqual (len (weekdays_data ),7 )


        day1 =weekdays_data [0 ]
        self .assertEqual (day1 ["day_number"],1 )
        self .assertEqual (day1 ["day_name"],"الاثنين")
        self .assertEqual (day1 ["date"],"2025-05-26")


        day2 =weekdays_data [1 ]
        self .assertEqual (day2 ["day_number"],2 )
        self .assertEqual (day2 ["day_name"],"الثلاثاء")
        self .assertEqual (day2 ["date"],"2025-05-27")


        day7 =weekdays_data [6 ]
        self .assertEqual (day7 ["day_number"],7 )
        self .assertEqual (day7 ["day_name"],"الأحد")
        self .assertEqual (day7 ["date"],"2025-06-01")

    @override_settings (WEEKDAY_START_FROM ="sunday")
    def test_practice_days_with_sunday_start (self ):
        """Test that practice days are handled correctly with Sunday start."""

        self .weekly_mission .practice_days =[1 ,3 ,5 ]
        self .weekly_mission .save ()

        url =f"/api/v1/missions/weekly-missions/{self.weekly_mission.id}/weekdays/"

        response =self .client .get (url )

        self .assertEqual (response .status_code ,status .HTTP_200_OK )
        weekdays_data =response .json ()


        day1 =weekdays_data [0 ]
        self .assertEqual (day1 ["day_number"],1 )
        self .assertEqual (day1 ["state"],"enabled")
        self .assertTrue (day1 ["is_practice_day"])


        day2 =weekdays_data [1 ]
        self .assertEqual (day2 ["day_number"],2 )
        self .assertEqual (day2 ["state"],"disabled")
        self .assertFalse (day2 ["is_practice_day"])


        day3 =weekdays_data [2 ]
        self .assertEqual (day3 ["day_number"],3 )
        self .assertEqual (day3 ["state"],"enabled")
        self .assertTrue (day3 ["is_practice_day"])

    @override_settings (WEEKDAY_START_FROM ="monday")
    def test_practice_days_with_monday_start (self ):
        """Test that practice days are handled correctly with Monday start."""

        self .weekly_mission .practice_days =[1 ,3 ,5 ]
        self .weekly_mission .save ()

        url =f"/api/v1/missions/weekly-missions/{self.weekly_mission.id}/weekdays/"

        response =self .client .get (url )

        self .assertEqual (response .status_code ,status .HTTP_200_OK )
        weekdays_data =response .json ()


        day1 =weekdays_data [0 ]
        self .assertEqual (day1 ["day_number"],1 )
        self .assertEqual (day1 ["state"],"enabled")
        self .assertTrue (day1 ["is_practice_day"])


        day2 =weekdays_data [1 ]
        self .assertEqual (day2 ["day_number"],2 )
        self .assertEqual (day2 ["state"],"disabled")
        self .assertFalse (day2 ["is_practice_day"])


        day3 =weekdays_data [2 ]
        self .assertEqual (day3 ["day_number"],3 )
        self .assertEqual (day3 ["state"],"enabled")
        self .assertTrue (day3 ["is_practice_day"])

    def test_week_start_date_edge_cases (self ):
        """Test week start date calculation with various edge cases."""

        sunday_start =date (2025 ,6 ,1 )


        week1_sunday =get_week_start_date (sunday_start ,1 ,"sunday")
        self .assertEqual (week1_sunday ,sunday_start )


        week1_monday =get_week_start_date (sunday_start ,1 ,"monday")
        self .assertEqual (week1_monday ,date (2025 ,5 ,26 ))


        saturday_start =date (2025 ,5 ,31 )


        week1_from_sat_sunday =get_week_start_date (saturday_start ,1 ,"sunday")
        self .assertEqual (week1_from_sat_sunday ,date (2025 ,5 ,25 ))


        week1_from_sat_monday =get_week_start_date (saturday_start ,1 ,"monday")
        self .assertEqual (week1_from_sat_monday ,date (2025 ,5 ,26 ))

    @override_settings (WEEKDAY_START_FROM ="invalid")
    def test_invalid_weekday_start_setting (self ):
        """Test behavior when WEEKDAY_START_FROM has an invalid value."""
        url =f"/api/v1/missions/weekly-missions/{self.weekly_mission.id}/weekdays/"

        response =self .client .get (url )


        self .assertEqual (response .status_code ,status .HTTP_200_OK )
        weekdays_data =response .json ()


        day1 =weekdays_data [0 ]
        self .assertEqual (day1 ["day_number"],1 )
        self .assertEqual (day1 ["day_name"],"Monday")

    def test_daily_goal_progress_with_different_week_starts (self ):
        """Test that daily goal progress is correctly handled with different week starts."""

        DailyGoalProgress .objects .create (
        daily_goal =self .daily_goal ,
        day_number =2 ,
        completed =True ,
        )


        with override_settings (WEEKDAY_START_FROM ="sunday"):
            url =f"/api/v1/missions/weekly-missions/{self.weekly_mission.id}/weekdays/"
            response =self .client .get (url )
            weekdays_data =response .json ()

            day2 =weekdays_data [1 ]
            self .assertEqual (day2 ["day_name"],"Monday")


        with override_settings (WEEKDAY_START_FROM ="monday"):
            url =f"/api/v1/missions/weekly-missions/{self.weekly_mission.id}/weekdays/"
            response =self .client .get (url )
            weekdays_data =response .json ()

            day2 =weekdays_data [1 ]
            self .assertEqual (day2 ["day_name"],"Tuesday")