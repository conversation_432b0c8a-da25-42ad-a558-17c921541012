import pytest
from rest_framework.test import APIClient
from django.urls import reverse
from rest_framework import status
from apps.missions.models import WeeklyMission, DailyGoal, DailyGoalProgress, Goal
from apps.okrs.models import SixWeekPeriod
from apps.domains.models import Domain
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta

User = get_user_model()


@pytest.mark.django_db
class TestWeeklyMissionsAndDailyGoals:
    @pytest.fixture(autouse=True)
    def setup(self):
        self.user = User.objects.create_user(
            email="<EMAIL>", password="testpassword", username="testuser"
        )

        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

        self.domain = Domain.objects.create(name="Health")

        start_date = timezone.now().date()
        end_date = start_date + timedelta(days=42)
        self.period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            start_date=start_date,
            end_date=end_date,
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
        )

        self.goal = Goal.objects.create(
            user=self.user,
            six_week_period=self.period,
            title="Exercise regularly",
            priority=Goal.PRIORITY_HIGH,
        )

        self.weekly_mission_list_url = reverse("missions:weekly-mission-list")
        self.goal_list_url = reverse("missions:goal-list-create")
        self.daily_goal_list_url = reverse("missions:daily-goal-list-create")
        self.daily_goal_bulk_create_url = reverse("missions:daily-goal-bulk-create")

    def test_create_weekly_mission(self):
        """Test creating a weekly mission"""
        data = {
            "six_week_period": str(self.period.id),
            "domain": str(self.domain.id),
            "week_number": 2,
            "practice_days": [1, 3, 5],
            "goal_ids": [str(self.goal.id)],
        }

        response = self.client.post(self.weekly_mission_list_url, data, format="json")

        if response.status_code != status.HTTP_201_CREATED:
            print(f"Failed to create weekly mission. Response: {response.status_code}")
            print(f"Response content: {response.content.decode()}")

        assert response.status_code == status.HTTP_201_CREATED

        weekly_mission_id = response.data["id"]
        weekly_mission = WeeklyMission.objects.get(id=weekly_mission_id)
        assert weekly_mission.user == self.user
        assert weekly_mission.six_week_period == self.period
        assert weekly_mission.domain == self.domain
        assert weekly_mission.week_number == 2
        assert weekly_mission.practice_days == [1, 3, 5]

        return weekly_mission

    def test_create_daily_goals(self):
        """Test creating daily goals for a weekly mission"""
        weekly_mission = self.test_create_weekly_mission()

        data = {
            "weekly_mission": str(weekly_mission.id),
            "title": "Do 30 minutes of cardio",
            "priority": DailyGoal.PRIORITY_HIGH,
        }

        response = self.client.post(self.daily_goal_list_url, data, format="json")
        assert response.status_code == status.HTTP_201_CREATED

        daily_goal_id = response.data["id"]
        daily_goal = DailyGoal.objects.get(id=daily_goal_id)
        assert daily_goal.weekly_mission == weekly_mission
        assert daily_goal.title == "Do 30 minutes of cardio"
        assert daily_goal.priority == DailyGoal.PRIORITY_HIGH

        progress_entries = daily_goal.daily_progress.all()
        assert progress_entries.count() == 3

        progress_day_numbers = [p.day_number for p in progress_entries]
        assert sorted(progress_day_numbers) == [1, 3, 5]

        return daily_goal

    def test_bulk_create_daily_goals(self):
        """Test bulk creation of daily goals"""
        weekly_mission = self.test_create_weekly_mission()

        data = {
            "weekly_mission": str(weekly_mission.id),
            "goals": [
                {
                    "title": "Do 30 minutes of cardio",
                    "priority": DailyGoal.PRIORITY_HIGH,
                },
                {"title": "Do 20 push-ups", "priority": DailyGoal.PRIORITY_LOW},
            ],
        }

        response = self.client.post(
            self.daily_goal_bulk_create_url, data, format="json"
        )
        assert response.status_code == status.HTTP_201_CREATED

        created_goals = response.data["created"]
        assert len(created_goals) == 2

        assert DailyGoal.objects.filter(weekly_mission=weekly_mission).count() >= 2

        assert DailyGoal.objects.filter(
            weekly_mission=weekly_mission, title="Do 30 minutes of cardio"
        ).exists()
        assert DailyGoal.objects.filter(
            weekly_mission=weekly_mission, title="Do 20 push-ups"
        ).exists()

        return weekly_mission

    def test_complete_daily_goal_progress(self):
        """Test marking daily goal progress as completed"""
        daily_goal = self.test_create_daily_goals()

        progress = daily_goal.get_progress_for_day(1)
        assert progress is not None
        assert not progress.completed

        url = reverse("missions:daily-goal-complete", args=[daily_goal.id, 1])
        response = self.client.post(url)
        assert response.status_code == status.HTTP_200_OK

        progress.refresh_from_db()
        assert progress.completed
        assert progress.completion_date is not None

        daily_goal.refresh_from_db()
        assert not daily_goal.is_completed()

        for day in [3, 5]:
            url = reverse("missions:daily-goal-complete", args=[daily_goal.id, day])
            self.client.post(url)

        daily_goal.refresh_from_db()
        assert daily_goal.is_completed()

    def test_weekdays_endpoint(self):
        """Test the weekdays endpoint for a weekly mission"""
        weekly_mission = self.test_bulk_create_daily_goals()

        url = reverse("missions:weekly-mission-weekdays", args=[weekly_mission.id])
        response = self.client.get(url)
        assert response.status_code == status.HTTP_200_OK

        assert len(response.data) == 7

        for day_data in response.data:
            day_number = day_data["day_number"]
            if day_number in [1, 3, 5]:
                assert day_data["is_practice_day"]
                assert day_data["state"] == "enabled"
                assert len(day_data["daily_goals"]) > 0
            else:
                assert not day_data["is_practice_day"]
                assert day_data["state"] == "disabled"
                assert len(day_data["daily_goals"]) == 0

    def test_filter_missions_by_week(self):
        """Test filtering missions by week number"""
        weekly_mission = self.test_create_weekly_mission()

        WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=3,
            practice_days=[2, 4, 6],
        )

        url = f"{self.weekly_mission_list_url}by_week/?week=2"
        response = self.client.get(url)
        assert response.status_code == status.HTTP_200_OK

        assert len(response.data["results"]) == 1
        assert response.data["results"][0]["week_number"] == 2

        url = f"{self.weekly_mission_list_url}by_week/?week=3"
        response = self.client.get(url)
        assert response.status_code == status.HTTP_200_OK

        assert len(response.data["results"]) == 1
        assert response.data["results"][0]["week_number"] == 3

    def test_toggle_mission_completion(self):
        """Test toggling the completion status of a mission"""
        weekly_mission = self.test_create_weekly_mission()

        assert not weekly_mission.is_completed()

        url = reverse(
            "missions:weekly-mission-toggle-completion", args=[weekly_mission.id]
        )
        response = self.client.post(url)
        assert response.status_code == status.HTTP_200_OK

        weekly_mission.refresh_from_db()
        assert weekly_mission.is_completed()
        assert weekly_mission.completion_date is not None

        response = self.client.post(url)
        assert response.status_code == status.HTTP_200_OK

        weekly_mission.refresh_from_db()
        assert not weekly_mission.is_completed()
        assert weekly_mission.completion_date is None

    def test_rate_weekly_success(self):
        """Test rating weekly success"""
        weekly_mission = self.test_create_weekly_mission()

        url = reverse("missions:weekly-mission-rate-success", args=[weekly_mission.id])
        data = {"rating": 8}

        response = self.client.post(url, data, format="json")
        assert response.status_code == status.HTTP_200_OK

        assert weekly_mission.success_ratings.count() == 1
        assert weekly_mission.success_ratings.first().rating == 8
