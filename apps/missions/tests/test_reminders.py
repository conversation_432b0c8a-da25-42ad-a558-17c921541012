import pytest
from django.test import TestCase
from django.utils import timezone
from datetime import datetime, timedelta
from unittest.mock import patch
from django.utils.timezone import make_aware
import pytz

from core.tasks.notifications.mission_reminder import send_mission_reminder_notification
from apps.missions.models import WeeklyMission
from apps.accounts.user.models import User
from apps.domains.models import Domain
from apps.okrs.models import SixWeekPeriod


@pytest.mark.django_db
class TestMissionReminders(TestCase):
    def setUp(self):
        self.user = User.objects.create(
            email="<EMAIL>", fullname="Test User", password="test"
        )
        self.domain = Domain.objects.create(name="test-domain", category="language")
        self.six_week_period = SixWeekPeriod.objects.create(
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timedelta(days=42),
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
            user=self.user,
            title="Test Six Week Period",
        )

    @patch("apps.missions.signals.send_mission_reminder_notification")
    def test_schedule_reminders_creates_tasks_for_future_dates(self, mock_task):
        mock_task.apply_async.reset_mock()
        mission = WeeklyMission.objects.create(
            practice_days=[0, 2, 4],
            reminder_time=datetime.strptime("10:00", "%H:%M").time(),
            user=self.user,
            week_number=1,
            domain=self.domain,
            six_week_period=self.six_week_period,
        )
        current_time = timezone.now()
        today = current_time.date()
        expected_calls = 0
        for day in mission.practice_days:
            reminder_date = today + timedelta(days=(day - today.weekday()) % 7)
            scheduled_time = make_aware(
                datetime.combine(reminder_date, mission.reminder_time)
            )
            if scheduled_time > current_time:
                expected_calls += 1
                mock_task.apply_async.assert_any_call(
                    eta=scheduled_time.astimezone(pytz.UTC), args=[mission.id]
                )
        assert mock_task.apply_async.call_count == expected_calls

    @patch("core.tasks.notifications.mission_reminder.send_email_with_html")
    def test_send_mission_reminder_notification_success(self, mock_send_email):
        mission = WeeklyMission.objects.create(
            practice_days=[0, 2, 4],
            reminder_time=datetime.strptime("10:00", "%H:%M").time(),
            user=self.user,
            week_number=1,
            domain=self.domain,
            six_week_period=self.six_week_period,
        )
        call_kwargs = mock_send_email.delay.call_args[1]
        assert self.user.email in call_kwargs["to_email"]
        assert f"Daily Mission Reminder: {mission.id}" in call_kwargs["subject"]

    @patch("core.tasks.notifications.mission_reminder.logger")
    @patch("core.tasks.notifications.mission_reminder.send_email_with_html")
    def test_send_mission_reminder_notification_handles_errors(
        self, mock_send_email, mock_logger
    ):
        mock_send_email.delay.side_effect = Exception("Test error")
        try:
            send_mission_reminder_notification(123)
        except Exception as e:
            self.fail(f"Function raised an exception: {e}")

    @patch("apps.missions.signals.send_mission_reminder_notification")
    def test_no_reminders_scheduled_for_past_times(self, mock_task):
        mock_task.apply_async.reset_mock()
        past_time = (timezone.now() - timedelta(hours=1)).time()
        WeeklyMission.objects.create(
            practice_days=[timezone.now().weekday()],
            reminder_time=past_time,
            user=self.user,
            week_number=1,
            domain=self.domain,
            six_week_period=self.six_week_period,
        )
        mock_task.apply_async.assert_not_called()

    @patch("apps.missions.signals.send_mission_reminder_notification")
    def test_multiple_practice_days_scheduling(self, mock_task):
        mock_task.apply_async.reset_mock()
        mission = WeeklyMission.objects.create(
            practice_days=[0, 1, 2, 3, 4],
            reminder_time=datetime.strptime("15:00", "%H:%M").time(),
            user=self.user,
            week_number=1,
            domain=self.domain,
            six_week_period=self.six_week_period,
        )
        current_time = timezone.now()
        today = current_time.date()
        future_calls = 0
        for day in mission.practice_days:
            reminder_date = today + timedelta(days=(day - today.weekday()) % 7)
            scheduled_time = make_aware(
                datetime.combine(reminder_date, mission.reminder_time)
            )
            if scheduled_time > current_time:
                future_calls += 1
                mock_task.apply_async.assert_any_call(
                    eta=scheduled_time.astimezone(pytz.UTC), args=[mission.id]
                )
        assert mock_task.apply_async.call_count == future_calls

    @patch("core.tasks.notifications.mission_reminder.send_email_with_html")
    def test_reminder_notification_content(self, mock_send_email):
        mock_send_email.delay.reset_mock()
        mission = WeeklyMission.objects.create(
            practice_days=[0, 2, 4],
            reminder_time=datetime.strptime("10:00", "%H:%M").time(),
            user=self.user,
            week_number=1,
            domain=self.domain,
            six_week_period=self.six_week_period,
        )
        call_kwargs = mock_send_email.delay.call_args[1]
        assert "html_content" in call_kwargs
        assert "text_content" in call_kwargs
        assert isinstance(call_kwargs["html_content"], str)
        assert isinstance(call_kwargs["text_content"], str)
