from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from apps.missions.models import Goal, DailyGoal, DailyGoalProgress, WeeklyMission
from apps.okrs.models import SixWeekPeriod
from apps.domains.models import Domain
import uuid
from django.utils import timezone
from datetime import timedelta

User = get_user_model()


class DailyGoalProgressTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            email="<EMAIL>", password="password123", username="testuser"
        )
        self.client.force_authenticate(user=self.user)

        start_date = timezone.now().date()
        end_date = start_date + timedelta(days=42)

        self.six_week_period = SixWeekPeriod.objects.create(
            title="Test Period",
            user=self.user,
            start_date=start_date,
            end_date=end_date,
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
        )

        self.domain = Domain.objects.create(
            name="Test Domain", category="personal", description="Test description"
        )

        self.goal = Goal.objects.create(
            title="Test Goal", user=self.user, six_week_period=self.six_week_period
        )

        self.weekly_mission = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.six_week_period,
            domain=self.domain,
            week_number=2,
            practice_days=[1, 3, 5],
        )

        self.weekly_mission.goals.add(self.goal)

        self.daily_goal = DailyGoal.objects.create(
            weekly_mission=self.weekly_mission, title="Test Daily Goal"
        )

        self.daily_goal.create_daily_progress_entries()

        self.update_progress_url = reverse(
            "missions:daily-goal-update-progress", args=[self.daily_goal.id]
        )
        self.progress_by_day_url = reverse(
            "missions:daily-goal-progress-by-day", args=[self.daily_goal.id]
        )

    def test_create_daily_goal_creates_progress_entries(self):
        """Test that creating a daily goal automatically creates progress entries for practice days"""

        progress_entries = DailyGoalProgress.objects.filter(daily_goal=self.daily_goal)
        self.assertEqual(progress_entries.count(), 3)

        day_numbers = [entry.day_number for entry in progress_entries]
        self.assertListEqual(sorted(day_numbers), [1, 3, 5])

        for entry in progress_entries:
            self.assertFalse(entry.completed)

    def test_update_progress_for_existing_day(self):
        """Test updating progress for an existing practice day"""
        data = {"day_number": 1, "completed": True}

        response = self.client.post(self.update_progress_url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        progress = DailyGoalProgress.objects.get(
            daily_goal=self.daily_goal, day_number=1
        )
        self.assertTrue(progress.completed)
        self.assertIsNotNone(progress.completion_date)

        response_json = response.json()
        self.assertEqual(response_json["day_number"], 1)
        self.assertTrue(response_json["completed"])

    def test_update_progress_for_nonexistent_day(self):
        """Test updating progress for a day that's not in practice_days"""

        data = {"day_number": 2, "completed": True}

        response = self.client.post(self.update_progress_url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        progress = DailyGoalProgress.objects.get(
            daily_goal=self.daily_goal, day_number=2
        )
        self.assertTrue(progress.completed)

        self.assertEqual(
            DailyGoalProgress.objects.filter(daily_goal=self.daily_goal).count(), 4
        )

    def test_mark_progress_as_incomplete(self):
        """Test marking a completed progress entry as incomplete"""

        data = {"day_number": 1, "completed": True}
        self.client.post(self.update_progress_url, data)

        data["completed"] = False
        response = self.client.post(self.update_progress_url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        progress = DailyGoalProgress.objects.get(
            daily_goal=self.daily_goal, day_number=1
        )
        self.assertFalse(progress.completed)
        self.assertIsNone(progress.completion_date)

    def test_get_progress_for_specific_day(self):
        """Test getting progress for a specific day"""

        DailyGoalProgress.objects.filter(
            daily_goal=self.daily_goal, day_number=1
        ).update(completed=True)

        response = self.client.get(f"{self.progress_by_day_url}?day_number=1")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_json = response.json()
        self.assertEqual(response_json["day_number"], 1)
        self.assertTrue(response_json["completed"])

    def test_get_progress_for_nonexistent_day(self):
        """Test getting progress for a day that doesn't have an entry yet"""
        response = self.client.get(f"{self.progress_by_day_url}?day_number=7")
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_invalid_day_number_returns_error(self):
        """Test that invalid day numbers return appropriate errors"""

        response = self.client.get(f"{self.progress_by_day_url}?day_number=8")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        data = {"day_number": 8, "completed": True}
        response = self.client.post(self.update_progress_url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_overall_completion_percentage(self):
        """Test the calculation of overall completion percentage"""

        serializer_response = self.client.get(
            reverse("missions:daily-goal-detail", args=[self.daily_goal.id])
        )
        response_json = serializer_response.json()
        self.assertEqual(response_json["completion_percentage"], 0)

        DailyGoalProgress.objects.filter(
            daily_goal=self.daily_goal, day_number=1
        ).update(completed=True)

        serializer_response = self.client.get(
            reverse("missions:daily-goal-detail", args=[self.daily_goal.id])
        )
        response_json = serializer_response.json()
        self.assertAlmostEqual(
            response_json["completion_percentage"], 33.33, delta=0.01
        )

        DailyGoalProgress.objects.filter(
            daily_goal=self.daily_goal, day_number=3
        ).update(completed=True)

        serializer_response = self.client.get(
            reverse("missions:daily-goal-detail", args=[self.daily_goal.id])
        )
        response_json = serializer_response.json()
        self.assertAlmostEqual(
            response_json["completion_percentage"], 66.67, delta=0.01
        )

        DailyGoalProgress.objects.filter(
            daily_goal=self.daily_goal, day_number=5
        ).update(completed=True)

        serializer_response = self.client.get(
            reverse("missions:daily-goal-detail", args=[self.daily_goal.id])
        )
        response_json = serializer_response.json()
        self.assertEqual(response_json["completion_percentage"], 100)

    def test_multiple_daily_goals(self):
        """Test that each daily goal has its own set of progress entries"""

        daily_goal2 = DailyGoal.objects.create(
            weekly_mission=self.weekly_mission, title="Second Daily Goal"
        )
        daily_goal2.create_daily_progress_entries()

        progress1 = DailyGoalProgress.objects.filter(daily_goal=self.daily_goal)
        progress2 = DailyGoalProgress.objects.filter(daily_goal=daily_goal2)

        self.assertEqual(progress1.count(), 3)
        self.assertEqual(progress2.count(), 3)

        data = {"day_number": 1, "completed": True}
        self.client.post(
            reverse("missions:daily-goal-update-progress", args=[self.daily_goal.id]),
            data,
        )

        self.assertTrue(
            DailyGoalProgress.objects.get(
                daily_goal=self.daily_goal, day_number=1
            ).completed
        )
        self.assertFalse(
            DailyGoalProgress.objects.get(
                daily_goal=daily_goal2, day_number=1
            ).completed
        )
