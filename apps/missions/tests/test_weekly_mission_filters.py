from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
import uuid
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status

from apps.missions.models import DailyGoal, WeeklyMission, DailyGoalProgress
from apps.okrs.models import SixWeekPeriod
from apps.domains.models import Domain

User = get_user_model()


class TestWeeklyMissionFilter(TestCase):
    """Test class for WeeklyMission filter fields"""

    def setUp(self):
        """Set up test data for filter tests"""

        self.user = User.objects.create_user(
            email=f"testuser_{uuid.uuid4()}@example.com",
            password="testpassword",
            username=f"testuser_{uuid.uuid4()}",
        )

        self.domain1 = Domain.objects.create(
            name="Domain 1", category="personal", description="Test domain 1"
        )

        self.domain2 = Domain.objects.create(
            name="Domain 2", category="work", description="Test domain 2"
        )

        start_date = timezone.now().date()
        end_date = start_date + timedelta(days=42)

        self.period1 = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period 1",
            start_date=start_date,
            end_date=end_date,
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
        )

        self.period2 = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period 2",
            start_date=start_date + timedelta(days=50),
            end_date=end_date + timedelta(days=50),
            status=SixWeekPeriod.STATUS_DRAFT,
        )

        self.missions = []
        for week in range(1, 5):
            mission = WeeklyMission.objects.create(
                user=self.user,
                six_week_period=self.period1,
                domain=self.domain1 if week % 2 == 1 else self.domain2,
                week_number=week,
                practice_days=[1, 3, 5] if week % 2 == 1 else [2, 4, 6],
            )
            self.missions.append(mission)

        self.period2_mission = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period2,
            domain=self.domain2,
            week_number=1,
            practice_days=[1, 2, 3],
        )

        self.completed_mission = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period1,
            domain=self.domain2,
            week_number=5,
            practice_days=[2, 4, 6],
        )

        self.daily_goals_week1 = [
            DailyGoal.objects.create(
                weekly_mission=self.missions[0],
                title=f"Daily Goal {i} for Week 1",
                priority=(
                    DailyGoal.PRIORITY_HIGH if i % 2 == 0 else DailyGoal.PRIORITY_LOW
                ),
            )
            for i in range(3)
        ]

        self.daily_goals_completed = [
            DailyGoal.objects.create(
                weekly_mission=self.completed_mission,
                title=f"Daily Goal {i} for Completed Mission",
                priority=DailyGoal.PRIORITY_HIGH,
            )
            for i in range(2)
        ]

        for goal in self.daily_goals_week1:
            for day in self.missions[0].practice_days:
                DailyGoalProgress.objects.create(
                    daily_goal=goal,
                    day_number=day,
                    completed=False,
                )

        for goal in self.daily_goals_completed:
            for day in self.completed_mission.practice_days:
                progress = DailyGoalProgress.objects.create(
                    daily_goal=goal,
                    day_number=day,
                    completed=True,
                    completion_date=timezone.now(),
                )

        self.completed_mission.mark_completed()

        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

        self.missions_url = reverse("missions:weekly-mission-list")

    def _get_results(self, response):
        """Helper method to extract results from paginated response."""
        return response.data.get("results", [])

    def test_filter_by_week_number(self):
        """Test filtering by week_number field"""
        url = f"{self.missions_url}?week_number=1"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 2)

        url = f"{self.missions_url}?week_number=3"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 1)

        url = f"{self.missions_url}?week_number=10"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 0)

    def test_filter_by_domain(self):
        """Test filtering by domain field"""
        url = f"{self.missions_url}?domain={self.domain1.id}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 2)

        url = f"{self.missions_url}?domain={self.domain2.id}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 4)

        url = f"{self.missions_url}?domain=00000000-0000-0000-0000-000000000000"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 0)

    def test_filter_by_six_week_period(self):
        """Test filtering by six_week_period field"""
        url = f"{self.missions_url}?six_week_period={self.period1.id}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 5)

        url = f"{self.missions_url}?six_week_period={self.period2.id}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 1)

        url = (
            f"{self.missions_url}?six_week_period=00000000-0000-0000-0000-000000000000"
        )
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 0)

    def test_filter_by_user(self):
        """Test filtering by user field"""
        url = f"{self.missions_url}?user={self.user.id}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 6)

        url = f"{self.missions_url}?user=00000000-0000-0000-0000-000000000000"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 0)

    def test_filter_by_created_at(self):
        """Test filtering by created field"""

        today = timezone.now().date().isoformat()
        yesterday = (timezone.now().date() - timedelta(days=1)).isoformat()
        tomorrow = (timezone.now().date() + timedelta(days=1)).isoformat()

        url = f"{self.missions_url}?created_after={yesterday}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertTrue(
            len(results) > 0, "Should return missions created after yesterday"
        )

        url = f"{self.missions_url}?created_before={tomorrow}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 6)

    def test_filter_by_is_completed(self):
        """Test filtering by is_completed field"""

        url = f"{self.missions_url}?is_completed=true"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 1)

        url = f"{self.missions_url}?is_completed=false"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertTrue(
            len(results) >= 5,
            f"Expected at least 5 incomplete missions, got {len(results)}",
        )

        completed_ids = [item["id"] for item in results]
        self.assertNotIn(str(self.completed_mission.id), completed_ids)

    def test_filter_by_has_daily_goals(self):
        """Test filtering by has_daily_goals field"""

        url = f"{self.missions_url}?has_daily_goals=true"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 2)

        mission_ids = [item["id"] for item in results]
        self.assertIn(str(self.missions[0].id), mission_ids)
        self.assertIn(str(self.completed_mission.id), mission_ids)

        url = f"{self.missions_url}?has_daily_goals=false"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertTrue(
            len(results) >= 4,
            f"Expected at least 4 missions without daily goals, got {len(results)}",
        )

        mission_ids = [item["id"] for item in results]
        self.assertNotIn(str(self.missions[0].id), mission_ids)
        self.assertNotIn(str(self.completed_mission.id), mission_ids)

    def test_filter_by_practice_day(self):
        """Test filtering by practice_day field"""

        url = f"{self.missions_url}?practice_day=1"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 3)

        url = f"{self.missions_url}?practice_day=2"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 4)

        url = f"{self.missions_url}?practice_day=7"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 0)

    def test_multiple_filters(self):
        """Test combining multiple filters"""

        url = f"{self.missions_url}?week_number=4&domain={self.domain2.id}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 1)

        url = f"{self.missions_url}?six_week_period={self.period1.id}&is_completed=false&has_daily_goals=true"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["id"], str(self.missions[0].id))

        url = f"{self.missions_url}?week_number=4&practice_day=4"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 1)

    def test_ordering(self):
        """Test ordering results"""

        url = f"{self.missions_url}?ordering=week_number"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(results[0]["week_number"], 1)
        self.assertEqual(results[1]["week_number"], 1)

        url = f"{self.missions_url}?ordering=-week_number"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        highest_week = max(
            mission.week_number
            for mission in [
                *self.missions,
                self.period2_mission,
                self.completed_mission,
            ]
        )
        self.assertEqual(results[0]["week_number"], highest_week)

        url = f"{self.missions_url}?ordering=created"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
