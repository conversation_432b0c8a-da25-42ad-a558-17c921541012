from django.test import TestCase, override_settings
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.utils.translation import activate
from apps.domains.models import Domain
from apps.okrs.models import SixWeekPeriod
from apps.missions.models import WeeklyMission, Goal
import json

User = get_user_model()


@override_settings(LANGUAGE_CODE="en")
class ErrorFormatTests(TestCase):
    """
    Test suite for error formats in the missions app.
    """

    def setUp(self):
        activate("en")

        self.client = APIClient()
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            fullname="Test User",
            username="testuser",
        )
        self.client.force_authenticate(user=self.user)

        self.client.credentials(HTTP_ACCEPT_LANGUAGE="en")

        self.domain = Domain.objects.create(
            name="Test Domain", description="Test Domain Description"
        )

        self.start_date = timezone.now().date()
        self.end_date = self.start_date + timezone.timedelta(days=42)

        self.period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            description="Test Description",
            start_date=self.start_date,
            end_date=self.end_date,
        )

        self.goal = Goal.objects.create(
            user=self.user,
            six_week_period=self.period,
            title="Test Goal",
            priority=Goal.PRIORITY_HIGH,
        )

        WeeklyMission.objects.all().delete()

    def test_duplicate_mission_error_format(self):
        """Test that duplicate mission errors are formatted correctly."""
        url = reverse("missions:weekly-mission-list")

        data = {
            "six_week_period": self.period.id,
            "domain": self.domain.id,
            "week_number": 1,
            "goal_ids": [self.goal.id],
            "practice_days": [],
            "daily_reminder": True,
            "reminder_time": "08:00:00",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        response_data = json.loads(response.content)
        self.assertIn("error", response_data)
        self.assertEqual(
            response_data["error"], "A mission for week 1 already exists in this period"
        )
        self.assertNotIsInstance(response_data["error"], dict)
        self.assertNotIsInstance(response_data["error"], list)

    def test_invalid_practice_days_error_format(self):
        """Test that invalid practice days errors are formatted correctly."""
        url = reverse("missions:weekly-mission-list")

        data = {
            "six_week_period": self.period.id,
            "domain": self.domain.id,
            "week_number": 1,
            "goal_ids": [self.goal.id],
            "practice_days": [0, 8],
            "daily_reminder": True,
            "reminder_time": "08:00:00",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        response_data = json.loads(response.content)
        self.assertIn("error", response_data)
        self.assertIsInstance(response_data["error"], str)

    def test_multiple_goals_for_week_1_error_format(self):
        """Test that the error for having multiple goals in week 1 is formatted correctly."""

        goal2 = Goal.objects.create(
            user=self.user,
            six_week_period=self.period,
            title="Second Goal",
            priority=Goal.PRIORITY_LOW,
        )

        url = reverse("missions:weekly-mission-list")
        data = {
            "six_week_period": self.period.id,
            "domain": self.domain.id,
            "week_number": 1,
            "goal_ids": [self.goal.id, goal2.id],
            "practice_days": [],
            "daily_reminder": True,
            "reminder_time": "08:00:00",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        response_data = json.loads(response.content)
        self.assertIn("error", response_data)

        self.assertEqual(
            response_data, {"error": "Exactly one goal is required for week 1"}
        )
