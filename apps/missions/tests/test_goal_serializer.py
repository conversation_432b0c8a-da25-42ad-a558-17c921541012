from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.utils import timezone
from django.contrib.auth import get_user_model
from apps.missions.models import Goal
from apps.okrs.models import SixWeekPeriod

User = get_user_model()


class GoalSerializerDuplicateTest(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", password="testpass123", email="<EMAIL>"
        )
        self.client.force_authenticate(user=self.user)

        self.period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timezone.timedelta(days=42),
        )

        self.existing_goal = Goal.objects.create(
            user=self.user,
            six_week_period=self.period,
            title="Coding",
            priority=Goal.PRIORITY_HIGH,
        )

    def test_create_duplicate_goal(self):
        """Test that creating a goal with the same title returns the existing goal."""
        url = reverse("missions:goal-list-create")

        data = {
            "six_week_period": str(self.period.id),
            "title": "Coding",
            "priority": Goal.PRIORITY_LOW,
        }

        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        goals_with_title = Goal.objects.filter(
            six_week_period=self.period, title="Coding"
        )
        self.assertEqual(goals_with_title.count(), 1)

        self.assertEqual(str(response.data["id"]), str(self.existing_goal.id))

        self.assertEqual(response.data["priority"], Goal.PRIORITY_HIGH)

    def test_create_unique_goal(self):
        """Test that creating a goal with a unique title works as expected."""
        url = reverse("missions:goal-list-create")

        data = {
            "six_week_period": self.period.id,
            "title": "Unique Goal Title",
            "priority": Goal.PRIORITY_HIGH,
        }

        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data["title"], "Unique Goal Title")

    def test_bulk_creation_with_duplicates(self):
        """Test creating multiple goals at once, including duplicates."""

        url = reverse("missions:goal-list-create")

        data = [
            {
                "six_week_period": str(self.period.id),
                "title": "Coding",
                "priority": Goal.PRIORITY_LOW,
            },
            {
                "six_week_period": str(self.period.id),
                "title": "Learning",
                "priority": Goal.PRIORITY_HIGH,
            },
            {
                "six_week_period": str(self.period.id),
                "title": "Exercise",
                "priority": Goal.PRIORITY_LOW,
            },
        ]

        for goal_data in data:
            response = self.client.post(url, goal_data, format="json")
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        goals = Goal.objects.filter(six_week_period=self.period)
        self.assertEqual(goals.count(), 3)

        goal_titles = set(goals.values_list("title", flat=True))
        self.assertEqual(goal_titles, {"Coding", "Learning", "Exercise"})
