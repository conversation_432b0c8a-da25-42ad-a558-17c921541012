from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
import uuid

from apps.missions.models import WeeklyMission, DailyGoal, DailyGoalProgress, Goal
from apps.okrs.models import SixWeekPeriod
from apps.domains.models import Domain

User = get_user_model()


class TestWeeklyMissionFilter(TestCase):
    """Test class for WeeklyMission filter fields"""

    def setUp(self):
        """Set up test data for filter tests"""

        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpassword",
            username="testuser",
        )

        self.another_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpassword",
            username="anotheruser",
        )

        self.admin_user = User.objects.create_user(
            email="<EMAIL>",
            password="adminpassword",
            username="adminuser",
            role="admin",
            is_staff=True,
        )

        self.domain1 = Domain.objects.create(
            name="Domain 1",
            category="personal",
            description="Test domain 1",
        )

        self.domain2 = Domain.objects.create(
            name="Domain 2",
            category="work",
            description="Test domain 2",
        )

        self.start_date = timezone.now().date()
        self.end_date = self.start_date + timedelta(days=42)

        self.period1 = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period 1",
            start_date=self.start_date,
            end_date=self.end_date,
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
        )

        self.period2 = SixWeekPeriod.objects.create(
            user=self.another_user,
            title="Test Period 2",
            start_date=self.start_date + timedelta(days=50),
            end_date=self.start_date + timedelta(days=50) + timedelta(days=42),
            status=SixWeekPeriod.STATUS_DRAFT,
        )

        self.missions = []
        for week in range(1, 7):
            mission = WeeklyMission.objects.create(
                user=self.user,
                six_week_period=self.period1,
                domain=self.domain1 if week % 2 == 1 else self.domain2,
                week_number=week,
                practice_days=[1, 3, 5] if week % 2 == 1 else [2, 4, 6],
            )
            self.missions.append(mission)

        self.mission_other_user = WeeklyMission.objects.create(
            user=self.another_user,
            six_week_period=self.period2,
            domain=self.domain2,
            week_number=1,
            practice_days=[1, 2, 7],
        )

        yesterday = timezone.now() - timedelta(days=1)
        self.mission_yesterday = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period1,
            domain=self.domain1,
            week_number=2,
            practice_days=[1, 3, 5],
        )

        WeeklyMission.objects.filter(pk=self.mission_yesterday.pk).update(
            created=yesterday
        )
        self.mission_yesterday.refresh_from_db()

        self.completed_mission = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period1,
            domain=self.domain2,
            week_number=3,
            practice_days=[2, 4, 6],
        )

        self.daily_goals_week1 = [
            DailyGoal.objects.create(
                weekly_mission=self.missions[0],
                title=f"Daily Goal {i} for Week 1",
                priority=(
                    DailyGoal.PRIORITY_HIGH if i % 2 == 0 else DailyGoal.PRIORITY_LOW
                ),
            )
            for i in range(3)
        ]

        self.daily_goals_completed = [
            DailyGoal.objects.create(
                weekly_mission=self.completed_mission,
                title=f"Daily Goal {i} for Completed Mission",
                priority=DailyGoal.PRIORITY_HIGH,
            )
            for i in range(2)
        ]

        for goal in self.daily_goals_week1:
            for day in self.missions[0].practice_days:
                DailyGoalProgress.objects.create(
                    daily_goal=goal,
                    day_number=day,
                    completed=False,
                )

        for goal in self.daily_goals_completed:
            for day in self.completed_mission.practice_days:
                progress = DailyGoalProgress.objects.create(
                    daily_goal=goal,
                    day_number=day,
                    completed=True,
                    completion_date=timezone.now(),
                )

        self.completed_mission.mark_completed()

        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

        self.weekly_mission_list_url = reverse("missions:weekly-mission-list")

    def _get_results(self, response):
        """Helper method to extract results from paginated response."""
        if isinstance(response.data, dict) and "results" in response.data:
            return response.data["results"]
        return response.data

    def test_filter_by_week_number(self):
        """Test filtering missions by week number."""

        url = f"{self.weekly_mission_list_url}?week_number=1"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 1)
        for result in results:
            self.assertEqual(result["week_number"], 1)

        url = f"{self.weekly_mission_list_url}?week_number=2"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 2)
        for result in results:
            self.assertEqual(result["week_number"], 2)

    def test_filter_by_domain(self):
        """Test filtering missions by domain."""

        url = f"{self.weekly_mission_list_url}?domain={self.domain1.id}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        domain1_missions = [
            mission for mission in self.missions if mission.domain_id == self.domain1.id
        ]
        expected_count = len(domain1_missions) + 1

        self.assertGreater(len(results), 0)

        for result in results:

            if isinstance(result["domain"], dict):
                self.assertEqual(result["domain"]["id"], str(self.domain1.id))
            else:
                self.assertEqual(result["domain"], str(self.domain1.id))

        url = f"{self.weekly_mission_list_url}?domain={self.domain2.id}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        expected_count = (
            sum(1 for mission in self.missions if mission.domain_id == self.domain2.id)
            + 1
        )

        self.assertEqual(len(results), expected_count)
        for result in results:

            if isinstance(result["domain"], dict):
                self.assertEqual(result["domain"]["id"], str(self.domain2.id))
            else:
                self.assertEqual(result["domain"], str(self.domain2.id))

    def test_filter_by_six_week_period(self):
        """Test filtering missions by six week period."""

        url = f"{self.weekly_mission_list_url}?six_week_period={self.period1.id}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertGreater(len(results), 0)

        for result in results:

            if isinstance(result["six_week_period"], dict):
                self.assertEqual(
                    str(result["six_week_period"]["id"]), str(self.period1.id)
                )
            else:
                self.assertEqual(str(result["six_week_period"]), str(self.period1.id))

        url = f"{self.weekly_mission_list_url}?six_week_period={self.period2.id}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 0)

        self.client.force_authenticate(user=self.admin_user)
        url = f"{self.weekly_mission_list_url}?six_week_period={self.period2.id}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 1)

        if isinstance(results[0]["six_week_period"], dict):
            self.assertEqual(
                str(results[0]["six_week_period"]["id"]), str(self.period2.id)
            )
        else:
            self.assertEqual(str(results[0]["six_week_period"]), str(self.period2.id))

        self.client.force_authenticate(user=self.user)

    def test_filter_by_user(self):
        """Test filtering missions by user."""

        self.client.force_authenticate(user=self.admin_user)

        url = f"{self.weekly_mission_list_url}?user={self.user.id}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertGreater(len(results), 0)

        for result in results:

            if isinstance(result["user"], dict):
                self.assertEqual(str(result["user"]["id"]), str(self.user.id))
            else:
                self.assertEqual(str(result["user"]), str(self.user.id))

        url = f"{self.weekly_mission_list_url}?user={self.another_user.id}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertGreater(len(results), 0)

        self.client.force_authenticate(user=self.user)

    def test_filter_by_created_at(self):
        """Test filtering by created_at field"""

        today = timezone.now().date().isoformat()
        yesterday = (timezone.now().date() - timedelta(days=1)).isoformat()
        tomorrow = (timezone.now().date() + timedelta(days=1)).isoformat()

        url = f"{self.weekly_mission_list_url}?created_at_min={yesterday}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertGreater(len(results), 0)

        url = f"{self.weekly_mission_list_url}?created_at_max={tomorrow}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertGreater(len(results), 0)

    def test_filter_by_is_completed(self):
        """Test filtering by is_completed field"""

        url = f"{self.weekly_mission_list_url}?is_completed=true"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        url = f"{self.weekly_mission_list_url}?is_completed=false"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertGreater(len(results), 0)

    def test_filter_by_has_daily_goals(self):
        """Test filtering by has_daily_goals field"""

        url = f"{self.weekly_mission_list_url}?has_daily_goals=true"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertGreaterEqual(len(results), 0)

        url = f"{self.weekly_mission_list_url}?has_daily_goals=false"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertGreaterEqual(len(results), 0)

    def test_filter_by_practice_day(self):
        """Test filtering by practice_day field"""

        url = f"{self.weekly_mission_list_url}?practice_day=1"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        expected_count = 4
        self.assertEqual(len(results), expected_count)

        url = f"{self.weekly_mission_list_url}?practice_day=2"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        expected_count = 4
        self.assertEqual(len(results), expected_count)

        url = f"{self.weekly_mission_list_url}?practice_day=7"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 0)

    def test_combined_filters(self):
        """Test combining multiple filters"""

        url = f"{self.weekly_mission_list_url}?week_number=3&domain={self.domain2.id}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["id"], str(self.completed_mission.id))

        url = f"{self.weekly_mission_list_url}?domain={self.domain2.id}&has_daily_goals=true&is_completed=true"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["id"], str(self.completed_mission.id))

    def test_search_field(self):
        """Test search field functionality"""

        test_goal = Goal.objects.create(
            user=self.user, six_week_period=self.period1, title="UniqueSearchableTitle"
        )
        self.missions[0].goals.add(test_goal)

        url = f"{self.weekly_mission_list_url}?search=UniqueSearchableTitle"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["id"], str(self.missions[0].id))

        url = f"{self.weekly_mission_list_url}?search=Domain 1"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        expected_count = (
            sum(1 for mission in self.missions if mission.domain_id == self.domain1.id)
            + 1
        )
        self.assertEqual(len(results), expected_count)

    def test_ordering(self):
        """Test ordering functionality"""

        url = f"{self.weekly_mission_list_url}?ordering=week_number"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        week_numbers = [result["week_number"] for result in results]

        week_groups = {}
        for result in results:
            week = result["week_number"]
            if week not in week_groups:
                week_groups[week] = []
            week_groups[week].append(result)

        self.assertEqual(sorted(week_numbers), week_numbers)
