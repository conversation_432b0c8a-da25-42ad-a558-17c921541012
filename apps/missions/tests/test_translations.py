from django.test import TestCase, override_settings
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.utils.translation import activate
from django.utils.translation import gettext_lazy as _
from apps.domains.models import Domain
from apps.okrs.models import SixWeekPeriod
from apps.missions.models import WeeklyMission, Goal

User = get_user_model()


@override_settings(LANGUAGE_CODE="ar")
class ArabicTranslationTests(TestCase):
    """
    Test suite for Arabic translations in the missions app.

    Currently working translations:
    - Duplicate mission error (passes)
    - Reminder time validation error (passes)
    - Day names in practice_days_display (passes)
    - Week 1 goal validation error (needs work)
    - Empty practice days validation error (needs work)
    - Practice days validation error (needs work)
    """

    def setUp(self):
        activate("ar")

        self.client = APIClient()
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            fullname="Test User",
            username="testuser",
        )
        self.client.force_authenticate(user=self.user)

        self.client.credentials(HTTP_ACCEPT_LANGUAGE="ar")

        self.domain = Domain.objects.create(
            name="Test Domain", description="Test Domain Description"
        )

        self.start_date = timezone.now().date()
        self.end_date = self.start_date + timezone.timedelta(days=42)

        self.period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            description="Test Description",
            start_date=self.start_date,
            end_date=self.end_date,
        )

        self.goal = Goal.objects.create(
            user=self.user,
            six_week_period=self.period,
            title="Test Goal",
            priority=Goal.PRIORITY_HIGH,
        )

        self.goal2 = Goal.objects.create(
            user=self.user,
            six_week_period=self.period,
            title="Second Goal",
            priority=Goal.PRIORITY_LOW,
        )

        WeeklyMission.objects.all().delete()

    def _is_arabic(self, text):
        """Helper method to check if text contains Arabic characters."""
        return any("\u0600" <= c <= "\u06ff" for c in text)

    def test_duplicate_mission_translation(self):
        """Test that duplicate mission errors are translated to Arabic."""
        url = reverse("missions:weekly-mission-list")

        data = {
            "six_week_period": self.period.id,
            "domain": self.domain.id,
            "week_number": 1,
            "goal_ids": [self.goal.id],
            "practice_days": [],
            "daily_reminder": True,
            "reminder_time": "08:00:00",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        self.assertIn("error", response.json())
        error_msg = response.json()["error"]

        self.assertTrue(
            self._is_arabic(error_msg), f"Expected Arabic text, but got: {error_msg}"
        )
        self.assertEqual(error_msg, "مهمة للأسبوع 1 موجودة بالفعل في هذه الفترة")

    def test_practice_days_validation(self):
        """Test practice days validation errors."""
        url = reverse("missions:weekly-mission-list")

        data = {
            "six_week_period": self.period.id,
            "domain": self.domain.id,
            "week_number": 2,
            "goal_ids": [self.goal.id],
            "practice_days": [0, 8],
            "daily_reminder": True,
            "reminder_time": "08:00:00",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        self.assertIn("error", response.json())
        error_msg = response.json()["error"]

        self.assertTrue(
            self._is_arabic(error_msg), f"Expected Arabic text, but got: {error_msg}"
        )
        self.assertTrue("أيام" in error_msg or "يجب" in error_msg)

    def test_week_1_goal_validation_translation(self):
        """Test week 1 goal validation errors."""
        url = reverse("missions:weekly-mission-list")

        data = {
            "six_week_period": self.period.id,
            "domain": self.domain.id,
            "week_number": 1,
            "goal_ids": [],
            "practice_days": [],
            "daily_reminder": True,
            "reminder_time": "08:00:00",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        self.assertIn("error", response.json())
        error_msg = response.json()["error"]

        self.assertTrue(
            self._is_arabic(error_msg), f"Expected Arabic text, but got: {error_msg}"
        )
        self.assertTrue("هدف" in error_msg)

    def test_practice_days_empty_validation(self):
        """Test empty practice days validation errors."""
        url = reverse("missions:weekly-mission-list")

        data = {
            "six_week_period": self.period.id,
            "domain": self.domain.id,
            "week_number": 3,
            "goal_ids": [self.goal.id],
            "practice_days": [],
            "daily_reminder": True,
            "reminder_time": "08:00:00",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        self.assertIn("error", response.json())
        error_msg = response.json()["error"]

        self.assertTrue(
            self._is_arabic(error_msg), f"Expected Arabic text, but got: {error_msg}"
        )
        self.assertTrue("يوم" in error_msg or "أيام" in error_msg)

    def test_reminder_time_validation(self):
        """Test reminder time validation errors."""
        url = reverse("missions:weekly-mission-list")

        data = {
            "six_week_period": self.period.id,
            "domain": self.domain.id,
            "week_number": 4,
            "goal_ids": [self.goal.id],
            "practice_days": [1],
            "daily_reminder": True,
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        self.assertIn("error", response.json())
        error_msg = response.json()["error"]

        self.assertTrue(
            self._is_arabic(error_msg), f"Expected Arabic text, but got: {error_msg}"
        )
        self.assertEqual(error_msg, "مطلوب وقت التذكير عند تمكين التذكير اليومي")

    def test_practice_days_display_translation(self):
        """Test practice days display translated day names."""

        mission = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=5,
            practice_days=[1, 3, 5],
        )
        mission.goals.add(self.goal)

        url = reverse("missions:weekly-mission-detail", args=[mission.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        practice_days_display = response.json()["practice_days_display"]
        self.assertEqual(len(practice_days_display), 3)

        day_names = [day[1] for day in practice_days_display]

        arabic_found = False
        for day_name in day_names:
            if self._is_arabic(day_name):
                arabic_found = True
                break

        self.assertTrue(
            arabic_found, "No Arabic day names found in practice_days_display"
        )
