import pytest
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from apps.missions.models import Goal
from apps.okrs.models import SixWeekPeriod

User = get_user_model()


@pytest.mark.django_db
class TestWeeklyGoalFilters:
    @pytest.fixture(autouse=True)
    def setup(self):

        self.user = User.objects.create_user(
            email="<EMAIL>", password="testpassword", username="testuser"
        )
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

        self.start_date = timezone.now().date()
        self.period1 = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period 1",
            start_date=self.start_date,
            end_date=self.start_date + timedelta(days=42),
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
        )

        self.period2 = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period 2",
            start_date=self.start_date + timedelta(days=43),
            end_date=self.start_date + timedelta(days=85),
            status="not_started",
        )

        self.high_priority_goal = Goal.objects.create(
            user=self.user,
            six_week_period=self.period1,
            title="High priority goal",
            priority=Goal.PRIORITY_HIGH,
        )

        self.low_priority_goal = Goal.objects.create(
            user=self.user,
            six_week_period=self.period1,
            title="Low priority goal",
            priority=Goal.PRIORITY_LOW,
        )

        self.period2_goal = Goal.objects.create(
            user=self.user,
            six_week_period=self.period2,
            title="Period 2 goal",
            priority=Goal.PRIORITY_HIGH,
        )

        self.goal_list_url = reverse("missions:goal-list-create")

    def get_results(self, response):
        """Helper method to extract results from paginated response."""
        return response.data.get("results", [])

    def test_filter_by_title(self):
        """Test filtering goals by title."""
        url = f"{self.goal_list_url}?title=high"
        response = self.client.get(url)

        assert response.status_code == status.HTTP_200_OK
        results = self.get_results(response)
        assert len(results) == 1
        assert results[0]["title"] == "High priority goal"

    def test_filter_by_priority(self):
        """Test filtering goals by priority."""
        url = f"{self.goal_list_url}?priority=high"
        response = self.client.get(url)

        assert response.status_code == status.HTTP_200_OK
        results = self.get_results(response)
        high_priority_goals = [goal for goal in results if goal["priority"] == "high"]
        assert len(high_priority_goals) == 2

        url = f"{self.goal_list_url}?priority=low"
        response = self.client.get(url)

        assert response.status_code == status.HTTP_200_OK
        results = self.get_results(response)
        low_priority_goals = [goal for goal in results if goal["priority"] == "low"]
        assert len(low_priority_goals) == 1

    def test_filter_by_six_week_period(self):
        """Test filtering goals by six week period."""
        url = f"{self.goal_list_url}?six_week_period={self.period1.id}"
        response = self.client.get(url)

        assert response.status_code == status.HTTP_200_OK
        results = self.get_results(response)
        assert len(results) == 2

        url = f"{self.goal_list_url}?six_week_period={self.period2.id}"
        response = self.client.get(url)

        assert response.status_code == status.HTTP_200_OK
        results = self.get_results(response)
        assert len(results) == 1

    def test_filter_by_user(self):
        """Test filtering goals by user."""
        url = f"{self.goal_list_url}?user={self.user.id}"
        response = self.client.get(url)

        assert response.status_code == status.HTTP_200_OK
        results = self.get_results(response)
        assert len(results) == 3

    def test_multiple_filters(self):
        """Test using multiple filters at once."""
        url = f"{self.goal_list_url}?priority=high&six_week_period={self.period1.id}"
        response = self.client.get(url)

        assert response.status_code == status.HTTP_200_OK
        results = self.get_results(response)
        assert len(results) == 1
        assert results[0]["title"] == "High priority goal"
