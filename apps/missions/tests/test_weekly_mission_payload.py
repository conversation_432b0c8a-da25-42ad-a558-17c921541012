from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.utils import timezone
from django.contrib.auth import get_user_model
from apps.missions.models import Goal, WeeklyMission
from apps.domains.models import Domain
from apps.okrs.models import SixWeekPeriod
import uuid

User = get_user_model()


class OriginalPayloadTest(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", password="testpass123", email="<EMAIL>"
        )
        self.client.force_authenticate(user=self.user)

        self.domain = Domain.objects.create(name="Test Domain")

        self.period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timezone.timedelta(days=42),
        )

        self.goal = Goal.objects.create(
            user=self.user,
            six_week_period=self.period,
            title="Coding",
            priority=Goal.PRIORITY_HIGH,
        )

    def test_original_payload_format(self):
        """Test that the original payload format from the issue works"""
        url = reverse("missions:weekly-mission-list")

        data = {
            "domain": str(self.domain.id),
            "practice_days": [],
            "daily_reminder": True,
            "reminder_time": "06:06",
            "is_completed": False,
            "six_week_period": str(self.period.id),
            "week_number": 1,
            "goal_ids": [str(self.goal.id)],
        }

        response = self.client.post(url, data, format="json")
        self.assertEqual(
            response.status_code,
            status.HTTP_201_CREATED,
            f"Expected 201 Created, got {response.status_code} with data: {response.data}",
        )

        missions = WeeklyMission.objects.filter(
            user=self.user, six_week_period=self.period, week_number=1
        )
        self.assertEqual(missions.count(), 1)
        self.assertEqual(missions.first().domain.id, self.domain.id)
