from django.test import TestCase
from rest_framework.test import APIClient
from rest_framework import status
from django.urls import reverse
from django.utils import timezone
from django.contrib.auth import get_user_model
from apps.missions.models import Goal, WeeklyMission
from apps.domains.models import Domain
from apps.okrs.models import SixWeekPeriod


User = get_user_model()


class WeeklyMissionCreationTest(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", password="testpass123", email="<EMAIL>"
        )
        self.client.force_authenticate(user=self.user)

        self.domain = Domain.objects.create(name="Test Domain")

        self.period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timezone.timedelta(days=42),
        )

        self.existing_goal = Goal.objects.create(
            user=self.user,
            six_week_period=self.period,
            title="Coding",
            priority=Goal.PRIORITY_HIGH,
        )

    def test_create_weekly_mission_with_existing_goal(self):
        """Test creating a weekly mission with a goal that already exists for the period."""
        url = reverse("missions:weekly-mission-list")

        data = {
            "six_week_period": str(self.period.id),
            "domain": str(self.domain.id),
            "week_number": 1,
            "goal_ids": [str(self.existing_goal.id)],
            "practice_days": [],
            "daily_reminder": True,
            "reminder_time": "08:00:00",
        }

        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response.data)

        mission_id = response.data["id"]
        mission = WeeklyMission.objects.get(id=mission_id)
        self.assertEqual(mission.goals.count(), 1)
        self.assertEqual(mission.goals.first().id, self.existing_goal.id)

    def test_create_weekly_mission_with_multiple_goals_one_duplicate(self):
        """Test creating a weekly mission with multiple goals, one of which already exists."""
        url = reverse("missions:weekly-mission-list")

        new_goal = Goal.objects.create(
            user=self.user,
            six_week_period=self.period,
            title="Learning",
            priority=Goal.PRIORITY_LOW,
        )

        data = {
            "six_week_period": str(self.period.id),
            "domain": str(self.domain.id),
            "week_number": 2,
            "goal_ids": [str(self.existing_goal.id), str(new_goal.id)],
            "practice_days": [1, 3, 5],
            "daily_reminder": False,
            "reminder_time": None,
        }

        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response.data)

        mission_id = response.data["id"]
        mission = WeeklyMission.objects.get(id=mission_id)
        self.assertEqual(mission.goals.count(), 2)
        goal_ids = set(mission.goals.values_list("id", flat=True))
        self.assertEqual(goal_ids, {self.existing_goal.id, new_goal.id})

    def test_create_weekly_mission_with_new_goal_same_title(self):
        """Test creating a weekly mission with a new goal that has the same title as an existing one."""

        url = reverse("missions:goal-list-create")

        mission_url = reverse("missions:weekly-mission-list")
        mission_data = {
            "six_week_period": str(self.period.id),
            "domain": str(self.domain.id),
            "week_number": 2,
            "goal_ids": [str(self.existing_goal.id)],
            "practice_days": [1, 3, 5],
            "daily_reminder": False,
            "reminder_time": None,
        }

        mission_response = self.client.post(mission_url, mission_data, format="json")

        self.assertEqual(mission_response.status_code, status.HTTP_201_CREATED)
        mission_id = mission_response.data["id"]

        goal_data = {
            "title": "Coding",
            "priority": Goal.PRIORITY_HIGH,
            "six_week_period": str(self.period.id),
        }

        goal_response = self.client.post(url, goal_data, format="json")

        goals_with_title = Goal.objects.filter(
            six_week_period=self.period, title="Coding"
        )
        self.assertEqual(goals_with_title.count(), 1)
        self.assertEqual(goals_with_title.first().id, self.existing_goal.id)
