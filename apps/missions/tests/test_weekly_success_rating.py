from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from django.utils import timezone
from apps.missions.models import WeeklyMission, WeeklySuccessRating, Goal
from apps.okrs.models import SixWeekPeriod
from apps.domains.models import Domain

User = get_user_model()


class WeeklySuccessRatingTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            username="testuser",
        )
        self.client.force_authenticate(user=self.user)

        self.domain = Domain.objects.create(
            name="Test Domain", description="Test Domain Description"
        )

        self.start_date = timezone.now().date()
        self.end_date = self.start_date + timezone.timedelta(days=42)

        self.period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            description="Test Description",
            start_date=self.start_date,
            end_date=self.end_date,
        )

        self.mission = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=2,
            practice_days=[1, 3, 5],
        )

    def test_rate_weekly_success(self):
        """Test rating weekly success"""
        url = reverse("missions:weekly-mission-rate-success", args=[self.mission.id])
        data = {"rating": 8}

        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        rating = WeeklySuccessRating.objects.filter(weekly_mission=self.mission).first()
        self.assertIsNotNone(rating)
        self.assertEqual(rating.rating, 8)

        self.assertEqual(response.data["success_rating"], 8)

    def test_rate_weekly_success_invalid_rating(self):
        """Test rating weekly success with invalid rating"""
        url = reverse("missions:weekly-mission-rate-success", args=[self.mission.id])

        data = {"rating": 0}
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        data = {"rating": 11}
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        self.assertEqual(WeeklySuccessRating.objects.count(), 0)

    def test_update_weekly_success_rating(self):
        """Test updating the success rating by submitting a new one"""
        url = reverse("missions:weekly-mission-rate-success", args=[self.mission.id])

        self.client.post(url, {"rating": 7}, format="json")

        response = self.client.post(url, {"rating": 9}, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertEqual(WeeklySuccessRating.objects.count(), 2)

        self.assertEqual(response.data["success_rating"], 9)

    def test_rating_persists_in_mission_response(self):
        """Test that the rating is included in mission detail response"""

        WeeklySuccessRating.objects.create(weekly_mission=self.mission, rating=6)

        url = reverse("missions:weekly-mission-detail", args=[self.mission.id])
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["success_rating"], 6)

    def test_latest_rating_is_used(self):
        """Test that only the latest rating is returned"""

        WeeklySuccessRating.objects.create(weekly_mission=self.mission, rating=5)

        import time

        time.sleep(0.1)

        WeeklySuccessRating.objects.create(weekly_mission=self.mission, rating=8)

        url = reverse("missions:weekly-mission-detail", args=[self.mission.id])
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["success_rating"], 8)
