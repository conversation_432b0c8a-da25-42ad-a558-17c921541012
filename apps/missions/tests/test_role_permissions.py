from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.utils import timezone
from datetime import timedelta

from django.contrib.auth import get_user_model
from apps.missions.models import Goal, DailyGoal, WeeklyMission
from apps.okrs.models import SixWeekPeriod
from apps.domains.models import Domain
from apps.clubs.models import Club, ClubType

User = get_user_model()


class MissionRolePermissionsTests(TestCase):
    """Test role-based permissions for mission views"""

    def setUp(self):

        self.admin = User.objects.create_user(
            email="<EMAIL>",
            password="password123",
            username="admin",
            role="admin",
            is_staff=True,
        )

        self.club_manager = User.objects.create_user(
            email="<EMAIL>",
            password="password123",
            username="manager",
            role="club_manager",
        )

        self.member1 = User.objects.create_user(
            email="<EMAIL>",
            password="password123",
            username="member1",
            role="member",
        )

        self.member2 = User.objects.create_user(
            email="<EMAIL>",
            password="password123",
            username="member2",
            role="member",
        )

        self.club_type = ClubType.objects.create(name="Test Club Type")
        self.club = Club.objects.create(
            name="Test Club", type=self.club_type, manager=self.club_manager
        )
        self.club.members.add(self.member1)

        self.domain = Domain.objects.create(
            name="Test Domain", category="personal", description="Test description"
        )

        self.start_date = timezone.now().date()
        self.end_date = self.start_date + timedelta(days=42)

        self.period_member1 = SixWeekPeriod.objects.create(
            user=self.member1,
            title="Test Period Member 1",
            start_date=self.start_date,
            end_date=self.end_date,
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
        )

        self.period_member2 = SixWeekPeriod.objects.create(
            user=self.member2,
            title="Test Period Member 2",
            start_date=self.start_date,
            end_date=self.end_date,
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
        )

        self.period_member2.shared_with.add(self.member1)

        self.goal_member1 = Goal.objects.create(
            user=self.member1,
            six_week_period=self.period_member1,
            title="Goal Member 1",
        )

        self.mission_member1 = WeeklyMission.objects.create(
            user=self.member1,
            six_week_period=self.period_member1,
            domain=self.domain,
            week_number=2,
            practice_days=[1, 2, 3],
        )
        self.mission_member1.goals.add(self.goal_member1)

        self.daily_goal_member1 = DailyGoal.objects.create(
            weekly_mission=self.mission_member1, title="Daily Goal Member 1"
        )

        self.goal_member2 = Goal.objects.create(
            user=self.member2,
            six_week_period=self.period_member2,
            title="Goal Member 2",
        )

        self.mission_member2 = WeeklyMission.objects.create(
            user=self.member2,
            six_week_period=self.period_member2,
            domain=self.domain,
            week_number=2,
            practice_days=[1, 2, 3],
        )
        self.mission_member2.goals.add(self.goal_member2)

        self.daily_goal_member2 = DailyGoal.objects.create(
            weekly_mission=self.mission_member2, title="Daily Goal Member 2"
        )

        self.client = APIClient()

        self.goals_url = reverse("missions:goal-list-create")
        self.daily_goals_url = reverse("missions:daily-goal-list-create")
        self.weekly_missions_url = reverse("missions:weekly-mission-list")

    def _get_items_from_response(self, response):
        """Helper to extract items from response, handling different response formats"""
        response_data = response.data

        if isinstance(response_data, dict) and "results" in response_data:
            return response_data["results"]

        elif isinstance(response_data, list):
            return response_data

        else:
            print(f"Unexpected response format: {type(response_data)}")
            print(f"Response content: {response_data}")
            self.fail("Unexpected response format")
            return []

    def test_admin_can_see_all_goals(self):
        """Test admin can see all goals"""
        self.client.force_authenticate(user=self.admin)

        response = self.client.get(self.goals_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        items = self._get_items_from_response(response)

        goal_ids = [str(self.goal_member1.id), str(self.goal_member2.id)]

        response_ids = [str(item["id"]) for item in items]

        for goal_id in goal_ids:
            self.assertIn(goal_id, response_ids)

    def test_club_manager_can_see_only_club_members_goals(self):
        """Test club manager can only see goals from their club members"""
        self.client.force_authenticate(user=self.club_manager)

        response = self.client.get(self.goals_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        items = self._get_items_from_response(response)

        response_ids = [str(item["id"]) for item in items]

        self.assertIn(str(self.goal_member1.id), response_ids)

        self.assertNotIn(str(self.goal_member2.id), response_ids)

    def test_member_can_see_own_and_shared_goals(self):
        """Test regular member can only see their own goals and shared goals"""
        self.client.force_authenticate(user=self.member1)

        response = self.client.get(self.goals_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        items = self._get_items_from_response(response)

        response_ids = [str(item["id"]) for item in items]

        self.assertIn(str(self.goal_member1.id), response_ids)
        self.assertIn(str(self.goal_member2.id), response_ids)

        self.client.force_authenticate(user=self.member2)

        response = self.client.get(self.goals_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        items = self._get_items_from_response(response)

        response_ids = [str(item["id"]) for item in items]

        self.assertIn(str(self.goal_member2.id), response_ids)
        self.assertNotIn(str(self.goal_member1.id), response_ids)

    def test_admin_can_see_all_daily_goals(self):
        """Test admin can see all daily goals"""
        self.client.force_authenticate(user=self.admin)

        response = self.client.get(self.daily_goals_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        items = self._get_items_from_response(response)

        response_ids = [str(item["id"]) for item in items]

        self.assertIn(str(self.daily_goal_member1.id), response_ids)
        self.assertIn(str(self.daily_goal_member2.id), response_ids)

    def test_club_manager_can_see_only_club_members_daily_goals(self):
        """Test club manager can only see daily goals from their club members"""
        self.client.force_authenticate(user=self.club_manager)

        response = self.client.get(self.daily_goals_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        items = self._get_items_from_response(response)

        response_ids = [str(item["id"]) for item in items]

        self.assertIn(str(self.daily_goal_member1.id), response_ids)

        self.assertNotIn(str(self.daily_goal_member2.id), response_ids)

    def test_admin_can_see_all_weekly_missions(self):
        """Test admin can see all weekly missions"""
        self.client.force_authenticate(user=self.admin)

        response = self.client.get(self.weekly_missions_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        items = self._get_items_from_response(response)

        response_ids = [str(item["id"]) for item in items]

        self.assertIn(str(self.mission_member1.id), response_ids)
        self.assertIn(str(self.mission_member2.id), response_ids)

    def test_club_manager_can_see_only_club_members_weekly_missions(self):
        """Test club manager can only see weekly missions from their club members"""
        self.client.force_authenticate(user=self.club_manager)

        response = self.client.get(self.weekly_missions_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        items = self._get_items_from_response(response)

        response_ids = [str(item["id"]) for item in items]

        self.assertIn(str(self.mission_member1.id), response_ids)

        self.assertNotIn(str(self.mission_member2.id), response_ids)
