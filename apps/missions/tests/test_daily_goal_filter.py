from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
import uuid

from apps.missions.models import DailyGoal, DailyGoalProgress, WeeklyMission
from apps.okrs.models import SixWeekPeriod
from apps.domains.models import Domain

User = get_user_model()


class TestDailyGoalFilter(TestCase):
    """Test class for DailyGoal filter fields"""

    def setUp(self):
        """Set up test data for filter tests"""

        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpassword",
            username="testuser",
        )

        self.domain1 = Domain.objects.create(
            name="Domain 1",
            category="personal",
            description="Test domain 1",
        )

        self.domain2 = Domain.objects.create(
            name="Domain 2",
            category="work",
            description="Test domain 2",
        )

        self.start_date = timezone.now().date()
        self.end_date = self.start_date + timedelta(days=42)

        self.period1 = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period 1",
            start_date=self.start_date,
            end_date=self.end_date,
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
        )

        self.period2 = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period 2",
            start_date=self.start_date + timedelta(days=50),
            end_date=self.start_date + timedelta(days=50) + timedelta(days=42),
            status=SixWeekPeriod.STATUS_DRAFT,
        )

        self.mission_week1 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period1,
            domain=self.domain1,
            week_number=1,
            practice_days=[1, 3, 5],
        )

        self.mission_week2 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period1,
            domain=self.domain2,
            week_number=2,
            practice_days=[2, 4, 6],
        )

        self.mission_week3 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period2,
            domain=self.domain2,
            week_number=1,
            practice_days=[1, 2, 3],
        )

        self.high_priority_goal = DailyGoal.objects.create(
            weekly_mission=self.mission_week2,
            title="High priority goal",
            priority=DailyGoal.PRIORITY_HIGH,
        )

        self.low_priority_goal = DailyGoal.objects.create(
            weekly_mission=self.mission_week2,
            title="Low priority goal",
            priority=DailyGoal.PRIORITY_LOW,
        )

        self.period2_goal = DailyGoal.objects.create(
            weekly_mission=self.mission_week3,
            title="Period 2 domain 2 goal",
            priority=DailyGoal.PRIORITY_HIGH,
        )

        yesterday = timezone.now() - timedelta(days=1)
        self.yesterday_goal = DailyGoal.objects.create(
            weekly_mission=self.mission_week1,
            title="Yesterday goal",
            priority=DailyGoal.PRIORITY_HIGH,
        )

        DailyGoal.objects.filter(pk=self.yesterday_goal.pk).update(created=yesterday)
        self.yesterday_goal.refresh_from_db()

        self.completed_goal = DailyGoal.objects.create(
            weekly_mission=self.mission_week2,
            title="Completed goal",
            priority=DailyGoal.PRIORITY_HIGH,
        )

        for goal in [
            self.high_priority_goal,
            self.low_priority_goal,
            self.completed_goal,
        ]:
            for day in [2, 4, 6]:
                DailyGoalProgress.objects.create(
                    daily_goal=goal,
                    day_number=day,
                    completed=False,
                )

        for goal in [self.period2_goal, self.yesterday_goal]:
            for day in [1, 2, 3]:
                DailyGoalProgress.objects.create(
                    daily_goal=goal,
                    day_number=day,
                    completed=False,
                )

        for progress in DailyGoalProgress.objects.filter(
            daily_goal=self.completed_goal
        ):
            progress.completed = True
            progress.completion_date = timezone.now()
            progress.save()

        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

        self.daily_goal_list_url = reverse("missions:daily-goal-list-create")

    def _get_results(self, response):
        """Helper method to extract results from paginated response."""
        if isinstance(response.data, dict) and "results" in response.data:
            return response.data["results"]
        return response.data

    def test_filter_by_title(self):
        """Test filtering by title field"""

        url = f"{self.daily_goal_list_url}?title=high"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["title"], "High priority goal")

        url = f"{self.daily_goal_list_url}?title=GOAL"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 5)

    def test_filter_by_priority(self):
        """Test filtering by priority field"""

        url = f"{self.daily_goal_list_url}?priority=high"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 4)

        url = f"{self.daily_goal_list_url}?priority=low"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["title"], "Low priority goal")

    def test_filter_by_weekly_mission(self):
        """Test filtering by weekly_mission field"""
        url = f"{self.daily_goal_list_url}?weekly_mission={self.mission_week2.id}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 3)

        url = f"{self.daily_goal_list_url}?weekly_mission={self.mission_week3.id}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["title"], "Period 2 domain 2 goal")

    def test_filter_by_week_number(self):
        """Test filtering by weekly_mission__week_number field"""
        url = f"{self.daily_goal_list_url}?weekly_mission__week_number=1"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 2)

        url = f"{self.daily_goal_list_url}?weekly_mission__week_number=2"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 3)

        url = f"{self.daily_goal_list_url}?weekly_mission__week_number=10"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 0)

    def test_filter_by_domain(self):
        """Test filtering by weekly_mission__domain field"""
        url = f"{self.daily_goal_list_url}?weekly_mission__domain={self.domain1.id}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["title"], "Yesterday goal")

        url = f"{self.daily_goal_list_url}?weekly_mission__domain={self.domain2.id}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 4)

    def test_filter_by_six_week_period(self):
        """Test filtering by weekly_mission__six_week_period field"""
        url = f"{self.daily_goal_list_url}?weekly_mission__six_week_period={self.period1.id}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 4)

        url = f"{self.daily_goal_list_url}?weekly_mission__six_week_period={self.period2.id}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["title"], "Period 2 domain 2 goal")

    def test_filter_by_created_at(self):
        """Test filtering by created_at field"""

        today = timezone.now().date().isoformat()
        yesterday = (timezone.now().date() - timedelta(days=1)).isoformat()
        tomorrow = (timezone.now().date() + timedelta(days=1)).isoformat()

        url = f"{self.daily_goal_list_url}?created_at_min={yesterday}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 5)

        url = f"{self.daily_goal_list_url}?created_at_max={tomorrow}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 5)

        url = f"{self.daily_goal_list_url}?created_at_min={yesterday}&created_at_max={today}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 5)

    def test_filter_by_completion_status(self):
        """Test filtering by completed field"""

        self.assertTrue(self.completed_goal.is_completed())

        url = f"{self.daily_goal_list_url}?completed=true"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["title"], "Completed goal")

        url = f"{self.daily_goal_list_url}?completed=false"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 4)

    def test_combined_filters(self):
        """Test combining multiple filters"""

        url = f"{self.daily_goal_list_url}?priority=high&weekly_mission__week_number=2"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 2)

        url = f"{self.daily_goal_list_url}?weekly_mission__domain={self.domain2.id}&priority=high&completed=true"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["title"], "Completed goal")

    def test_search_field(self):
        """Test search field functionality"""

        url = f"{self.daily_goal_list_url}?search=priority"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 2)

        url = f"{self.daily_goal_list_url}?search=COMPLETED"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = self._get_results(response)

        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["title"], "Completed goal")

    def test_ordering(self):
        """Test ordering functionality"""

        url = f"{self.daily_goal_list_url}?ordering=priority"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results_asc = self._get_results(response)

        url = f"{self.daily_goal_list_url}?ordering=-priority"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results_desc = self._get_results(response)

        priorities_asc = [result["priority"] for result in results_asc]
        priorities_desc = [result["priority"] for result in results_desc]

        self.assertNotEqual(priorities_asc, priorities_desc)

        url = f"{self.daily_goal_list_url}?ordering=title"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
