from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework import status
from datetime import date, timedelta
from apps.domains.models import Domain
from apps.missions.models import WeeklyMission, DailyGoal
from apps.okrs.models import SixWeekPeriod

User = get_user_model()


class WeeklyMissionGoalsEndpointTests(TestCase):
    def setUp(self):
        self.client = APIClient()

        self.user = User.objects.create_user(
            email="<EMAIL>", password="password123", username="testuser"
        )
        self.client.force_authenticate(user=self.user)

        self.start_date = date.today()
        self.end_date = self.start_date + timedelta(days=42)

        self.period = SixWeekPeriod.objects.create(
            title="Test Period",
            user=self.user,
            start_date=self.start_date,
            end_date=self.end_date,
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
        )

        self.domain = Domain.objects.create(name="Test Domain", category="personal")

        self.mission = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.period,
            domain=self.domain,
            week_number=2,
            practice_days=[1, 3, 5],
        )

        self.daily_goal1 = DailyGoal.objects.create(
            weekly_mission=self.mission,
            title="Test Goal 1",
            priority=DailyGoal.PRIORITY_HIGH,
        )

        self.daily_goal2 = DailyGoal.objects.create(
            weekly_mission=self.mission,
            title="Test Goal 2",
            priority=DailyGoal.PRIORITY_LOW,
        )

        self.goals_url = reverse(
            "missions:weekly-mission-goals", args=[self.mission.id]
        )

    def test_goals_endpoint_returns_correct_structure(self):
        """Test that the goals endpoint returns all goals for a mission."""
        response = self.client.get(self.goals_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()
        self.assertEqual(len(data), 2)

        goal_titles = [goal["title"] for goal in data]
        self.assertIn("Test Goal 1", goal_titles)
        self.assertIn("Test Goal 2", goal_titles)

        for goal in data:
            self.assertIn("id", goal)
            self.assertIn("title", goal)
            self.assertIn("priority", goal)

    def test_goals_endpoint_with_nonexistent_mission(self):
        """Test that the goals endpoint returns 404 for nonexistent mission."""
        nonexistent_url = reverse(
            "missions:weekly-mission-goals",
            args=["00000000-0000-0000-0000-000000000000"],
        )
        response = self.client.get(nonexistent_url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_goals_endpoint_with_unauthorized_user(self):
        """Test that only authorized users can access mission goals."""

        other_user = User.objects.create_user(
            email="<EMAIL>", password="password123", username="otheruser"
        )

        self.client.force_authenticate(user=other_user)

        response = self.client.get(self.goals_url)

        self.assertIn(
            response.status_code, [status.HTTP_403_FORBIDDEN, status.HTTP_404_NOT_FOUND]
        )

    def test_goals_priorities(self):
        """Test that goals have correct priorities."""
        response = self.client.get(self.goals_url)
        data = response.json()

        goal1 = next(g for g in data if g["title"] == "Test Goal 1")
        goal2 = next(g for g in data if g["title"] == "Test Goal 2")

        self.assertEqual(goal1["priority"], DailyGoal.PRIORITY_HIGH)
        self.assertEqual(goal2["priority"], DailyGoal.PRIORITY_LOW)
