from django_filters import rest_framework as filters
from apps.missions.models import WeeklyMission, Goal


class WeeklyGoalFilter(filters.FilterSet):
    """
    FilterSet for Goal model that allows filtering weekly goals by various criteria.
    """

    title = filters.CharFilter(
        lookup_expr="icontains",
        help_text="Filter by title containing text (case insensitive)",
    )
    priority = filters.ChoiceFilter(
        choices=Goal.PRIORITY_CHOICES, help_text="Filter by priority (high or low)"
    )
    six_week_period = filters.UUIDFilter(help_text="Filter by six week period ID")
    user = filters.UUIDFilter(help_text="Filter by user ID")
    created_at = filters.DateFromToRangeFilter(
        help_text="Filter by creation date range"
    )

    class Meta:
        model = Goal
        fields = [
            "title",
            "priority",
            "six_week_period",
            "user",
            "created_at",
        ]


class WeeklyMissionFilter(filters.FilterSet):
    """
    FilterSet for WeeklyMission model that allows filtering weekly missions by various criteria.
    """

    week_number = filters.NumberFilter(help_text="Filter by week number (1-6)")
    domain = filters.UUIDFilter(help_text="Filter by domain ID")
    six_week_period = filters.UUIDFilter(help_text="Filter by six week period ID")
    user = filters.UUIDFilter(help_text="Filter by user ID")
    created_at = filters.DateFromToRangeFilter(
        help_text="Filter by creation date range"
    )
    is_completed = filters.BooleanFilter(
        method="filter_is_completed", help_text="Filter by completion status"
    )
    has_daily_goals = filters.BooleanFilter(
        method="filter_has_daily_goals",
        help_text="Filter missions that have daily goals (true) or don't have daily goals (false)",
    )
    practice_day = filters.NumberFilter(
        method="filter_practice_day",
        help_text="Filter missions where the specified day (1-7) is a practice day",
    )

    def filter_is_completed(self, queryset, name, value):
        """
        Custom filter to filter by completion status.
        Since is_completed is a method, not a field, we need to evaluate it for each mission.
        """
        completed_ids = []
        incomplete_ids = []

        for mission in queryset:
            if mission.is_completed():
                completed_ids.append(mission.id)
            else:
                incomplete_ids.append(mission.id)

        if value:
            return queryset.filter(id__in=completed_ids)
        else:
            return queryset.filter(id__in=incomplete_ids)

    def filter_has_daily_goals(self, queryset, name, value):
        """
        Filter to find missions that either have or don't have daily goals.
        """
        if value:
            return queryset.filter(daily_goals__isnull=False).distinct()
        return queryset.filter(daily_goals__isnull=True)

    def filter_practice_day(self, queryset, name, value):
        """
        Filter to find missions where the specified day is a practice day.
        Using a custom filter method for database compatibility.
        """

        day_number = int(value)
        matching_ids = []

        for mission in queryset:
            if day_number in mission.practice_days:
                matching_ids.append(mission.id)

        return queryset.filter(id__in=matching_ids)

    class Meta:
        model = WeeklyMission
        fields = [
            "week_number",
            "domain",
            "six_week_period",
            "user",
            "created_at",
        ]
