from django_filters import rest_framework as filters
from apps.missions.models import DailyGoal


class DailyGoalFilter(filters.FilterSet):
    """
    FilterSet for DailyGoal model that allows filtering daily goals by various criteria.
    """

    title = filters.CharFilter(
        lookup_expr="icontains",
        help_text="Filter by title containing text (case insensitive)",
    )
    priority = filters.ChoiceFilter(
        choices=DailyGoal.PRIORITY_CHOICES, help_text="Filter by priority (high or low)"
    )
    weekly_mission = filters.UUIDFilter(help_text="Filter by weekly mission ID")
    weekly_mission__week_number = filters.NumberFilter(
        help_text="Filter by week number of associated mission"
    )
    weekly_mission__domain = filters.UUIDFilter(
        help_text="Filter by domain ID of associated mission"
    )
    weekly_mission__six_week_period = filters.UUIDFilter(
        help_text="Filter by six week period ID of associated mission"
    )
    created_at = filters.DateFromToRangeFilter(
        help_text="Filter by creation date range"
    )

    completed = filters.BooleanFilter(
        method="filter_completed",
        help_text="Filter by completion status (true for completed, false for incomplete)",
    )

    def filter_completed(self, queryset, name, value):
        """
        Custom filter method to filter goals by completion status.

        Since is_completed isn't a direct model field but a method, we need to:
        1. Get all IDs of goals matching our filter criteria
        2. Filter the queryset based on these IDs
        """

        goals_list = list(queryset.select_related("weekly_mission"))

        completed_ids = []
        incomplete_ids = []

        for goal in goals_list:

            if not hasattr(goal, "daily_progress") or not goal.daily_progress.exists():

                goal.create_daily_progress_entries()

            if goal.is_completed():
                completed_ids.append(goal.id)
            else:
                incomplete_ids.append(goal.id)

        if value:
            return queryset.filter(id__in=completed_ids)
        else:
            return queryset.filter(id__in=incomplete_ids)

    class Meta:
        model = DailyGoal
        fields = [
            "title",
            "priority",
            "weekly_mission",
            "weekly_mission__week_number",
            "weekly_mission__domain",
            "weekly_mission__six_week_period",
            "created_at",
        ]
