# Generated by Django 4.2.9 on 2025-03-17 14:50

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("missions", "0003_auto_20250316_2031"),
    ]

    operations = [
        migrations.AlterField(
            model_name="weeklymission",
            name="practice_days",
            field=models.JSONField(),
        ),
        migrations.AddConstraint(
            model_name="weeklymission",
            constraint=models.CheckConstraint(
                check=models.Q(
                    models.Q(
                        ("daily_reminder", False),
                        models.Q(
                            ("daily_reminder", True), ("reminder_time__isnull", False)
                        ),
                        _connector="OR",
                    )
                ),
                name="reminder_time_required",
            ),
        ),
    ]
