# Generated by Django 4.2.9 on 2025-03-23 15:41

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):
    dependencies = [
        ("missions", "0006_alter_weeklymission_practice_days"),
    ]

    operations = [
        migrations.CreateModel(
            name="DailyGoal",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("title", models.TextField()),
                (
                    "priority",
                    models.CharField(
                        choices=[("high", "High"), ("low", "Low")],
                        default="low",
                        max_length=4,
                    ),
                ),
                ("completion_date", models.DateTimeField(blank=True, null=True)),
                (
                    "weekly_mission",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="daily_goals",
                        to="missions.weeklymission",
                    ),
                ),
            ],
            options={
                "ordering": ["-priority", "-created"],
            },
        ),
        migrations.AddConstraint(
            model_name="dailygoal",
            constraint=models.UniqueConstraint(
                fields=("weekly_mission", "title"), name="unique_mission_daily_goal"
            ),
        ),
    ]
