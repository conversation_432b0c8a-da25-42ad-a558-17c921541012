# Generated by Django 4.2.9 on 2025-03-17 01:30

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("okrs", "0001_initial"),
        ("missions", "0001_initial"),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="weeklymission",
            name="unique_user_domain_week_goal",
        ),
        migrations.AddField(
            model_name="weeklymission",
            name="six_week_period",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="missions",
                to="okrs.sixweekperiod",
            ),
        ),
        migrations.AddConstraint(
            model_name="weeklymission",
            constraint=models.UniqueConstraint(
                fields=("six_week_period", "domain", "week_number", "goal"),
                name="unique_period_domain_week_goal",
            ),
        ),
    ]
