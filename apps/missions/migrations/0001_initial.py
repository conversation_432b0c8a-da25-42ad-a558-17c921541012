# Generated by Django 4.2.9 on 2025-03-17 01:02

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("domains", "0006_alter_domain_icon"),
    ]

    operations = [
        migrations.CreateModel(
            name="WeeklyMission",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "week_number",
                    models.IntegerField(
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(6),
                        ]
                    ),
                ),
                ("goal", models.TextField()),
                (
                    "practice_days",
                    models.JSONField(
                        help_text="List of days when the user wants to practice"
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[("high", "High"), ("low", "Low")],
                        default="low",
                        max_length=4,
                    ),
                ),
                ("daily_reminder", models.BooleanField(default=False)),
                ("reminder_time", models.TimeField(blank=True, null=True)),
                ("completed", models.BooleanField(default=False)),
                ("completion_date", models.DateTimeField(blank=True, null=True)),
                (
                    "domain",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="weekly_missions",
                        to="domains.domain",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="weekly_missions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["week_number", "-created"],
            },
        ),
        migrations.AddConstraint(
            model_name="weeklymission",
            constraint=models.UniqueConstraint(
                fields=("user", "domain", "week_number", "goal"),
                name="unique_user_domain_week_goal",
            ),
        ),
        migrations.AddConstraint(
            model_name="weeklymission",
            constraint=models.CheckConstraint(
                check=models.Q(("week_number__gte", 1), ("week_number__lte", 6)),
                name="valid_week_number",
            ),
        ),
    ]
