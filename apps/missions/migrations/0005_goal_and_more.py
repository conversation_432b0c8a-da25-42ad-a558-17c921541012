# Generated by Django 4.2.9 on 2025-03-17 17:20

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("okrs", "0002_sixweekperiod_exact_six_weeks"),
        ("missions", "0004_alter_weeklymission_practice_days_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Goal",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("title", models.TextField()),
                (
                    "priority",
                    models.CharField(
                        choices=[("high", "High"), ("low", "Low")],
                        default="low",
                        max_length=4,
                    ),
                ),
            ],
            options={
                "ordering": ["-priority", "-created"],
            },
        ),
        migrations.RemoveConstraint(
            model_name="weeklymission",
            name="unique_period_domain_week_goal",
        ),
        migrations.RemoveField(
            model_name="weeklymission",
            name="completed",
        ),
        migrations.RemoveField(
            model_name="weeklymission",
            name="goal",
        ),
        migrations.RemoveField(
            model_name="weeklymission",
            name="priority",
        ),
        migrations.AlterField(
            model_name="weeklymission",
            name="practice_days",
            field=models.CharField(
                choices=[
                    ("monday", "Monday"),
                    ("tuesday", "Tuesday"),
                    ("wednesday", "Wednesday"),
                    ("thursday", "Thursday"),
                    ("friday", "Friday"),
                    ("saturday", "Saturday"),
                    ("sunday", "Sunday"),
                ],
                max_length=255,
            ),
        ),
        migrations.AddField(
            model_name="goal",
            name="six_week_period",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="goals",
                to="okrs.sixweekperiod",
            ),
        ),
        migrations.AddField(
            model_name="goal",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="goals",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="weeklymission",
            name="goals",
            field=models.ManyToManyField(related_name="missions", to="missions.goal"),
        ),
        migrations.AddConstraint(
            model_name="goal",
            constraint=models.UniqueConstraint(
                fields=("six_week_period", "title"), name="unique_period_goal"
            ),
        ),
    ]
