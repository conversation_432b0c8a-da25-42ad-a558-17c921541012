# Generated by Django 4.2.9 on 2025-03-25 21:54

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):
    dependencies = [
        ("missions", "0007_dailygoal_dailygoal_unique_mission_daily_goal"),
    ]

    operations = [
        migrations.CreateModel(
            name="DailyGoalProgress",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "day_number",
                    models.IntegerField(
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(7),
                        ]
                    ),
                ),
                ("completed", models.BooleanField(default=False)),
                ("completion_date", models.DateTimeField(blank=True, null=True)),
                (
                    "daily_goal",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="daily_progress",
                        to="missions.dailygoal",
                    ),
                ),
            ],
            options={
                "ordering": ["day_number"],
            },
        ),
        migrations.AddConstraint(
            model_name="dailygoalprogress",
            constraint=models.UniqueConstraint(
                fields=("daily_goal", "day_number"), name="unique_daily_goal_day"
            ),
        ),
        migrations.AddConstraint(
            model_name="dailygoalprogress",
            constraint=models.CheckConstraint(
                check=models.Q(("day_number__gte", 1), ("day_number__lte", 7)),
                name="valid_day_number",
            ),
        ),
    ]
