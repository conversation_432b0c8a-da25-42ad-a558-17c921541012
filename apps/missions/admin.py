from django.contrib import admin
from apps.missions.models import WeeklyMission, Goal, DailyGoal


@admin.register(WeeklyMission)
class WeeklyMissionAdmin(admin.ModelAdmin):
    list_display = [
        "user",
        "six_week_period",
        "domain",
        "week_number",
        "created",
        "updated",
    ]
    list_filter = ["six_week_period", "domain"]
    search_fields = ["user__username", "six_week_period__title", "domain__name"]
    ordering = ["six_week_period", "week_number"]
    readonly_fields = ["created", "updated"]


@admin.register(Goal)
class GoalAdmin(admin.ModelAdmin):
    list_display = [
        "user",
        "six_week_period",
        "title",
        "priority",
        "created",
        "updated",
    ]
    list_filter = ["six_week_period", "priority"]
    search_fields = ["user__username", "six_week_period__title", "title"]
    ordering = ["priority", "-created"]
    readonly_fields = ["created", "updated"]


@admin.register(DailyGoal)
class DailyGoalAdmin(admin.ModelAdmin):
    list_display = [
        "title",
        "weekly_mission",
        "priority",
        "is_completed",
        "created",
        "updated",
    ]
    list_filter = ["weekly_mission__week_number", "priority"]
    search_fields = ["title", "weekly_mission__user__username"]
    ordering = ["priority", "-created"]
    readonly_fields = ["created", "updated"]
