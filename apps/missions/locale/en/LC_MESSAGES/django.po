# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-03 16:41+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: apps/missions/constants.py:3 apps/missions/serializers.py:98
#: apps/missions/serializers/weekly_mission.py:161
msgid "Monday"
msgstr ""

#: apps/missions/constants.py:4 apps/missions/serializers.py:99
#: apps/missions/serializers/weekly_mission.py:162
msgid "Tuesday"
msgstr ""

#: apps/missions/constants.py:5 apps/missions/serializers.py:100
#: apps/missions/serializers/weekly_mission.py:163
msgid "Wednesday"
msgstr ""

#: apps/missions/constants.py:6 apps/missions/serializers.py:101
#: apps/missions/serializers/weekly_mission.py:164
msgid "Thursday"
msgstr ""

#: apps/missions/constants.py:7 apps/missions/serializers.py:102
#: apps/missions/serializers/weekly_mission.py:165
msgid "Friday"
msgstr ""

#: apps/missions/constants.py:8 apps/missions/serializers.py:103
#: apps/missions/serializers/weekly_mission.py:166
msgid "Saturday"
msgstr ""

#: apps/missions/constants.py:9 apps/missions/serializers.py:104
#: apps/missions/serializers/weekly_mission.py:167
msgid "Sunday"
msgstr ""

#: apps/missions/serializers.py:37
#: apps/missions/serializers/goal/goal_serializers.py:26
msgid ""
"You can only create goals for your own periods or periods shared with you"
msgstr ""

#: apps/missions/serializers.py:46
#: apps/missions/serializers/goal/goal_serializers.py:35
msgid "Cannot add goals to completed or archived periods"
msgstr ""

#: apps/missions/serializers.py:127
#: apps/missions/serializers/weekly_mission.py:34
msgid ""
"Practice days must be a comma-separated list of integers between 1 and 7"
msgstr ""

#: apps/missions/serializers.py:134
#: apps/missions/serializers/weekly_mission.py:41
msgid "Practice days must be a list or a comma-separated string of integers"
msgstr ""

#: apps/missions/serializers.py:140
#: apps/missions/serializers/weekly_mission.py:52
msgid "At least one practice day is required"
msgstr ""

#: apps/missions/serializers.py:148
#: apps/missions/serializers/weekly_mission.py:60
msgid "Practice days must be integers between 1 and 7"
msgstr ""

#: apps/missions/serializers.py:160
#: apps/missions/serializers/weekly_mission.py:72
msgid "Reminder time must be in HH:MM:SS format"
msgstr ""

#: apps/missions/serializers.py:169
#: apps/missions/serializers/weekly_mission.py:81
msgid ""
"You can only create missions for your own periods or periods shared with you"
msgstr ""

#: apps/missions/serializers.py:178
#: apps/missions/serializers/weekly_mission.py:90
msgid "Cannot add missions to completed or archived periods"
msgstr ""

#: apps/missions/serializers.py:184
#: apps/missions/serializers/weekly_mission.py:215
msgid "At least one goal is required"
msgstr ""

#: apps/missions/serializers.py:192
#: apps/missions/serializers/weekly_mission.py:223
#: apps/missions/serializers/weekly_mission.py:369
msgid "Exactly one goal is required for week 1"
msgstr ""

#: apps/missions/serializers.py:199
#: apps/missions/serializers/weekly_mission.py:230
#: apps/missions/serializers/weekly_mission.py:375
msgid "All goals must belong to the specified period"
msgstr ""

#: apps/missions/serializers.py:207
#: apps/missions/serializers/weekly_mission.py:97
msgid "Reminder time is required when daily reminder is enabled"
msgstr ""

#: apps/missions/serializers.py:222
#: apps/missions/serializers/weekly_mission.py:253
#, python-brace-format
msgid "A mission for week {0} already exists in this period"
msgstr ""

#: apps/missions/serializers/goal/daily_goal_serializers.py:57
msgid ""
"You can only create daily goals for your own missions or missions in periods "
"shared with you"
msgstr ""

#: apps/missions/serializers/goal/daily_goal_serializers.py:63
#: apps/missions/views/goals/daily_goal_views.py:703
#: apps/missions/views/goals/daily_goals_bulk_create.py:81
msgid "Daily goals are not allowed for week 1"
msgstr ""

#: apps/missions/serializers/goal/daily_goal_serializers.py:84
msgid "Weekly mission ID is required"
msgstr ""

#: apps/missions/serializers/goal/daily_goal_serializers.py:91
#: apps/missions/serializers/goal/daily_goal_serializers.py:92
msgid "A list of goals is required"
msgstr ""

#: apps/missions/serializers/goal/daily_goal_serializers.py:106
#: apps/missions/views/goals/daily_goal_views.py:621
msgid "Day number must be between 1 and 7"
msgstr ""

#: apps/missions/serializers/goal/daily_goal_serializers.py:111
msgid "Completed must be a boolean"
msgstr ""

#: apps/missions/serializers/goal/daily_goal_serializers.py:117
msgid "Completed cannot be true for a goal that is not due for completion"
msgstr ""

#: apps/missions/serializers/weekday.py:14
msgid "UUID of the daily goal"
msgstr ""

#: apps/missions/serializers/weekday.py:15
msgid "Title of the daily goal"
msgstr ""

#: apps/missions/serializers/weekday.py:17
msgid "Priority level of the goal (high or low)"
msgstr ""

#: apps/missions/serializers/weekday.py:20
msgid "Percentage of completion for the goal (0-100)"
msgstr ""

#: apps/missions/serializers/weekday.py:23
msgid "Whether the goal has been completed for this day"
msgstr ""

#: apps/missions/serializers/weekday.py:28
msgid "ISO formatted date and time when the goal was completed (if completed)"
msgstr ""

#: apps/missions/serializers/weekday.py:40
msgid "Day number (1-7, where 1 is Monday)"
msgstr ""

#: apps/missions/serializers/weekday.py:43
msgid "Localized name of the day (e.g., 'Monday', 'الاثنين')"
msgstr ""

#: apps/missions/serializers/weekday.py:45
msgid "ISO formatted date (YYYY-MM-DD)"
msgstr ""

#: apps/missions/serializers/weekday.py:49
msgid ""
"State of the day ('enabled' for practice days, 'disabled' for non-practice "
"days)"
msgstr ""

#: apps/missions/serializers/weekday.py:54
msgid "Whether this is a designated practice day for the mission"
msgstr ""

#: apps/missions/serializers/weekday.py:58
msgid "Overall percentage of completed goals for this day (0-100)"
msgstr ""

#: apps/missions/serializers/weekday.py:62
msgid "List of daily goals for this day (empty for non-practice days)"
msgstr ""

#: apps/missions/serializers/weekly_mission.py:110
msgid "Cannot add missions to past weeks"
msgstr ""

#: apps/missions/serializers/weekly_mission.py:243
msgid "This field is required."
msgstr ""

#: apps/missions/serializers/weekly_mission.py:312
msgid "List of goal UUIDs to associate with this weekly mission"
msgstr ""

#: apps/missions/serializers/weekly_mission.py:317
msgid "UUID of the domain for this weekly mission"
msgstr ""

#: apps/missions/serializers/weekly_mission.py:320
msgid "Week number (1-6)"
msgstr ""

#: apps/missions/serializers/weekly_mission.py:326
msgid ""
"Text for the main goal - will update the title of the first associated goal"
msgstr ""

#: apps/missions/serializers/weekly_mission.py:332
msgid "List of weekday numbers (1-7) for practice days"
msgstr ""

#: apps/missions/serializers/weekly_mission.py:335
msgid "Whether to enable daily reminders"
msgstr ""

#: apps/missions/serializers/weekly_mission.py:340
msgid ""
"Time for daily reminders in HH:MM:SS format, required if daily_reminder is "
"True"
msgstr ""

#: apps/missions/serializers/weekly_mission.py:382
#: apps/missions/views/weekly_mission.py:318
msgid "Week number must be between 1 and 6"
msgstr ""

#: apps/missions/serializers/weekly_mission.py:430
msgid "Rating must be between 1 and 10"
msgstr ""

#: apps/missions/signals.py:49
msgid "New weekly mission created"
msgstr ""

#: apps/missions/signals.py:52
#, python-brace-format
msgid "Your weekly mission for {title} has been created."
msgstr ""

#: apps/missions/signals.py:88
#, python-brace-format
msgid "Mission: {id}"
msgstr ""

#: apps/missions/signals.py:89
msgid "Practice mission:"
msgstr ""

#: apps/missions/signals.py:131
msgid "New daily goal created"
msgstr ""

#: apps/missions/signals.py:134
#, python-brace-format
msgid "A new daily goal has been created: {title}"
msgstr ""

#: apps/missions/signals.py:146
msgid "Goal completed!"
msgstr ""

#: apps/missions/signals.py:150
#, python-brace-format
msgid "Congratulations! You've completed your goal: {title}"
msgstr ""

#: apps/missions/signals.py:162
msgid "Great work this week!"
msgstr ""

#: apps/missions/signals.py:166
#, python-brace-format
msgid ""
"You rated your progress for the week highly ({rating}/5). Keep up the good "
"work!"
msgstr ""

#: apps/missions/signals.py:171
msgid "Room for improvement"
msgstr ""

#: apps/missions/signals.py:175
#, python-brace-format
msgid ""
"You rated your progress for the week as {rating}/5. What can we do to help "
"you improve next week?"
msgstr ""

#: apps/missions/views/goals/daily_goal_views.py:267
#: apps/missions/views/goals/daily_goal_views.py:348
#: apps/missions/views/goals/daily_goal_views.py:439
#: apps/missions/views/goals/daily_goal_views.py:528
#: apps/missions/views/goals/daily_goal_views.py:613
msgid "Daily goal not found"
msgstr ""

#: apps/missions/views/goals/daily_goal_views.py:275
#: apps/missions/views/goals/goal_views.py:244
msgid "You don't have permission to change the priority of this goal"
msgstr ""

#: apps/missions/views/goals/daily_goal_views.py:290
#: apps/missions/views/goals/goal_views.py:259
msgid "Could not update priority"
msgstr ""

#: apps/missions/views/goals/daily_goal_views.py:358
#: apps/missions/views/goals/daily_goal_views.py:449
#: apps/missions/views/goals/daily_goal_views.py:538
msgid "You don't have permission to update this goal"
msgstr ""

#: apps/missions/views/goals/daily_goal_views.py:364
#: apps/missions/views/goals/daily_goal_views.py:455
msgid "Day number is required"
msgstr ""

#: apps/missions/views/goals/daily_goal_views.py:373
#: apps/missions/views/goals/daily_goal_views.py:464
msgid "No progress entry found for this day"
msgstr ""

#: apps/missions/views/goals/daily_goal_views.py:381
msgid "Could not mark as completed"
msgstr ""

#: apps/missions/views/goals/daily_goal_views.py:472
msgid "Could not mark as incomplete"
msgstr ""

#: apps/missions/views/goals/daily_goal_views.py:629
msgid "No progress found for the specified day"
msgstr ""

#: apps/missions/views/goals/daily_goal_views.py:688
#: apps/missions/views/goals/daily_goals_bulk_create.py:66
msgid "Weekly mission not found"
msgstr ""

#: apps/missions/views/goals/daily_goal_views.py:697
#: apps/missions/views/goals/daily_goals_bulk_create.py:75
msgid "You don't have permission to add goals to this mission"
msgstr ""

#: apps/missions/views/goals/daily_goals_bulk_create.py:107
#, python-brace-format
msgid "{count} daily goals created successfully"
msgstr ""

#: apps/missions/views/goals/goal_views.py:236
msgid "Goal not found"
msgstr ""

#: apps/missions/views/weekly_mission.py:290
msgid "Could not update completion status"
msgstr ""

#: apps/missions/views/weekly_mission.py:362
msgid "Search query is required"
msgstr ""

#: apps/missions/views/weekly_mission.py:449
msgid "Period is not in progress or current week cannot be determined"
msgstr ""
