# This file contains the Arabic translations for the missions app
# This file is distributed under the same license as the PACKAGE package.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: django-tajweed\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-03 16:20+0000\n"
"PO-Revision-Date: 2025-03-23 14:44+0000\n"
"Last-Translator: translator <<EMAIL>>\n"
"Language-Team: Arabic <<EMAIL>>\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 "
"&& n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#: apps/missions/constants.py:3 apps/missions/serializers.py:98
#: apps/missions/serializers/weekly_mission.py:161
msgid "Monday"
msgstr "الاثنين"

#: apps/missions/constants.py:4 apps/missions/serializers.py:99
#: apps/missions/serializers/weekly_mission.py:162
msgid "Tuesday"
msgstr "الثلاثاء"

#: apps/missions/constants.py:5 apps/missions/serializers.py:100
#: apps/missions/serializers/weekly_mission.py:163
msgid "Wednesday"
msgstr "الأربعاء"

#: apps/missions/constants.py:6 apps/missions/serializers.py:101
#: apps/missions/serializers/weekly_mission.py:164
msgid "Thursday"
msgstr "الخميس"

#: apps/missions/constants.py:7 apps/missions/serializers.py:102
#: apps/missions/serializers/weekly_mission.py:165
msgid "Friday"
msgstr "الجمعة"

#: apps/missions/constants.py:8 apps/missions/serializers.py:103
#: apps/missions/serializers/weekly_mission.py:166
msgid "Saturday"
msgstr "السبت"

#: apps/missions/constants.py:9 apps/missions/serializers.py:104
#: apps/missions/serializers/weekly_mission.py:167
msgid "Sunday"
msgstr "الأحد"

#: apps/missions/serializers.py:37
#: apps/missions/serializers/goal/goal_serializers.py:26
msgid ""
"You can only create goals for your own periods or periods shared with you"
msgstr "يمكنك إنشاء أهداف فقط للفترات الخاصة بك أو الفترات المشتركة معك"

#: apps/missions/serializers.py:46
#: apps/missions/serializers/goal/goal_serializers.py:35
msgid "Cannot add goals to completed or archived periods"
msgstr "لا يمكن إضافة أهداف إلى الفترات المكتملة أو المؤرشفة"

#: apps/missions/serializers.py:127
#: apps/missions/serializers/weekly_mission.py:34
msgid ""
"Practice days must be a comma-separated list of integers between 1 and 7"
msgstr "يجب أن تكون أيام التدريب قائمة مفصولة بفواصل من الأرقام بين 1 و 7"

#: apps/missions/serializers.py:134
#: apps/missions/serializers/weekly_mission.py:41
msgid "Practice days must be a list or a comma-separated string of integers"
msgstr "يجب أن تكون أيام التدريب قائمة أو سلسلة نصية مفصولة بفواصل من الأرقام"

#: apps/missions/serializers.py:140
#: apps/missions/serializers/weekly_mission.py:52
msgid "At least one practice day is required"
msgstr "مطلوب يوم تدريب واحد على الأقل"

#: apps/missions/serializers.py:148
#: apps/missions/serializers/weekly_mission.py:60
msgid "Practice days must be integers between 1 and 7"
msgstr "يجب أن تكون أيام التدريب أرقامًا بين 1 و 7"

#: apps/missions/serializers.py:160
#: apps/missions/serializers/weekly_mission.py:72
msgid "Reminder time must be in HH:MM:SS format"
msgstr "يجب أن يكون وقت التذكير بتنسيق HH:MM:SS"

#: apps/missions/serializers.py:169
#: apps/missions/serializers/weekly_mission.py:81
msgid ""
"You can only create missions for your own periods or periods shared with you"
msgstr "يمكنك إنشاء مهام فقط للفترات الخاصة بك أو الفترات المشتركة معك"

#: apps/missions/serializers.py:178
#: apps/missions/serializers/weekly_mission.py:90
msgid "Cannot add missions to completed or archived periods"
msgstr "لا يمكن إضافة مهام إلى الفترات المكتملة أو المؤرشفة"

#: apps/missions/serializers.py:184
#: apps/missions/serializers/weekly_mission.py:215
msgid "At least one goal is required"
msgstr "مطلوب هدف واحد على الأقل"

#: apps/missions/serializers.py:192
#: apps/missions/serializers/weekly_mission.py:223
#: apps/missions/serializers/weekly_mission.py:369
msgid "Exactly one goal is required for week 1"
msgstr "مطلوب هدف واحد بالضبط للأسبوع الأول"

#: apps/missions/serializers.py:199
#: apps/missions/serializers/weekly_mission.py:230
#: apps/missions/serializers/weekly_mission.py:375
msgid "All goals must belong to the specified period"
msgstr "يجب أن تنتمي جميع الأهداف إلى الفترة المحددة"

#: apps/missions/serializers.py:207
#: apps/missions/serializers/weekly_mission.py:97
msgid "Reminder time is required when daily reminder is enabled"
msgstr "مطلوب وقت التذكير عند تمكين التذكير اليومي"

#: apps/missions/serializers.py:222
#: apps/missions/serializers/weekly_mission.py:253
#, python-brace-format
msgid "A mission for week {0} already exists in this period"
msgstr "مهمة للأسبوع {0} موجودة بالفعل في هذه الفترة"

#: apps/missions/serializers/goal/daily_goal_serializers.py:57
msgid ""
"You can only create daily goals for your own missions or missions in periods "
"shared with you"
msgstr ""
"يمكنك إنشاء أهداف يومية فقط للمهام الخاصة بك أو المهام في الفترات المشتركة "
"معك"

#: apps/missions/serializers/goal/daily_goal_serializers.py:63
#: apps/missions/views/goals/daily_goal_views.py:703
#: apps/missions/views/goals/daily_goals_bulk_create.py:81
msgid "Daily goals are not allowed for week 1"
msgstr "لا يسمح بالأهداف اليومية للأسبوع الأول"

#: apps/missions/serializers/goal/daily_goal_serializers.py:84
msgid "Weekly mission ID is required"
msgstr "معرف المهمة الأسبوعية مطلوب"

#: apps/missions/serializers/goal/daily_goal_serializers.py:91
#: apps/missions/serializers/goal/daily_goal_serializers.py:92
msgid "A list of goals is required"
msgstr "قائمة الأهداف مطلوبة"

#: apps/missions/serializers/goal/daily_goal_serializers.py:106
#: apps/missions/views/goals/daily_goal_views.py:621
msgid "Day number must be between 1 and 7"
msgstr "يجب أن يكون رقم اليوم بين 1 و 7"

#: apps/missions/serializers/goal/daily_goal_serializers.py:111
msgid "Completed must be a boolean"
msgstr "يجب أن تكون قيمة 'مكتمل' منطقية (boolean)"

#: apps/missions/serializers/goal/daily_goal_serializers.py:117
msgid "Completed cannot be true for a goal that is not due for completion"
msgstr "لا يمكن أن تكون قيمة 'مكتمل' صحيحة لهدف لم يحن موعد إكماله بعد"

#: apps/missions/serializers/weekday.py:14
msgid "UUID of the daily goal"
msgstr "المعرف الفريد للهدف اليومي (UUID)"

#: apps/missions/serializers/weekday.py:15
msgid "Title of the daily goal"
msgstr "عنوان الهدف اليومي"

#: apps/missions/serializers/weekday.py:17
msgid "Priority level of the goal (high or low)"
msgstr "مستوى أولوية الهدف (مرتفع أو منخفض)"

#: apps/missions/serializers/weekday.py:20
msgid "Percentage of completion for the goal (0-100)"
msgstr "النسبة المئوية لإكمال الهدف (0-100)"

#: apps/missions/serializers/weekday.py:23
msgid "Whether the goal has been completed for this day"
msgstr "ما إذا كان الهدف قد اكتمل لهذا اليوم"

#: apps/missions/serializers/weekday.py:28
msgid "ISO formatted date and time when the goal was completed (if completed)"
msgstr "التاريخ والوقت بتنسيق ISO عند إكمال الهدف (إذا اكتمل)"

#: apps/missions/serializers/weekday.py:40
msgid "Day number (1-7, where 1 is Monday)"
msgstr "رقم اليوم (1-7، حيث 1 هو الاثنين)"

#: apps/missions/serializers/weekday.py:43
msgid "Localized name of the day (e.g., 'Monday', 'الاثنين')"
msgstr "اسم اليوم المترجم (مثال: 'Monday'، 'الاثنين')"

#: apps/missions/serializers/weekday.py:45
msgid "ISO formatted date (YYYY-MM-DD)"
msgstr "التاريخ بتنسيق ISO (YYYY-MM-DD)"

#: apps/missions/serializers/weekday.py:49
msgid ""
"State of the day ('enabled' for practice days, 'disabled' for non-practice "
"days)"
msgstr "حالة اليوم ('مفعل' لأيام التدريب، 'معطل' لأيام عدم التدريب)"

#: apps/missions/serializers/weekday.py:54
msgid "Whether this is a designated practice day for the mission"
msgstr "ما إذا كان هذا يوم تدريب محدد للمهمة"

#: apps/missions/serializers/weekday.py:58
msgid "Overall percentage of completed goals for this day (0-100)"
msgstr "النسبة المئوية الإجمالية للأهداف المكتملة لهذا اليوم (0-100)"

#: apps/missions/serializers/weekday.py:62
msgid "List of daily goals for this day (empty for non-practice days)"
msgstr "قائمة الأهداف اليومية لهذا اليوم (فارغة لأيام عدم التدريب)"

#: apps/missions/serializers/weekly_mission.py:110
msgid "Cannot add missions to past weeks"
msgstr "لا يمكن إضافة مهام إلى الأسابيع الماضية"

#: apps/missions/serializers/weekly_mission.py:243
msgid "This field is required."
msgstr "هذا الحقل مطلوب."

#: apps/missions/serializers/weekly_mission.py:312
msgid "List of goal UUIDs to associate with this weekly mission"
msgstr "قائمة معرفات الأهداف (UUIDs) لربطها بهذه المهمة الأسبوعية"

#: apps/missions/serializers/weekly_mission.py:317
msgid "UUID of the domain for this weekly mission"
msgstr "المعرف الفريد (UUID) للمجال لهذه المهمة الأسبوعية"

#: apps/missions/serializers/weekly_mission.py:320
msgid "Week number (1-6)"
msgstr "رقم الأسبوع (1-6)"

#: apps/missions/serializers/weekly_mission.py:326
msgid ""
"Text for the main goal - will update the title of the first associated goal"
msgstr "نص الهدف الرئيسي - سيتم تحديث عنوان الهدف الأول المرتبط"

#: apps/missions/serializers/weekly_mission.py:332
msgid "List of weekday numbers (1-7) for practice days"
msgstr "قائمة أرقام أيام الأسبوع (1-7) لأيام التدريب"

#: apps/missions/serializers/weekly_mission.py:335
msgid "Whether to enable daily reminders"
msgstr "ما إذا كان سيتم تمكين التذكيرات اليومية"

#: apps/missions/serializers/weekly_mission.py:340
msgid ""
"Time for daily reminders in HH:MM:SS format, required if daily_reminder is "
"True"
msgstr ""
"وقت التذكيرات اليومية بتنسيق HH:MM:SS، مطلوب إذا كانت قيمة daily_reminder هي "
"True"

#: apps/missions/serializers/weekly_mission.py:382
#: apps/missions/views/weekly_mission.py:318
msgid "Week number must be between 1 and 6"
msgstr "يجب أن يكون رقم الأسبوع بين 1 و 6"

#: apps/missions/serializers/weekly_mission.py:430
msgid "Rating must be between 1 and 10"
msgstr "يجب أن يكون التقييم بين 1 و 10"

#: apps/missions/signals.py:49
msgid "New weekly mission created"
msgstr "تم إنشاء مهمة أسبوعية جديدة"

#: apps/missions/signals.py:52
#, python-brace-format
msgid "Your weekly mission for {title} has been created."
msgstr "تم إنشاء مهمتك الأسبوعية لـ {title}."

#: apps/missions/signals.py:88
#, python-brace-format
msgid "Mission: {id}"
msgstr "المهمة: {id}"

#: apps/missions/signals.py:89
msgid "Practice mission:"
msgstr "مهمة التدريب:"

#: apps/missions/signals.py:131
msgid "New daily goal created"
msgstr "تم إنشاء هدف يومي جديد"

#: apps/missions/signals.py:134
#, python-brace-format
msgid "A new daily goal has been created: {title}"
msgstr "تم إنشاء هدف يومي جديد: {title}"

#: apps/missions/signals.py:146
msgid "Goal completed!"
msgstr "اكتمل الهدف!"

#: apps/missions/signals.py:150
#, python-brace-format
msgid "Congratulations! You've completed your goal: {title}"
msgstr "تهانينا! لقد أكملت هدفك: {title}"

#: apps/missions/signals.py:162
msgid "Great work this week!"
msgstr "عمل رائع هذا الأسبوع!"

#: apps/missions/signals.py:166
#, python-brace-format
msgid ""
"You rated your progress for the week highly ({rating}/5). Keep up the good "
"work!"
msgstr ""
"لقد قيمت تقدمك لهذا الأسبوع بدرجة عالية ({rating}/5). واصل العمل الجيد!"

#: apps/missions/signals.py:171
msgid "Room for improvement"
msgstr "مجال للتحسين"

#: apps/missions/signals.py:175
#, python-brace-format
msgid ""
"You rated your progress for the week as {rating}/5. What can we do to help "
"you improve next week?"
msgstr ""
"لقد قيمت تقدمك لهذا الأسبوع بـ {rating}/5. ما الذي يمكننا فعله لمساعدتك على "
"التحسن الأسبوع القادم؟"

#: apps/missions/views/goals/daily_goal_views.py:267
#: apps/missions/views/goals/daily_goal_views.py:348
#: apps/missions/views/goals/daily_goal_views.py:439
#: apps/missions/views/goals/daily_goal_views.py:528
#: apps/missions/views/goals/daily_goal_views.py:613
msgid "Daily goal not found"
msgstr "لم يتم العثور على الهدف اليومي"

#: apps/missions/views/goals/daily_goal_views.py:275
#: apps/missions/views/goals/goal_views.py:244
msgid "You don't have permission to change the priority of this goal"
msgstr "ليس لديك إذن لتغيير أولوية هذا الهدف"

#: apps/missions/views/goals/daily_goal_views.py:290
#: apps/missions/views/goals/goal_views.py:259
msgid "Could not update priority"
msgstr "تعذر تحديث الأولوية"

#: apps/missions/views/goals/daily_goal_views.py:358
#: apps/missions/views/goals/daily_goal_views.py:449
#: apps/missions/views/goals/daily_goal_views.py:538
msgid "You don't have permission to update this goal"
msgstr "ليس لديك إذن لتحديث هذا الهدف"

#: apps/missions/views/goals/daily_goal_views.py:364
#: apps/missions/views/goals/daily_goal_views.py:455
msgid "Day number is required"
msgstr "رقم اليوم مطلوب"

#: apps/missions/views/goals/daily_goal_views.py:373
#: apps/missions/views/goals/daily_goal_views.py:464
msgid "No progress entry found for this day"
msgstr "لم يتم العثور على أي إدخال للتقدم لهذا اليوم"

#: apps/missions/views/goals/daily_goal_views.py:381
msgid "Could not mark as completed"
msgstr "تعذر وضع علامة كمكتمل"

#: apps/missions/views/goals/daily_goal_views.py:472
msgid "Could not mark as incomplete"
msgstr "تعذر وضع علامة كغير مكتمل"

#: apps/missions/views/goals/daily_goal_views.py:629
msgid "No progress found for the specified day"
msgstr "لم يتم العثور على تقدم لليوم المحدد"

#: apps/missions/views/goals/daily_goal_views.py:688
#: apps/missions/views/goals/daily_goals_bulk_create.py:66
msgid "Weekly mission not found"
msgstr "لم يتم العثور على المهمة الأسبوعية"

#: apps/missions/views/goals/daily_goal_views.py:697
#: apps/missions/views/goals/daily_goals_bulk_create.py:75
msgid "You don't have permission to add goals to this mission"
msgstr "ليس لديك إذن لإضافة أهداف إلى هذه المهمة"

#: apps/missions/views/goals/daily_goals_bulk_create.py:107
#, python-brace-format
msgid "{count} daily goals created successfully"
msgstr "تم إنشاء {count} أهداف يومية بنجاح"

#: apps/missions/views/goals/goal_views.py:236
msgid "Goal not found"
msgstr "لم يتم العثور على الهدف"

#: apps/missions/views/weekly_mission.py:290
msgid "Could not update completion status"
msgstr "تعذر تحديث حالة الإكمال"

#: apps/missions/views/weekly_mission.py:362
msgid "Search query is required"
msgstr "مطلوب إدخال استعلام البحث"

#: apps/missions/views/weekly_mission.py:449
msgid "Period is not in progress or current week cannot be determined"
msgstr "الفترة ليست قيد التقدم أو لا يمكن تحديد الأسبوع الحالي"

#~ msgid "Practice days are not allowed for week 1"
#~ msgstr "لا يسمح بأيام التدريب للأسبوع الأول"
