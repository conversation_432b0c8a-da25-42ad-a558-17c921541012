from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from apps.domains.serializers.domain import DomainSerializer
from apps.missions.models import WeeklyMission, Goal, DailyGoal, WeeklySuccessRating
from apps.okrs.models import SixWeekPeriod
from drf_spectacular.utils import (
    extend_schema_field,
    extend_schema_serializer,
    OpenApiExample,
)
from drf_spectacular.types import OpenApiTypes
from apps.missions.serializers.goal import GoalSerializer, DailyGoalSerializer
from django.conf import settings
from apps.domains.models import Domain


class WeeklyMissionBaseSerializer(serializers.ModelSerializer):
    """Base serializer for WeeklyMission model with common validation logic"""

    class Meta:
        model = WeeklyMission
        fields = []

    def validate_practice_days(self, value):
        """
        Validate that practice_days is a list of integers between 1 and 7 or a comma-separated string of integers.
        """
        if isinstance(value, str):
            try:
                value = [int(day.strip()) for day in value.split(",") if day.strip()]
            except ValueError:
                raise serializers.ValidationError(
                    _(
                        "Practice days must be a comma-separated list of integers between 1 and 7"
                    )
                )

        if not isinstance(value, list):
            raise serializers.ValidationError(
                _(
                    "Practice days must be a list or a comma-separated string of integers"
                )
            )

        if not value:
            if (
                self.initial_data
                and isinstance(self.initial_data, dict)
                and self.initial_data.get("week_number") != 1
            ):
                raise serializers.ValidationError(
                    _("At least one practice day is required")
                )

        valid_days = set(range(1, 8))

        for day in value:
            if not isinstance(day, int) or day not in valid_days:
                raise serializers.ValidationError(
                    _("Practice days must be integers between 1 and 7")
                )

        return value

    def validate_reminder_time(self, value):
        """Validate that reminder_time is in HH:MM:SS format."""
        if value and not isinstance(value, str):
            try:
                return value
            except (ValueError, TypeError):
                raise serializers.ValidationError(
                    _("Reminder time must be in HH:MM:SS format")
                )
        return value

    def validate_six_week_period(self, value):
        user = self.context["request"].user
        if value.user != user and user not in value.shared_with.all():
            raise serializers.ValidationError(
                _(
                    "You can only create missions for your own periods or periods shared with you"
                )
            )

        if value.status not in [
            SixWeekPeriod.STATUS_DRAFT,
            SixWeekPeriod.STATUS_IN_PROGRESS,
        ]:
            raise serializers.ValidationError(
                _("Cannot add missions to completed or archived periods")
            )
        return value

    def validate(self, data):
        if data.get("daily_reminder") and not data.get("reminder_time"):
            raise serializers.ValidationError(
                _("Reminder time is required when daily reminder is enabled")
            )

        six_week_period = data.get("six_week_period")
        week_number = data.get("week_number")

        if (
            six_week_period
            and six_week_period.status == SixWeekPeriod.STATUS_IN_PROGRESS
        ):
            current_week = six_week_period.get_current_week()
            if current_week and week_number < current_week:
                raise serializers.ValidationError(
                    _("Cannot add missions to past weeks")
                )

        return data


class WeeklyMissionReadSerializer(WeeklyMissionBaseSerializer):
    """Serializer for reading WeeklyMission instances"""

    goals = GoalSerializer(many=True, read_only=True)
    daily_goals = DailyGoalSerializer(many=True, read_only=True)
    is_completed = serializers.BooleanField(read_only=True)
    practice_days_display = serializers.SerializerMethodField()
    domain = DomainSerializer(read_only=True)
    success_rating = serializers.SerializerMethodField()

    class Meta:
        model = WeeklyMission
        fields = [
            "id",
            "user",
            "six_week_period",
            "domain",
            "week_number",
            "goals",
            "daily_goals",
            "practice_days",
            "practice_days_display",
            "daily_reminder",
            "reminder_time",
            "is_completed",
            "completion_date",
            "success_rating",
            "created",
            "updated",
        ]
        read_only_fields = [
            "user",
            "completion_date",
            "is_completed",
            "practice_days_display",
            "daily_goals",
            "success_rating",
            "created",
            "updated",
        ]

    @extend_schema_field(OpenApiTypes.OBJECT)
    def get_practice_days_display(self, obj):
        """Return the translated day names for the practice days."""
        day_mapping = {
            1: _("Monday"),
            2: _("Tuesday"),
            3: _("Wednesday"),
            4: _("Thursday"),
            5: _("Friday"),
            6: _("Saturday"),
            7: _("Sunday"),
        }

        result = []
        if hasattr(obj, "practice_days") and obj.practice_days:
            for day_number in obj.practice_days:
                if day_number in day_mapping:
                    day_name = day_mapping[day_number]
                    result.append((day_number, day_name))

        return result

    @extend_schema_field(OpenApiTypes.INT)
    def get_success_rating(self, obj):
        """Return the most recent success rating or None if not rated."""
        latest_rating = obj.success_ratings.first()
        if latest_rating:
            return latest_rating.rating
        return None


class WeeklyMissionCreateSerializer(WeeklyMissionBaseSerializer):
    """Serializer for creating WeeklyMission instances"""

    goal_ids = serializers.PrimaryKeyRelatedField(
        queryset=Goal.objects.all(), write_only=True, many=True, source="goals"
    )

    domain = serializers.PrimaryKeyRelatedField(queryset=Domain.objects.all())

    id = serializers.UUIDField(read_only=True)

    class Meta:
        model = WeeklyMission
        fields = [
            "id",
            "six_week_period",
            "domain",
            "week_number",
            "goal_ids",
            "practice_days",
            "daily_reminder",
            "reminder_time",
            "is_completed",
        ]

    def validate_goal_ids(self, goals):
        if not goals:
            raise serializers.ValidationError(_("At least one goal is required"))

        if self.initial_data and isinstance(self.initial_data, dict):
            six_week_period = self.initial_data.get("six_week_period")
            week_number = self.initial_data.get("week_number")

            if week_number == 1 and len(goals) != 1:
                raise serializers.ValidationError(
                    _("Exactly one goal is required for week 1")
                )

            if six_week_period:
                for goal in goals:
                    if str(goal.six_week_period.id) != six_week_period:
                        raise serializers.ValidationError(
                            _("All goals must belong to the specified period")
                        )

        return goals

    def validate(self, data):
        data = super().validate(data)

        six_week_period = data.get("six_week_period")
        week_number = data.get("week_number")
        domain = data.get("domain")

        if not domain:
            raise serializers.ValidationError({"domain": _("This field is required.")})

        if six_week_period and week_number:
            existing_mission = WeeklyMission.objects.filter(
                six_week_period=six_week_period,
                week_number=week_number,
            ).first()

            if existing_mission:
                raise serializers.ValidationError(
                    _("A mission for week {0} already exists in this period").format(
                        week_number
                    )
                )

        return data

    def create(self, validated_data):
        user = self.context["request"].user
        validated_data["user"] = user

        goals = validated_data.pop("goals", [])

        weekly_mission = WeeklyMission.objects.create(**validated_data)

        for goal in goals:
            try:
                existing_goal = Goal.objects.filter(
                    six_week_period=weekly_mission.six_week_period, title=goal.title
                ).first()

                if existing_goal:
                    weekly_mission.goals.add(existing_goal)
                else:
                    weekly_mission.goals.add(goal)
            except Exception as e:
                weekly_mission.goals.add(goal)

        return weekly_mission


@extend_schema_serializer(
    examples=[
        OpenApiExample(
            "WeeklyMissionUpdate Example",
            summary="Update a weekly mission",
            description="Example of updating a weekly mission with all possible fields",
            value={
                "domain": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                "week_number": 2,
                "goal_ids": ["3fa85f64-5717-4562-b3fc-2c963f66afa6"],
                "practice_days": [1, 3, 5],
                "daily_reminder": True,
                "reminder_time": "09:00:00",
                "weekly_goal": "Updated goal title",
            },
            request_only=True,
        )
    ]
)
class WeeklyMissionUpdateSerializer(WeeklyMissionBaseSerializer):
    """Serializer for updating WeeklyMission instances"""

    goal_ids = serializers.PrimaryKeyRelatedField(
        queryset=Goal.objects.all(),
        write_only=True,
        many=True,
        source="goals",
        required=False,
        help_text=_("List of goal UUIDs to associate with this weekly mission"),
    )
    domain = serializers.PrimaryKeyRelatedField(
        queryset=Domain.objects.all(),
        required=False,
        help_text=_("UUID of the domain for this weekly mission"),
    )
    week_number = serializers.IntegerField(
        required=False, min_value=1, max_value=6, help_text=_("Week number (1-6)")
    )
    weekly_goal = serializers.CharField(
        required=False,
        write_only=True,
        help_text=_(
            "Text for the main goal - will update the title of the first associated goal"
        ),
    )
    practice_days = serializers.ListField(
        child=serializers.IntegerField(min_value=1, max_value=7),
        required=False,
        help_text=_("List of weekday numbers (1-7) for practice days"),
    )
    daily_reminder = serializers.BooleanField(
        required=False, help_text=_("Whether to enable daily reminders")
    )
    reminder_time = serializers.TimeField(
        required=False,
        help_text=_(
            "Time for daily reminders in HH:MM:SS format, required if daily_reminder is True"
        ),
    )

    class Meta:
        model = WeeklyMission
        fields = [
            "domain",
            "week_number",
            "goal_ids",
            "practice_days",
            "daily_reminder",
            "reminder_time",
            "weekly_goal",
        ]

    def validate_goal_ids(self, goals):
        if (
            self.initial_data
            and isinstance(self.initial_data, dict)
            and self.instance is not None
        ):
            six_week_period = self.instance.six_week_period
            week_number = self.initial_data.get(
                "week_number", self.instance.week_number
            )

            if week_number == 1 and len(goals) != 1:
                raise serializers.ValidationError(
                    _("Exactly one goal is required for week 1")
                )

            for goal in goals:
                if goal.six_week_period.id != six_week_period.id:
                    raise serializers.ValidationError(
                        _("All goals must belong to the specified period")
                    )

        return goals

    def validate_week_number(self, value):
        if value < 1 or value > 6:
            raise serializers.ValidationError(_("Week number must be between 1 and 6"))
        return value

    def update(self, instance, validated_data):
        weekly_goal = validated_data.pop("weekly_goal", None)
        goals = validated_data.pop("goals", None)

        weekly_mission = super().update(instance, validated_data)

        if goals is not None:
            weekly_mission.goals.set(goals)

        if weekly_goal and weekly_mission.goals.exists():
            first_goal = weekly_mission.goals.first()
            first_goal.title = weekly_goal
            first_goal.save()

        return weekly_mission


class WeeklyMissionSerializer(WeeklyMissionReadSerializer):
    pass


@extend_schema_serializer(
    examples=[
        OpenApiExample(
            "Weekly Success Rating Example",
            summary="Rate weekly success",
            description="Rate how successful you felt your week was on a scale of 1-10",
            value={"rating": 8},
            request_only=True,
        )
    ]
)
class WeeklySuccessRatingSerializer(serializers.ModelSerializer):
    """Serializer for recording user's weekly success rating"""

    weekly_mission_id = serializers.UUIDField(write_only=True, required=False)

    class Meta:
        model = WeeklySuccessRating
        fields = ["id", "weekly_mission_id", "rating", "created"]
        read_only_fields = ["id", "created"]

    def validate_rating(self, value):
        """Ensure the rating is between 1 and 10"""
        if value < 1 or value > 10:
            raise serializers.ValidationError(_("Rating must be between 1 and 10"))
        return value

    def create(self, validated_data):
        if (
            "weekly_mission_id" not in validated_data
            and "weekly_mission" in self.context
        ):
            validated_data["weekly_mission"] = self.context["weekly_mission"]

        return super().create(validated_data)
