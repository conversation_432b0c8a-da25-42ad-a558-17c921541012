from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from apps.missions.models import DailyGoal, DailyGoalProgress


class DailyGoalProgressSerializer(serializers.ModelSerializer):
    class Meta:
        model = DailyGoalProgress
        fields = [
            "id",
            "day_number",
            "completed",
            "completion_date",
            "created",
            "updated",
        ]
        read_only_fields = ["created", "updated"]


class DailyGoalSerializer(serializers.ModelSerializer):
    is_completed = serializers.BooleanField(read_only=True)
    progress = DailyGoalProgressSerializer(
        source="daily_progress", many=True, read_only=True
    )
    completion_percentage = serializers.SerializerMethodField()

    class Meta:
        model = DailyGoal
        fields = [
            "id",
            "weekly_mission",
            "title",
            "priority",
            "is_completed",
            "completion_date",
            "progress",
            "completion_percentage",
            "created",
            "updated",
        ]
        read_only_fields = [
            "completion_date",
            "created",
            "updated",
            "completion_percentage",
        ]

    def get_completion_percentage(self, obj):
        return obj.get_overall_completion_percentage()

    def validate_weekly_mission(self, value):
        user = self.context["request"].user

        if value.user != user and user not in value.six_week_period.shared_with.all():
            raise serializers.ValidationError(
                _(
                    "You can only create daily goals for your own missions or missions in periods shared with you"
                )
            )

        if value.week_number == 1:
            raise serializers.ValidationError(
                _("Daily goals are not allowed for week 1")
            )

        return value

    def create(self, validated_data):
        daily_goal = super().create(validated_data)

        daily_goal.create_daily_progress_entries()
        return daily_goal


class DailyGoalCreateInputSerializer(serializers.Serializer):
    title = serializers.CharField(required=True)
    priority = serializers.ChoiceField(
        choices=DailyGoal.PRIORITY_CHOICES, default=DailyGoal.PRIORITY_LOW
    )


class BulkDailyGoalCreateSerializer(serializers.Serializer):
    weekly_mission = serializers.UUIDField(
        required=True, error_messages={"required": _("Weekly mission ID is required")}
    )
    goals = serializers.ListField(
        child=serializers.DictField(),
        required=True,
        allow_empty=False,
        error_messages={
            "required": _("A list of goals is required"),
            "empty": _("A list of goals is required"),
        },
    )

    class Meta:
        fields = ["weekly_mission", "goals"]


class DailyGoalProgressUpdateSerializer(serializers.Serializer):
    day_number = serializers.IntegerField(min_value=1, max_value=7)
    completed = serializers.BooleanField()

    def validate_day_number(self, value):
        if value < 1 or value > 7:
            raise serializers.ValidationError(_("Day number must be between 1 and 7"))
        return value

    def validate_completed(self, value):
        if not isinstance(value, bool):
            raise serializers.ValidationError(_("Completed must be a boolean"))
        return value

    def validate(self, data):
        if data["completed"] and data["day_number"] > 5:
            raise serializers.ValidationError(
                _("Completed cannot be true for a goal that is not due for completion")
            )
        return data

    def update(self, instance, validated_data):
        instance.completed = validated_data["completed"]
        instance.save()
        return instance


class DailyGoalCompleteSerializer(serializers.Serializer):
    """
    Serializer for marking daily goals as complete or incomplete.
    Doesn't require any input data - uses the URL path parameter for the day.
    """

    pass
