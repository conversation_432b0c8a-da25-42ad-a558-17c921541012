from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from apps.missions.models import Goal
from apps.okrs.models import SixWeekPeriod


class GoalSerializer(serializers.ModelSerializer):
    class Meta:
        model = Goal
        fields = [
            "id",
            "user",
            "six_week_period",
            "title",
            "priority",
            "created",
            "updated",
        ]
        read_only_fields = ["user", "created", "updated"]

    def validate_six_week_period(self, value):
        user = self.context["request"].user
        if value.user != user and user not in value.shared_with.all():
            raise serializers.ValidationError(
                _(
                    "You can only create goals for your own periods or periods shared with you"
                )
            )

        if value.status not in [
            SixWeekPeriod.STATUS_DRAFT,
            SixWeekPeriod.STATUS_IN_PROGRESS,
        ]:
            raise serializers.ValidationError(
                _("Cannot add goals to completed or archived periods")
            )
        return value

    def validate(self, data):
        if not self.instance:
            title = data.get("title")
            six_week_period = data.get("six_week_period")

            existing_goal = Goal.objects.filter(
                six_week_period=six_week_period, title=title
            ).first()

            if existing_goal:
                self.instance = existing_goal
                return {}

        return data

    def create(self, validated_data):
        if self.instance:
            return self.instance

        validated_data["user"] = self.context["request"].user
        return super().create(validated_data)
