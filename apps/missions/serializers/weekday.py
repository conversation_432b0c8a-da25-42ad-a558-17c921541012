from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from apps.missions.models import DailyGoal, DailyGoalProgress
from django.utils import translation
from drf_spectacular.utils import extend_schema_field


class DayGoalSerializer(serializers.Serializer):
    """
    Serializer for representing daily goals within a specific day of the week.
    Used as a nested serializer in WeekdaySerializer.
    """

    id = serializers.CharField(help_text=_("UUID of the daily goal"))
    title = serializers.CharField(help_text=_("Title of the daily goal"))
    priority = serializers.CharField(
        default="high", help_text=_("Priority level of the goal (high or low)")
    )
    completion_percentage = serializers.IntegerField(
        default=0, help_text=_("Percentage of completion for the goal (0-100)")
    )
    completed = serializers.BooleanField(
        help_text=_("Whether the goal has been completed for this day")
    )
    completion_date = serializers.CharField(
        allow_null=True,
        help_text=_(
            "ISO formatted date and time when the goal was completed (if completed)"
        ),
    )


class WeekdaySerializer(serializers.Serializer):
    """
    Serializer for representing a day of the week in a weekly mission.
    Includes day information and associated goals if it's a practice day.
    """

    day_number = serializers.IntegerField(
        help_text=_("Day number (1-7, where 1 is Monday)")
    )
    day_name = serializers.CharField(
        help_text=_("Localized name of the day (e.g., 'Monday', 'الاثنين')")
    )
    date = serializers.CharField(help_text=_("ISO formatted date (YYYY-MM-DD)"))
    state = serializers.CharField(
        default="enabled",
        help_text=_(
            "State of the day ('enabled' for practice days, 'disabled' for non-practice days)"
        ),
    )
    is_practice_day = serializers.BooleanField(
        source="is_practice",
        help_text=_("Whether this is a designated practice day for the mission"),
    )
    completion_percentage = serializers.IntegerField(
        default=0,
        help_text=_("Overall percentage of completed goals for this day (0-100)"),
    )
    daily_goals = serializers.ListField(
        source="goals",
        help_text=_("List of daily goals for this day (empty for non-practice days)"),
    )

    @extend_schema_field(DayGoalSerializer(many=True))
    def get_daily_goals(self, obj):
        """Return properly serialized goals for documentation."""
        return obj.get("goals", [])

    def get_day_name(self, obj):
        """Return the day name in the current language using Django's translation."""
        day_names_en = {
            1: "Monday",
            2: "Tuesday",
            3: "Wednesday",
            4: "Thursday",
            5: "Friday",
            6: "Saturday",
            7: "Sunday",
        }

        day_names_es = {
            1: "Lunes",
            2: "Martes",
            3: "Miércoles",
            4: "Jueves",
            5: "Viernes",
            6: "Sábado",
            7: "Domingo",
        }

        day_names_ar = {
            1: "الاثنين",
            2: "الثلاثاء",
            3: "الأربعاء",
            4: "الخميس",
            5: "الجمعة",
            6: "السبت",
            7: "الأحد",
        }

        current_language = translation.get_language()

        if current_language and current_language.startswith("ar"):
            return day_names_ar[obj["day_number"]]
        elif current_language and current_language.startswith("es"):
            return day_names_es[obj["day_number"]]
        else:
            return day_names_en[obj["day_number"]]
