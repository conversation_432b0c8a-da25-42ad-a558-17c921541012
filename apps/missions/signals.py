from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils.timezone import now, make_aware
from django.utils.translation import gettext as _
from datetime import datetime, timedelta
import pytz
import logging

from apps.missions.models import (
    WeeklyMission,
    DailyGoal,
    DailyGoalProgress,
    WeeklySuccessRating,
)
from core.tasks.notifications.mission_reminder import send_mission_reminder_notification
from apps.google_calendar.models import GoogleCalendarCredentials
from apps.google_calendar.utils.create_google_calendar_event import (
    create_google_calendar_event,
)
from apps.notifications.utils import create_notification

logger = logging.getLogger(__name__)


@receiver(post_save, sender=WeeklyMission)
def schedule_reminders(sender, instance, created, **kwargs):
    """Schedule multiple reminders based on practice_days."""
    if not created:
        return

    if not instance.practice_days:
        logger.info(
            f"No practice days set for mission {instance.id}, skipping reminder scheduling"
        )
        return

    if not instance.reminder_time:
        logger.info(
            f"No reminder time set for mission {instance.id}, skipping reminder scheduling"
        )
        return

    today = now().date()
    current_time = now()

    if created:
        user = instance.six_week_period.user
        create_notification(
            message=_("New weekly mission created"),
            notification_type="Process",
            target=user,
            description=_("Your weekly mission for {title} has been created.").format(
                title=instance.six_week_period.title
            ),
        )

    try:
        google_creds = GoogleCalendarCredentials.objects.get(
            user=instance.six_week_period.user
        )
        credentials = google_creds.get_credentials()
    except GoogleCalendarCredentials.DoesNotExist:
        credentials = None
        logger.info(
            f"No Google Calendar credentials found for user {instance.six_week_period.user.id}"
        )

    for day_offset in instance.practice_days:
        try:
            reminder_date = today + timedelta(days=(day_offset - today.weekday()) % 7)

            scheduled_datetime = make_aware(
                datetime.combine(reminder_date, instance.reminder_time)
            ).astimezone(pytz.UTC)

            if scheduled_datetime > current_time:
                logger.info(
                    f"Scheduling reminder for mission {instance.id} at {scheduled_datetime} UTC"
                )

                send_mission_reminder_notification.apply_async(
                    eta=scheduled_datetime, args=[instance.id]
                )

                if credentials:
                    event_end_time = scheduled_datetime + timedelta(minutes=30)
                    event = {
                        "summary": _("Mission: {id}").format(id=instance.id),
                        "description": _("Practice mission:"),
                        "start": {
                            "dateTime": scheduled_datetime.isoformat(),
                            "timeZone": "UTC",
                        },
                        "end": {
                            "dateTime": event_end_time.isoformat(),
                            "timeZone": "UTC",
                        },
                        "reminders": {
                            "useDefault": False,
                            "overrides": [
                                {"method": "popup", "minutes": 10},
                            ],
                        },
                    }
                    try:
                        create_google_calendar_event(event, credentials)
                        logger.info(
                            f"Created Google Calendar event for mission {instance.id} at {scheduled_datetime} UTC"
                        )
                    except Exception as e:
                        logger.error(
                            f"Error creating Google Calendar event for mission {instance.id}: {str(e)}"
                        )
            else:
                logger.info(
                    f"Skipping past reminder time {scheduled_datetime} UTC for mission {instance.id}"
                )

        except (TypeError, ValueError) as e:
            logger.error(
                f"Error scheduling reminder for mission {instance.id} on day {day_offset}: {str(e)}"
            )


@receiver(post_save, sender=DailyGoal)
def daily_goal_notification(sender, instance, created, **kwargs):
    """Send notification when a daily goal is created"""
    if created:
        user = instance.weekly_mission.six_week_period.user
        create_notification(
            message=_("New daily goal created"),
            notification_type="Task Assignment",
            target=user,
            description=_("A new daily goal has been created: {title}").format(
                title=instance.title
            ),
        )


@receiver(post_save, sender=DailyGoalProgress)
def goal_progress_notification(sender, instance, created, **kwargs):
    """Send notification when significant progress is made on a goal"""
    if not created and instance.completed:
        user = instance.daily_goal.weekly_mission.six_week_period.user
        create_notification(
            message=_("Goal completed!"),
            notification_type="Completed",
            target=user,
            description=_(
                "Congratulations! You've completed your goal: {title}"
            ).format(title=instance.daily_goal.title),
        )


@receiver(post_save, sender=WeeklySuccessRating)
def success_rating_notification(sender, instance, created, **kwargs):
    """Send notification when a weekly success rating is submitted"""
    if created:
        user = instance.weekly_mission.six_week_period.user
        if instance.rating >= 4:
            create_notification(
                message=_("Great work this week!"),
                notification_type="Feedback Reminder",
                target=user,
                description=_(
                    "You rated your progress for the week highly ({rating}/5). Keep up the good work!"
                ).format(rating=instance.rating),
            )
        elif instance.rating <= 2:
            create_notification(
                message=_("Room for improvement"),
                notification_type="Feedback Reminder",
                target=user,
                description=_(
                    "You rated your progress for the week as {rating}/5. What can we do to help you improve next week?"
                ).format(rating=instance.rating),
            )
