from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.utils.translation import gettext as _
from drf_spectacular.utils import extend_schema, OpenApiExample

from apps.missions.models import WeeklyMission
from apps.missions.serializers.goal import (
    DailyGoalSerializer,
    BulkDailyGoalCreateSerializer,
)
from apps.missions.permissions import MissionRoleBasedPermission


@extend_schema(
    tags=["daily-goals"],
    summary="Bulk create daily goals",
    description="Creates multiple daily goals at once for a weekly mission. Accepts a weekly_mission_id and a list of goals with title and priority.",
    request=BulkDailyGoalCreateSerializer,
    responses={
        201: {"description": "Goals successfully created"},
        400: {"description": "Invalid request data"},
        403: {"description": "Permission denied"},
        404: {"description": "Weekly mission not found"},
    },
    examples=[
        OpenApiExample(
            "Example Bulk Create Request",
            value={
                "weekly_mission": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                "goals": [
                    {"title": "Read 20 pages", "priority": "high"},
                    {"title": "Meditate for 10 minutes", "priority": "low"},
                    {"title": "Exercise for 30 minutes", "priority": "high"},
                ],
            },
            request_only=True,
        ),
    ],
)
class DailyGoalsBulkCreateView(APIView):
    """
    Creates multiple daily goals at once for a weekly mission.

    Expects:
    - weekly_mission_id: UUID of the weekly mission
    - goals: List of objects with title and priority
    """

    permission_classes = [IsAuthenticated, MissionRoleBasedPermission]

    def post(self, request):
        serializer = BulkDailyGoalCreateSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        weekly_mission_id = serializer.validated_data["weekly_mission"]
        goals = serializer.validated_data["goals"]

        try:
            weekly_mission = WeeklyMission.objects.get(id=weekly_mission_id)
        except WeeklyMission.DoesNotExist:
            return Response(
                {"error": _("Weekly mission not found")},
                status=status.HTTP_404_NOT_FOUND,
            )

        if (
            weekly_mission.user != request.user
            and request.user not in weekly_mission.six_week_period.shared_with.all()
        ):
            return Response(
                {"error": _("You don't have permission to add goals to this mission")},
                status=status.HTTP_403_FORBIDDEN,
            )

        if weekly_mission.week_number == 1:
            return Response(
                {"error": _("Daily goals are not allowed for week 1")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        created_goals = []
        errors = []

        for goal_data in goals:
            goal_data = dict(goal_data)
            goal_data["weekly_mission"] = weekly_mission_id

            goal_serializer = DailyGoalSerializer(
                data=goal_data, context={"request": request}
            )

            if goal_serializer.is_valid():
                daily_goal = goal_serializer.save()
                created_goals.append(goal_serializer.data)
            else:
                errors.append({"goal": goal_data, "errors": goal_serializer.errors})

        if not created_goals and errors:
            return Response({"errors": errors}, status=status.HTTP_400_BAD_REQUEST)

        return Response(
            {
                "message": _("{count} daily goals created successfully").format(
                    count=len(created_goals)
                ),
                "created": created_goals,
                "errors": errors,
            },
            status=status.HTTP_201_CREATED,
        )
