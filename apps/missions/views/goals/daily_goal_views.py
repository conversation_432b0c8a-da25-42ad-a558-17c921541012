from rest_framework import generics, status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.utils.translation import gettext_lazy as _
from django.db.models import Q
from apps.missions.models import DailyGoal, DailyGoalProgress, WeeklyMission
from apps.missions.serializers.goal import (
    DailyGoalSerializer,
    DailyGoalProgressSerializer,
    DailyGoalProgressUpdateSerializer,
    DailyGoalCompleteSerializer,
    BulkDailyGoalCreateSerializer,
)
from apps.missions.filters import DailyGoalFilter
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from core.abstract.paginations import MetaPageNumberPagination
from apps.missions.permissions import MissionRoleBasedPermission
from drf_spectacular.utils import (
    extend_schema,
    OpenApiParameter,
    OpenApiResponse,
    OpenApiExample,
)


@extend_schema(
    tags=["daily-goals"],
    summary="List and create daily goals",
    description="Endpoints for listing all daily goals and creating new ones.",
    responses={
        200: DailyGoalSerializer(many=True),
        201: DailyGoalSerializer,
        400: OpenApiResponse(description="Invalid request data."),
        401: OpenApiResponse(
            description="Authentication credentials were not provided."
        ),
        403: OpenApiResponse(
            description="You do not have permission to perform this action."
        ),
    },
    parameters=[
        OpenApiParameter(
            name="title",
            description="Filter by title containing text (case insensitive)",
            required=False,
            type=str,
        ),
        OpenApiParameter(
            name="priority",
            description="Filter by priority (high or low)",
            required=False,
            type=str,
            enum=["high", "low"],
        ),
        OpenApiParameter(
            name="weekly_mission",
            description="Filter by weekly mission ID",
            required=False,
            type=str,
        ),
        OpenApiParameter(
            name="weekly_mission__week_number",
            description="Filter by week number of associated mission",
            required=False,
            type=int,
        ),
        OpenApiParameter(
            name="weekly_mission__domain",
            description="Filter by domain ID of associated mission",
            required=False,
            type=str,
        ),
        OpenApiParameter(
            name="weekly_mission__six_week_period",
            description="Filter by six week period ID of associated mission",
            required=False,
            type=str,
        ),
        OpenApiParameter(
            name="created_at_after",
            description="Filter by creation date after (YYYY-MM-DD)",
            required=False,
            type=str,
        ),
        OpenApiParameter(
            name="created_at_before",
            description="Filter by creation date before (YYYY-MM-DD)",
            required=False,
            type=str,
        ),
        OpenApiParameter(
            name="completed",
            description="Filter by completion status (true for completed, false for incomplete)",
            required=False,
            type=bool,
        ),
        OpenApiParameter(
            name="ordering",
            description="Order results by field (prefix with '-' for descending)",
            required=False,
            type=str,
            enum=[
                "created",
                "-created",
                "updated",
                "-updated",
                "priority",
                "-priority",
            ],
        ),
        OpenApiParameter(
            name="search", description="Search in title field", required=False, type=str
        ),
        OpenApiParameter(
            name="page", description="Page number", required=False, type=int
        ),
        OpenApiParameter(
            name="limit",
            description="Number of results per page",
            required=False,
            type=int,
        ),
    ],
    examples=[
        OpenApiExample(
            "Example Daily Goal",
            value={
                "title": "Meditate for 10 minutes",
                "priority": "high",
                "weekly_mission": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
            },
            request_only=True,
        ),
    ],
)
class DailyGoalListCreateView(generics.ListCreateAPIView):
    """
    GET: List all daily goals
    Returns a list of all daily goals created by the authenticated user or shared with them.

    POST: Create a new daily goal
    Creates a new daily goal. Only authenticated users can create daily goals.
    Daily goals are associated with a specific weekly mission.
    """

    serializer_class = DailyGoalSerializer
    permission_classes = [IsAuthenticated, MissionRoleBasedPermission]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = DailyGoalFilter
    search_fields = ["title"]
    ordering_fields = ["created", "updated", "priority"]
    ordering = ["-created"]
    pagination_class = MetaPageNumberPagination

    def get_queryset(self):
        user = self.request.user

        if user.is_staff or user.role == "admin":
            return DailyGoal.objects.all().select_related("weekly_mission")

        if user.role == "club_manager":
            return (
                DailyGoal.objects.filter(
                    Q(weekly_mission__user=user)
                    | Q(weekly_mission__user__club_members__manager=user)
                )
                .select_related("weekly_mission")
                .distinct()
            )

        return DailyGoal.objects.filter(
            Q(weekly_mission__user=self.request.user)
            | Q(weekly_mission__six_week_period__shared_with=self.request.user)
        ).select_related("weekly_mission")


@extend_schema(
    tags=["daily-goals"],
    summary="Retrieve, update, or delete a daily goal",
    description="Endpoints for retrieving, updating and deleting specific daily goals.",
    responses={
        200: DailyGoalSerializer,
        204: OpenApiResponse(
            description="No content, daily goal successfully deleted."
        ),
        401: OpenApiResponse(
            description="Authentication credentials were not provided."
        ),
        403: OpenApiResponse(
            description="You do not have permission to perform this action."
        ),
        404: OpenApiResponse(description="Daily goal not found."),
    },
)
class DailyGoalDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    GET: Retrieve a specific daily goal
    Returns details of a specific daily goal. Users can access daily goals they created or shared with them.

    PUT/PATCH: Update a daily goal
    Updates a daily goal. Only the owner of the daily goal can perform this action.

    DELETE: Delete a daily goal
    Deletes a daily goal. Only the owner of the daily goal can perform this action.
    """

    serializer_class = DailyGoalSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        if self.request.method == "GET":
            return DailyGoal.objects.filter(
                Q(weekly_mission__user=self.request.user)
                | Q(weekly_mission__six_week_period__shared_with=self.request.user)
            ).select_related("weekly_mission")
        return DailyGoal.objects.filter(
            weekly_mission__user=self.request.user
        ).select_related("weekly_mission")


@extend_schema(
    tags=["daily-goal-actions"],
    summary="Toggle daily goal priority",
    description="Toggles the priority of a daily goal between high and low. Only the owner of the daily goal can perform this action.",
    request=None,
    responses={
        200: DailyGoalSerializer,
        400: OpenApiResponse(description="Could not update priority."),
        401: OpenApiResponse(
            description="Authentication credentials were not provided."
        ),
        403: OpenApiResponse(
            description="You don't have permission to change the priority of this goal."
        ),
        404: OpenApiResponse(description="Daily goal not found."),
    },
    parameters=[
        OpenApiParameter(
            name="pk",
            description="The UUID of the daily goal",
            required=True,
            type=str,
            location=OpenApiParameter.PATH,
        ),
    ],
)
class DailyGoalTogglePriorityView(APIView):
    permission_classes = [IsAuthenticated]

    def get_object(self, pk):
        queryset = DailyGoal.objects.filter(
            Q(weekly_mission__user=self.request.user)
            | Q(weekly_mission__six_week_period__shared_with=self.request.user)
        ).select_related("weekly_mission")

        try:
            return queryset.get(pk=pk)
        except DailyGoal.DoesNotExist:
            return None

    def post(self, request, pk):
        daily_goal = self.get_object(pk)
        if not daily_goal:
            return Response(
                {"error": _("Daily goal not found")},
                status=status.HTTP_404_NOT_FOUND,
            )

        if daily_goal.weekly_mission.user != request.user:
            return Response(
                {
                    "error": _(
                        "You don't have permission to change the priority of this goal"
                    )
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        new_priority = (
            DailyGoal.PRIORITY_LOW
            if daily_goal.priority == DailyGoal.PRIORITY_HIGH
            else DailyGoal.PRIORITY_HIGH
        )
        if daily_goal.set_priority(new_priority):
            serializer = DailyGoalSerializer(daily_goal, context={"request": request})
            return Response(serializer.data)
        return Response(
            {"error": _("Could not update priority")},
            status=status.HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    tags=["daily-goal-actions"],
    summary="Mark daily goal as completed",
    description="Marks a daily goal as completed for a specific day. Only the owner of the daily goal can perform this action.",
    request=DailyGoalCompleteSerializer,
    responses={
        200: DailyGoalSerializer,
        400: OpenApiResponse(
            description="Could not mark as completed. Day number may be invalid."
        ),
        401: OpenApiResponse(
            description="Authentication credentials were not provided."
        ),
        403: OpenApiResponse(
            description="You don't have permission to update this goal."
        ),
        404: OpenApiResponse(description="Daily goal not found."),
    },
    parameters=[
        OpenApiParameter(
            name="pk",
            description="The UUID of the daily goal",
            required=True,
            type=str,
            location=OpenApiParameter.PATH,
        ),
        OpenApiParameter(
            name="day_number",
            description="Day number (1-7)",
            required=True,
            type=int,
            location=OpenApiParameter.PATH,
        ),
    ],
)
class DailyGoalCompleteView(APIView):
    permission_classes = [IsAuthenticated]

    def get_object(self, pk):
        queryset = DailyGoal.objects.filter(
            Q(weekly_mission__user=self.request.user)
            | Q(weekly_mission__six_week_period__shared_with=self.request.user)
        ).select_related("weekly_mission")

        try:
            return queryset.get(pk=pk)
        except DailyGoal.DoesNotExist:
            return None

    def post(self, request, pk, day_number):
        daily_goal = self.get_object(pk)
        if not daily_goal:
            return Response(
                {"error": _("Daily goal not found")},
                status=status.HTTP_404_NOT_FOUND,
            )

        if (
            daily_goal.weekly_mission.user != request.user
            and request.user
            not in daily_goal.weekly_mission.six_week_period.shared_with.all()
        ):
            return Response(
                {"error": _("You don't have permission to update this goal")},
                status=status.HTTP_403_FORBIDDEN,
            )

        if day_number is None:
            return Response(
                {"error": _("Day number is required")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        day_number = int(day_number)
        progress = daily_goal.get_progress_for_day(day_number)

        if not progress:
            return Response(
                {"error": _("No progress entry found for this day")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if progress.mark_completed():
            serializer = DailyGoalSerializer(daily_goal, context={"request": request})
            return Response(serializer.data)
        return Response(
            {"error": _("Could not mark as completed")},
            status=status.HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    tags=["daily-goal-actions"],
    summary="Mark daily goal as incomplete",
    description="Marks a daily goal as incomplete for a specific day. Only the owner of the daily goal can perform this action.",
    request=DailyGoalCompleteSerializer,
    responses={
        200: DailyGoalSerializer,
        400: OpenApiResponse(
            description="Could not mark as incomplete. Day number may be invalid."
        ),
        401: OpenApiResponse(
            description="Authentication credentials were not provided."
        ),
        403: OpenApiResponse(
            description="You don't have permission to update this goal."
        ),
        404: OpenApiResponse(description="Daily goal not found."),
    },
    parameters=[
        OpenApiParameter(
            name="pk",
            description="The UUID of the daily goal",
            required=True,
            type=str,
            location=OpenApiParameter.PATH,
        ),
        OpenApiParameter(
            name="day_number",
            description="Day number (1-7)",
            required=True,
            type=int,
            location=OpenApiParameter.PATH,
        ),
    ],
)
class DailyGoalIncompleteView(APIView):
    permission_classes = [IsAuthenticated]

    def get_object(self, pk):
        queryset = DailyGoal.objects.filter(
            Q(weekly_mission__user=self.request.user)
            | Q(weekly_mission__six_week_period__shared_with=self.request.user)
        ).select_related("weekly_mission")

        try:
            return queryset.get(pk=pk)
        except DailyGoal.DoesNotExist:
            return None

    def post(self, request, pk, day_number):
        daily_goal = self.get_object(pk)
        if not daily_goal:
            return Response(
                {"error": _("Daily goal not found")},
                status=status.HTTP_404_NOT_FOUND,
            )

        if (
            daily_goal.weekly_mission.user != request.user
            and request.user
            not in daily_goal.weekly_mission.six_week_period.shared_with.all()
        ):
            return Response(
                {"error": _("You don't have permission to update this goal")},
                status=status.HTTP_403_FORBIDDEN,
            )

        if day_number is None:
            return Response(
                {"error": _("Day number is required")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        day_number = int(day_number)
        progress = daily_goal.get_progress_for_day(day_number)

        if not progress:
            return Response(
                {"error": _("No progress entry found for this day")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if progress.mark_incomplete():
            serializer = DailyGoalSerializer(daily_goal, context={"request": request})
            return Response(serializer.data)
        return Response(
            {"error": _("Could not mark as incomplete")},
            status=status.HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    tags=["daily-goal-actions"],
    summary="Update daily goal progress for a specific day",
    description="Updates the completion status of a daily goal for a specific day of the week.",
    request=DailyGoalProgressUpdateSerializer,
    responses={
        200: DailyGoalProgressSerializer,
        400: OpenApiResponse(description="Invalid request data."),
        401: OpenApiResponse(
            description="Authentication credentials were not provided."
        ),
        403: OpenApiResponse(
            description="You don't have permission to update this goal."
        ),
        404: OpenApiResponse(description="Daily goal not found."),
    },
    parameters=[
        OpenApiParameter(
            name="pk",
            description="The UUID of the daily goal",
            required=True,
            type=str,
            location=OpenApiParameter.PATH,
        ),
    ],
    examples=[
        OpenApiExample(
            "Update Progress Example",
            value={"day_number": 3, "completed": True},
            request_only=True,
        ),
    ],
)
class DailyGoalUpdateProgressView(APIView):
    permission_classes = [IsAuthenticated]

    def get_object(self, pk):
        queryset = DailyGoal.objects.filter(
            Q(weekly_mission__user=self.request.user)
            | Q(weekly_mission__six_week_period__shared_with=self.request.user)
        ).select_related("weekly_mission")

        try:
            return queryset.get(pk=pk)
        except DailyGoal.DoesNotExist:
            return None

    def post(self, request, pk):
        daily_goal = self.get_object(pk)
        if not daily_goal:
            return Response(
                {"error": _("Daily goal not found")},
                status=status.HTTP_404_NOT_FOUND,
            )

        if (
            daily_goal.weekly_mission.user != request.user
            and request.user
            not in daily_goal.weekly_mission.six_week_period.shared_with.all()
        ):
            return Response(
                {"error": _("You don't have permission to update this goal")},
                status=status.HTTP_403_FORBIDDEN,
            )

        serializer = DailyGoalProgressUpdateSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        day_number = serializer.validated_data["day_number"]
        completed = serializer.validated_data["completed"]

        progress, created = DailyGoalProgress.objects.get_or_create(
            daily_goal=daily_goal,
            day_number=day_number,
            defaults={"completed": completed},
        )

        if not created:
            if completed:
                progress.mark_completed()
            else:
                progress.mark_incomplete()

        progress_serializer = DailyGoalProgressSerializer(progress)
        return Response(progress_serializer.data)


@extend_schema(
    tags=["daily-goal-actions"],
    summary="Get daily goal progress for a specific day",
    description="Retrieves the progress status of a daily goal for a specific day of the week.",
    responses={
        200: DailyGoalProgressSerializer,
        400: OpenApiResponse(description="Day number must be between 1 and 7."),
        401: OpenApiResponse(
            description="Authentication credentials were not provided."
        ),
        404: OpenApiResponse(description="Daily goal or progress for day not found."),
    },
    parameters=[
        OpenApiParameter(
            name="pk",
            description="The UUID of the daily goal",
            required=True,
            type=str,
            location=OpenApiParameter.PATH,
        ),
        OpenApiParameter(
            name="day_number",
            description="Day number (1-7)",
            required=True,
            type=int,
            location=OpenApiParameter.QUERY,
        ),
    ],
)
class DailyGoalProgressByDayView(APIView):
    permission_classes = [IsAuthenticated]

    def get_object(self, pk):
        queryset = DailyGoal.objects.filter(
            Q(weekly_mission__user=self.request.user)
            | Q(weekly_mission__six_week_period__shared_with=self.request.user)
        ).select_related("weekly_mission")

        try:
            return queryset.get(pk=pk)
        except DailyGoal.DoesNotExist:
            return None

    def get(self, request, pk):
        daily_goal = self.get_object(pk)
        if not daily_goal:
            return Response(
                {"error": _("Daily goal not found")},
                status=status.HTTP_404_NOT_FOUND,
            )

        day_number = request.query_params.get("day_number")

        if not day_number or not day_number.isdigit() or not 1 <= int(day_number) <= 7:
            return Response(
                {"error": _("Day number must be between 1 and 7")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        progress = daily_goal.get_progress_for_day(int(day_number))

        if not progress:
            return Response(
                {"error": _("No progress found for the specified day")},
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = DailyGoalProgressSerializer(progress)
        return Response(serializer.data)


@extend_schema(
    tags=["daily-goal-actions"],
    summary="Bulk create daily goals",
    description="Creates multiple daily goals at once for a weekly mission. Only authenticated users can create daily goals.",
    request=BulkDailyGoalCreateSerializer,
    responses={
        201: OpenApiResponse(description="Goals successfully created"),
        207: OpenApiResponse(
            description="Partial success - some goals created, some failed"
        ),
        400: OpenApiResponse(
            description="Invalid request data. May include: Invalid mission ID, empty goals list, daily goals not allowed for week 1."
        ),
        401: OpenApiResponse(
            description="Authentication credentials were not provided."
        ),
        403: OpenApiResponse(
            description="You don't have permission to add goals to this mission."
        ),
        404: OpenApiResponse(description="Weekly mission not found."),
    },
    examples=[
        OpenApiExample(
            "Bulk Create Example",
            value={
                "weekly_mission": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                "goals": [
                    {"title": "Read 20 pages", "priority": "high"},
                    {"title": "Meditate for 10 minutes", "priority": "low"},
                    {"title": "Exercise for 30 minutes", "priority": "high"},
                ],
            },
            request_only=True,
        ),
    ],
)
class DailyGoalBulkCreateView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        bulk_serializer = BulkDailyGoalCreateSerializer(data=request.data)
        if not bulk_serializer.is_valid():
            return Response(bulk_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        weekly_mission_id = bulk_serializer.validated_data["weekly_mission"]
        goals = bulk_serializer.validated_data["goals"]

        try:
            weekly_mission = WeeklyMission.objects.get(id=weekly_mission_id)
        except WeeklyMission.DoesNotExist:
            return Response(
                {"error": _("Weekly mission not found")},
                status=status.HTTP_404_NOT_FOUND,
            )

        if (
            weekly_mission.user != request.user
            and request.user not in weekly_mission.six_week_period.shared_with.all()
        ):
            return Response(
                {"error": _("You don't have permission to add goals to this mission")},
                status=status.HTTP_403_FORBIDDEN,
            )

        if weekly_mission.week_number == 1:
            return Response(
                {"error": _("Daily goals are not allowed for week 1")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        created_goals = []
        errors = []

        for goal_data in goals:
            goal_data = dict(goal_data)
            goal_data["weekly_mission"] = weekly_mission_id
            serializer = DailyGoalSerializer(
                data=goal_data, context={"request": request}
            )

            if serializer.is_valid():
                daily_goal = serializer.save()
                created_goals.append(serializer.data)
            else:
                errors.append({"data": goal_data, "errors": serializer.errors})

        response_data = {"created": created_goals, "errors": errors}

        if errors and not created_goals:
            return Response(response_data, status=status.HTTP_400_BAD_REQUEST)
        elif errors:
            return Response(response_data, status=status.HTTP_207_MULTI_STATUS)

        return Response(response_data, status=status.HTTP_201_CREATED)
