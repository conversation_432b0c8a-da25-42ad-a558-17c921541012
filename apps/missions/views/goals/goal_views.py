from rest_framework import generics, status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.utils.translation import gettext_lazy as _
from django.db.models import Q
from apps.missions.models import Goal
from apps.missions.serializers.goal import GoalSerializer
from apps.missions.filters import WeeklyGoalFilter
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from core.abstract.paginations import MetaPageNumberPagination
from apps.missions.permissions import MissionRoleBasedPermission
from drf_spectacular.utils import (
    extend_schema,
    OpenApiParameter,
    OpenApiResponse,
    OpenApiExample,
)


@extend_schema(
    tags=["goals"],
    summary="List and create goals",
    description="Endpoints for listing all goals and creating new ones.",
    responses={
        200: GoalSerializer(many=True),
        201: GoalSerializer,
        401: OpenApiResponse(
            description="Authentication credentials were not provided."
        ),
        403: OpenApiResponse(
            description="You do not have permission to perform this action."
        ),
    },
    parameters=[
        OpenApiParameter(
            name="title",
            description="Filter by title containing text (case insensitive)",
            required=False,
            type=str,
        ),
        OpenApiParameter(
            name="priority",
            description="Filter by priority (high or low)",
            required=False,
            type=str,
            enum=["high", "low"],
        ),
        OpenApiParameter(
            name="six_week_period",
            description="Filter by six week period ID",
            required=False,
            type=str,
        ),
        OpenApiParameter(
            name="user", description="Filter by user ID", required=False, type=str
        ),
        OpenApiParameter(
            name="created_at_after",
            description="Filter by creation date after (YYYY-MM-DD)",
            required=False,
            type=str,
        ),
        OpenApiParameter(
            name="created_at_before",
            description="Filter by creation date before (YYYY-MM-DD)",
            required=False,
            type=str,
        ),
        OpenApiParameter(
            name="ordering",
            description="Order results by field (prefix with '-' for descending)",
            required=False,
            type=str,
            enum=[
                "created",
                "-created",
                "updated",
                "-updated",
                "priority",
                "-priority",
            ],
        ),
        OpenApiParameter(
            name="search", description="Search in title field", required=False, type=str
        ),
        OpenApiParameter(
            name="page", description="Page number", required=False, type=int
        ),
        OpenApiParameter(
            name="limit",
            description="Number of results per page",
            required=False,
            type=int,
        ),
    ],
)
class GoalListCreateView(generics.ListCreateAPIView):
    """
    GET: List all goals
    Returns a list of all goals created by the authenticated user or shared with them.

    POST: Create a new goal
    Creates a new goal. Only authenticated users can create goals. Goals are associated with a specific 6-week period.
    """

    serializer_class = GoalSerializer
    permission_classes = [IsAuthenticated, MissionRoleBasedPermission]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = WeeklyGoalFilter
    search_fields = ["title"]
    ordering_fields = ["created", "updated", "priority"]
    ordering = ["-created"]
    pagination_class = MetaPageNumberPagination

    def get_queryset(self):
        user = self.request.user

        if user.is_staff or user.role == "admin":
            return Goal.objects.all()

        if user.role == "club_manager":
            return Goal.objects.filter(
                Q(user=user) | Q(user__club_members__manager=user)
            ).distinct()

        return Goal.objects.filter(
            Q(user=user) | Q(six_week_period__shared_with=user)
        ).distinct()


@extend_schema(
    tags=["goals"],
    summary="Retrieve, update, or delete a goal",
    description="Endpoints for retrieving, updating and deleting specific goals.",
    responses={
        200: GoalSerializer,
        204: OpenApiResponse(description="No content, goal successfully deleted."),
        401: OpenApiResponse(
            description="Authentication credentials were not provided."
        ),
        403: OpenApiResponse(
            description="You do not have permission to perform this action."
        ),
        404: OpenApiResponse(description="Goal not found."),
    },
    examples=[
        OpenApiExample(
            "Example Goal",
            value={
                "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                "user": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                "six_week_period": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                "title": "Read 20 pages daily",
                "priority": "high",
                "created": "2023-01-01T12:00:00Z",
                "updated": "2023-01-01T12:00:00Z",
            },
            request_only=False,
            response_only=True,
        ),
    ],
)
class GoalDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    GET: Retrieve a specific goal
    Returns details of a specific goal. Users can access goals they created or that were shared with them.

    PUT/PATCH: Update a goal
    Updates a goal. Only the owner of the goal can perform this action.

    DELETE: Delete a goal
    Deletes a goal. Only the owner of the goal can perform this action.
    """

    serializer_class = GoalSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):

        if self.request.method == "GET":
            return Goal.objects.filter(
                Q(user=self.request.user)
                | Q(six_week_period__shared_with=self.request.user)
            ).distinct()

        return Goal.objects.filter(user=self.request.user)


@extend_schema(
    tags=["goal-actions"],
    summary="Toggle goal priority",
    description="Toggles the priority of a goal between high and low. Only the owner of the goal can perform this action.",
    request=None,
    responses={
        200: GoalSerializer,
        400: OpenApiResponse(description="Could not update priority."),
        401: OpenApiResponse(
            description="Authentication credentials were not provided."
        ),
        403: OpenApiResponse(
            description="You don't have permission to change the priority of this goal."
        ),
        404: OpenApiResponse(description="Goal not found."),
    },
    parameters=[
        OpenApiParameter(
            name="pk",
            description="The UUID of the goal",
            required=True,
            type=str,
            location=OpenApiParameter.PATH,
        ),
    ],
)
class GoalTogglePriorityView(APIView):
    permission_classes = [IsAuthenticated]

    def get_object(self, pk):

        queryset = Goal.objects.filter(
            Q(user=self.request.user)
            | Q(six_week_period__shared_with=self.request.user)
        ).distinct()

        try:
            return queryset.get(pk=pk)
        except Goal.DoesNotExist:
            return None

    def post(self, request, pk):
        goal = self.get_object(pk)
        if not goal:
            return Response(
                {"error": _("Goal not found")},
                status=status.HTTP_404_NOT_FOUND,
            )

        if goal.user != request.user:
            return Response(
                {
                    "error": _(
                        "You don't have permission to change the priority of this goal"
                    )
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        new_priority = (
            Goal.PRIORITY_LOW
            if goal.priority == Goal.PRIORITY_HIGH
            else Goal.PRIORITY_HIGH
        )
        if goal.set_priority(new_priority):
            serializer = GoalSerializer(goal, context={"request": request})
            return Response(serializer.data)
        return Response(
            {"error": _("Could not update priority")},
            status=status.HTTP_400_BAD_REQUEST,
        )
