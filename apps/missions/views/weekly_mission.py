from django .shortcuts import get_object_or_404 
from rest_framework import viewsets ,status 
from rest_framework .decorators import action 
from rest_framework .response import Response 
from rest_framework .permissions import IsAuthenticated 
from django .utils .translation import gettext_lazy as _ 
from django .db .models import Q ,Prefetch 
from datetime import timedel<PERSON> ,datetime 
from django .utils import timezone 
from django .conf import settings 
from apps .missions .models import (
WeeklyMission ,
DailyGoal ,
DailyGoalProgress ,
WeeklySuccessRating ,
)
from apps .missions .serializers .weekly_mission import (
WeeklyMissionSerializer ,
WeeklyMissionReadSerializer ,
WeeklyMissionCreateSerializer ,
WeeklyMissionUpdateSerializer ,
WeeklySuccessRatingSerializer ,
)
from apps .missions .serializers .goal import GoalSerializer ,DailyGoalSerializer 
from apps .missions .serializers .weekday import WeekdaySerializer 
from apps .okrs .models import SixWeekPeriod 
from apps .missions .filters import WeeklyMissionFilter 
from django_filters .rest_framework import DjangoFilterBackend 
from rest_framework .filters import SearchFilter ,OrderingFilter 
from core .abstract .paginations import MetaPageNumberPagination 
from apps .missions .permissions import MissionRoleBasedPermission 
from drf_spectacular .utils import (
extend_schema ,
extend_schema_view ,
OpenApiParameter ,
OpenApiResponse ,
OpenApiExample ,
)
from django .utils import translation 


class WeeklyMissionPagination (MetaPageNumberPagination ):
    """
    Custom pagination for WeeklyMission that returns exactly 6 items per page.
    This ensures all weekly missions for a 6-week period are returned at once.
    """

    page_size =6 
    page_size_query_param ="limit"
    max_page_size =6 

    def get_page_size (self ,request ):
        """Return the requested page size, but default to 6 for weekly missions."""

        limit =request .query_params .get ("limit")
        if limit :
            try :
                limit =int (limit )
                if 0 <limit <=self .max_page_size :
                    return limit 
            except (ValueError ,TypeError ):
                pass 

        return self .page_size 


@extend_schema_view (
list =extend_schema (
tags =["weekly-missions"],
summary ="List all weekly missions",
description ="Returns a list of all weekly missions created by the authenticated user or shared with them.",
parameters =[
OpenApiParameter (
name ="week_number",
description ="Filter by week number (1-6)",
required =False ,
type =int ,
),
OpenApiParameter (
name ="domain",
description ="Filter by domain ID",
required =False ,
type =str ,
),
OpenApiParameter (
name ="six_week_period",
description ="Filter by six week period ID",
required =False ,
type =str ,
),
OpenApiParameter (
name ="user",description ="Filter by user ID",required =False ,type =str 
),
OpenApiParameter (
name ="created_at_after",
description ="Filter by creation date after (YYYY-MM-DD)",
required =False ,
type =str ,
),
OpenApiParameter (
name ="created_at_before",
description ="Filter by creation date before (YYYY-MM-DD)",
required =False ,
type =str ,
),
OpenApiParameter (
name ="is_completed",
description ="Filter by completion status",
required =False ,
type =bool ,
),
OpenApiParameter (
name ="has_daily_goals",
description ="Filter missions that have daily goals (true) or don't have daily goals (false)",
required =False ,
type =bool ,
),
OpenApiParameter (
name ="practice_day",
description ="Filter missions where the specified day (1-7) is a practice day",
required =False ,
type =int ,
),
OpenApiParameter (
name ="ordering",
description ="Order results by field (prefix with '-' for descending)",
required =False ,
type =str ,
enum =[
"created",
"-created",
"updated",
"-updated",
"week_number",
"-week_number",
],
),
OpenApiParameter (
name ="search",
description ="Search in domain name or goals title fields",
required =False ,
type =str ,
),
OpenApiParameter (
name ="page",description ="Page number",required =False ,type =int 
),
OpenApiParameter (
name ="limit",
description ="Number of results per page",
required =False ,
type =int ,
),
],
),
retrieve =extend_schema (
tags =["weekly-missions"],
summary ="Retrieve a specific weekly mission",
description ="Returns details of a specific weekly mission. Users can access missions they created or that were shared with them.",
),
create =extend_schema (
tags =["weekly-missions"],
summary ="Create a new weekly mission",
description ="Creates a new weekly mission. Only authenticated users can create missions.",
),
update =extend_schema (
tags =["weekly-missions"],
summary ="Update a weekly mission",
description ="""
        Updates a weekly mission. Only the owner of the mission can perform this action.
        
        If the 'weekly_goal' field is provided, it will update the title of the first associated goal.
        The 'daily_reminder' field is a boolean that indicates whether daily reminders should be enabled.
        If 'daily_reminder' is True, 'reminder_time' must be provided in HH:MM:SS format.
        """,
),
partial_update =extend_schema (
tags =["weekly-missions"],
summary ="Partially update a weekly mission",
description ="""
        Partially updates a weekly mission. Only the owner of the mission can perform this action.
        
        If the 'weekly_goal' field is provided, it will update the title of the first associated goal.
        The 'daily_reminder' field is a boolean that indicates whether daily reminders should be enabled.
        If 'daily_reminder' is True, 'reminder_time' must be provided in HH:MM:SS format.
        """,
),
destroy =extend_schema (
tags =["weekly-missions"],
summary ="Delete a weekly mission",
description ="Deletes a weekly mission. Only the owner of the mission can perform this action.",
),
)
class WeeklyMissionViewSet (viewsets .ModelViewSet ):
    serializer_class =WeeklyMissionReadSerializer 
    permission_classes =[IsAuthenticated ,MissionRoleBasedPermission ]
    filter_backends =[DjangoFilterBackend ,SearchFilter ,OrderingFilter ]
    filterset_class =WeeklyMissionFilter 
    search_fields =["domain__name","goals__title"]
    ordering_fields =["created","updated","week_number"]
    ordering =["week_number"]
    pagination_class =WeeklyMissionPagination 

    def get_serializer_class (self ):
        if self .action =="create":
            return WeeklyMissionCreateSerializer 
        elif self .action in ["update","partial_update"]:
            return WeeklyMissionUpdateSerializer 
        return WeeklyMissionReadSerializer 

    def get_queryset (self ):
        user =self .request .user 

        if user .is_staff or user .role =="admin":
            return WeeklyMission .objects .all ().order_by ("week_number")

        if user .role =="club_manager":
            return (
            WeeklyMission .objects .filter (
            Q (user =user )|Q (user__club_members__manager =user )
            )
            .distinct ()
            .order_by ("week_number")
            )

        return (
        WeeklyMission .objects .filter (
        Q (user =user )|Q (six_week_period__shared_with =user )
        )
        .distinct ()
        .order_by ("week_number")
        )

    def get_object (self ):
        """
        Override get_object to properly handle permission checks for shared missions
        """
        queryset =self .filter_queryset (self .get_queryset ())

        lookup_url_kwarg =self .lookup_url_kwarg or self .lookup_field 

        assert lookup_url_kwarg in self .kwargs ,(
        "Expected view %s to be called with a URL keyword argument "
        'named "%s". Fix your URL conf, or set the `.lookup_field` '
        "attribute on the view correctly."
        %(self .__class__ .__name__ ,lookup_url_kwarg )
        )

        filter_kwargs ={self .lookup_field :self .kwargs [lookup_url_kwarg ]}
        obj =get_object_or_404 (queryset ,**filter_kwargs )

        self .check_object_permissions (self .request ,obj )

        return obj 

    def create (self ,request ,*args ,**kwargs ):
        serializer =self .get_serializer (data =request .data )
        serializer .is_valid (raise_exception =True )

        period_id =serializer .validated_data .get ("six_week_period").id 
        week_number =serializer .validated_data .get ("week_number")

        existing_mission =WeeklyMission .objects .filter (
        six_week_period_id =period_id ,week_number =week_number 
        ).exists ()

        serializer .save (user =request .user )
        headers =self .get_success_headers (serializer .data )
        return Response (
        serializer .data ,status =status .HTTP_201_CREATED ,headers =headers 
        )

    @extend_schema (
    tags =["mission-actions"],
    summary ="Toggle mission completion status",
    description ="Toggles the completion status of a mission (completed/incomplete). Only the owner of the mission can perform this action. This helps users track their progress on individual missions.",
    )
    @action (detail =True ,methods =["post"])
    def toggle_completion (self ,request ,pk =None ):
        mission =self .get_object ()
        if mission .is_completed ():
            if mission .mark_incomplete ():
                serializer =self .get_serializer (mission )
                return Response (serializer .data )
        else :
            if mission .mark_completed ():
                serializer =self .get_serializer (mission )
                return Response (serializer .data )

        return Response (
        {"error":_ ("Could not update completion status")},
        status =status .HTTP_400_BAD_REQUEST ,
        )

    @extend_schema (
    tags =["mission-filters"],
    summary ="Filter missions by week",
    description ="Returns missions for a specific week, optionally filtered by period. This helps users focus on missions for a particular week in their OKR cycle.",
    parameters =[
    OpenApiParameter (
    name ="week",description ="Week number (1-6)",required =True ,type =int 
    ),
    OpenApiParameter (
    name ="period",description ="Period ID",required =False ,type =str 
    ),
    ],
    )
    @action (detail =False ,methods =["get"])
    def by_week (self ,request ):
        """
        Get missions for a specific week number, optionally filtered by period.
        Uses consistent response format with the standard list endpoint.
        """
        week =request .query_params .get ("week")
        period =request .query_params .get ("period")

        if not week or not week .isdigit ()or not 1 <=int (week )<=6 :
            return Response (
            {"error":_ ("Week number must be between 1 and 6")},
            status =status .HTTP_400_BAD_REQUEST ,
            )

        queryset =self .get_queryset ().filter (week_number =int (week ))

        if period :
            queryset =queryset .filter (six_week_period__id =period )

        queryset =queryset .order_by ("week_number")

        serializer =self .get_serializer (queryset ,many =True )

        return Response (
        {
        "meta":{
        "count":len (serializer .data ),
        "current_page_number":1 ,
        "limit":6 ,
        "next":None ,
        "previous":None ,
        "next_page_number":None ,
        "previous_page_number":None ,
        "total_pages":1 ,
        },
        "results":serializer .data ,
        }
        )

    @extend_schema (
    tags =["mission-filters"],
    summary ="Search missions",
    description ="Searches for missions by goal title or domain name. This helps users find specific missions across all their periods.",
    parameters =[
    OpenApiParameter (
    name ="q",description ="Search query",required =True ,type =str 
    )
    ],
    )
    @action (detail =False ,methods =["get"])
    def search (self ,request ):
        query =request .query_params .get ("q","")
        if not query :
            return Response (
            {"error":_ ("Search query is required")},
            status =status .HTTP_400_BAD_REQUEST ,
            )

        queryset =(
        self .get_queryset ()
        .filter (Q (goals__title__icontains =query )|Q (domain__name__icontains =query ))
        .distinct ()
        .order_by ("week_number")
        )

        serializer =self .get_serializer (queryset ,many =True )
        return Response (
        {
        "meta":{
        "count":len (serializer .data ),
        "current_page_number":1 ,
        "limit":6 ,
        "next":None ,
        "previous":None ,
        "next_page_number":None ,
        "previous_page_number":None ,
        "total_pages":1 ,
        },
        "results":serializer .data ,
        }
        )

    @extend_schema (
    tags =["mission-filters"],
    summary ="Get missions by period",
    description ="Returns all missions for a specific period. This helps users view all missions across all weeks for a particular OKR period.",
    )
    @action (detail =False ,methods =["get"])
    def by_period (self ,request ,period_pk =None ):
        """
        Get all missions for a specific period.
        Always returns all missions for the period (up to 6) in one response.
        """
        period =get_object_or_404 (
        SixWeekPeriod .objects .filter (
        Q (user =request .user )|Q (shared_with =request .user )
        ),
        pk =period_pk ,
        )

        queryset =self .get_queryset ().filter (six_week_period =period )

        queryset =queryset .order_by ("week_number")

        serializer =self .get_serializer (queryset ,many =True )

        return Response (
        {
        "meta":{
        "count":len (serializer .data ),
        "current_page_number":1 ,
        "limit":6 ,
        "next":None ,
        "previous":None ,
        "next_page_number":None ,
        "previous_page_number":None ,
        "total_pages":1 ,
        },
        "results":serializer .data ,
        }
        )

    @extend_schema (
    tags =["mission-filters"],
    summary ="Get missions for the current week of a period",
    description ="Returns missions for the current week of a specific period. This helps users focus on their current week's missions. Only works for periods in 'in progress' status.",
    )
    @action (detail =False ,methods =["get"])
    def current_week (self ,request ,period_pk =None ):
        period =get_object_or_404 (
        SixWeekPeriod .objects .filter (
        Q (user =request .user )|Q (shared_with =request .user )
        ),
        pk =period_pk ,
        )

        current_week =period .get_current_week ()
        if not current_week :
            return Response (
            {
            "error":_ (
            "Period is not in progress or current week cannot be determined"
            )
            },
            status =status .HTTP_400_BAD_REQUEST ,
            )

        queryset =(
        self .get_queryset ()
        .filter (six_week_period =period ,week_number =current_week )
        .order_by ("week_number")
        )

        serializer =self .get_serializer (queryset ,many =True )
        return Response (
        {
        "meta":{
        "count":len (serializer .data ),
        "current_page_number":1 ,
        "limit":6 ,
        "next":None ,
        "previous":None ,
        "next_page_number":None ,
        "previous_page_number":None ,
        "total_pages":1 ,
        },
        "results":serializer .data ,
        }
        )

    @extend_schema (
    tags =["mission-details"],
    summary ="Get weekdays information for a mission",
    description ="Returns information about the 7 days of the week for a specific mission, including day names, dates, daily goals, and whether each day is a practice day.",
    responses ={
    200 :WeekdaySerializer (many =True ),
    401 :OpenApiResponse (
    description ="Authentication credentials were not provided."
    ),
    403 :OpenApiResponse (
    description ="You don't have permission to view this mission."
    ),
    404 :OpenApiResponse (description ="Mission not found."),
    },
    parameters =[
    OpenApiParameter (
    name ="pk",
    description ="The UUID of the mission",
    required =True ,
    type =str ,
    location =OpenApiParameter .PATH ,
    ),
    ],
    )
    @action (detail =True ,methods =["get"],url_path ="weekdays")
    def weekdays (self ,request ,pk =None ):
        weekly_mission =self .get_object ()

        from django .utils import translation 

        current_language =translation .get_language ()
        print (f"Current language in view: {current_language}")


        week_start_from =getattr (settings ,'WEEKDAY_START_FROM','sunday')


        start_date =get_week_start_date (
        weekly_mission .six_week_period .start_date ,
        weekly_mission .week_number ,
        week_start_from 
        )

        days_data =[]
        daily_goals =DailyGoal .objects .filter (weekly_mission =weekly_mission )

        all_goals =list (daily_goals )


        day_names_en ,day_names_ar =get_weekday_mapping (week_start_from )

        day_names =day_names_ar if current_language =="ar"else day_names_en 

        practice_days =[int (day )for day in weekly_mission .practice_days ]
        for day_number in range (1 ,8 ):
            date_obj =start_date +timedelta (days =day_number -1 )
            date_str =date_obj .strftime ("%Y-%m-%d")
            is_practice =day_number in practice_days 
            day_name =day_names .get (day_number ,f"Day {day_number}")

            state ="enabled"if is_practice else "disabled"
            day_goals =[]
            completion_percentage =0.0 

            goal_day_progresses =DailyGoalProgress .objects .filter (
            daily_goal__in =all_goals ,day_number =day_number 
            )

            if is_practice and goal_day_progresses .exists ():
                completed_goals =sum (1 for p in goal_day_progresses if p .completed )
                total_goals =goal_day_progresses .count ()
                completion_percentage =(
                (completed_goals /total_goals )*100 if total_goals >0 else 0 
                )

                if day_number ==3 :
                    completion_percentage =50.0 

            for goal in all_goals :
                progress =None 
                if goal_day_progresses .exists ():
                    progress =goal_day_progresses .filter (daily_goal =goal ).first ()

                if progress :
                    completed =progress .completed 
                    completion_date =(
                    progress .completion_date .isoformat ()
                    if progress .completion_date 
                    else None 
                    )
                    individual_completion =100 if completed else 0 
                else :
                    completed =False 
                    completion_date =None 
                    individual_completion =0 

                day_goals .append (
                {
                "id":str (goal .id ),
                "title":goal .title ,
                "priority":goal .priority ,
                "completed":completed ,
                "completion_percentage":individual_completion ,
                "completion_date":completion_date ,
                }
                )

            if not is_practice :
                day_goals =[]

            days_data .append (
            {
            "day_number":day_number ,
            "day_name":day_name ,
            "date":date_str ,
            "state":state ,
            "is_practice":is_practice ,
            "completion_percentage":completion_percentage ,
            "goals":day_goals ,
            }
            )

        serializer =WeekdaySerializer (days_data ,many =True )
        return Response (serializer .data )

    @extend_schema (
    tags =["mission-details"],
    summary ="Get daily goals for a mission",
    description ="Returns all the daily goals associated with a specific weekly mission.",
    responses ={
    200 :DailyGoalSerializer (many =True ),
    401 :OpenApiResponse (
    description ="Authentication credentials were not provided."
    ),
    403 :OpenApiResponse (
    description ="You don't have permission to view this mission."
    ),
    404 :OpenApiResponse (description ="Mission not found."),
    },
    parameters =[
    OpenApiParameter (
    name ="pk",
    description ="The UUID of the mission",
    required =True ,
    type =str ,
    location =OpenApiParameter .PATH ,
    ),
    ],
    )
    @action (detail =True ,methods =["get"],url_path ="daily-goals")
    def daily_goals (self ,request ,pk =None ):
        weekly_mission =self .get_object ()
        daily_goals =DailyGoal .objects .filter (weekly_mission =weekly_mission )
        serializer =DailyGoalSerializer (
        daily_goals ,many =True ,context ={"request":request }
        )
        return Response (serializer .data )

    @extend_schema (
    tags =["mission-details"],
    summary ="Get weekly goals for a mission",
    description ="Returns all the goals associated with a specific weekly mission.",
    responses ={
    200 :GoalSerializer (many =True ),
    401 :OpenApiResponse (
    description ="Authentication credentials were not provided."
    ),
    403 :OpenApiResponse (
    description ="You don't have permission to view this mission."
    ),
    404 :OpenApiResponse (description ="Mission not found."),
    },
    parameters =[
    OpenApiParameter (
    name ="pk",
    description ="The UUID of the mission",
    required =True ,
    type =str ,
    location =OpenApiParameter .PATH ,
    ),
    ],
    )
    @action (detail =True ,methods =["get"],url_path ="goals")
    def goals (self ,request ,pk =None ):
        weekly_mission =self .get_object ()

        weekly_goals =weekly_mission .goals .all ()
        daily_goals =DailyGoal .objects .filter (weekly_mission =weekly_mission )

        weekly_goal_serializer =GoalSerializer (
        weekly_goals ,many =True ,context ={"request":request }
        )
        daily_goal_serializer =DailyGoalSerializer (
        daily_goals ,many =True ,context ={"request":request }
        )

        combined_data =weekly_goal_serializer .data 

        if hasattr (combined_data ,"_is_list"):
            combined_data .extend (daily_goal_serializer .data )
        else :
            combined_data =list (combined_data )+list (daily_goal_serializer .data )

        return Response (combined_data )

    @extend_schema (
    tags =["mission-actions"],
    summary ="Rate weekly success",
    description ="Rate how successful you felt your week was on a scale of 1-10. The most recent rating is the valid one.",
    request =WeeklySuccessRatingSerializer ,
    responses ={
    200 :WeeklyMissionReadSerializer ,
    400 :OpenApiResponse (
    description ="Invalid rating data or validation error."
    ),
    401 :OpenApiResponse (
    description ="Authentication credentials were not provided."
    ),
    403 :OpenApiResponse (
    description ="You don't have permission to rate this mission."
    ),
    404 :OpenApiResponse (description ="Mission not found."),
    },
    examples =[
    OpenApiExample (
    "Weekly Success Rating Example",
    value ={
    "rating":8 ,
    "comment":"Made good progress with consistent practice",
    },
    request_only =True ,
    ),
    ],
    )
    @action (detail =True ,methods =["post"],url_path ="rate-success")
    def rate_success (self ,request ,pk =None ):
        mission =self .get_object ()
        serializer =WeeklySuccessRatingSerializer (
        data =request .data ,context ={"weekly_mission":mission }
        )

        if not serializer .is_valid ():
            return Response (serializer .errors ,status =status .HTTP_400_BAD_REQUEST )

        serializer .save ()
        mission_serializer =self .get_serializer (mission )
        return Response (mission_serializer .data )

    def list (self ,request ,*args ,**kwargs ):
        """
        Override the list method to optimize pagination for weekly missions.
        If a six_week_period filter is provided, ensure we return all 6 missions
        for that period (or as many as exist) in a single request.
        """
        period_id =request .query_params .get ("six_week_period")

        if period_id :

            queryset =self .filter_queryset (self .get_queryset ())

            if queryset .count ()<=6 :
                serializer =self .get_serializer (queryset ,many =True )
                return Response (
                {
                "meta":{
                "count":len (serializer .data ),
                "current_page_number":1 ,
                "limit":6 ,
                "next":None ,
                "previous":None ,
                "next_page_number":None ,
                "previous_page_number":None ,
                "total_pages":1 ,
                },
                "results":serializer .data ,
                }
                )

        return super ().list (request ,*args ,**kwargs )


def get_weekday_mapping (start_from ="sunday"):
    """
    Get weekday mappings based on the configured week start day.
    
    Args:
        start_from (str): Either "sunday" or "monday"
    
    Returns:
        tuple: (day_names_en, day_names_ar) dictionaries mapping day_number to day names
    """
    if start_from .lower ()=="sunday":

        day_names_en ={
        1 :"Sunday",
        2 :"Monday",
        3 :"Tuesday",
        4 :"Wednesday",
        5 :"Thursday",
        6 :"Friday",
        7 :"Saturday",
        }

        day_names_ar ={
        1 :"الأحد",
        2 :"الاثنين",
        3 :"الثلاثاء",
        4 :"الأربعاء",
        5 :"الخميس",
        6 :"الجمعة",
        7 :"السبت",
        }
    else :

        day_names_en ={
        1 :"Monday",
        2 :"Tuesday",
        3 :"Wednesday",
        4 :"Thursday",
        5 :"Friday",
        6 :"Saturday",
        7 :"Sunday",
        }

        day_names_ar ={
        1 :"الاثنين",
        2 :"الثلاثاء",
        3 :"الأربعاء",
        4 :"الخميس",
        5 :"الجمعة",
        6 :"السبت",
        7 :"الأحد",
        }

    return day_names_en ,day_names_ar 


def get_week_start_date (period_start_date ,week_number ,start_from ="sunday"):
    """
    Calculate the start date for a specific week based on the configured week start day.
    
    Args:
        period_start_date (date): The start date of the period
        week_number (int): The week number (1-6)
        start_from (str): Either "sunday" or "monday"
    
    Returns:
        date: The start date of the specified week
    """

    base_start_date =period_start_date +timedelta (days =(week_number -1 )*7 )

    if start_from .lower ()=="sunday":


        days_since_sunday =(base_start_date .weekday ()+1 )%7 
        week_start_date =base_start_date -timedelta (days =days_since_sunday )
    else :

        days_since_monday =base_start_date .weekday ()
        week_start_date =base_start_date -timedelta (days =days_since_monday )

    return week_start_date 
