from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework_nested.routers import NestedDefaultRouter

from apps.okrs.views.weekly_progress import WeeklyProgressViewSet
from apps.okrs.views.six_week_period import SixWeekPeriodViewSet

app_name = "okrs"

router = DefaultRouter()
router.register("periods", SixWeekPeriodViewSet, basename="period")

periods_router = NestedDefaultRouter(router, "periods", lookup="period")
periods_router.register("progress", WeeklyProgressViewSet, basename="period-progress")

urlpatterns = [
    path("", include(router.urls)),
    path("", include(periods_router.urls)),
    path(
        "periods/week/<int:week_number>/",
        SixWeekPeriodViewSet.as_view({"get": "week_filter"}),
        name="period-week-progress",
    ),
]
