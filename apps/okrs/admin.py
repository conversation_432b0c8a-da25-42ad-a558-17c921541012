from django.contrib import admin

from .models import SixWeekPeriod, WeeklyProgress


@admin.register(SixWeekPeriod)
class SixWeekPeriodAdmin(admin.ModelAdmin):
    list_display = ("user", "title", "start_date", "end_date", "is_public")
    list_filter = ("user", "is_public")
    search_fields = ("user__username", "title")
    list_per_page = 20


@admin.register(WeeklyProgress)
class WeeklyProgressAdmin(admin.ModelAdmin):
    list_display = (
        "six_week_period",
        "week_number",
        "reflection",
        "challenges",
        "learnings",
        "next_week_plan",
    )
    list_filter = ("six_week_period",)
    search_fields = ("six_week_period__title",)
    list_per_page = 20
