# Generated by Django 4.2.9 on 2025-03-17 14:50

import datetime
from django.db import migrations, models
import django.db.models.expressions


class Migration(migrations.Migration):
    dependencies = [
        ("okrs", "0001_initial"),
    ]

    operations = [
        migrations.AddConstraint(
            model_name="sixweekperiod",
            constraint=models.CheckConstraint(
                check=models.Q(
                    (
                        "end_date",
                        django.db.models.expressions.CombinedExpression(
                            models.F("start_date"),
                            "+",
                            models.Value(datetime.timedelta(days=42)),
                        ),
                    )
                ),
                name="exact_six_weeks",
            ),
        ),
    ]
