# Generated by Django 4.2.9 on 2025-03-17 01:10

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="SixWeekPeriod",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("title", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True)),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("archived", "Archived"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                ("is_public", models.BooleanField(default=False)),
                (
                    "shared_with",
                    models.ManyToManyField(
                        blank=True,
                        related_name="shared_six_week_periods",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="six_week_periods",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created"],
            },
        ),
        migrations.CreateModel(
            name="WeeklyProgress",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("week_number", models.IntegerField()),
                ("reflection", models.TextField(blank=True)),
                ("challenges", models.TextField(blank=True)),
                ("learnings", models.TextField(blank=True)),
                ("next_week_plan", models.TextField(blank=True)),
                (
                    "six_week_period",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="weekly_progress",
                        to="okrs.sixweekperiod",
                    ),
                ),
            ],
            options={
                "ordering": ["week_number"],
            },
        ),
        migrations.AddConstraint(
            model_name="weeklyprogress",
            constraint=models.UniqueConstraint(
                fields=("six_week_period", "week_number"), name="unique_week_per_period"
            ),
        ),
        migrations.AddConstraint(
            model_name="weeklyprogress",
            constraint=models.CheckConstraint(
                check=models.Q(("week_number__gte", 1), ("week_number__lte", 6)),
                name="valid_week_number_progress",
            ),
        ),
        migrations.AddConstraint(
            model_name="sixweekperiod",
            constraint=models.CheckConstraint(
                check=models.Q(("end_date__gt", models.F("start_date"))),
                name="end_date_after_start_date",
            ),
        ),
    ]
