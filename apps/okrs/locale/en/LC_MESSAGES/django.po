# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-29 08:13+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: apps/okrs/constants.py:3
msgid "Monday"
msgstr "الاثنين"

#: apps/okrs/constants.py:4
msgid "Tuesday"
msgstr "الثلاثاء"

#: apps/okrs/constants.py:5
msgid "Wednesday"
msgstr "الأربعاء"

#: apps/okrs/constants.py:6
msgid "Thursday"
msgstr "الخميس"

#: apps/okrs/constants.py:7
msgid "Friday"
msgstr "الجمعة"

#: apps/okrs/constants.py:8
msgid "Saturday"
msgstr "السبت"

#: apps/okrs/constants.py:9
msgid "Sunday"
msgstr "الأحد"

#: apps/okrs/models.py:16
msgid "Draft"
msgstr ""

#: apps/okrs/models.py:17
msgid "In Progress"
msgstr ""

#: apps/okrs/models.py:18
msgid "Completed"
msgstr ""

#: apps/okrs/models.py:19
msgid "Archived"
msgstr ""

#: apps/okrs/models.py:59 apps/okrs/serializers/six_week_period.py:62
msgid "Start date cannot be in the past"
msgstr ""

#: apps/okrs/models.py:157 apps/okrs/serializers/weekly_progress.py:33
#: apps/okrs/views/weekly_progress.py:77
msgid "Cannot add progress to archived or completed periods"
msgstr ""

#: apps/okrs/serializers/six_week_period.py:54
msgid "Only periods in draft status can be modified"
msgstr ""

#: apps/okrs/serializers/six_week_period.py:66
msgid "End date must be after start date"
msgstr ""

#: apps/okrs/serializers/six_week_period.py:72
msgid "Period must be exactly 6 weeks (42 days)"
msgstr ""

#: apps/okrs/signals.py:35
#, python-brace-format
msgid "Auto-generated mission {mission_num} for {username}"
msgstr ""

#: apps/okrs/signals.py:43
msgid "New six-week period created"
msgstr ""

#: apps/okrs/signals.py:47
#, python-brace-format
msgid "A new six-week period '{title}' has been created for your journey."
msgstr ""

#: apps/okrs/signals.py:61
#, python-brace-format
msgid "New six-week period: {title}"
msgstr ""

#: apps/okrs/signals.py:65
#, python-brace-format
msgid ""
"A new six-week period has been created for you starting on {start_date}."
msgstr ""

#: apps/okrs/signals.py:70
#, python-brace-format
msgid "Six-week period completed: {title}"
msgstr ""

#: apps/okrs/signals.py:75
#, python-brace-format
msgid "Your six-week period '{title}' has been completed."
msgstr ""

#: apps/okrs/signals.py:86
msgid "Significant progress update"
msgstr ""

#: apps/okrs/signals.py:90
#, python-brace-format
msgid "You've made significant progress in week {week_number} of '{title}'."
msgstr ""

#: apps/okrs/views/six_week_period.py:118
msgid "Start date format must be YYYY-MM-DD."
msgstr ""

#: apps/okrs/views/six_week_period.py:127
msgid "End date format must be YYYY-MM-DD."
msgstr ""

#: apps/okrs/views/six_week_period.py:146
msgid "You do not have permission to access this period"
msgstr ""

#: apps/okrs/views/six_week_period.py:150
msgid "Only the owner can perform this action"
msgstr ""

#: apps/okrs/views/six_week_period.py:175
msgid "Period could not be started"
msgstr ""

#: apps/okrs/views/six_week_period.py:191
msgid "Period could not be completed"
msgstr ""

#: apps/okrs/views/six_week_period.py:207
msgid "Period could not be archived"
msgstr ""

#: apps/okrs/views/six_week_period.py:228
msgid "No valid users provided"
msgstr ""

#: apps/okrs/views/six_week_period.py:255
#: apps/okrs/views/six_week_period.py:302 apps/okrs/views/weekly_progress.py:82
msgid "Week number must be between 1 and 6"
msgstr ""

#: apps/okrs/views/six_week_period.py:280
#: apps/okrs/views/weekly_progress.py:107
msgid "Period is not in progress or current week cannot be determined"
msgstr ""

#: apps/okrs/views/weekly_progress.py:87
msgid "Progress already exists for this week"
msgstr ""

#: apps/okrs/views/weekly_progress.py:119
msgid "No progress entry for current week"
msgstr ""
