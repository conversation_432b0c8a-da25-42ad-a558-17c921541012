from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from apps.missions.models import WeeklyMission
from apps.missions.serializers.weekly_mission import WeeklyMissionSerializer
from apps.okrs.serializers.weekly_progress import WeeklyProgressSerializer
from apps.okrs.models import SixWeekPeriod, WeeklyProgress


class SixWeekPeriodSerializer(serializers.ModelSerializer):
    weekly_progress = WeeklyProgressSerializer(many=True, read_only=True)
    current_week = serializers.SerializerMethodField()
    missions = serializers.SerializerMethodField()
    week_missions = serializers.SerializerMethodField()

    class Meta:
        model = SixWeekPeriod
        fields = [
            "id",
            "user",
            "title",
            "description",
            "start_date",
            "end_date",
            "status",
            "is_public",
            "shared_with",
            "weekly_progress",
            "current_week",
            "missions",
            "week_missions",
            "created",
            "updated",
        ]
        read_only_fields = ["user", "status"]

    def get_current_week(self, obj):
        return obj.get_current_week()

    def get_missions(self, obj):
        missions = obj.missions.all().order_by("week_number")
        return WeeklyMissionSerializer(missions, many=True).data

    def get_week_missions(self, obj):
        result = {}
        for week in range(1, 7):
            missions = obj.missions.filter(week_number=week).order_by("-created")
            result[str(week)] = WeeklyMissionSerializer(missions, many=True).data
        return result

    def validate(self, data):
        if self.instance and self.instance.status != SixWeekPeriod.STATUS_DRAFT:
            raise serializers.ValidationError(
                _("Only periods in draft status can be modified")
            )

        start_date = data.get("start_date")
        end_date = data.get("end_date")

        if start_date and end_date:
            if start_date < timezone.now().date():
                raise serializers.ValidationError(_("Start date cannot be in the past"))

            if end_date <= start_date:
                raise serializers.ValidationError(
                    _("End date must be after start date")
                )

            duration = (end_date - start_date).days
            if duration != 42:
                raise serializers.ValidationError(
                    _("Period must be exactly 6 weeks (42 days)")
                )

        return data

    def create(self, validated_data):
        validated_data["user"] = self.context["request"].user
        return super().create(validated_data)
