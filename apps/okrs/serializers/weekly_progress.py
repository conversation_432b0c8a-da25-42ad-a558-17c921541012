from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from apps.missions.models import WeeklyMission
from apps.missions.serializers.weekly_mission import WeeklyMissionSerializer
from apps.okrs.models import SixWeekPeriod, WeeklyProgress


class WeeklyProgressSerializer(serializers.ModelSerializer):
    class Meta:
        model = WeeklyProgress
        fields = [
            "id",
            "six_week_period",
            "week_number",
            "reflection",
            "challenges",
            "learnings",
            "next_week_plan",
            "created",
            "updated",
        ]
        read_only_fields = ["six_week_period"]

    def validate(self, data):
        if self.context.get("six_week_period"):
            period = self.context["six_week_period"]
            if period.status in [
                SixWeekPeriod.STATUS_ARCHIVED,
                SixWeekPeriod.STATUS_COMPLETED,
            ]:
                raise serializers.ValidationError(
                    _("Cannot add progress to archived or completed periods")
                )
        return data
