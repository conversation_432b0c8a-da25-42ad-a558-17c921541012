from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.utils.translation import activate, get_language
from apps.okrs.models import SixWeekPeriod, WeeklyProgress

User = get_user_model()


class SixWeekPeriodTests(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            fullname="Test User",
            username="testuser",
        )
        self.start_date = timezone.now().date()
        self.end_date = self.start_date + timezone.timedelta(days=42)

    def test_create_six_week_period(self):
        period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            description="Test Description",
            start_date=self.start_date,
            end_date=self.end_date,
        )
        self.assertEqual(period.user, self.user)
        self.assertEqual(period.title, "Test Period")
        self.assertEqual(period.description, "Test Description")
        self.assertEqual(period.status, SixWeekPeriod.STATUS_DRAFT)
        self.assertFalse(period.is_public)

    def test_invalid_period_duration(self):
        with self.assertRaises(ValidationError):
            period = SixWeekPeriod(
                user=self.user,
                title="Invalid Period",
                start_date=self.start_date,
                end_date=self.start_date + timezone.timedelta(days=30),
            )
            period.full_clean()

    def test_status_transitions(self):
        period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            start_date=self.start_date,
            end_date=self.end_date,
        )

        self.assertTrue(period.start_period())
        self.assertEqual(period.status, SixWeekPeriod.STATUS_IN_PROGRESS)

        self.assertTrue(period.complete_period())
        self.assertEqual(period.status, SixWeekPeriod.STATUS_COMPLETED)

        self.assertTrue(period.archive_period())
        self.assertEqual(period.status, SixWeekPeriod.STATUS_ARCHIVED)

    def test_invalid_status_transitions(self):
        period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            start_date=self.start_date,
            end_date=self.end_date,
            status=SixWeekPeriod.STATUS_COMPLETED,
        )

        self.assertFalse(period.start_period())
        self.assertEqual(period.status, SixWeekPeriod.STATUS_COMPLETED)

    def test_get_current_week(self):
        period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timezone.timedelta(days=42),
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
        )
        self.assertEqual(period.get_current_week(), 1)

    def test_get_week_dates(self):
        period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            start_date=self.start_date,
            end_date=self.end_date,
        )

        week_start, week_end = period.get_week_dates(1)
        self.assertEqual(week_start, self.start_date)
        self.assertEqual(week_end, self.start_date + timezone.timedelta(days=6))

        week_start, week_end = period.get_week_dates(6)
        self.assertEqual(week_start, self.start_date + timezone.timedelta(days=35))
        self.assertEqual(week_end, self.end_date)

    def test_translation_status_choices(self):
        current_language = get_language()
        try:
            activate("fr")
            period = SixWeekPeriod.objects.create(
                user=self.user,
                title="Test Period",
                start_date=self.start_date,
                end_date=self.end_date,
            )
            self.assertEqual(period.get_status_display(), "Brouillon")
        finally:
            activate(current_language)

    def test_invalid_date_order(self):
        with self.assertRaises(ValidationError):
            period = SixWeekPeriod(
                user=self.user,
                title="Invalid Period",
                start_date=self.end_date,
                end_date=self.start_date,
            )
            period.full_clean()

    def test_past_start_date(self):
        with self.assertRaises(ValidationError):
            period = SixWeekPeriod(
                user=self.user,
                title="Past Period",
                start_date=self.start_date - timezone.timedelta(days=1),
                end_date=self.start_date + timezone.timedelta(days=41),
            )
            period.full_clean()

    def test_invalid_status_sequence(self):
        period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            start_date=self.start_date,
            end_date=self.end_date,
        )

        self.assertFalse(period.complete_period())
        self.assertEqual(period.status, SixWeekPeriod.STATUS_DRAFT)

        self.assertFalse(period.archive_period())
        self.assertEqual(period.status, SixWeekPeriod.STATUS_DRAFT)

        period.status = SixWeekPeriod.STATUS_IN_PROGRESS
        period.save()
        self.assertFalse(period.archive_period())
        self.assertEqual(period.status, SixWeekPeriod.STATUS_IN_PROGRESS)

    def test_title_max_length(self):
        with self.assertRaises(ValidationError):
            period = SixWeekPeriod(
                user=self.user,
                title="T" * 256,
                start_date=self.start_date,
                end_date=self.end_date,
            )
            period.full_clean()

    def test_shared_with_self(self):
        period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            start_date=self.start_date,
            end_date=self.end_date,
        )
        period.shared_with.add(self.user)
        self.assertEqual(period.shared_with.count(), 1)


class WeeklyProgressTests(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            fullname="Test User",
            username="testuser",
        )
        self.period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timezone.timedelta(days=42),
        )

    def test_create_weekly_progress(self):
        progress = WeeklyProgress.objects.create(
            six_week_period=self.period,
            week_number=1,
            reflection="Test Reflection",
            challenges="Test Challenges",
            learnings="Test Learnings",
            next_week_plan="Test Plan",
        )
        self.assertEqual(progress.six_week_period, self.period)
        self.assertEqual(progress.week_number, 1)
        self.assertEqual(progress.reflection, "Test Reflection")

    def test_invalid_week_number(self):
        with self.assertRaises(ValidationError):
            progress = WeeklyProgress(
                six_week_period=self.period, week_number=7, reflection="Invalid Week"
            )
            progress.full_clean()

    def test_unique_week_per_period(self):
        WeeklyProgress.objects.create(six_week_period=self.period, week_number=1)
        with self.assertRaises(ValidationError):
            progress = WeeklyProgress(six_week_period=self.period, week_number=1)
            progress.full_clean()

    def test_week_zero(self):
        with self.assertRaises(ValidationError):
            progress = WeeklyProgress(six_week_period=self.period, week_number=0)
            progress.full_clean()

    def test_negative_week_number(self):
        with self.assertRaises(ValidationError):
            progress = WeeklyProgress(six_week_period=self.period, week_number=-1)
            progress.full_clean()

    def test_progress_on_completed_period(self):
        self.period.status = SixWeekPeriod.STATUS_COMPLETED
        self.period.save()

        with self.assertRaises(ValidationError):
            progress = WeeklyProgress(six_week_period=self.period, week_number=1)
            progress.full_clean()

    def test_progress_on_archived_period(self):
        self.period.status = SixWeekPeriod.STATUS_ARCHIVED
        self.period.save()

        with self.assertRaises(ValidationError):
            progress = WeeklyProgress(six_week_period=self.period, week_number=1)
            progress.full_clean()
