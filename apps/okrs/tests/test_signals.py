from django.test import TestCase
from django.utils import timezone
from datetime import timedelta

from apps.accounts.user.models import User
from apps.okrs.models import SixWeekPeriod


class SignalsTestCase(TestCase):
    def test_periods_created_for_new_member(self):
        """Test that 6 periods are created when a new user with role 'member' is created."""

        user = User.objects.create(
            username="testmember",
            email="<EMAIL>",
            fullname="Test Member",
            role="member",
        )

        periods = SixWeekPeriod.objects.filter(user=user)
        self.assertEqual(periods.count(), 6)

        user_periods_count = 0

        for i in range(1, 7):
            period = periods.filter(
                title=f"MISSION_{user_periods_count}_WEEK_{i}"
            ).first()
            self.assertIsNotNone(
                period,
                f"Period with title MISSION_{user_periods_count}_WEEK_{i} not found",
            )

            today = timezone.now().date()
            expected_start = today + timedelta(days=(i - 1) * 42)
            expected_end = expected_start + timedelta(days=42)

            self.assertEqual(period.start_date, expected_start)
            self.assertEqual(period.end_date, expected_end)

            self.assertEqual(period.user, user)
            self.assertFalse(period.is_public)
            self.assertEqual(period.status, SixWeekPeriod.STATUS_DRAFT)

    def test_periods_not_created_for_non_member(self):
        """Test that periods are not created for users with roles other than 'member'."""

        user = User.objects.create(
            username="testadmin",
            email="<EMAIL>",
            fullname="Test Admin",
            role="admin",
        )

        periods = SixWeekPeriod.objects.filter(user=user)
        self.assertEqual(periods.count(), 0)

        user = User.objects.create(
            username="testmanager",
            email="<EMAIL>",
            fullname="Test Manager",
            role="club_manager",
        )

        periods = SixWeekPeriod.objects.filter(user=user)
        self.assertEqual(periods.count(), 0)

    def test_periods_not_created_for_existing_user(self):
        """Test that periods are not created when an existing user is updated."""

        user = User.objects.create(
            username="testmember2",
            email="<EMAIL>",
            fullname="Test Member 2",
            role="member",
        )

        periods = SixWeekPeriod.objects.filter(user=user)
        self.assertEqual(periods.count(), 6)

        user.fullname = "Updated Test Member"
        user.save()

        periods = SixWeekPeriod.objects.filter(user=user)
        self.assertEqual(periods.count(), 6)

    def test_periods_created_when_role_changes_to_member(self):
        """Test that periods are not created when a user's role changes to 'member'."""

        user = User.objects.create(
            username="changingrole",
            email="<EMAIL>",
            fullname="Changing Role",
            role="admin",
        )

        periods = SixWeekPeriod.objects.filter(user=user)
        self.assertEqual(periods.count(), 0)

        user.role = "member"
        user.save()

        periods = SixWeekPeriod.objects.filter(user=user)
        self.assertEqual(periods.count(), 0)
