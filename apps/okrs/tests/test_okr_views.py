from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.utils.translation import activate, get_language
from apps.okrs.models import SixWeekPeriod, WeeklyProgress

User = get_user_model()


class SixWeekPeriodViewTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            fullname="Test User",
            username="testuser",
        )
        self.other_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            fullname="Other User",
            username="otheruser",
        )
        self.client.force_authenticate(user=self.user)
        self.start_date = timezone.now().date()
        self.end_date = self.start_date + timezone.timedelta(days=42)

        self.initial_period_count = SixWeekPeriod.objects.count()

    def test_create_period(self):
        url = reverse("okrs:period-list")
        data = {
            "title": "Test Period",
            "description": "Test Description",
            "start_date": self.start_date.isoformat(),
            "end_date": self.end_date.isoformat(),
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        self.assertEqual(SixWeekPeriod.objects.count(), self.initial_period_count + 1)
        self.assertEqual(response.data["title"], "Test Period")

    def test_list_periods(self):
        SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period 1",
            start_date=self.start_date,
            end_date=self.end_date,
        )
        SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period 2",
            start_date=self.start_date,
            end_date=self.end_date,
        )

        url = reverse("okrs:period-list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        expected_periods = 8
        self.assertEqual(len(response.data["results"]), expected_periods)

    def test_start_period(self):
        period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            start_date=self.start_date,
            end_date=self.end_date,
        )
        url = reverse("okrs:period-start", args=[period.id])
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        period.refresh_from_db()
        self.assertEqual(period.status, SixWeekPeriod.STATUS_IN_PROGRESS)

    def test_complete_period(self):
        period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            start_date=self.start_date,
            end_date=self.end_date,
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
        )
        url = reverse("okrs:period-complete", args=[period.id])
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        period.refresh_from_db()
        self.assertEqual(period.status, SixWeekPeriod.STATUS_COMPLETED)

    def test_share_period(self):
        period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            start_date=self.start_date,
            end_date=self.end_date,
        )
        url = reverse("okrs:period-share", args=[period.id])
        data = {"user_ids": [self.other_user.id]}
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        period.refresh_from_db()
        self.assertIn(self.other_user, period.shared_with.all())

    def test_unauthorized_access(self):
        self.client.force_authenticate(user=None)
        url = reverse("okrs:period-list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_period_invalid_dates(self):
        url = reverse("okrs:period-list")
        data = {
            "title": "Test Period",
            "start_date": self.end_date.isoformat(),
            "end_date": self.start_date.isoformat(),
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_period_past_date(self):
        url = reverse("okrs:period-list")
        past_start = self.start_date - timezone.timedelta(days=1)
        past_end = past_start + timezone.timedelta(days=42)
        data = {
            "title": "Past Period",
            "start_date": past_start.isoformat(),
            "end_date": past_end.isoformat(),
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_update_period_wrong_status(self):
        period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            start_date=self.start_date,
            end_date=self.end_date,
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
        )
        url = reverse("okrs:period-detail", args=[period.id])
        data = {"title": "Updated Title"}
        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_other_user_start_period(self):
        period = SixWeekPeriod.objects.create(
            user=self.other_user,
            title="Other's Period",
            start_date=self.start_date,
            end_date=self.end_date,
        )
        url = reverse("okrs:period-start", args=[period.id])
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_share_period_invalid_user(self):
        period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            start_date=self.start_date,
            end_date=self.end_date,
        )
        url = reverse("okrs:period-share", args=[period.id])
        data = {"user_ids": [9999]}
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_translation_error_messages(self):
        current_language = get_language()
        try:
            activate("fr")
            url = reverse("okrs:period-list")
            data = {
                "title": "Test Period",
                "start_date": self.end_date.isoformat(),
                "end_date": self.start_date.isoformat(),
            }
            response = self.client.post(url, data, format="json")
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        finally:
            activate(current_language)

    def test_filter_periods_by_date(self):
        date1 = timezone.now().date()
        date2 = date1 + timezone.timedelta(days=7)
        date3 = date2 + timezone.timedelta(days=7)

        period1 = SixWeekPeriod.objects.create(
            user=self.user,
            title="Early Period",
            start_date=date1,
            end_date=date1 + timezone.timedelta(days=42),
        )
        period2 = SixWeekPeriod.objects.create(
            user=self.user,
            title="Middle Period",
            start_date=date2,
            end_date=date2 + timezone.timedelta(days=42),
        )
        period3 = SixWeekPeriod.objects.create(
            user=self.user,
            title="Late Period",
            start_date=date3,
            end_date=date3 + timezone.timedelta(days=42),
        )

        url = reverse("okrs:period-list")

        response = self.client.get(f"{url}?start_date={date2.isoformat()}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data["results"]
        period_titles = [p["title"] for p in results]
        self.assertIn("Middle Period", period_titles)
        self.assertIn("Late Period", period_titles)
        self.assertNotIn("Early Period", period_titles)

        end_date = (date2 + timezone.timedelta(days=42)).isoformat()
        response = self.client.get(f"{url}?end_date={end_date}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data["results"]
        period_titles = [p["title"] for p in results]
        self.assertIn("Early Period", period_titles)
        self.assertIn("Middle Period", period_titles)
        self.assertNotIn("Late Period", period_titles)

        response = self.client.get(
            f"{url}?start_date={date2.isoformat()}&end_date={end_date}"
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data["results"]
        period_titles = [p["title"] for p in results]
        self.assertNotIn("Early Period", period_titles)
        self.assertIn("Middle Period", period_titles)
        self.assertNotIn("Late Period", period_titles)

    def test_filter_periods_invalid_dates(self):
        date1 = timezone.now().date()
        period1 = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period 1",
            start_date=date1,
            end_date=date1 + timezone.timedelta(days=42),
        )
        period2 = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period 2",
            start_date=date1 + timezone.timedelta(days=7),
            end_date=date1 + timezone.timedelta(days=49),
        )

        url = reverse("okrs:period-list")

        response = self.client.get(f"{url}?start_date=invalid-date")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("error", response.data)
        self.assertIn("YYYY-MM-DD", str(response.data["error"]))

        response = self.client.get(f"{url}?start_date=&end_date=")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data["results"]
        period_titles = [p["title"] for p in results]
        self.assertIn("Test Period 1", period_titles)
        self.assertIn("Test Period 2", period_titles)

        response = self.client.get(f"{url}?end_date=invalid-date")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("error", response.data)
        self.assertIn("YYYY-MM-DD", str(response.data["error"]))

        response = self.client.get(f"{url}?start_date=invalid&end_date=also-invalid")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("error", response.data)

    def test_filter_periods_by_week_number(self):
        period2 = SixWeekPeriod.objects.create(
            user=self.user,
            title="MISSION_1_WEEK_3",
            start_date=self.start_date,
            end_date=self.end_date,
        )
        period3 = SixWeekPeriod.objects.create(
            user=self.user,
            title="MISSION_0_WEEK_4",
            start_date=self.start_date,
            end_date=self.end_date,
        )

        SixWeekPeriod.objects.create(
            user=self.other_user,
            title="MISSION_0_WEEK_3",
            start_date=self.start_date,
            end_date=self.end_date,
        )

        url = reverse("okrs:period-week-progress", kwargs={"week_number": 3})
        response = self.client.get(url)
        print(response.json(), "resopnse\n\ns")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertEqual(len(response.data), 2)
        titles = [period["title"] for period in response.data]
        self.assertIn("MISSION_0_WEEK_3", titles)
        self.assertIn("MISSION_1_WEEK_3", titles)
        self.assertNotIn("MISSION_0_WEEK_4", titles)

    def test_filter_periods_by_week_invalid_number(self):
        url = reverse("okrs:period-week-progress", kwargs={"week_number": 7})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("error", response.data)

    def test_filter_periods_by_week_unauthenticated(self):
        self.client.logout()
        url = reverse("okrs:period-week-progress", kwargs={"week_number": 3})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class WeeklyProgressViewTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            fullname="Test User",
            username="testuser",
        )
        self.client.force_authenticate(user=self.user)
        self.period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timezone.timedelta(days=42),
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
        )

    def test_create_progress(self):
        url = reverse("okrs:period-progress-list", args=[self.period.id])
        data = {
            "week_number": 1,
            "reflection": "Test Reflection",
            "challenges": "Test Challenges",
            "learnings": "Test Learnings",
            "next_week_plan": "Test Plan",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(WeeklyProgress.objects.count(), 1)
        self.assertEqual(response.data["reflection"], "Test Reflection")

    def test_get_current_week_progress(self):
        progress = WeeklyProgress.objects.create(
            six_week_period=self.period, week_number=1, reflection="Test Reflection"
        )
        url = reverse("okrs:period-progress-current-week", args=[self.period.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["reflection"], "Test Reflection")

    def test_invalid_week_number(self):
        url = reverse("okrs:period-progress-list", args=[self.period.id])
        data = {"week_number": 7, "reflection": "Invalid Week"}
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_progress_completed_period(self):
        self.period.status = SixWeekPeriod.STATUS_COMPLETED
        self.period.save()

        url = reverse("okrs:period-progress-list", args=[self.period.id])
        data = {"week_number": 1, "reflection": "Test Reflection"}
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_progress_archived_period(self):
        self.period.status = SixWeekPeriod.STATUS_ARCHIVED
        self.period.save()

        url = reverse("okrs:period-progress-list", args=[self.period.id])
        data = {"week_number": 1, "reflection": "Test Reflection"}
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_duplicate_week_progress(self):
        WeeklyProgress.objects.create(
            six_week_period=self.period, week_number=1, reflection="Original Reflection"
        )

        url = reverse("okrs:period-progress-list", args=[self.period.id])
        data = {"week_number": 1, "reflection": "Duplicate Reflection"}
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_update_other_user_progress(self):
        other_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            fullname="Other User",
            username="otheruser",
        )
        other_period = SixWeekPeriod.objects.create(
            user=other_user,
            title="Other's Period",
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timezone.timedelta(days=42),
            status=SixWeekPeriod.STATUS_IN_PROGRESS,
        )
        progress = WeeklyProgress.objects.create(
            six_week_period=other_period,
            week_number=1,
            reflection="Original Reflection",
        )

        url = reverse(
            "okrs:period-progress-detail", args=[other_period.id, progress.id]
        )
        data = {"reflection": "Updated Reflection"}
        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_get_nonexistent_week_progress(self):
        url = reverse("okrs:period-progress-current-week", args=[self.period.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_delete_progress(self):
        progress = WeeklyProgress.objects.create(
            six_week_period=self.period, week_number=1, reflection="Test Reflection"
        )
        url = reverse("okrs:period-progress-detail", args=[self.period.id, progress.id])
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(WeeklyProgress.objects.count(), 0)
