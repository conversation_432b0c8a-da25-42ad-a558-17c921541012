from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.utils.translation import gettext_lazy as _
from django.shortcuts import get_object_or_404
from django.core.exceptions import ValidationError
from apps.missions.models import WeeklyMission
from apps.okrs.models import SixWeekPeriod, WeeklyProgress
from apps.okrs.serializers.six_week_period import SixWeekPeriodSerializer
from apps.okrs.serializers.weekly_progress import WeeklyMissionSerializer
from apps.missions.serializers.weekly_mission import WeeklyMissionSerializer
from apps.okrs.serializers.weekly_progress import WeeklyProgressSerializer

from rest_framework.exceptions import (
    ValidationError as DRFValidationError,
    PermissionDenied as <PERSON>FPermissionDenied,
)
from drf_spectacular.utils import (
    extend_schema,
    extend_schema_view,
)


@extend_schema_view(
    list=extend_schema(
        tags=["progress"],
        summary="List all progress entries",
        description="Returns a list of all weekly progress entries for a specific period. Only the owner of the period can view the progress entries.",
    ),
    retrieve=extend_schema(
        tags=["progress"],
        summary="Retrieve a specific progress entry",
        description="Returns details of a specific weekly progress entry. Only the owner of the period can view the progress entry.",
    ),
    create=extend_schema(
        tags=["progress"],
        summary="Create a new progress entry",
        description="Creates a new weekly progress entry for a specific period. Only the owner of the period can create progress entries.",
    ),
    update=extend_schema(
        tags=["progress"],
        summary="Update a progress entry",
        description="Updates a weekly progress entry. Only the owner of the period can update progress entries.",
    ),
    partial_update=extend_schema(
        tags=["progress"],
        summary="Partially update a progress entry",
        description="Partially updates a weekly progress entry. Only the owner of the period can update progress entries.",
    ),
    destroy=extend_schema(
        tags=["progress"],
        summary="Delete a progress entry",
        description="Deletes a weekly progress entry. Only the owner of the period can delete progress entries.",
    ),
)
class WeeklyProgressViewSet(viewsets.ModelViewSet):
    serializer_class = WeeklyProgressSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        period_pk = self.kwargs.get("period_pk")
        return WeeklyProgress.objects.filter(
            six_week_period__pk=period_pk, six_week_period__user=self.request.user
        )

    def perform_create(self, serializer):
        period_pk = self.kwargs.get("period_pk")
        period = get_object_or_404(SixWeekPeriod, pk=period_pk, user=self.request.user)

        try:
            if period.status in [
                SixWeekPeriod.STATUS_ARCHIVED,
                SixWeekPeriod.STATUS_COMPLETED,
            ]:
                raise ValidationError(
                    _("Cannot add progress to archived or completed periods")
                )

            week_number = serializer.validated_data.get("week_number")
            if not 1 <= week_number <= 6:
                raise ValidationError(_("Week number must be between 1 and 6"))

            if WeeklyProgress.objects.filter(
                six_week_period=period, week_number=week_number
            ).exists():
                raise ValidationError(_("Progress already exists for this week"))

            serializer.save(six_week_period=period)
        except ValidationError as e:
            raise DRFValidationError({"non_field_errors": [str(e)]})

    @extend_schema(
        tags=["progress"],
        summary="Get progress for the current week",
        description="Returns the progress entry for the current week in a period. This is useful for users to track their current week's progress. Only works for periods in 'in progress' status.",
    )
    @action(detail=False, methods=["get"])
    def current_week(self, request, period_pk=None):
        period = get_object_or_404(SixWeekPeriod, pk=period_pk, user=request.user)

        current_week = period.get_current_week()
        if not current_week:
            return Response(
                {
                    "error": _(
                        "Period is not in progress or current week cannot be determined"
                    )
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        progress = WeeklyProgress.objects.filter(
            six_week_period=period, week_number=current_week
        ).first()

        if not progress:
            return Response(
                {"error": _("No progress entry for current week")},
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = self.get_serializer(progress)
        return Response(serializer.data)
