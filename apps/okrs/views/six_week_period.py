from datetime import datetime

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.pagination import PageNumberPagination
from django.utils.translation import gettext_lazy as _
from django.db.models import Q
from django.core.exceptions import ValidationError, PermissionDenied
from apps.missions.models import WeeklyMission
from apps.okrs.models import SixWeekPeriod
from apps.okrs.serializers.six_week_period import SixWeekPeriodSerializer
from apps.okrs.serializers.weekly_progress import WeeklyMissionSerializer
from apps.missions.serializers.weekly_mission import WeeklyMissionSerializer
from apps.okrs.serializers.weekly_progress import WeeklyProgressSerializer
from rest_framework.exceptions import (
    ValidationError as DRFValidationError,
    PermissionDenied as DRFPermissionDenied,
)
from drf_spectacular.utils import (
    extend_schema,
    extend_schema_view,
    OpenApiParameter,
)


class StandardResultsSetPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = "page_size"
    max_page_size = 100


@extend_schema_view(
    list=extend_schema(
        tags=["periods"],
        summary="List all periods",
        description="Returns a paginated list of all 6-week periods created by the authenticated user. This endpoint is useful for users to view their OKR periods.",
    ),
    retrieve=extend_schema(
        tags=["periods"],
        summary="Retrieve a specific period",
        description="Returns details of a specific 6-week period. Users can access periods they created, periods shared with them, or public periods.",
    ),
    create=extend_schema(
        tags=["periods"],
        summary="Create a new period",
        description="Creates a new 6-week period. Only authenticated users can create periods. The period will be in 'draft' status initially.",
    ),
    update=extend_schema(
        tags=["periods"],
        summary="Update a period",
        description="Updates a 6-week period. Only the owner of the period can perform this action, and only if the period is in 'draft' status.",
    ),
    partial_update=extend_schema(
        tags=["periods"],
        summary="Partially update a period",
        description="Partially updates a 6-week period. Only the owner of the period can perform this action, and only if the period is in 'draft' status.",
    ),
    destroy=extend_schema(
        tags=["periods"],
        summary="Delete a period",
        description="Deletes a 6-week period. Only the owner of the period can perform this action, and only if the period is in 'draft' status.",
    ),
)
@extend_schema_view(
    list=extend_schema(
        tags=["periods"],
        summary="List all periods",
        description="Returns a list of periods. By default, shows only periods created by the current user. Can be filtered by start and end dates.",
        parameters=[
            OpenApiParameter(
                name="start_date",
                description="Filter periods starting on or after this date (YYYY-MM-DD)",
                required=False,
                type=str,
            ),
            OpenApiParameter(
                name="end_date",
                description="Filter periods ending on or before this date (YYYY-MM-DD)",
                required=False,
                type=str,
            ),
        ],
    ),
    week_filter=extend_schema(
        tags=["periods"],
        summary="Get periods by week number",
        description="Returns periods that are currently in the specified week number (1-6). Only returns periods owned by the current user that are in progress.",
        parameters=[
            OpenApiParameter(
                name="week_number",
                description="Week number (1-6)",
                required=True,
                type=int,
            ),
        ],
    ),
)
class SixWeekPeriodViewSet(viewsets.ModelViewSet):
    serializer_class = SixWeekPeriodSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = StandardResultsSetPagination

    def get_queryset(self):
        user = self.request.user
        start_date = self.request.query_params.get("start_date")
        end_date = self.request.query_params.get("end_date")

        filters = Q(user=user) | Q(shared_with=user) | Q(is_public=True)

        if start_date:
            try:
                start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
                filters &= Q(start_date__gte=start_date)
            except ValueError:
                raise DRFValidationError(
                    {"start_date": [_("Start date format must be YYYY-MM-DD.")]}
                )

        if end_date:
            try:
                end_date = datetime.strptime(end_date, "%Y-%m-%d").date()
                filters &= Q(end_date__lte=end_date)
            except ValueError:
                raise DRFValidationError(
                    {"end_date": [_("End date format must be YYYY-MM-DD.")]}
                )

        base_qs = SixWeekPeriod.objects.filter(filters).order_by("-created")

        if self.action == "list":
            return base_qs.filter(user=user)

        return base_qs

    def get_object(self):
        try:
            queryset = self.filter_queryset(self.get_queryset())
            lookup_url_kwarg = self.lookup_url_kwarg or self.lookup_field
            filter_kwargs = {self.lookup_field: self.kwargs[lookup_url_kwarg]}
            obj = queryset.filter(**filter_kwargs).first()

            if not obj:
                raise DRFPermissionDenied(
                    _("You do not have permission to access this period")
                )

            if self.action not in ["retrieve"] and obj.user != self.request.user:
                raise DRFPermissionDenied(_("Only the owner can perform this action"))

            self.check_object_permissions(self.request, obj)
            return obj
        except PermissionDenied as e:
            raise DRFPermissionDenied(str(e))

    def create(self, request, *args, **kwargs):
        try:
            return super().create(request, *args, **kwargs)
        except ValidationError as e:
            raise DRFValidationError({"non_field_errors": [str(e)]})

    @extend_schema(
        tags=["period-actions"],
        summary="Start a period",
        description="Transitions a period from 'draft' to 'in progress' status. Only the owner of the period can perform this action. This marks the official start of the 6-week OKR cycle.",
    )
    @action(detail=True, methods=["post"])
    def start(self, request, pk=None):
        period = self.get_object()
        if period.start_period():
            serializer = self.get_serializer(period)
            return Response(serializer.data)
        return Response(
            {"error": _("Period could not be started")},
            status=status.HTTP_400_BAD_REQUEST,
        )

    @extend_schema(
        tags=["period-actions"],
        summary="Complete a period",
        description="Marks a period as 'completed'. Only the owner of the period can perform this action, and only if the period is 'in progress'. This signifies the end of the 6-week OKR cycle.",
    )
    @action(detail=True, methods=["post"])
    def complete(self, request, pk=None):
        period = self.get_object()
        if period.complete_period():
            serializer = self.get_serializer(period)
            return Response(serializer.data)
        return Response(
            {"error": _("Period could not be completed")},
            status=status.HTTP_400_BAD_REQUEST,
        )

    @extend_schema(
        tags=["period-actions"],
        summary="Archive a period",
        description="Archives a period. Only the owner of the period can perform this action, and only if the period is 'completed'. Archived periods are read-only and cannot be modified.",
    )
    @action(detail=True, methods=["post"])
    def archive(self, request, pk=None):
        period = self.get_object()
        if period.archive_period():
            serializer = self.get_serializer(period)
            return Response(serializer.data)
        return Response(
            {"error": _("Period could not be archived")},
            status=status.HTTP_400_BAD_REQUEST,
        )

    @extend_schema(
        tags=["period-actions"],
        summary="Share a period with other users",
        description="Shares a period with other users. Only the owner of the period can perform this action. Shared periods can be viewed by the specified users.",
    )
    @action(detail=True, methods=["post"])
    def share(self, request, pk=None):
        period = self.get_object()
        user_ids = request.data.get("user_ids", [])
        from django.contrib.auth import get_user_model

        User = get_user_model()

        try:
            users = User.objects.filter(id__in=user_ids)
            if not users.exists():
                return Response(
                    {"error": _("No valid users provided")},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            period.shared_with.add(*users)
            serializer = self.get_serializer(period)
            return Response(serializer.data)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @extend_schema(
        tags=["missions"],
        summary="Get missions for a specific week",
        description="Returns all missions for a specific week in a period. Users can access missions for periods they created or that were shared with them.",
        parameters=[
            OpenApiParameter(
                name="week", description="Week number (1-6)", required=True, type=int
            )
        ],
    )
    @action(detail=True, methods=["get"])
    def week_missions(self, request, pk=None):
        period = self.get_object()
        week = request.query_params.get("week")

        if not week or not week.isdigit() or not 1 <= int(week) <= 6:
            return Response(
                {"error": _("Week number must be between 1 and 6")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        missions = WeeklyMission.objects.filter(
            six_week_period=period, week_number=int(week)
        ).order_by("-priority", "-created")

        serializer = WeeklyMissionSerializer(missions, many=True)
        return Response(serializer.data)

    @extend_schema(
        tags=["missions"],
        summary="Get missions for the current week",
        description="Returns all missions for the current week in a period. This is useful for users to focus on their current week's objectives. Only works for periods in 'in progress' status.",
    )
    @action(detail=True, methods=["get"])
    def current_week_missions(self, request, pk=None):
        period = self.get_object()
        current_week = period.get_current_week()

        if not current_week:
            return Response(
                {
                    "error": _(
                        "Period is not in progress or current week cannot be determined"
                    )
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        missions = WeeklyMission.objects.filter(
            six_week_period=period, week_number=current_week
        ).order_by("-priority", "-created")

        serializer = WeeklyMissionSerializer(missions, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def week_filter(self, request, week_number=None):
        """Get periods by week number from title (MISSION_X_WEEK_Y format)."""
        try:
            week_number = int(week_number)
            if not 1 <= week_number <= 6:
                raise ValueError
        except (TypeError, ValueError):
            return Response(
                {"error": _("Week number must be between 1 and 6")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        queryset = SixWeekPeriod.objects.filter(
            user=request.user, title__endswith=f"WEEK_{week_number}"
        )

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
