from django.db import models
from django.conf import settings
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from core.abstract.models import AbstractModel


class SixWeekPeriod(AbstractModel):
    STATUS_DRAFT = "draft"
    STATUS_IN_PROGRESS = "in_progress"
    STATUS_COMPLETED = "completed"
    STATUS_ARCHIVED = "archived"

    STATUS_CHOICES = [
        (STATUS_DRAFT, _("Draft")),
        (STATUS_IN_PROGRESS, _("In Progress")),
        (STATUS_COMPLETED, _("Completed")),
        (STATUS_ARCHIVED, _("Archived")),
    ]

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="six_week_periods",
    )
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    start_date = models.DateField()
    end_date = models.DateField()
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default=STATUS_DRAFT
    )
    is_public = models.<PERSON>olean<PERSON>ield(default=False)
    shared_with = models.ManyToManyField(
        settings.AUTH_USER_MODEL, related_name="shared_six_week_periods", blank=True
    )

    class Meta:
        ordering = ["-created"]
        constraints = [
            models.CheckConstraint(
                check=models.Q(end_date__gt=models.F("start_date")),
                name="end_date_after_start_date",
            ),
            models.CheckConstraint(
                check=models.Q(
                    end_date=models.F("start_date") + timezone.timedelta(days=42)
                ),
                name="exact_six_weeks",
            ),
        ]

    def __str__(self):
        return f"{self.title} ({self.user.username})"

    def clean(self):
        if self.start_date and self.start_date < timezone.now().date():
            raise ValidationError(_("Start date cannot be in the past"))

    def get_current_week(self):
        if self.status != self.STATUS_IN_PROGRESS:
            return None

        today = timezone.now().date()
        if today < self.start_date or today > self.end_date:
            return None

        days_passed = (today - self.start_date).days
        current_week = (days_passed // 7) + 1
        return min(current_week, 6)

    def get_week_dates(self, week_number):
        if not 1 <= week_number <= 6:
            return None

        week_start = self.start_date + timezone.timedelta(days=(week_number - 1) * 7)
        week_end = week_start + timezone.timedelta(days=6)
        if week_number == 6:
            week_end = self.end_date
        return week_start, week_end

    def start_period(self):
        if self.status == self.STATUS_DRAFT:
            self.status = self.STATUS_IN_PROGRESS
            self.save()
            return True
        return False

    def complete_period(self):
        if self.status == self.STATUS_IN_PROGRESS:
            self.status = self.STATUS_COMPLETED
            self.save()
            return True
        return False

    def archive_period(self):
        if self.status == self.STATUS_COMPLETED:
            self.status = self.STATUS_ARCHIVED
            self.save()
            return True
        return False

    def is_active(self):
        """Check if the period is currently active"""
        today = timezone.now().date()
        return self.start_date <= today <= self.end_date

    def was_changed_from_active(self):
        """Check if the status changed from active to inactive"""
        if not hasattr(self, "_previous_is_active"):
            return False
        return self._previous_is_active and not self.is_active()

    def save(self, *args, **kwargs):
        if self.pk:

            try:
                orig = SixWeekPeriod.objects.get(pk=self.pk)
                self._previous_is_active = orig.is_active()
            except SixWeekPeriod.DoesNotExist:
                self._previous_is_active = False
        super().save(*args, **kwargs)


class WeeklyProgress(AbstractModel):
    six_week_period = models.ForeignKey(
        SixWeekPeriod, on_delete=models.CASCADE, related_name="weekly_progress"
    )
    week_number = models.IntegerField()
    reflection = models.TextField(blank=True)
    challenges = models.TextField(blank=True)
    learnings = models.TextField(blank=True)
    next_week_plan = models.TextField(blank=True)

    class Meta:
        ordering = ["week_number"]
        constraints = [
            models.UniqueConstraint(
                fields=["six_week_period", "week_number"], name="unique_week_per_period"
            ),
            models.CheckConstraint(
                check=models.Q(week_number__gte=1) & models.Q(week_number__lte=6),
                name="valid_week_number_progress",
            ),
        ]

    def __str__(self):
        return f"Week {self.week_number} Progress - {self.six_week_period.title}"

    def clean(self):
        if self.six_week_period.status in [
            SixWeekPeriod.STATUS_ARCHIVED,
            SixWeekPeriod.STATUS_COMPLETED,
        ]:
            raise ValidationError(
                _("Cannot add progress to archived or completed periods")
            )

    def is_update_significant(self):
        """Check if the progress update is significant based on content changes"""
        if not self.pk:
            return False

        try:
            orig = WeeklyProgress.objects.get(pk=self.pk)

            content_changes = (
                self.reflection != orig.reflection
                or self.challenges != orig.challenges
                or self.learnings != orig.learnings
                or self.next_week_plan != orig.next_week_plan
            )

            char_diff = (
                abs(len(self.reflection) - len(orig.reflection))
                + abs(len(self.challenges) - len(orig.challenges))
                + abs(len(self.learnings) - len(orig.learnings))
                + abs(len(self.next_week_plan) - len(orig.next_week_plan))
            )

            return content_changes and char_diff > 50

        except WeeklyProgress.DoesNotExist:
            return False
