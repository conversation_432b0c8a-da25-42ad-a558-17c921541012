from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils import timezone
from django.utils.translation import gettext as _
from django.conf import settings
from datetime import timedelta
import logging

from apps.accounts.user.models import User
from .models import SixWeekPeriod, WeeklyProgress
from apps.notifications.utils import create_notification

logger = logging.getLogger(__name__)


@receiver(post_save, sender=User)
def create_periods_for_new_member(sender, instance, created, **kwargs):
    """
    Signal to automatically create 6 periods for new users with role 'member'.
    Each period will have a title in the format MISSION_0_WEEK_X.
    """
    if created and instance.role == "member":
        logger.info(f"Creating periods for new member: {instance.username}")
        start_date = timezone.now().date()
        user_periods_count = 0

        for mission_num in range(1, 7):
            mission_start_date = start_date + timedelta(days=(mission_num - 1) * 42)
            mission_end_date = mission_start_date + timedelta(days=42)

            period = SixWeekPeriod.objects.create(
                user=instance,
                title=f"MISSION_{user_periods_count}_WEEK_{mission_num}",
                description=_(
                    "Auto-generated mission {mission_num} for {username}"
                ).format(mission_num=mission_num, username=instance.username),
                start_date=mission_start_date,
                end_date=mission_end_date,
                is_public=False,
            )

            create_notification(
                message=_("New six-week period created"),
                notification_type="Process",
                target=instance,
                description=_(
                    "A new six-week period '{title}' has been created for your journey."
                ).format(title=period.title),
            )

        logger.info(f"Created 6 periods for {instance.username}")
    else:
        logger.info(f"Skipping period creation for {instance.username}")


@receiver(post_save, sender=SixWeekPeriod)
def six_week_period_notification(sender, instance, created, **kwargs):
    """Send notification when a six-week period is created or updated"""
    if created:
        create_notification(
            message=_("New six-week period: {title}").format(title=instance.title),
            notification_type="Process",
            target=instance.user,
            description=_(
                "A new six-week period has been created for you starting on {start_date}."
            ).format(start_date=instance.start_date.strftime("%Y-%m-%d")),
        )
    elif not instance.is_active() and instance.was_changed_from_active():
        create_notification(
            message=_("Six-week period completed: {title}").format(
                title=instance.title
            ),
            notification_type="Completed",
            target=instance.user,
            description=_("Your six-week period '{title}' has been completed.").format(
                title=instance.title
            ),
        )


@receiver(post_save, sender=WeeklyProgress)
def weekly_progress_notification(sender, instance, created, **kwargs):
    """Send notification when weekly progress is updated"""
    if not created and instance.is_update_significant():
        create_notification(
            message=_("Significant progress update"),
            notification_type="Process",
            target=instance.six_week_period.user,
            description=_(
                "You've made significant progress in week {week_number} of '{title}'."
            ).format(
                week_number=instance.week_number, title=instance.six_week_period.title
            ),
        )


def ready():
    post_save.connect(create_periods_for_new_member, sender=User)
