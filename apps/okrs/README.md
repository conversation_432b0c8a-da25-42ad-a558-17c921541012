# OKRs and Missions System

This module implements a 6-week OKR (Objectives and Key Results) system with weekly missions and progress tracking.

## Overview

The OKRs system is designed to help users track their goals over a 6-week period. It consists of three main components:

1. **Six-Week Periods**: The main container for organizing goals over a 6-week timeframe.
2. **Weekly Missions**: Specific tasks or goals assigned to each week within a period.
3. **Weekly Progress**: Tracking achievements, challenges, and plans for each week.

## Models

### SixWeekPeriod

A 6-week period represents a cycle of goal tracking. It has the following statuses:

- **Draft**: Initial state, can be modified freely
- **In Progress**: Active period, missions can be added and completed
- **Completed**: Period has ended, no more modifications allowed
- **Archived**: Period is stored for historical reference

### WeeklyMission

A mission represents a specific goal for a particular week. Missions have:

- A goal description
- A domain (work, personal, health, etc.)
- A week number (1-6)
- A priority (high or low)
- A completion status

### WeeklyProgress

Progress entries track achievements, challenges, and plans for each week. They include:

- Achievements for the week
- Challenges faced
- Plans for the next week

## Workflow

1. Create a 6-week period (draft status)
2. Add missions for each week
3. Start the period (changes status to in progress)
4. Track weekly progress
5. Mark missions as completed
6. Complete the period after 6 weeks
7. Archive the period for future reference

## Sharing and Collaboration

Periods can be:
- Private (visible only to the owner)
- Shared with specific users
- Public (visible to all users)

## API Endpoints

### Periods

- `GET /api/v1/periods/`: List all periods
- `POST /api/v1/periods/`: Create a new period
- `GET /api/v1/periods/{id}/`: Retrieve a specific period
- `PUT /api/v1/periods/{id}/`: Update a period
- `PATCH /api/v1/periods/{id}/`: Partially update a period
- `DELETE /api/v1/periods/{id}/`: Delete a period
- `POST /api/v1/periods/{id}/start/`: Start a period
- `POST /api/v1/periods/{id}/complete/`: Complete a period
- `POST /api/v1/periods/{id}/archive/`: Archive a period
- `POST /api/v1/periods/{id}/share/`: Share a period with other users
- `GET /api/v1/periods/{id}/week_missions/?week=1`: Get missions for a specific week
- `GET /api/v1/periods/{id}/current_week_missions/`: Get missions for the current week

### Progress

- `GET /api/v1/periods/{period_id}/progress/`: List all progress entries for a period
- `POST /api/v1/periods/{period_id}/progress/`: Create a new progress entry
- `GET /api/v1/periods/{period_id}/progress/{id}/`: Retrieve a specific progress entry
- `PUT /api/v1/periods/{period_id}/progress/{id}/`: Update a progress entry
- `PATCH /api/v1/periods/{period_id}/progress/{id}/`: Partially update a progress entry
- `DELETE /api/v1/periods/{period_id}/progress/{id}/`: Delete a progress entry
- `GET /api/v1/periods/{period_id}/progress/current_week/`: Get progress for the current week

## Permissions

- Only the owner of a period can modify it
- Users can view periods that are shared with them or are public
- Only the owner can start, complete, or archive a period
- Only the owner can add progress entries

## Validation Rules

- A period must have a start date
- The end date is automatically calculated (start date + 42 days)
- Week numbers must be between 1 and 6
- Only one progress entry is allowed per week
- Progress entries cannot be added to archived or completed periods
- Only periods in draft status can be modified
- Only periods in progress can be completed
- Only completed periods can be archived

## Notifications

The system sends notifications for various events:
- When a period is shared with a user
- When a period is started
- When a period is completed
- When a new week begins in a period

These notifications are delivered in real-time via WebSockets. 