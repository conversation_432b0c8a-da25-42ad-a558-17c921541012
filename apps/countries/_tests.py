import uuid
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

from apps.accounts.user.models import User
from apps.countries.models import Country, City, School, State, TimeZone
from core.utilities import tprint

CACHE_TTL = 60 * 60 * 24 * 30


class CountryViewsTest(APITestCase):
    def setUp(self):
        self.country = Country.objects.create(
            name="Test Country",
            iso3="TST",
            iso2="TS",
            phone_code="123",
            capital="Test Capital",
            region="Test Region",
            subregion="Test Subregion",
        )

        self.state = State.objects.create(
            name="Test State",
            state_code="TS",
            latitude=34.000000,
            longitude=65.000000,
        )
        self.country.states.add(self.state)

        self.city = City.objects.create(
            name="Test City",
            state=self.state,
            latitude=36.000000,
            longitude=70.000000,
        )

        self.timezone = TimeZone.objects.create(
            zone_name="Test TimeZone",
            gmt_offset=16200,
            gmt_offset_name="UTC+04:30",
            abbreviation="TST",
            tz_name="Test Time",
        )
        self.country.timezones.add(self.timezone)
        self.user = User.objects.create_user(
            email="<EMAIL>", password="password", username="user"
        )
        self.client.force_authenticate(user=self.user)  # type: ignore

    def test_country_list_view(self):
        url = reverse("country-list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("Test Country", response.json()["results"][0]["name"])

    def test_country_detail_view(self):
        url = reverse("country-detail", kwargs={"id": self.country.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["name"], "Test Country")

    def test_country_detail_view_not_found(self):
        url = reverse("country-detail", kwargs={"id": uuid.uuid4()})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_city_list_view(self):
        url = reverse("city-list", kwargs={"id": self.country.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("Test City", response.json()[0]["name"])

    def test_city_list_view_light(self):
        url = reverse("city-list", kwargs={"id": self.country.id}) + "?full=false"
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("Test City", response.json()[0]["name"])

    def test_city_list_view_full(self):
        url = reverse("city-list", kwargs={"id": self.country.id}) + "?full=true"
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("state_name", response.json()[0])

    def test_city_list_view_no_pagination(self):
        url = reverse("city-list", kwargs={"id": self.country.id}) + "?pagination=false"
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()), 1)

    def test_state_has_cities(self):
        self.assertTrue(self.state.cities.exists())  # type: ignore

    def test_country_has_states(self):
        self.assertTrue(self.country.states.exists())

    def test_country_has_timezones(self):
        self.assertTrue(self.country.timezones.exists())

    def test_city_view_without_country(self):
        url = reverse("city-list", kwargs={"id": uuid.uuid4()})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()), 0)

    def test_invalid_city_full_filter(self):
        url = reverse("city-list", kwargs={"id": self.country.id}) + "?full=invalid"
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_invalid_city_pagination_filter(self):
        url = (
            reverse("city-list", kwargs={"id": self.country.id}) + "?pagination=invalid"
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_country_list_no_pagination(self):
        url = reverse("country-list") + "?pagination=false"
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()), 1)

    def test_country_list_full_details(self):
        url = reverse("country-list") + "?full=true"
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("states", response.json()["results"][0])

    def test_country_list_with_search(self):
        url = reverse("country-list") + "?search=Test"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("Test Country", response.json()["results"][0]["name"])


class SchoolViewsTest(APITestCase):
    def setUp(self):
        self.country = Country.objects.create(name="Test Country", iso3="TST")
        self.city = City.objects.create(name="Test City")
        self.school = School.objects.create(
            name="Test School", country=self.country, city=self.city
        )
        self.user = User.objects.create_user(
            email="<EMAIL>", password="password", username="user"
        )
        self.client.force_authenticate(user=self.user)  # type: ignore

    def test_school_list_view(self):
        url = reverse("school-list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("Test School", response.json()["results"][0]["name"])

    def test_school_list_view_full(self):
        url = reverse("school-list") + "?full=true"
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("city_name", response.json()["results"][0])

    def test_school_detail_view(self):
        url = reverse("school-detail", kwargs={"pk": self.school.pk})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["name"], "Test School")

    def test_school_list_view_no_pagination(self):
        url = reverse("school-list") + "?pagination=false"
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()), 1)

    def test_school_list_with_search(self):
        url = reverse("school-list") + "?search=Test"
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("Test School", response.json()["results"][0]["name"])
