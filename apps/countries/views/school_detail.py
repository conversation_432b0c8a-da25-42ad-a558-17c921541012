from rest_framework import generics
from rest_framework.response import Response
from django.utils.translation import gettext as _
from drf_spectacular.utils import extend_schema
from rest_framework.permissions import AllowAny
from rest_framework.request import Request
from django.core.cache import cache
from django.utils.translation import get_language

from apps.countries.models import School
from apps.countries.serializers.school import SchoolSerializer


class SchoolDetailView(generics.RetrieveAPIView):
    queryset = School.objects.all()
    permission_classes = [AllowAny]
    lookup_field = "pk"
    serializer_class = SchoolSerializer

    @extend_schema(
        tags=["countries"], responses={200: SchoolSerializer, 404: "Not found"}
    )
    def get(self, request: Request, *args, **kwargs):
        school_id = kwargs.get("pk")
        current_language = get_language()

        cache_key = f"school_detail_{school_id}_lang_{current_language}"

        cached_response = cache.get(cache_key)
        if cached_response:
            return Response(cached_response)

        response = super().get(request, *args, **kwargs)
        cache.set(cache_key, response.data, None)
        return response
