from rest_framework import generics, filters
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.response import Response
from django.utils.translation import gettext as _
from drf_spectacular.utils import extend_schema, OpenApiParameter
from drf_spectacular.types import OpenApiTypes
from rest_framework import status
from rest_framework.permissions import AllowAny
from rest_framework.request import Request
from django.utils.translation import get_language
from django.core.cache import cache

from apps.countries.models import School
from apps.countries.serializers.school_light import SchoolLightSerializer
from apps.countries.serializers.school import SchoolSerializer
from apps.countries.filters import SchoolFilterSet
from core.abstract.paginations import MetaPageNumberPagination


class SchoolListView(generics.ListAPIView):
    queryset = School.objects.all()
    pagination_class = MetaPageNumberPagination
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_class = SchoolFilterSet
    search_fields = ["name", "city__name", "country__name"]
    permission_classes = [AllowAny]

    @extend_schema(
        description="List of schools. Use 'full=true' to get full school details, and 'pagination=false' to disable pagination.",
        tags=["countries"],
        responses={
            200: SchoolLightSerializer(many=True),
            400: "Invalid request",
        },
        parameters=[
            OpenApiParameter(
                name="full",
                type=OpenApiTypes.BOOL,
                location=OpenApiParameter.QUERY,
                description="Return full school details",
            ),
            OpenApiParameter(
                name="pagination",
                type=OpenApiTypes.BOOL,
                location=OpenApiParameter.QUERY,
                description="Disable pagination if set to false",
            ),
            OpenApiParameter(
                name="page",
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                description="Page number for paginated results",
            ),
            OpenApiParameter(
                name="limit",
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                description="Number of items per page",
            ),
        ],
    )
    def get(self, request: Request, *args, **kwargs):
        full = request.query_params.get("full", "false").lower() == "true"
        pagination = request.query_params.get("pagination", "true").lower() == "true"
        current_language = get_language()

        query_params = request.query_params.copy()
        query_string = "&".join([f"{k}={v}" for k, v in sorted(query_params.items())])
        cache_key = f"schools_list_full_{full}_pagination_{pagination}_query_{query_string}_lang_{current_language}"

        if not pagination:
            self.pagination_class = None
            cached_response = cache.get(cache_key)
            if cached_response:
                return Response(cached_response)

        queryset = self.filter_queryset(self.get_queryset())

        if not queryset.exists():
            return Response(
                {"error": _("Not found.")}, status=status.HTTP_404_NOT_FOUND
            )

        serializer_class = self.get_serializer_class()

        if pagination:
            paginated_queryset = self.paginate_queryset(queryset)
            serializer = serializer_class(
                paginated_queryset, many=True, context={"request": request}
            )
            return self.get_paginated_response(serializer.data)
        else:
            serializer = serializer_class(
                queryset, many=True, context={"request": request}
            )
            response_data = serializer.data
            cache.set(cache_key, response_data, None)
            return Response(response_data)

    def get_serializer_class(self):
        if (
            self.request
            and hasattr(self.request, "query_params")
            and self.request.query_params.get("full") == "true"
        ):
            return SchoolSerializer
        return SchoolLightSerializer
