from rest_framework import generics, filters
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.response import Response
from django.utils.translation import gettext as _
from drf_spectacular.utils import extend_schema, OpenApiParameter
from drf_spectacular.types import OpenApiTypes
from rest_framework.permissions import AllowAny
from rest_framework.request import Request
from django.core.cache import cache
from django.utils.translation import get_language

from apps.countries.models import Country
from apps.countries.serializers.country_light import CountryLightSerializer
from apps.countries.serializers.country_full import CountryFullSerializer
from apps.countries.filters import CountryFilterSet
from core.abstract.paginations import MetaPageNumberPagination


class CountryListView(generics.ListAPIView):
    queryset = Country.objects.all()
    pagination_class = MetaPageNumberPagination
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_class = CountryFilterSet
    permission_classes = [AllowAny]
    search_fields = ["name", "iso3", "iso2", "phone_code", "capital"]
    filterset_fields = ["region", "subregion"]

    @extend_schema(
        description="List of countries. Use 'full=true' to get full country details, and 'pagination=false' to disable pagination.",
        tags=["countries"],
        responses=CountryLightSerializer,
        parameters=[
            OpenApiParameter(
                name="full",
                type=OpenApiTypes.BOOL,
                location=OpenApiParameter.QUERY,
                description="Return full country details",
            ),
            OpenApiParameter(
                name="pagination",
                type=OpenApiTypes.BOOL,
                location=OpenApiParameter.QUERY,
                description="Disable pagination if set to false",
            ),
            OpenApiParameter(
                name="page",
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                description="Page number for paginated results",
            ),
            OpenApiParameter(
                name="limit",
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                description="Number of items per page",
            ),
        ],
    )
    def get(self, request: Request, *args, **kwargs):
        full = request.query_params.get("full", "false").lower() == "true"
        pagination = request.query_params.get("pagination", "true").lower() == "true"
        current_language = get_language()

        cache_key = f"countries_list_full_{full}_pagination_{pagination}_lang_{current_language}"
        if not pagination:
            self.pagination_class = None
            cached_response = cache.get(cache_key)
            if cached_response:
                return Response(cached_response)

        response = super().get(request, *args, **kwargs)

        if not pagination:
            cache.set(cache_key, response.data, None)

        return response

    def get_serializer_class(self):
        if (
            self.request
            and hasattr(self.request, "query_params")
            and self.request.query_params.get("full") == "true"
        ):
            return CountryFullSerializer
        return CountryLightSerializer
