from rest_framework import generics, filters
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.response import Response
from django.utils.translation import gettext as _
from drf_spectacular.utils import extend_schema, OpenApiParameter
from drf_spectacular.types import OpenApiTypes
from rest_framework.permissions import AllowAny
from rest_framework.request import Request
from django.core.cache import cache
from django.utils.translation import get_language

from apps.countries.models import City, Country
from apps.countries.serializers.city_full import CityFullSerializer
from apps.countries.serializers.city_light import CityLightSerializer
from apps.countries.filters import CityFilterSet
from core.abstract.paginations import MetaPageNumberPagination


class CityListView(generics.ListAPIView):
    permission_classes = [AllowAny]
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_class = CityFilterSet
    search_fields = ["name", "state__name", "state__countries__name"]
    pagination_class = MetaPageNumberPagination

    def get_queryset(self):
        id = self.kwargs.get("id")
        try:
            country = Country.objects.get(id=id)
            return City.objects.filter(state__countries=country)
        except Country.DoesNotExist:
            return City.objects.none()

    @extend_schema(
        tags=["countries"],
        responses={
            200: CityFullSerializer(many=True),
            400: "Invalid country ID",
        },
        parameters=[
            OpenApiParameter(
                name="full",
                type=OpenApiTypes.BOOL,
                location=OpenApiParameter.QUERY,
                description="Return full city details (default is light view)",
            ),
            OpenApiParameter(
                name="pagination",
                type=OpenApiTypes.BOOL,
                location=OpenApiParameter.QUERY,
                description="Disable pagination if set to false",
            ),
            OpenApiParameter(
                name="page",
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                description="Page number for paginated results",
            ),
            OpenApiParameter(
                name="limit",
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                description="Number of items per page",
            ),
        ],
    )
    def get(self, request: Request, *args, **kwargs):
        country_id = kwargs.get("id")
        full = request.query_params.get("full", "false").lower() == "true"
        pagination = request.query_params.get("pagination", "true").lower() == "true"
        current_language = get_language()

        cache_key = f"cities_list_{country_id}_full_{full}_pagination_{pagination}_lang_{current_language}"
        if not pagination:
            self.pagination_class = None
            cached_response = cache.get(cache_key)
            if cached_response:
                return Response(cached_response)

        queryset = self.filter_queryset(self.get_queryset())

        if not queryset.exists():
            return Response([])

        serializer_class = CityFullSerializer if full else CityLightSerializer

        if pagination:
            paginated_queryset = self.paginate_queryset(queryset)
            serializer = serializer_class(
                paginated_queryset, many=True, context={"request": request}
            )
            return self.get_paginated_response(serializer.data)
        else:
            serializer = serializer_class(
                queryset, many=True, context={"request": request}
            )
            response_data = serializer.data
            cache.set(cache_key, response_data, None)
            return Response(response_data)
