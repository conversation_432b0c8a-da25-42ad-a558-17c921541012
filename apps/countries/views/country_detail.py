from rest_framework import generics
from rest_framework.response import Response
from django.utils.translation import gettext as _
from drf_spectacular.utils import extend_schema
from rest_framework.permissions import AllowAny
from rest_framework.request import Request
from django.core.cache import cache
from django.utils.translation import get_language

from apps.countries.models import Country
from apps.countries.serializers.country_full import (
    CountryFullSerializer,
)
from apps.countries.serializers.country_light import CountryLightSerializer


class CountryDetailView(generics.RetrieveAPIView):
    queryset = Country.objects.all()
    permission_classes = [AllowAny]
    lookup_field = "id"

    @extend_schema(tags=["countries"], responses=CountryFullSerializer)
    def get(self, request: Request, *args, **kwargs):
        country_id = kwargs.get("id")
        full = request.query_params.get("full", "false").lower() == "true"
        current_language = get_language()

        cache_key = f"country_detail_{country_id}_full_{full}_lang_{current_language}"
        cached_response = cache.get(cache_key)
        if cached_response:
            return Response(cached_response)

        response = super().get(request, *args, **kwargs)
        cache.set(cache_key, response.data, None)
        return response

    def get_serializer_class(self):
        if (
            self.request
            and hasattr(self.request, "query_params")
            and self.request.query_params.get("full") == "true"
        ):
            return CountryFullSerializer
        return CountryLightSerializer
