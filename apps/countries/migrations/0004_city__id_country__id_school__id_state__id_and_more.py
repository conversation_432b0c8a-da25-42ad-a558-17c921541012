# Generated by Django 4.2.9 on 2025-03-10 16:36

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):
    dependencies = [
        ("countries", "0003_school"),
    ]

    operations = [
        migrations.AddField(
            model_name="city",
            name="_id",
            field=models.IntegerField(
                db_index=True, editable=False, null=True, unique=True
            ),
        ),
        migrations.AddField(
            model_name="country",
            name="_id",
            field=models.IntegerField(
                db_index=True, editable=False, null=True, unique=True
            ),
        ),
        migrations.AddField(
            model_name="school",
            name="_id",
            field=models.IntegerField(
                db_index=True, editable=False, null=True, unique=True
            ),
        ),
        migrations.AddField(
            model_name="state",
            name="_id",
            field=models.IntegerField(
                db_index=True, editable=False, null=True, unique=True
            ),
        ),
        migrations.AddField(
            model_name="timezone",
            name="_id",
            field=models.IntegerField(
                db_index=True, editable=False, null=True, unique=True
            ),
        ),
        migrations.AlterField(
            model_name="city",
            name="created",
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name="country",
            name="created",
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name="school",
            name="created",
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name="state",
            name="created",
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name="timezone",
            name="created",
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
    ]
