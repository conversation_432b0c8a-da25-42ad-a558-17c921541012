# Generated by Django 4.2.9 on 2024-09-21 12:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("countries", "0001_initial"),
    ]

    operations = [
        migrations.AlterField(
            model_name="city",
            name="latitude",
            field=models.DecimalField(
                blank=True, decimal_places=8, max_digits=11, null=True
            ),
        ),
        migrations.AlterField(
            model_name="city",
            name="longitude",
            field=models.DecimalField(
                blank=True, decimal_places=8, max_digits=11, null=True
            ),
        ),
        migrations.AlterField(
            model_name="city",
            name="name",
            field=models.CharField(blank=True, max_length=150, null=True),
        ),
        migrations.AlterField(
            model_name="city",
            name="state",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="cities",
                to="countries.state",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="country",
            name="capital",
            field=models.Char<PERSON>ield(blank=True, max_length=150, null=True),
        ),
        migrations.AlterField(
            model_name="country",
            name="currency",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="country",
            name="currency_name",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="country",
            name="currency_symbol",
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AlterField(
            model_name="country",
            name="emoji",
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name="country",
            name="iso2",
            field=models.CharField(blank=True, max_length=2, null=True),
        ),
        migrations.AlterField(
            model_name="country",
            name="iso3",
            field=models.CharField(blank=True, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name="country",
            name="latitude",
            field=models.DecimalField(
                blank=True, decimal_places=8, max_digits=11, null=True
            ),
        ),
        migrations.AlterField(
            model_name="country",
            name="longitude",
            field=models.DecimalField(
                blank=True, decimal_places=8, max_digits=11, null=True
            ),
        ),
        migrations.AlterField(
            model_name="country",
            name="name",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="country",
            name="nationality",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="country",
            name="native",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="country",
            name="numeric_code",
            field=models.CharField(blank=True, max_length=5, null=True),
        ),
        migrations.AlterField(
            model_name="country",
            name="phone_code",
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AlterField(
            model_name="country",
            name="region",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="country",
            name="states",
            field=models.ManyToManyField(
                blank=True, related_name="countries", to="countries.state"
            ),
        ),
        migrations.AlterField(
            model_name="country",
            name="subregion",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="country",
            name="timezones",
            field=models.ManyToManyField(
                blank=True, related_name="countries", to="countries.timezone"
            ),
        ),
        migrations.AlterField(
            model_name="country",
            name="tld",
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AlterField(
            model_name="state",
            name="latitude",
            field=models.DecimalField(
                blank=True, decimal_places=8, max_digits=11, null=True
            ),
        ),
        migrations.AlterField(
            model_name="state",
            name="longitude",
            field=models.DecimalField(
                blank=True, decimal_places=8, max_digits=11, null=True
            ),
        ),
        migrations.AlterField(
            model_name="state",
            name="name",
            field=models.CharField(blank=True, max_length=150, null=True),
        ),
        migrations.AlterField(
            model_name="state",
            name="state_code",
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name="timezone",
            name="abbreviation",
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name="timezone",
            name="gmt_offset",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="timezone",
            name="gmt_offset_name",
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name="timezone",
            name="tz_name",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="timezone",
            name="zone_name",
            field=models.CharField(blank=True, max_length=150, null=True),
        ),
    ]
