# Generated by Django 4.2.9 on 2024-09-21 12:13

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="State",
            fields=[
                ("created", models.DateTimeField(auto_now_add=True)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("state_code", models.CharField(max_length=10)),
                (
                    "latitude",
                    models.DecimalField(
                        blank=True, decimal_places=6, max_digits=10, null=True
                    ),
                ),
                (
                    "longitude",
                    models.DecimalField(
                        blank=True, decimal_places=6, max_digits=10, null=True
                    ),
                ),
            ],
            options={
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="TimeZone",
            fields=[
                ("created", models.DateTimeField(auto_now_add=True)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("zone_name", models.CharField(max_length=100)),
                ("gmt_offset", models.IntegerField()),
                ("gmt_offset_name", models.CharField(max_length=20)),
                ("abbreviation", models.CharField(max_length=10)),
                ("tz_name", models.CharField(max_length=50)),
            ],
            options={
                "ordering": ["zone_name"],
            },
        ),
        migrations.CreateModel(
            name="Country",
            fields=[
                ("created", models.DateTimeField(auto_now_add=True)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("iso3", models.CharField(max_length=3)),
                ("iso2", models.CharField(max_length=2)),
                ("numeric_code", models.CharField(max_length=3)),
                ("phone_code", models.CharField(max_length=5)),
                ("capital", models.CharField(max_length=100)),
                ("currency", models.CharField(max_length=50)),
                ("currency_name", models.CharField(max_length=50)),
                ("currency_symbol", models.CharField(max_length=10)),
                ("tld", models.CharField(max_length=10)),
                ("native", models.CharField(max_length=255)),
                ("region", models.CharField(max_length=50)),
                ("subregion", models.CharField(max_length=50)),
                ("nationality", models.CharField(max_length=50)),
                (
                    "latitude",
                    models.DecimalField(decimal_places=6, max_digits=10),
                ),
                (
                    "longitude",
                    models.DecimalField(decimal_places=6, max_digits=10),
                ),
                ("emoji", models.CharField(max_length=10)),
                (
                    "states",
                    models.ManyToManyField(
                        related_name="countries", to="countries.state"
                    ),
                ),
                (
                    "timezones",
                    models.ManyToManyField(
                        related_name="countries", to="countries.timezone"
                    ),
                ),
            ],
            options={
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="City",
            fields=[
                ("created", models.DateTimeField(auto_now_add=True)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                (
                    "latitude",
                    models.DecimalField(decimal_places=6, max_digits=10),
                ),
                (
                    "longitude",
                    models.DecimalField(decimal_places=6, max_digits=10),
                ),
                (
                    "state",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="cities",
                        to="countries.state",
                    ),
                ),
            ],
            options={
                "ordering": ["name"],
            },
        ),
    ]
