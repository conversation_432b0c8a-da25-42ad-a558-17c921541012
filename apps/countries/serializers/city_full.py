from rest_framework import serializers
from apps.countries.models import City
from django.utils.translation import gettext_lazy as _
import logging

logger = logging.getLogger(__name__)


class CityFullSerializer(serializers.ModelSerializer):
    state_name = serializers.CharField(source="state.name")

    class Meta:
        model = City
        fields = ["id", "name", "latitude", "longitude", "state_name"]
