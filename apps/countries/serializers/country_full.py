from rest_framework import serializers
from apps.countries.models import Country
from django.utils.translation import gettext_lazy as _
import logging

from apps.countries.serializers.state import StateSerializer
from apps.countries.serializers.time_zone import TimeZoneSerializer

logger = logging.getLogger(__name__)


class CountryFullSerializer(serializers.ModelSerializer):
    states = StateSerializer(many=True, read_only=True)
    timezones = TimeZoneSerializer(many=True, read_only=True)
    name = serializers.SerializerMethodField()

    def get_name(self, obj):
        from apps.countries import constants

        country_name = obj.name

        return _(country_name)

    class Meta:
        model = Country
        fields = "__all__"
