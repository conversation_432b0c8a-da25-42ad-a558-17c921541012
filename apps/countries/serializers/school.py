from rest_framework import serializers
from apps.countries.models import School
from django.utils.translation import gettext_lazy as _
import logging

logger = logging.getLogger(__name__)


class SchoolSerializer(serializers.ModelSerializer):
    city_name = serializers.CharField(source="city.name", allow_null=True)
    country_name = serializers.SerializerMethodField()

    def get_country_name(self, obj):
        if not obj.country:
            return None

        from apps.countries import constants

        country_name = obj.country.name

        return _(country_name)

    class Meta:
        model = School
        fields = ["id", "name", "info", "city_name", "country_name"]
