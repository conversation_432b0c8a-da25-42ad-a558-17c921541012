from rest_framework import serializers
from apps.countries.models import Country
from django.utils.translation import gettext_lazy as _
import logging

logger = logging.getLogger(__name__)


class CountryLightSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()

    def get_name(self, obj):
        from apps.countries import constants

        country_name = obj.name

        return _(country_name)

    class Meta:
        model = Country
        fields = [
            "id",
            "name",
            "iso3",
            "phone_code",
            "capital",
            "region",
            "subregion",
        ]
