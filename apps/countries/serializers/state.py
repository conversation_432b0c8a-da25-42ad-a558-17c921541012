from rest_framework import serializers
from apps.countries.models import State
from django.utils.translation import gettext_lazy as _
import logging

from apps.countries.serializers.city import CitySerializer

logger = logging.getLogger(__name__)


class StateSerializer(serializers.ModelSerializer):
    cities = CitySerializer(many=True, read_only=True)

    class Meta:
        model = State
        fields = "__all__"
