from django.contrib import admin

from .models import Country, City, State, TimeZone, School


class CountryAdmin(admin.ModelAdmin):
    list_display = [
        "name",
        "iso3",
        "iso2",
        "phone_code",
        "capital",
        "currency",
        "currency_name",
        "currency_symbol",
        "tld",
        "native",
        "region",
        "subregion",
    ]
    search_fields = [
        "name",
        "iso3",
        "iso2",
        "phone_code",
        "capital",
        "currency",
        "currency_name",
        "currency_symbol",
        "tld",
        "native",
        "region",
        "subregion",
    ]
    list_filter = ["region", "subregion"]
    ordering = ["name"]


admin.site.register(Country, CountryAdmin)


class CityAdmin(admin.ModelAdmin):
    list_display = ["name", "state", "latitude", "longitude"]
    search_fields = ["name", "state", "latitude", "longitude"]
    list_filter = ["state"]
    ordering = ["name"]


admin.site.register(City, CityAdmin)


class StateAdmin(admin.ModelAdmin):
    list_display = ["name", "state_code", "latitude", "longitude"]
    search_fields = ["name", "state_code", "latitude", "longitude"]
    ordering = ["name"]


admin.site.register(State, StateAdmin)


class TimeZoneAdmin(admin.ModelAdmin):
    list_display = [
        "zone_name",
        "gmt_offset",
        "gmt_offset_name",
        "abbreviation",
        "tz_name",
    ]
    search_fields = [
        "zone_name",
        "gmt_offset",
        "gmt_offset_name",
        "abbreviation",
        "tz_name",
    ]
    ordering = ["zone_name"]


admin.site.register(TimeZone, TimeZoneAdmin)

admin.site.register(School)
