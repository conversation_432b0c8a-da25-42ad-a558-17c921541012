from django.db import models
from core.abstract.models import AbstractAutoIncrementModel


class TimeZone(AbstractAutoIncrementModel):
    zone_name = models.CharField(max_length=150, null=True, blank=True)
    gmt_offset = models.IntegerField(null=True, blank=True)
    gmt_offset_name = models.CharField(max_length=50, null=True, blank=True)
    abbreviation = models.CharField(max_length=20, null=True, blank=True)
    tz_name = models.CharField(max_length=100, null=True, blank=True)

    def __str__(self):
        return self.zone_name or "Unknown TimeZone"

    class Meta:
        ordering = ["zone_name"]


class State(AbstractAutoIncrementModel):
    name = models.CharField(max_length=150, null=True, blank=True)
    state_code = models.CharField(max_length=20, null=True, blank=True)
    latitude = models.DecimalField(
        max_digits=11, decimal_places=8, null=True, blank=True
    )
    longitude = models.DecimalField(
        max_digits=11, decimal_places=8, null=True, blank=True
    )

    def __str__(self):
        return self.name or "Unknown State"

    class Meta:
        ordering = ["name"]


class City(AbstractAutoIncrementModel):
    name = models.CharField(max_length=150, null=True, blank=True)
    state = models.ForeignKey(
        State,
        related_name="cities",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    latitude = models.DecimalField(
        max_digits=11, decimal_places=8, null=True, blank=True
    )
    longitude = models.DecimalField(
        max_digits=11, decimal_places=8, null=True, blank=True
    )

    def __str__(self):
        return self.name or "Unknown City"

    class Meta:
        ordering = ["name"]


class Country(AbstractAutoIncrementModel):
    name = models.CharField(max_length=255, null=True, blank=True)
    iso3 = models.CharField(max_length=3, null=True, blank=True)
    iso2 = models.CharField(max_length=2, null=True, blank=True)
    numeric_code = models.CharField(max_length=5, null=True, blank=True)
    phone_code = models.CharField(max_length=10, null=True, blank=True)
    capital = models.CharField(max_length=150, null=True, blank=True)
    currency = models.CharField(max_length=100, null=True, blank=True)
    currency_name = models.CharField(max_length=100, null=True, blank=True)
    currency_symbol = models.CharField(max_length=10, null=True, blank=True)
    tld = models.CharField(max_length=10, null=True, blank=True)
    native = models.CharField(max_length=255, null=True, blank=True)
    region = models.CharField(max_length=100, null=True, blank=True)
    subregion = models.CharField(max_length=100, null=True, blank=True)
    nationality = models.CharField(max_length=100, null=True, blank=True)
    latitude = models.DecimalField(
        max_digits=11, decimal_places=8, null=True, blank=True
    )
    longitude = models.DecimalField(
        max_digits=11, decimal_places=8, null=True, blank=True
    )
    emoji = models.CharField(max_length=20, null=True, blank=True)
    states = models.ManyToManyField(State, related_name="countries", blank=True)
    timezones = models.ManyToManyField(TimeZone, related_name="countries", blank=True)

    def __str__(self):
        return self.name or "Unknown Country"

    class Meta:
        ordering = ["name"]


class School(AbstractAutoIncrementModel):
    name = models.CharField(max_length=255)
    country = models.ForeignKey(
        Country,
        related_name="schools",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    city = models.ForeignKey(
        City,
        related_name="schools",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    info = models.TextField(null=True, blank=True)

    def __str__(self):
        return self.name or "Unknown School"

    class Meta:
        ordering = ["name"]
        indexes = [models.Index(fields=["name", "country", "city"])]
