from django.test import TestCase, override_settings
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from django.utils.translation import activate, get_language, gettext as _
from django.conf import settings
from typing import Dict, List, Any, Optional

from apps.countries.models import Country, State, City, School
from apps.accounts.user.models import User
from apps.countries import constants


class CountryTranslationTestCase(APITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpassword123",
            username="testuser",
            is_email_verified=True,
        )

        cls.saudi_arabia = Country.objects.create(
            name="Saudi Arabia",
            iso3="SAU",
            iso2="SA",
            phone_code="966",
            capital="Riyadh",
            region="Asia",
            subregion="Western Asia",
        )

        cls.egypt = Country.objects.create(
            name="Egypt",
            iso3="EGY",
            iso2="EG",
            phone_code="20",
            capital="Cairo",
            region="Africa",
            subregion="Northern Africa",
        )

        cls.state = State.objects.create(
            name="Riyadh Province",
            state_code="RD",
        )

        cls.city = City.objects.create(
            name="Riyadh",
            state=cls.state,
        )

        cls.school = School.objects.create(
            name="King Saud University",
            country=cls.saudi_arabia,
            city=cls.city,
            info="A prestigious university in Saudi Arabia",
        )

        cls.saudi_arabia.states.add(cls.state)

    def setUp(self):
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

        activate(settings.LANGUAGE_CODE)

    @override_settings(LANGUAGE_CODE="en")
    def test_country_list_english_translation(self):
        """Test that country names are translated to English"""

        activate("en")

        response = self.client.get(
            reverse("countries:country-list"), HTTP_ACCEPT_LANGUAGE="en"
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        response_data = response.json()
        self.assertTrue(
            "results" in response_data, "Response should contain 'results' key"
        )

        saudi_arabia = None
        for country in response_data["results"]:
            if country.get("iso3") == "SAU":
                saudi_arabia = country
                break

        self.assertIsNotNone(saudi_arabia, "Saudi Arabia should be in the results")
        if saudi_arabia:
            self.assertEqual(saudi_arabia.get("name"), "Saudi Arabia")

    @override_settings(LANGUAGE_CODE="ar")
    def test_country_list_arabic_translation(self):
        """Test that country names are translated to Arabic"""

        activate("ar")

        response = self.client.get(
            reverse("countries:country-list"), HTTP_ACCEPT_LANGUAGE="ar"
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        response_data = response.json()
        self.assertTrue(
            "results" in response_data, "Response should contain 'results' key"
        )

        saudi_arabia = None
        for country in response_data["results"]:
            if country.get("iso3") == "SAU":
                saudi_arabia = country
                break

        self.assertIsNotNone(saudi_arabia, "Saudi Arabia should be in the results")
        if saudi_arabia:
            self.assertEqual(saudi_arabia.get("name"), "المملكة العربية السعودية")

    def test_country_detail_translation(self):
        """Test that country detail view returns translated names"""

        activate("en")
        response = self.client.get(
            reverse("countries:country-detail", kwargs={"id": self.saudi_arabia.id}),
            HTTP_ACCEPT_LANGUAGE="en",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(data.get("name"), "Saudi Arabia")

        activate("ar")
        response = self.client.get(
            reverse("countries:country-detail", kwargs={"id": self.saudi_arabia.id}),
            HTTP_ACCEPT_LANGUAGE="ar",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertEqual(data.get("name"), "المملكة العربية السعودية")

    def test_school_list_country_translation(self):
        """Test that school list view returns translated country names"""

        activate("en")
        response = self.client.get(
            reverse("countries:school-list"),
            {"full": "true"},
            HTTP_ACCEPT_LANGUAGE="en",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        response_data = response.json()
        self.assertTrue(
            "results" in response_data, "Response should contain 'results' key"
        )

        school = None
        for item in response_data["results"]:
            if item.get("name") == "King Saud University":
                school = item
                break

        self.assertIsNotNone(school, "King Saud University should be in the results")
        if school:
            self.assertEqual(school.get("country_name"), "Saudi Arabia")

        activate("ar")
        response = self.client.get(
            reverse("countries:school-list"),
            {"full": "true"},
            HTTP_ACCEPT_LANGUAGE="ar",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        response_data = response.json()
        self.assertTrue(
            "results" in response_data, "Response should contain 'results' key"
        )

        school = None
        for item in response_data["results"]:
            if item.get("name") == "King Saud University":
                school = item
                break

        self.assertIsNotNone(school, "King Saud University should be in the results")
        if school:
            self.assertEqual(school.get("country_name"), "المملكة العربية السعودية")

    def test_school_detail_country_translation(self):
        """Test that school detail view returns translated country names"""

        activate("en")
        response = self.client.get(
            reverse("countries:school-detail", kwargs={"pk": self.school.id}),
            HTTP_ACCEPT_LANGUAGE="en",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(data.get("country_name"), "Saudi Arabia")

        activate("ar")
        response = self.client.get(
            reverse("countries:school-detail", kwargs={"pk": self.school.id}),
            HTTP_ACCEPT_LANGUAGE="ar",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertEqual(data.get("country_name"), "المملكة العربية السعودية")

    def tearDown(self):
        activate(settings.LANGUAGE_CODE)
