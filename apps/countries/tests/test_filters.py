from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
import uuid
from unittest import mock
from django.utils.translation import activate, get_language

from apps.accounts.user.models import User
from apps.countries.models import Country, State, City, School
from apps.countries.filters import CountryFilterSet, CityFilterSet, SchoolFilterSet


class CountryFilterTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpassword123",
            username="testuser",
            is_email_verified=True,
        )
        self.client.force_authenticate(user=self.user)

        self.usa = Country.objects.create(
            name="United States",
            iso3="USA",
            iso2="US",
            phone_code="1",
            capital="Washington D.C.",
            region="Americas",
            subregion="Northern America",
        )

        self.canada = Country.objects.create(
            name="Canada",
            iso3="CAN",
            iso2="CA",
            phone_code="1",
            capital="Ottawa",
            region="Americas",
            subregion="Northern America",
        )

        self.france = Country.objects.create(
            name="France",
            iso3="FRA",
            iso2="FR",
            phone_code="33",
            capital="Paris",
            region="Europe",
            subregion="Western Europe",
        )

        self.japan = Country.objects.create(
            name="Japan",
            iso3="JPN",
            iso2="JP",
            phone_code="81",
            capital="Tokyo",
            region="Asia",
            subregion="Eastern Asia",
        )

        self.california = State.objects.create(
            name="California",
            state_code="CA",
        )
        self.usa.states.add(self.california)

        self.ontario = State.objects.create(
            name="Ontario",
            state_code="ON",
        )
        self.canada.states.add(self.ontario)

        self.los_angeles = City.objects.create(
            name="Los Angeles",
            state=self.california,
            latitude=34.0522,
            longitude=-118.2437,
        )

        self.toronto = City.objects.create(
            name="Toronto",
            state=self.ontario,
            latitude=43.6532,
            longitude=-79.3832,
        )

        self.ucla = School.objects.create(
            name="UCLA",
            city=self.los_angeles,
            country=self.usa,
            info="University of California, Los Angeles",
        )

        self.toronto_university = School.objects.create(
            name="University of Toronto",
            city=self.toronto,
            country=self.canada,
            info="Top Canadian university",
        )

    def test_name_filter(self):
        url = reverse("countries:country-list")
        response = self.client.get(f"{url}?name=united")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 1)
        self.assertEqual(response.json()["results"][0]["name"], "الولايات المتحدة")

        response = self.client.get(f"{url}?name=an")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(response.json()["meta"]["count"], 2)

        response = self.client.get(f"{url}?name=xyz")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 0)

    def test_iso_filters(self):
        url = reverse("countries:country-list")
        response = self.client.get(f"{url}?iso3=USA")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 1)
        self.assertEqual(response.json()["results"][0]["name"], "الولايات المتحدة")

        response = self.client.get(f"{url}?iso2=JP")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 1)
        self.assertEqual(response.json()["results"][0]["name"], "اليابان")

    def test_region_filter(self):
        url = reverse("countries:country-list")
        response = self.client.get(f"{url}?region=Americas")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 2)

        response = self.client.get(f"{url}?region=Europe")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 1)
        self.assertEqual(response.json()["results"][0]["name"], "فرنسا")

    def test_subregion_filter(self):
        url = reverse("countries:country-list")
        response = self.client.get(f"{url}?subregion=Northern America")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 2)

        response = self.client.get(f"{url}?subregion=Western Europe")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 1)
        self.assertEqual(response.json()["results"][0]["name"], "فرنسا")

    def test_search_filter(self):
        url = reverse("countries:country-list")
        response = self.client.get(f"{url}?search=united")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 1)
        self.assertEqual(response.json()["results"][0]["name"], "الولايات المتحدة")

        response = self.client.get(f"{url}?search=tokyo")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 1)
        self.assertEqual(response.json()["results"][0]["name"], "اليابان")

        response = self.client.get(f"{url}?search=america")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsNotNone(response.json()["meta"]["count"])

    def test_has_states_filter(self):
        url = reverse("countries:country-list")
        response = self.client.get(f"{url}?has_states=true")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 2)

        response = self.client.get(f"{url}?has_states=false")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 2)

    def test_has_cities_filter(self):
        url = reverse("countries:country-list")
        response = self.client.get(f"{url}?has_cities=true")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 2)

        response = self.client.get(f"{url}?has_cities=false")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 2)

    def test_has_schools_filter(self):
        url = reverse("countries:country-list")
        response = self.client.get(f"{url}?has_schools=true")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 2)

        response = self.client.get(f"{url}?has_schools=false")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 2)

    def test_combined_filters(self):
        url = reverse("countries:country-list")
        response = self.client.get(f"{url}?region=Americas&has_states=true")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 2)

        response = self.client.get(f"{url}?region=Americas&has_states=true&name=united")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 1)
        self.assertEqual(response.json()["results"][0]["name"], "الولايات المتحدة")


class CityFilterTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpassword123",
            username="testuser",
            is_email_verified=True,
        )
        self.client.force_authenticate(user=self.user)

        self.usa = Country.objects.create(
            name="United States",
            iso3="USA",
            iso2="US",
            phone_code="1",
            capital="Washington D.C.",
            region="Americas",
            subregion="Northern America",
        )

        self.california = State.objects.create(
            name="California",
            state_code="CA",
        )
        self.usa.states.add(self.california)

        self.new_york = State.objects.create(
            name="New York",
            state_code="NY",
        )
        self.usa.states.add(self.new_york)

        self.los_angeles = City.objects.create(
            name="Los Angeles",
            state=self.california,
            latitude=34.0522,
            longitude=-118.2437,
        )

        self.san_francisco = City.objects.create(
            name="San Francisco",
            state=self.california,
            latitude=37.7749,
            longitude=-122.4194,
        )

        self.new_york_city = City.objects.create(
            name="New York City",
            state=self.new_york,
            latitude=40.7128,
            longitude=-74.0060,
        )

        self.ucla = School.objects.create(
            name="UCLA",
            city=self.los_angeles,
            country=self.usa,
            info="University of California, Los Angeles",
        )

        self.nyu = School.objects.create(
            name="NYU",
            city=self.new_york_city,
            country=self.usa,
            info="New York University",
        )

    def test_name_filter(self):
        url = reverse("countries:country-cities", kwargs={"id": self.usa.id})
        response = self.client.get(f"{url}?name=angeles")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        if "meta" in response.json():
            self.assertEqual(response.json()["meta"]["count"], 1)
            self.assertEqual(response.json()["results"][0]["name"], "Los Angeles")
        else:
            cities = [
                city for city in response.json() if "angeles" in city["name"].lower()
            ]
            self.assertEqual(len(cities), 1)
            self.assertEqual(cities[0]["name"], "Los Angeles")

        response = self.client.get(f"{url}?name=new")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        if "meta" in response.json():
            self.assertEqual(response.json()["meta"]["count"], 1)
            self.assertEqual(response.json()["results"][0]["name"], "New York City")
        else:
            cities = [city for city in response.json() if "new" in city["name"].lower()]
            self.assertEqual(len(cities), 1)
            self.assertEqual(cities[0]["name"], "New York City")

    def test_state_filter(self):
        url = reverse("countries:country-cities", kwargs={"id": self.usa.id})
        response = self.client.get(f"{url}?state={self.california.id}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        if "meta" in response.json():
            self.assertEqual(response.json()["meta"]["count"], 2)
        else:
            self.assertEqual(len(response.json()), 2)

        response = self.client.get(f"{url}?state_name=california")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        if "meta" in response.json():
            self.assertEqual(response.json()["meta"]["count"], 2)
        else:
            self.assertEqual(len(response.json()), 2)

    def test_latitude_longitude_filters(self):
        url = reverse("countries:country-cities", kwargs={"id": self.usa.id})
        response = self.client.get(f"{url}?latitude_gt=35")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        if "meta" in response.json():
            self.assertEqual(response.json()["meta"]["count"], 2)
        else:
            self.assertEqual(len(response.json()), 2)

        response = self.client.get(f"{url}?longitude_lt=-70")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        if "meta" in response.json():
            self.assertIsNotNone(response.json()["meta"]["count"])
        else:
            self.assertIsNotNone(response.json())

    def test_has_schools_filter(self):
        url = reverse("countries:country-cities", kwargs={"id": self.usa.id})
        response = self.client.get(f"{url}?has_schools=true")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        if "meta" in response.json():
            self.assertEqual(response.json()["meta"]["count"], 2)
        else:
            self.assertEqual(len(response.json()), 2)

        response = self.client.get(f"{url}?has_schools=false")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        if "meta" in response.json():
            self.assertEqual(response.json()["meta"]["count"], 1)
        else:
            self.assertEqual(len(response.json()), 1)

    def test_search_filter(self):
        url = reverse("countries:country-cities", kwargs={"id": self.usa.id})
        response = self.client.get(f"{url}?search=angeles")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        if "meta" in response.json():
            self.assertEqual(response.json()["meta"]["count"], 1)
            self.assertEqual(response.json()["results"][0]["name"], "Los Angeles")
        else:
            cities = [
                city for city in response.json() if "angeles" in city["name"].lower()
            ]
            self.assertEqual(len(cities), 1)
            self.assertEqual(cities[0]["name"], "Los Angeles")

        response = self.client.get(f"{url}?search=california")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        if "meta" in response.json():
            self.assertEqual(response.json()["meta"]["count"], 2)
        else:
            self.assertEqual(len(response.json()), 2)


class SchoolFilterTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpassword123",
            username="testuser",
            is_email_verified=True,
        )
        self.client.force_authenticate(user=self.user)

        self.usa = Country.objects.create(
            name="United States",
            iso3="USA",
            iso2="US",
            phone_code="1",
            capital="Washington D.C.",
            region="Americas",
            subregion="Northern America",
        )

        self.uk = Country.objects.create(
            name="United Kingdom",
            iso3="GBR",
            iso2="GB",
            phone_code="44",
            capital="London",
            region="Europe",
            subregion="Northern Europe",
        )

        self.california = State.objects.create(
            name="California",
            state_code="CA",
        )
        self.usa.states.add(self.california)

        self.massachusetts = State.objects.create(
            name="Massachusetts",
            state_code="MA",
        )
        self.usa.states.add(self.massachusetts)

        self.england = State.objects.create(
            name="England",
            state_code="ENG",
        )
        self.uk.states.add(self.england)

        self.los_angeles = City.objects.create(
            name="Los Angeles",
            state=self.california,
            latitude=34.0522,
            longitude=-118.2437,
        )

        self.cambridge = City.objects.create(
            name="Cambridge",
            state=self.massachusetts,
            latitude=42.3736,
            longitude=-71.1097,
        )

        self.london = City.objects.create(
            name="London",
            state=self.england,
            latitude=51.5074,
            longitude=-0.1278,
        )

        self.ucla = School.objects.create(
            name="UCLA",
            city=self.los_angeles,
            country=self.usa,
            info="University of California, Los Angeles",
        )

        self.harvard = School.objects.create(
            name="Harvard University",
            city=self.cambridge,
            country=self.usa,
            info="Ivy League research university",
        )

        self.imperial = School.objects.create(
            name="Imperial College London",
            city=self.london,
            country=self.uk,
            info="Public research university in London",
        )

    def test_name_filter(self):
        url = reverse("countries:school-list")
        response = self.client.get(f"{url}?name=harvard")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 1)
        self.assertEqual(response.json()["results"][0]["name"], "Harvard University")

        response = self.client.get(f"{url}?name=university")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(response.json()["meta"]["count"], 1)

    def test_city_filter(self):
        url = reverse("countries:school-list")
        response = self.client.get(f"{url}?city={self.los_angeles.id}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 1)
        self.assertEqual(response.json()["results"][0]["name"], "UCLA")

        response = self.client.get(f"{url}?city_name=london")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 1)
        self.assertEqual(
            response.json()["results"][0]["name"], "Imperial College London"
        )

    def test_country_filter(self):
        url = reverse("countries:school-list")
        response = self.client.get(f"{url}?country={self.usa.id}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 2)

        response = self.client.get(f"{url}?country_name=united kingdom")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 1)
        self.assertEqual(
            response.json()["results"][0]["name"], "Imperial College London"
        )

    def test_state_filter(self):
        url = reverse("countries:school-list")
        response = self.client.get(f"{url}?state={self.california.id}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 1)
        self.assertEqual(response.json()["results"][0]["name"], "UCLA")

        response = self.client.get(f"{url}?state_name=england")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 1)
        self.assertEqual(
            response.json()["results"][0]["name"], "Imperial College London"
        )

    def test_info_contains_filter(self):
        url = reverse("countries:school-list")
        response = self.client.get(f"{url}?info_contains=ivy")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 1)
        self.assertEqual(response.json()["results"][0]["name"], "Harvard University")

        response = self.client.get(f"{url}?info_contains=research")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(response.json()["meta"]["count"], 1)

    def test_search_filter(self):
        url = reverse("countries:school-list")
        response = self.client.get(f"{url}?search=london")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 1)
        self.assertEqual(
            response.json()["results"][0]["name"], "Imperial College London"
        )

        response = self.client.get(f"{url}?search=university")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(response.json()["meta"]["count"], 1)

    def test_combined_filters(self):
        url = reverse("countries:school-list")
        response = self.client.get(f"{url}?country={self.usa.id}&name=harvard")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 1)
        self.assertEqual(response.json()["results"][0]["name"], "Harvard University")

        response = self.client.get(f"{url}?country={self.usa.id}&name=imperial")
        if response.status_code == status.HTTP_200_OK:
            self.assertEqual(response.json()["meta"]["count"], 0)
        else:
            self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class CountryFilterSetTestCase(TestCase):
    """Test case for CountryFilterSet."""

    def setUp(self):
        """Set up test case."""
        self.filter_set = CountryFilterSet()

        self.original_language = get_language()

    def tearDown(self):
        """Clean up after test case."""

        activate(self.original_language)

    def test_filter_name_english(self):
        """Test filter_name method with English name."""

        queryset = mock.MagicMock()
        filtered_queryset = mock.MagicMock()
        queryset.filter.return_value = filtered_queryset

        result = self.filter_set.filter_name(queryset, "name", "United States")

        queryset.filter.assert_called_once_with(name__icontains="United States")

        self.assertEqual(result, filtered_queryset)

    def test_filter_name_empty_value(self):
        """Test filter_name method with empty value."""

        queryset = mock.MagicMock()

        result = self.filter_set.filter_name(queryset, "name", "")

        queryset.filter.assert_not_called()

        self.assertEqual(result, queryset)

    @mock.patch("apps.countries.filters.get_country_translation")
    def test_filter_name_arabic(self, mock_get_translation):
        """Test filter_name method with Arabic name."""

        mock_get_translation.return_value = "الولايات المتحدة"

        queryset = mock.MagicMock()
        english_matches = mock.MagicMock()
        queryset.filter.return_value = english_matches

        all_countries = mock.MagicMock()
        country1 = mock.MagicMock()
        country1.pk = 1
        country1.name = "United States"
        all_countries.__iter__.return_value = [country1]

        queryset.exclude.return_value = all_countries

        arabic_matches = mock.MagicMock()
        queryset.filter.side_effect = [english_matches, arabic_matches]

        result = self.filter_set.filter_name(queryset, "name", "الولايات")

        mock_get_translation.assert_called_once_with("United States", "ar")

        self.assertEqual(queryset.filter.call_count, 2)

        self.assertEqual(result, english_matches | arabic_matches)

    @mock.patch("apps.countries.filters.get_country_translation")
    def test_filter_search(self, mock_get_translation):
        """Test filter_search method."""

        mock_get_translation.return_value = "ألمانيا"

        queryset = mock.MagicMock()
        standard_matches = mock.MagicMock()
        queryset.filter.return_value = standard_matches

        self.filter_set.filter_name = mock.MagicMock()
        name_matches = mock.MagicMock()
        self.filter_set.filter_name.return_value = name_matches

        result = self.filter_set.filter_search(queryset, "search", "Germany")

        self.filter_set.filter_name.assert_called_once_with(queryset, "name", "Germany")

        self.assertEqual(
            result, (standard_matches | name_matches).distinct.return_value
        )
