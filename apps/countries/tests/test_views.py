from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from django.core.cache import cache
from unittest.mock import patch, MagicMock
import uuid
import unittest

from apps.countries.models import Country, State, City, School
from apps.accounts.user.models import User


class CountryViewsTestCase(APITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpassword123",
            username="testuser",
            is_email_verified=True,
        )

        cls.admin_user = User.objects.create_superuser(
            email="<EMAIL>",
            password="adminpass123",
            username="admin",
            is_email_verified=True,
        )

        cls.country1 = Country.objects.create(
            name="Test Country 1",
            iso2="TC",
            iso3="TCY",
            phone_code="123",
            capital="Test Capital",
            region="Test Region",
            subregion="Test Subregion",
        )

        cls.country2 = Country.objects.create(
            name="Test Country 2",
            iso2="T2",
            iso3="TC2",
            phone_code="456",
            capital="Test Capital 2",
            region="Test Region",
            subregion="Test Subregion 2",
        )

        cls.state1 = State.objects.create(
            name="Test State 1",
            state_code="TS1",
            latitude=10.0,
            longitude=20.0,
        )

        cls.state2 = State.objects.create(
            name="Test State 2",
            state_code="TS2",
            latitude=30.0,
            longitude=40.0,
        )

        cls.country1.states.add(cls.state1)
        cls.country2.states.add(cls.state2)

        cls.city1 = City.objects.create(
            name="Test City 1",
            state=cls.state1,
            latitude=15.0,
            longitude=25.0,
        )

        cls.city2 = City.objects.create(
            name="Test City 2",
            state=cls.state2,
            latitude=35.0,
            longitude=45.0,
        )

        cls.school1 = School.objects.create(
            name="Test School 1",
            country=cls.country1,
            city=cls.city1,
        )

        cls.school2 = School.objects.create(
            name="Test School 2",
            country=cls.country2,
            city=cls.city2,
        )

    def setUp(self):
        self.client = APIClient()
        cache.clear()

    def test_country_list_view_authenticated(self):
        """Test that authenticated users can access the country list view"""
        self.client.force_authenticate(user=self.user)
        url = reverse("countries:country-list")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 2)

    def test_country_list_view_full_details(self):
        """Test that the full parameter returns detailed country information"""
        self.client.force_authenticate(user=self.user)
        url = reverse("countries:country-list") + "?full=true"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 2)
        self.assertIn("states", response.json()["results"][0])

    def test_country_list_view_no_pagination(self):
        """Test that pagination can be disabled"""
        self.client.force_authenticate(user=self.user)
        url = reverse("countries:country-list") + "?pagination=false"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsInstance(response.json(), list)
        self.assertGreaterEqual(len(response.json()), 2)

    def test_country_list_view_filtering(self):
        """Test filtering countries by region"""
        self.client.force_authenticate(user=self.user)
        url = reverse("countries:country-list") + f"?region={self.country1.region}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 2)

    def test_country_list_view_search(self):
        """Test searching countries by name"""
        self.client.force_authenticate(user=self.user)
        url = reverse("countries:country-list") + "?search=Country 1"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 1)
        self.assertEqual(response.json()["results"][0]["name"], "Test Country 1")

    def test_country_detail_view(self):
        """Test retrieving a specific country"""
        self.client.force_authenticate(user=self.user)
        url = reverse("countries:country-detail", kwargs={"id": self.country1.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["name"], "Test Country 1")

    def test_country_detail_view_full(self):
        """Test retrieving a specific country with full details"""
        self.client.force_authenticate(user=self.user)
        url = (
            reverse("countries:country-detail", kwargs={"id": self.country1.id})
            + "?full=true"
        )
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["name"], "Test Country 1")
        self.assertIn("states", response.json())

    def test_city_list_view(self):
        """Test retrieving cities for a specific country"""
        self.client.force_authenticate(user=self.user)
        url = reverse("countries:country-cities", kwargs={"id": self.country1.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        if "meta" in response.json():
            self.assertEqual(response.json()["meta"]["count"], 1)
        else:
            self.assertEqual(len(response.json()), 1)

    def test_city_list_view_full(self):
        """Test retrieving cities with full details"""
        self.client.force_authenticate(user=self.user)
        url = (
            reverse("countries:country-cities", kwargs={"id": self.country1.id})
            + "?full=true"
        )
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        if hasattr(response, "data") and isinstance(response.data, list):
            self.assertEqual(len(response.data), 1)
        elif "meta" in response.json():
            self.assertEqual(response.json()["meta"]["count"], 1)
        else:
            self.assertEqual(len(response.json()), 1)

    def test_city_list_view_invalid_country(self):
        """Test retrieving cities for a non-existent country"""
        self.client.force_authenticate(user=self.user)
        url = reverse("countries:country-cities", kwargs={"id": uuid.uuid4()})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        if "meta" in response.json():
            self.assertEqual(response.json()["meta"]["count"], 0)
        else:
            self.assertEqual(len(response.json()), 0)

    def test_school_list_view(self):
        """Test retrieving schools"""
        self.client.force_authenticate(user=self.user)
        url = reverse("countries:school-list")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 2)

    def test_school_list_view_full(self):
        """Test retrieving schools with full details"""
        self.client.force_authenticate(user=self.user)
        url = reverse("countries:school-list") + "?full=true"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 2)
        self.assertIn("country_name", response.json()["results"][0])

    def test_school_list_view_filter_by_country(self):
        """Test filtering schools by country"""
        self.client.force_authenticate(user=self.user)
        url = reverse("countries:school-list") + f"?country={self.country1.id}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 1)
        self.assertEqual(response.json()["results"][0]["name"], "Test School 1")

    def test_school_list_view_filter_by_city(self):
        """Test filtering schools by city"""
        self.client.force_authenticate(user=self.user)
        url = reverse("countries:school-list") + f"?city={self.city1.id}"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 1)
        self.assertEqual(response.json()["results"][0]["name"], "Test School 1")

    def test_school_list_view_search(self):
        """Test searching schools by name"""
        self.client.force_authenticate(user=self.user)
        url = reverse("countries:school-list") + "?search=School 1"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["meta"]["count"], 1)
        self.assertEqual(response.json()["results"][0]["name"], "Test School 1")

    def test_school_detail_view(self):
        """Test retrieving a specific school"""
        self.client.force_authenticate(user=self.user)
        url = reverse("countries:school-detail", kwargs={"pk": self.school1.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["name"], "Test School 1")
        self.assertIn("country_name", response.json())

    def test_country_detail_view_not_found(self):
        """Test retrieving a non-existent country"""
        self.client.force_authenticate(user=self.user)
        url = reverse("countries:country-detail", kwargs={"id": uuid.uuid4()})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class CountryCachingTestCase(APITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpassword123",
            username="testuser",
            is_email_verified=True,
        )

        cls.country = Country.objects.create(
            name="Test Country",
            iso2="TC",
            iso3="TCY",
            phone_code="123",
            capital="Test Capital",
            region="Test Region",
            subregion="Test Subregion",
        )

        cls.state = State.objects.create(
            name="Test State",
            state_code="TS",
            latitude=10.0,
            longitude=20.0,
        )

        cls.country.states.add(cls.state)

        cls.city = City.objects.create(
            name="Test City",
            state=cls.state,
            latitude=15.0,
            longitude=25.0,
        )

        cls.school = School.objects.create(
            name="Test School",
            country=cls.country,
            city=cls.city,
        )

    def setUp(self):
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        cache.clear()

    @unittest.skip("Caching is disabled in test environment")
    def test_country_list_caching(self):
        """Test that country list is cached"""
        url = reverse("countries:country-list") + "?pagination=false"

        with patch(
            "django.db.models.query.QuerySet.all", return_value=Country.objects.all()
        ) as mock_queryset:
            response1 = self.client.get(url)
            self.assertEqual(response1.status_code, status.HTTP_200_OK)
            self.assertTrue(mock_queryset.called)

        with patch(
            "django.db.models.query.QuerySet.all", return_value=Country.objects.all()
        ) as mock_queryset:
            response2 = self.client.get(url)
            self.assertEqual(response2.status_code, status.HTTP_200_OK)
            self.assertFalse(mock_queryset.called)

        self.assertEqual(response1.data, response2.data)

    @unittest.skip("Caching is disabled in test environment")
    def test_country_detail_caching(self):
        """Test that country detail is cached"""
        url = reverse("countries:country-detail", kwargs={"id": self.country.id})

        with patch(
            "django.db.models.query.QuerySet.get", return_value=self.country
        ) as mock_get:
            response1 = self.client.get(url)
            self.assertEqual(response1.status_code, status.HTTP_200_OK)
            self.assertTrue(mock_get.called)

        with patch(
            "django.db.models.query.QuerySet.get", return_value=self.country
        ) as mock_get:
            response2 = self.client.get(url)
            self.assertEqual(response2.status_code, status.HTTP_200_OK)
            self.assertFalse(mock_get.called)

        self.assertEqual(response1.data, response2.data)

    @unittest.skip("Caching is disabled in test environment")
    def test_city_list_caching(self):
        """Test that city list is cached"""
        url = (
            reverse("countries:country-cities", kwargs={"id": self.country.id})
            + "?pagination=false"
        )

        with patch(
            "django.db.models.query.QuerySet.filter",
            return_value=City.objects.filter(state__countries=self.country),
        ) as mock_filter:
            response1 = self.client.get(url)
            self.assertEqual(response1.status_code, status.HTTP_200_OK)
            self.assertTrue(mock_filter.called)

        with patch(
            "django.db.models.query.QuerySet.filter",
            return_value=City.objects.filter(state__countries=self.country),
        ) as mock_filter:
            response2 = self.client.get(url)
            self.assertEqual(response2.status_code, status.HTTP_200_OK)
            self.assertFalse(mock_filter.called)

        self.assertEqual(response1.data, response2.data)

    @unittest.skip("Caching is disabled in test environment")
    def test_school_list_caching(self):
        """Test that school list is cached"""
        url = reverse("countries:school-list") + "?pagination=false"

        with patch(
            "django.db.models.query.QuerySet.all", return_value=School.objects.all()
        ) as mock_queryset:
            response1 = self.client.get(url)
            self.assertEqual(response1.status_code, status.HTTP_200_OK)
            self.assertTrue(mock_queryset.called)

        with patch(
            "django.db.models.query.QuerySet.all", return_value=School.objects.all()
        ) as mock_queryset:
            response2 = self.client.get(url)
            self.assertEqual(response2.status_code, status.HTTP_200_OK)
            self.assertFalse(mock_queryset.called)

        self.assertEqual(response1.data, response2.data)

    @unittest.skip("Caching is disabled in test environment")
    def test_school_detail_caching(self):
        """Test that school detail is cached"""
        url = reverse("countries:school-detail", kwargs={"pk": self.school.id})

        with patch(
            "django.db.models.query.QuerySet.get", return_value=self.school
        ) as mock_get:
            response1 = self.client.get(url)
            self.assertEqual(response1.status_code, status.HTTP_200_OK)
            self.assertTrue(mock_get.called)

        with patch(
            "django.db.models.query.QuerySet.get", return_value=self.school
        ) as mock_get:
            response2 = self.client.get(url)
            self.assertEqual(response2.status_code, status.HTTP_200_OK)
            self.assertFalse(mock_get.called)

        self.assertEqual(response1.data, response2.data)

    @unittest.skip("Caching is disabled in test environment")
    def test_cache_invalidation(self):
        """Test that cache is invalidated when a model is updated or deleted"""
        pass
