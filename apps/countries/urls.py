from django.urls import path

from apps.countries.views.city_list import CityListView
from apps.countries.views.country_list import CountryListView
from apps.countries.views.country_detail import CountryDetailView
from apps.countries.views.school_list import SchoolListView
from apps.countries.views.school_detail import SchoolDetailView

app_name = "countries"

urlpatterns = [
    path("", CountryListView.as_view(), name="country-list"),
    path("<uuid:id>/", CountryDetailView.as_view(), name="country-detail"),
    path("<uuid:id>/cities/", CityListView.as_view(), name="country-cities"),
    path("schools/", SchoolListView.as_view(), name="school-list"),
    path("schools/<uuid:pk>/", SchoolDetailView.as_view(), name="school-detail"),
]
