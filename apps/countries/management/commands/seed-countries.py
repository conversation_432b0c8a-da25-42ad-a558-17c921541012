import json
from django.core.management.base import BaseCommand
import logging
from django.db import transaction
from apps.countries.models import Country, State, City, TimeZone

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Load country data from a JSON file"

    def handle(self, *args, **kwargs):
        if Country.objects.exists():
            self.stdout.write(
                self.style.SUCCESS("Countries already exist. Skipping seeding.")
            )
            return

        file_path = "apps/countries/static/countries.json"
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                countries = json.load(f)
        except FileNotFoundError:
            logger.error(f"File not found: {file_path}")
            self.stderr.write(self.style.ERROR(f"File not found: {file_path}"))
            return
        except json.JSONDecodeError:
            logger.error("Failed to decode JSON.")
            self.stderr.write(self.style.ERROR("Failed to decode JSON."))
            return

        with transaction.atomic():
            all_timezones = {}
            for country_data in countries:
                for tz_data in country_data.get("timezones", []):
                    zone_name = tz_data.get("zoneName", "")
                    if zone_name and zone_name not in all_timezones:
                        all_timezones[zone_name] = {
                            "gmt_offset": tz_data.get("gmtOffset", 0),
                            "gmt_offset_name": tz_data.get("gmtOffsetName", ""),
                            "abbreviation": tz_data.get("abbreviation", ""),
                            "tz_name": tz_data.get("tzName", ""),
                        }

            timezone_objects = []
            for zone_name, tz_data in all_timezones.items():
                timezone_objects.append(
                    TimeZone(
                        zone_name=zone_name,
                        gmt_offset=tz_data["gmt_offset"],
                        gmt_offset_name=tz_data["gmt_offset_name"],
                        abbreviation=tz_data["abbreviation"],
                        tz_name=tz_data["tz_name"],
                    )
                )

            created_timezones = {}
            if timezone_objects:
                TimeZone.objects.bulk_create(timezone_objects, ignore_conflicts=True)

                for tz in TimeZone.objects.all():
                    created_timezones[tz.zone_name] = tz

            all_states = {}
            for country_data in countries:
                for state_data in country_data.get("states", []):
                    state_name = state_data.get("name", "")
                    state_code = state_data.get("state_code", "")
                    if state_name and state_code:
                        key = f"{state_name}_{state_code}"
                        if key not in all_states:
                            all_states[key] = {
                                "name": state_name,
                                "state_code": state_code,
                                "latitude": state_data.get("latitude", None),
                                "longitude": state_data.get("longitude", None),
                                "cities": state_data.get("cities", []),
                                "countries": [],
                            }
                        all_states[key]["countries"].append(
                            country_data.get("iso3", "")
                        )

            state_objects = []
            for state_data in all_states.values():
                state_objects.append(
                    State(
                        name=state_data["name"],
                        state_code=state_data["state_code"],
                        latitude=state_data["latitude"],
                        longitude=state_data["longitude"],
                    )
                )

            created_states = {}
            if state_objects:
                State.objects.bulk_create(state_objects, ignore_conflicts=True)

                for state in State.objects.all():
                    created_states[f"{state.name}_{state.state_code}"] = state

            country_objects = []
            country_timezone_relations = []
            country_state_relations = []

            for country_data in countries:
                iso3 = country_data.get("iso3", "")
                if not iso3:
                    continue

                country = Country(
                    name=country_data.get("name", ""),
                    iso2=country_data.get("iso2", ""),
                    iso3=iso3,
                    numeric_code=country_data.get("numeric_code", ""),
                    phone_code=country_data.get("phone_code", ""),
                    capital=country_data.get("capital", ""),
                    currency=country_data.get("currency", ""),
                    currency_name=country_data.get("currency_name", ""),
                    currency_symbol=country_data.get("currency_symbol", ""),
                    tld=country_data.get("tld", ""),
                    native=country_data.get("native", ""),
                    region=country_data.get("region", ""),
                    subregion=country_data.get("subregion", ""),
                    nationality=country_data.get("nationality", ""),
                    latitude=country_data.get("latitude", None),
                    longitude=country_data.get("longitude", None),
                    emoji=country_data.get("emoji", ""),
                )
                country_objects.append(country)

            Country.objects.bulk_create(country_objects, ignore_conflicts=True)

            created_countries = {}
            for country in Country.objects.all():
                created_countries[country.iso3] = country

            for country_data in countries:
                iso3 = country_data.get("iso3", "")
                if iso3 not in created_countries:
                    continue

                country = created_countries[iso3]

                for tz_data in country_data.get("timezones", []):
                    zone_name = tz_data.get("zoneName", "")
                    if zone_name in created_timezones:
                        country.timezones.add(created_timezones[zone_name])

                for state_data in country_data.get("states", []):
                    state_name = state_data.get("name", "")
                    state_code = state_data.get("state_code", "")
                    key = f"{state_name}_{state_code}"

                    if key in created_states:
                        country.states.add(created_states[key])

            city_objects = []
            for state_key, state_data in all_states.items():
                if state_key not in created_states:
                    continue

                state = created_states[state_key]

                for city_data in state_data["cities"]:
                    city_objects.append(
                        City(
                            name=city_data.get("name", ""),
                            state=state,
                            latitude=city_data.get("latitude", None),
                            longitude=city_data.get("longitude", None),
                        )
                    )

            batch_size = 1000
            for i in range(0, len(city_objects), batch_size):
                City.objects.bulk_create(
                    city_objects[i : i + batch_size], ignore_conflicts=True
                )
                self.stdout.write(
                    f"Created cities batch {i//batch_size + 1}/{(len(city_objects)-1)//batch_size + 1}"
                )

        self.stdout.write(self.style.SUCCESS("Country data loaded successfully"))
        self.stdout.write(
            self.style.SUCCESS(
                f"Created {Country.objects.count()} countries, {State.objects.count()} states, {City.objects.count()} cities, and {TimeZone.objects.count()} timezones"
            )
        )
