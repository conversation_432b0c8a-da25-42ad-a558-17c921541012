import django_filters
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from django_filters.rest_framework import (
    FilterSet,
    Char<PERSON>ilter,
    <PERSON><PERSON>anFilter,
    NumberFilter,
    UUIDFilter,
)
from django.utils.translation import get_language

from apps.countries.models import Country, City, School
from apps.countries.utils import get_country_translation


class CountryFilterSet(FilterSet):
    """
    FilterSet for Country model.

    Provides filtering capabilities for Country objects with various filter options.
    """

    name = CharFilter(
        method="filter_name",
        help_text=_(
            "Filter countries by name in English or Arabic (case-insensitive, contains)"
        ),
    )
    iso2 = CharFilter(
        field_name="iso2",
        lookup_expr="iexact",
        help_text=_("Filter countries by ISO2 code (case-insensitive, exact match)"),
    )
    iso3 = CharFilter(
        field_name="iso3",
        lookup_expr="iexact",
        help_text=_("Filter countries by ISO3 code (case-insensitive, exact match)"),
    )
    phone_code = CharFilter(
        field_name="phone_code",
        lookup_expr="icontains",
        help_text=_("Filter countries by phone code (case-insensitive, contains)"),
    )
    capital = CharFilter(
        field_name="capital",
        lookup_expr="icontains",
        help_text=_("Filter countries by capital city (case-insensitive, contains)"),
    )
    region = CharFilter(
        field_name="region",
        lookup_expr="iexact",
        help_text=_("Filter countries by region (case-insensitive, exact match)"),
    )
    subregion = CharFilter(
        field_name="subregion",
        lookup_expr="iexact",
        help_text=_("Filter countries by subregion (case-insensitive, exact match)"),
    )
    search = CharFilter(
        method="filter_search",
        help_text=_(
            "Search across name (in English or Arabic), ISO codes, capital, and region fields"
        ),
    )
    has_states = BooleanFilter(
        method="filter_has_states",
        help_text=_(
            "Filter countries that have states (true) or don't have states (false)"
        ),
    )
    has_cities = BooleanFilter(
        method="filter_has_cities",
        help_text=_(
            "Filter countries that have cities (true) or don't have cities (false)"
        ),
    )
    has_schools = BooleanFilter(
        method="filter_has_schools",
        help_text=_(
            "Filter countries that have schools (true) or don't have schools (false)"
        ),
    )

    def filter_name(self, queryset, name, value):
        """
        Filter countries by name in English or Arabic.

        This method checks if the provided value matches either the English name
        or its Arabic translation. It optimizes the search by using Django's Q objects
        and avoiding unnecessary database calls.
        """
        if not value:
            return queryset

        english_matches = queryset.filter(name__icontains=value)

        current_language = get_language()

        if current_language == "en" and queryset.count() == english_matches.count():
            return english_matches

        remaining_countries = queryset.exclude(
            pk__in=english_matches.values_list("pk", flat=True)
        )

        arabic_matches_ids = []
        for country in remaining_countries:
            arabic_name = get_country_translation(country.name, "ar")

            if value.lower() in arabic_name.lower():
                arabic_matches_ids.append(country.pk)

        if current_language == "ar" and not arabic_matches_ids:
            all_countries = queryset.all()

            for country in all_countries:
                if value.lower() in country.name.lower():
                    arabic_matches_ids.append(country.pk)

        if arabic_matches_ids:
            arabic_matches = queryset.filter(pk__in=arabic_matches_ids)
            return english_matches | arabic_matches

        return english_matches

    def filter_search(self, queryset, name, value):
        """
        Search across multiple fields: name (in English or Arabic), ISO codes, capital, and region.

        This method performs a comprehensive search across various fields including
        multilingual name search.
        """
        if not value:
            return queryset

        standard_matches = queryset.filter(
            Q(iso2__icontains=value)
            | Q(iso3__icontains=value)
            | Q(capital__icontains=value)
            | Q(region__icontains=value)
            | Q(subregion__icontains=value)
            | Q(phone_code__icontains=value)
        )

        name_matches = self.filter_name(queryset, "name", value)

        return (standard_matches | name_matches).distinct()

    def filter_has_states(self, queryset, name, value):
        """
        Filter countries that have states (true) or don't have states (false).
        """
        if value is None:
            return queryset
        if value:
            return queryset.filter(states__isnull=False).distinct()
        return queryset.filter(states__isnull=True)

    def filter_has_cities(self, queryset, name, value):
        """
        Filter countries that have cities (true) or don't have cities (false).
        """
        if value is None:
            return queryset
        if value:
            return queryset.filter(states__cities__isnull=False).distinct()
        return queryset.exclude(states__cities__isnull=False)

    def filter_has_schools(self, queryset, name, value):
        """
        Filter countries that have schools (true) or don't have schools (false).
        """
        if value is None:
            return queryset
        if value:
            return queryset.filter(schools__isnull=False).distinct()
        return queryset.filter(schools__isnull=True)

    class Meta:
        model = Country
        fields = [
            "name",
            "iso2",
            "iso3",
            "phone_code",
            "capital",
            "region",
            "subregion",
            "search",
            "has_states",
            "has_cities",
            "has_schools",
        ]


class CityFilterSet(FilterSet):
    """
    FilterSet for City model.

    Provides filtering capabilities for City objects with various filter options.
    """

    name = CharFilter(
        field_name="name",
        lookup_expr="icontains",
        help_text=_("Filter cities by name (case-insensitive, contains)"),
    )
    state = UUIDFilter(field_name="state__id", help_text=_("Filter cities by state ID"))
    state_name = CharFilter(
        field_name="state__name",
        lookup_expr="icontains",
        help_text=_("Filter cities by state name (case-insensitive, contains)"),
    )
    country = UUIDFilter(
        field_name="state__countries__id", help_text=_("Filter cities by country ID")
    )
    country_name = CharFilter(
        field_name="state__countries__name",
        lookup_expr="icontains",
        help_text=_("Filter cities by country name (case-insensitive, contains)"),
    )
    search = CharFilter(
        method="filter_search",
        help_text=_("Search across name, state name, and country name fields"),
    )
    has_schools = BooleanFilter(
        method="filter_has_schools",
        help_text=_(
            "Filter cities that have schools (true) or don't have schools (false)"
        ),
    )
    latitude_gt = NumberFilter(
        field_name="latitude",
        lookup_expr="gt",
        help_text=_("Filter cities with latitude greater than the specified value"),
    )
    latitude_lt = NumberFilter(
        field_name="latitude",
        lookup_expr="lt",
        help_text=_("Filter cities with latitude less than the specified value"),
    )
    longitude_gt = NumberFilter(
        field_name="longitude",
        lookup_expr="gt",
        help_text=_("Filter cities with longitude greater than the specified value"),
    )
    longitude_lt = NumberFilter(
        field_name="longitude",
        lookup_expr="lt",
        help_text=_("Filter cities with longitude less than the specified value"),
    )

    def filter_search(self, queryset, name, value):
        """
        Search across multiple fields: name, state name, and country name.
        """
        if not value:
            return queryset
        return queryset.filter(
            Q(name__icontains=value)
            | Q(state__name__icontains=value)
            | Q(state__countries__name__icontains=value)
        ).distinct()

    def filter_has_schools(self, queryset, name, value):
        """
        Filter cities that have schools (true) or don't have schools (false).
        """
        if value is None:
            return queryset
        if value:
            return queryset.filter(schools__isnull=False).distinct()
        return queryset.filter(schools__isnull=True)

    class Meta:
        model = City
        fields = [
            "name",
            "state",
            "state_name",
            "country",
            "country_name",
            "search",
            "has_schools",
            "latitude_gt",
            "latitude_lt",
            "longitude_gt",
            "longitude_lt",
        ]


class SchoolFilterSet(FilterSet):
    """
    FilterSet for School model.

    Provides filtering capabilities for School objects with various filter options.
    """

    name = CharFilter(
        field_name="name",
        lookup_expr="icontains",
        help_text=_("Filter schools by name (case-insensitive, contains)"),
    )
    city = UUIDFilter(field_name="city__id", help_text=_("Filter schools by city ID"))
    city_name = CharFilter(
        field_name="city__name",
        lookup_expr="icontains",
        help_text=_("Filter schools by city name (case-insensitive, contains)"),
    )
    country = UUIDFilter(
        field_name="country__id", help_text=_("Filter schools by country ID")
    )
    country_name = CharFilter(
        field_name="country__name",
        lookup_expr="icontains",
        help_text=_("Filter schools by country name (case-insensitive, contains)"),
    )
    state = UUIDFilter(
        field_name="city__state__id", help_text=_("Filter schools by state ID")
    )
    state_name = CharFilter(
        field_name="city__state__name",
        lookup_expr="icontains",
        help_text=_("Filter schools by state name (case-insensitive, contains)"),
    )
    search = CharFilter(
        method="filter_search",
        help_text=_(
            "Search across name, city name, state name, and country name fields"
        ),
    )
    info_contains = CharFilter(
        field_name="info",
        lookup_expr="icontains",
        help_text=_("Filter schools by info text (case-insensitive, contains)"),
    )

    def filter_search(self, queryset, name, value):
        """
        Search across multiple fields: name, city name, state name, and country name.
        """
        if not value:
            return queryset
        return queryset.filter(
            Q(name__icontains=value)
            | Q(city__name__icontains=value)
            | Q(city__state__name__icontains=value)
            | Q(country__name__icontains=value)
            | Q(info__icontains=value)
        ).distinct()

    class Meta:
        model = School
        fields = [
            "name",
            "city",
            "city_name",
            "country",
            "country_name",
            "state",
            "state_name",
            "search",
            "info_contains",
        ]
