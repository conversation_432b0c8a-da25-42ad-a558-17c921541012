"""Translation management service."""

import os
import json
import subprocess
import polib
import logging
from filelock import FileLock
from googletrans import Translator
from typing import Dict, List, Optional
from django.core.cache import cache
from django.conf import settings
from concurrent.futures import ThreadPoolExecutor, as_completed
from functools import lru_cache

logger = logging.getLogger(__name__)

# Get base locale directory from Django settings
LOCALE_DIR = settings.LOCALE_PATHS[0] if settings.LOCALE_PATHS else "locale"
TRANSLATION_CACHE_DIR = os.path.join(LOCALE_DIR, "translation_cache")
TRANSLATION_LOCK_FILE = os.path.join(LOCALE_DIR, "translation.lock")
CACHE_TIMEOUT = 60 * 60 * 24  # 24 hours


class TranslationError(Exception):
    """Base exception for translation errors."""

    pass


class TranslationCacheError(TranslationError):
    """Exception raised for translation cache errors."""

    pass


class TranslationFileError(TranslationError):
    """Exception raised for translation file errors."""

    pass


class TranslationManager:
    """Manages translation operations including caching and file handling."""

    def __init__(self):
        """Initialize the translation manager with Google Translate."""
        self.translator = Translator()
        os.makedirs(TRANSLATION_CACHE_DIR, exist_ok=True)

    @lru_cache(maxsize=1000)
    def _get_cache_key(self, text: str, dest_language: str) -> str:
        """Generate a cache key for a text and language combination."""
        return f"translation_{dest_language}_{hash(text)}"

    def _normalize_arabic(self, text: str) -> str:
        """Normalize Arabic text by removing diacritics."""
        # Remove diacritics (harakat)
        diacritics = ["ً", "ٌ", "ٍ", "َ", "ُ", "ِ", "ّ", "ْ"]
        for diacritic in diacritics:
            text = text.replace(diacritic, "")
        return text

    def load_translation_cache(self, dest_language: str) -> Dict[str, str]:
        """
        Load the translation cache for the specific language from a JSON file.

        Args:
            dest_language: The target language code.

        Returns:
            A dictionary of cached translations.
        """
        cache_file = os.path.join(TRANSLATION_CACHE_DIR, f"{dest_language}.json")
        if os.path.exists(cache_file):
            try:
                with open(cache_file, "r", encoding="utf-8") as file:
                    return json.load(file)
            except json.JSONDecodeError as e:
                logger.error(f"Translation cache {cache_file} is corrupted: {str(e)}")
                return {}  # Return empty dict instead of raising error
        return {}

    def save_translation_cache(self, cache: Dict[str, str], dest_language: str) -> None:
        """
        Save the translation cache for the specific language to a JSON file.

        Args:
            cache: Dictionary of translations to cache.
            dest_language: The target language code.

        Raises:
            TranslationCacheError: If there's an error saving the cache.
        """
        cache_file = os.path.join(TRANSLATION_CACHE_DIR, f"{dest_language}.json")
        cache_lock = f"{cache_file}.lock"

        try:
            with FileLock(cache_lock):
                with open(cache_file, "w", encoding="utf-8") as file:
                    json.dump(cache, file, ensure_ascii=False, indent=4)
        except Exception as e:
            logger.error(f"Error saving translation cache: {str(e)}")
            raise TranslationCacheError(f"Cache save error: {str(e)}")

    def translate_text(
        self, text: str, dest_language: str, cache: Dict[str, str]
    ) -> str:
        """
        Translate a single text string to the target language.

        Args:
            text: The text to translate.
            dest_language: The target language code.
            cache: The translation cache dictionary.

        Returns:
            The translated text, or the original text if translation fails.
        """
        if text in cache:
            return cache[text]

        try:
            translation = self.translator.translate(text, dest=dest_language)
            result = (
                self._normalize_arabic(translation.text)
                if dest_language == "ar"
                else translation.text
            )
            cache[text] = result
            return result
        except Exception as e:
            logger.error(f"Translation error: {str(e)}")
            return text  # Return original text on error

    def translate_texts_batch(
        self, texts: List[str], dest_language: str, cache: Dict[str, str]
    ) -> Dict[str, str]:
        """
        Translate multiple texts in parallel using thread pool.

        Args:
            texts: List of texts to translate.
            dest_language: The target language code.
            cache: Dictionary of cached translations.

        Returns:
            Dictionary mapping original texts to their translations.
        """
        results = {}
        with ThreadPoolExecutor(max_workers=5) as executor:
            future_to_text = {
                executor.submit(self.translate_text, text, dest_language, cache): text
                for text in texts
                if text and text not in cache
            }

            for future in as_completed(future_to_text):
                text = future_to_text[future]
                try:
                    results[text] = future.result()
                except Exception as e:
                    logger.error(f"Error translating text '{text}': {e}")
                    results[text] = text

        return results

    def translate_po_file(self, po_file_path: str, dest_language: str) -> None:
        """
        Translate all untranslated entries in a .po file and update the msgstr fields.

        Args:
            po_file_path: Path to the PO file.
            dest_language: The target language code.

        Raises:
            TranslationFileError: If there's an error with the PO file.
        """
        try:
            cache = self.load_translation_cache(dest_language)
        except TranslationCacheError as e:
            logger.warning(f"Using empty cache due to error: {e}")
            cache = {}

        try:
            po = polib.pofile(po_file_path)
        except FileNotFoundError:
            logger.error(f"PO file not found: {po_file_path}")
            raise TranslationFileError(f"PO file not found: {po_file_path}")
        except Exception as e:
            logger.error(f"Error reading PO file {po_file_path}: {e}")
            raise TranslationFileError(f"Error reading PO file: {str(e)}")

        logger.info(f"Translating PO file: {po_file_path}")

        # Get all untranslated texts
        untranslated_texts = [
            entry.msgid for entry in po.untranslated_entries() if not entry.msgstr
        ]

        if not untranslated_texts:
            logger.info("No untranslated strings found")
            return

        # Translate in batches
        translations = self.translate_texts_batch(
            untranslated_texts, dest_language, cache
        )

        # Update PO file
        for entry in po.untranslated_entries():
            entry.msgstr = self.translate_text(entry.msgid, dest_language, cache)
            logger.info(f"Translated '{entry.msgid}' -> '{entry.msgstr}'")

        try:
            po.save(po_file_path)
            self.save_translation_cache(cache, dest_language)
            logger.info(f"Translation completed and saved for {po_file_path}")
        except Exception as e:
            logger.error(f"Error saving translations: {e}")
            raise TranslationFileError(f"Error saving translations: {str(e)}")

    def translate_all_po_files(self, languages: List[str]) -> None:
        """
        Translate all PO files for the specified languages and compile them.

        Args:
            languages: List of language codes to translate to.

        Raises:
            TranslationError: If there's an error in the translation process.
        """
        try:
            for lang in languages:
                po_file_path = os.path.join(LOCALE_DIR, f"{lang}/LC_MESSAGES/django.po")

                # Create messages if they don't exist
                subprocess.run(
                    ["python", "manage.py", "makemessages", "-l", lang],
                    check=True,
                    capture_output=True,
                )

                self.translate_po_file(po_file_path, lang)

            # Compile messages
            subprocess.run(
                ["python", "manage.py", "compilemessages", "--exclude", "env"],
                check=True,
                capture_output=True,
            )

        except subprocess.CalledProcessError as e:
            logger.error(f"Error running Django command: {e}")
            raise TranslationError(f"Command error: {str(e)}")
        except Exception as e:
            logger.error(f"Error in translation process: {e}")
            raise TranslationError(f"Translation error: {str(e)}")

    def clear_translation_cache(self, dest_language: str = None) -> None:
        """
        Clear the translation cache for a specific language or all languages.

        Args:
            dest_language: Optional language code. If None, clears all caches.
        """
        try:
            if dest_language:
                cache_file = os.path.join(
                    TRANSLATION_CACHE_DIR, f"{dest_language}.json"
                )
                if os.path.exists(cache_file):
                    os.remove(cache_file)
            else:
                for file in os.listdir(TRANSLATION_CACHE_DIR):
                    if file.endswith(".json"):
                        os.remove(os.path.join(TRANSLATION_CACHE_DIR, file))
            logger.info(
                f"Cleared translation cache for {dest_language or 'all languages'}"
            )
        except Exception as e:
            logger.error(f"Error clearing translation cache: {e}")
            raise TranslationCacheError(f"Cache clear error: {str(e)}")
