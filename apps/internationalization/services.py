"""Translation management service."""

import os
import json
import subprocess
import polib
import logging
from filelock import FileLock
from googletrans import Translator
from typing import Dict, List, Optional, Set, Tuple
from django.core.cache import cache
from django.conf import settings
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from functools import lru_cache

logger = logging.getLogger(__name__)


LOCALE_DIR = settings.LOCALE_PATHS[0] if settings.LOCALE_PATHS else "locale"
TRANSLATION_CACHE_DIR = os.path.join(LOCALE_DIR, "translation_cache")
TRANSLATION_LOCK_FILE = os.path.join(LOCALE_DIR, "translation.lock")
CACHE_TIMEOUT = 60 * 60 * 24


class TranslationError(Exception):
    """Base exception for translation errors."""

    pass


class TranslationCacheError(TranslationError):
    """Exception raised for translation cache errors."""

    pass


class TranslationFileError(TranslationError):
    """Exception raised for translation file errors."""

    pass


class TranslationManager:
    """Manages translation operations including caching and file handling."""

    def __init__(self, verbose=True):
        """
        Initialize the translation manager with Google Translate.

        Args:
            verbose: Whether to show detailed logs.
        """
        self.translator = Translator()
        self.verbose = verbose
        os.makedirs(TRANSLATION_CACHE_DIR, exist_ok=True)

    @lru_cache(maxsize=1000)
    def _get_cache_key(self, text: str, dest_language: str) -> str:
        """Generate a cache key for a text and language combination."""
        return f"translation_{dest_language}_{hash(text)}"

    def _normalize_arabic(self, text: str) -> str:
        """Normalize Arabic text by removing diacritics."""

        diacritics = ["ً", "ٌ", "ٍ", "َ", "ُ", "ِ", "ّ", "ْ"]
        for diacritic in diacritics:
            text = text.replace(diacritic, "")
        return text

    def load_translation_cache(self, dest_language: str) -> Dict[str, str]:
        """
        Load the translation cache for the specific language from a JSON file.

        Args:
            dest_language: The target language code.

        Returns:
            A dictionary of cached translations.
        """
        cache_file = os.path.join(TRANSLATION_CACHE_DIR, f"{dest_language}.json")
        if os.path.exists(cache_file):
            try:
                with open(cache_file, "r", encoding="utf-8") as file:
                    return json.load(file)
            except json.JSONDecodeError as e:
                logger.error(f"Translation cache {cache_file} is corrupted: {str(e)}")
                return {}
        return {}

    def save_translation_cache(self, cache: Dict[str, str], dest_language: str) -> None:
        """
        Save the translation cache for the specific language to a JSON file.

        Args:
            cache: Dictionary of translations to cache.
            dest_language: The target language code.

        Raises:
            TranslationCacheError: If there's an error saving the cache.
        """
        cache_file = os.path.join(TRANSLATION_CACHE_DIR, f"{dest_language}.json")
        cache_lock = f"{cache_file}.lock"

        try:
            with FileLock(cache_lock):
                with open(cache_file, "w", encoding="utf-8") as file:
                    json.dump(cache, file, ensure_ascii=False, indent=4)
        except Exception as e:
            logger.error(f"Error saving translation cache: {str(e)}")
            raise TranslationCacheError(f"Cache save error: {str(e)}")

    def translate_text(
        self, text: str, dest_language: str, cache: Dict[str, str]
    ) -> str:
        """
        Translate a single text string to the target language.

        Args:
            text: The text to translate.
            dest_language: The target language code.
            cache: The translation cache dictionary.

        Returns:
            The translated text, or the original text if translation fails.
        """
        if text in cache:
            if self.verbose:
                logger.info(f"Using cached translation for '{text}'")
            return cache[text]

        try:
            if self.verbose:
                logger.info(f"Translating: '{text}' to {dest_language}")

            translation = self.translator.translate(text, dest=dest_language)
            result = (
                self._normalize_arabic(translation.text)
                if dest_language == "ar"
                else translation.text
            )

            if self.verbose:
                logger.info(f"Translation result: '{result}'")

            cache[text] = result
            return result
        except Exception as e:
            error_msg = f"Translation error for '{text}': {str(e)}"
            logger.error(error_msg)
            if self.verbose:
                logger.info("Returning original text due to translation error")
            return text

    def translate_texts_batch(
        self, texts: List[str], dest_language: str, cache: Dict[str, str]
    ) -> Dict[str, str]:
        """
        Translate multiple texts in parallel using thread pool.

        Args:
            texts: List of texts to translate.
            dest_language: The target language code.
            cache: Dictionary of cached translations.

        Returns:
            Dictionary mapping original texts to their translations.
        """
        results = {}
        with ThreadPoolExecutor(max_workers=5) as executor:
            future_to_text = {
                executor.submit(self.translate_text, text, dest_language, cache): text
                for text in texts
                if text and text not in cache
            }

            for future in as_completed(future_to_text):
                text = future_to_text[future]
                try:
                    results[text] = future.result()
                except Exception as e:
                    logger.error(f"Error translating text '{text}': {e}")
                    results[text] = text

        return results

    def translate_po_file(self, po_file_path: str, dest_language: str) -> None:
        """
        Translate all untranslated entries in a .po file and update the msgstr fields.

        Args:
            po_file_path: Path to the PO file.
            dest_language: The target language code.

        Raises:
            TranslationFileError: If there's an error with the PO file.
        """
        if self.verbose:
            logger.info(
                f"Starting translation of PO file: {po_file_path} to {dest_language}"
            )

        try:
            cache = self.load_translation_cache(dest_language)
            if self.verbose:
                logger.info(
                    f"Loaded translation cache for {dest_language} with {len(cache)} entries"
                )
        except TranslationCacheError as e:
            logger.warning(f"Using empty cache due to error: {e}")
            cache = {}

        try:
            po = polib.pofile(po_file_path)
            if self.verbose:
                logger.info(f"Successfully loaded PO file: {po_file_path}")
                logger.info(
                    f"Total entries: {len(po)}, Translated: {len(po.translated_entries())}, Untranslated: {len(po.untranslated_entries())}"
                )
        except FileNotFoundError:
            error_msg = f"PO file not found: {po_file_path}"
            logger.error(error_msg)
            raise TranslationFileError(error_msg)
        except Exception as e:
            error_msg = f"Error reading PO file {po_file_path}: {e}"
            logger.error(error_msg)
            raise TranslationFileError(f"Error reading PO file: {str(e)}")

        logger.info(f"Translating PO file: {po_file_path}")

        untranslated_entries = po.untranslated_entries()
        untranslated_texts = [
            entry.msgid for entry in untranslated_entries if not entry.msgstr
        ]

        if not untranslated_texts:
            logger.info("No untranslated strings found")
            return

        if self.verbose:
            logger.info(f"Found {len(untranslated_texts)} untranslated strings")
            if len(untranslated_texts) > 5:
                logger.info(f"First 5 untranslated strings: {untranslated_texts[:5]}")
            else:
                logger.info(f"Untranslated strings: {untranslated_texts}")

        _ = self.translate_texts_batch(untranslated_texts, dest_language, cache)

        translated_count = 0
        for entry in untranslated_entries:
            if self.verbose:
                logger.info(f"Translating entry: '{entry.msgid}'")

            entry.msgstr = self.translate_text(entry.msgid, dest_language, cache)

            if entry.msgstr:
                translated_count += 1
                if self.verbose:
                    logger.info(f"Translated '{entry.msgid}' -> '{entry.msgstr}'")
            else:
                if self.verbose:
                    logger.warning(f"Failed to translate '{entry.msgid}'")

        try:
            if self.verbose:
                logger.info(f"Saving translated PO file: {po_file_path}")

            po.save(po_file_path)

            if self.verbose:
                logger.info(
                    f"Saving translation cache for {dest_language} with {len(cache)} entries"
                )

            self.save_translation_cache(cache, dest_language)

            logger.info(
                f"Translation completed and saved for {po_file_path}. Translated {translated_count} entries."
            )
        except Exception as e:
            error_msg = f"Error saving translations: {e}"
            logger.error(error_msg)
            raise TranslationFileError(f"Error saving translations: {str(e)}")

    def translate_all_po_files(self, languages: List[str]) -> None:
        """
        Translate all PO files for the specified languages and compile them.

        Args:
            languages: List of language codes to translate to.

        Raises:
            TranslationError: If there's an error in the translation process.
        """
        if self.verbose:
            logger.info(f"Starting translation for languages: {languages}")

        try:
            for lang in languages:
                po_file_path = os.path.join(LOCALE_DIR, f"{lang}/LC_MESSAGES/django.po")

                if self.verbose:
                    logger.info(f"Running makemessages for language: {lang}")

                try:
                    result = subprocess.run(
                        ["python", "manage.py", "makemessages", "-l", lang],
                        check=True,
                        capture_output=True,
                        text=True,
                    )

                    if self.verbose:
                        if result.stdout:
                            logger.info(f"makemessages stdout: {result.stdout}")
                        if result.stderr:
                            logger.info(f"makemessages stderr: {result.stderr}")

                except subprocess.CalledProcessError as e:
                    logger.warning(
                        f"Warning: makemessages failed for {lang} but continuing: {e}"
                    )
                    if self.verbose:
                        if e.stdout:
                            logger.warning(f"makemessages stdout: {e.stdout}")
                        if e.stderr:
                            logger.warning(f"makemessages stderr: {e.stderr}")

                if self.verbose:
                    logger.info(f"Translating PO file: {po_file_path}")

                self.translate_po_file(po_file_path, lang)

            if self.verbose:
                logger.info("Running compilemessages")

            try:
                result = subprocess.run(
                    ["python", "manage.py", "compilemessages", "--exclude", "env"],
                    check=True,
                    capture_output=True,
                    text=True,
                )

                if self.verbose:
                    if result.stdout:
                        logger.info(f"compilemessages stdout: {result.stdout}")
                    if result.stderr:
                        logger.info(f"compilemessages stderr: {result.stderr}")

            except subprocess.CalledProcessError as e:
                logger.warning(f"Warning: compilemessages failed but continuing: {e}")
                if self.verbose:
                    if e.stdout:
                        logger.warning(f"compilemessages stdout: {e.stdout}")
                    if e.stderr:
                        logger.warning(f"compilemessages stderr: {e.stderr}")

            if self.verbose:
                logger.info("Translation process completed successfully")

        except subprocess.CalledProcessError as e:
            error_msg = f"Error running Django command: {e}"
            logger.error(error_msg)
            if self.verbose:
                if e.stdout:
                    logger.error(f"Command stdout: {e.stdout}")
                if e.stderr:
                    logger.error(f"Command stderr: {e.stderr}")
            raise TranslationError(f"Command error: {str(e)}")
        except Exception as e:
            error_msg = f"Error in translation process: {e}"
            logger.error(error_msg)
            raise TranslationError(f"Translation error: {str(e)}")

    def clear_translation_cache(self, dest_language: Optional[str] = None) -> None:
        """
        Clear the translation cache for a specific language or all languages.

        Args:
            dest_language: Optional language code. If None, clears all caches.
        """
        try:
            if dest_language:
                cache_file = os.path.join(
                    TRANSLATION_CACHE_DIR, f"{dest_language}.json"
                )
                if os.path.exists(cache_file):
                    os.remove(cache_file)
            else:
                for file in os.listdir(TRANSLATION_CACHE_DIR):
                    if file.endswith(".json"):
                        os.remove(os.path.join(TRANSLATION_CACHE_DIR, file))
            logger.info(
                f"Cleared translation cache for {dest_language or 'all languages'}"
            )
        except Exception as e:
            logger.error(f"Error clearing translation cache: {e}")
            raise TranslationCacheError(f"Cache clear error: {str(e)}")
