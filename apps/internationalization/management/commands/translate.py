"""Management command to translate PO files."""

import logging
import sys
import os
import json
import polib
import subprocess
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from apps.internationalization.services import (
    Translation<PERSON>anager,
    TranslationError,
    TranslationFileError,
    TRANSLATION_CACHE_DIR,
    LOCALE_DIR,
)
from typing import List, Dict, Any, Optional


logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """
    Django management command to translate PO files.

    This command uses the TranslationManager to translate all untranslated strings
    in the project's PO files. It supports multiple languages and provides options
    for controlling the translation process.

    Example:
        python manage.py translate --languages ar fr es
        python manage.py translate --clear-cache
        python manage.py translate --force
    """

    help = "Translate untranslated strings in PO files and compile them"

    def add_arguments(self, parser):
        """Add command line arguments."""
        parser.add_argument(
            "--languages",
            nargs="+",
            type=str,
            help="List of language codes to translate to (e.g., ar fr es)",
        )
        parser.add_argument(
            "--clear-cache",
            action="store_true",
            help="Clear the translation cache before translating",
        )
        parser.add_argument(
            "--force",
            action="store_true",
            help="Force translation of all strings, even if already translated",
        )
        parser.add_argument(
            "--verbose",
            action="store_true",
            default=True,
            help="Show detailed logs of the translation process (default: True)",
        )
        parser.add_argument(
            "--quiet",
            action="store_true",
            help="Suppress detailed logs (overrides --verbose)",
        )
        parser.add_argument(
            "--cache-only",
            action="store_true",
            help="Only use translations from the cache, don't call external translation service",
        )
        parser.add_argument(
            "--update-from-cache",
            action="store_true",
            default=True,
            help="Update PO files with translations from the cache before translating (default: True)",
        )

    def get_languages(self, options: dict) -> List[str]:
        """
        Get the list of languages to translate.

        Args:
            options: Command options dictionary.

        Returns:
            List of language codes to translate.
        """
        languages = options.get("languages")
        if not languages:
            languages = getattr(settings, "MODELTRANSLATION_LANGUAGES", [])
            if not languages:
                languages = [
                    lang[0]
                    for lang in settings.LANGUAGES
                    if lang[0] != settings.LANGUAGE_CODE
                ]

        if not languages:
            raise CommandError(
                "No languages specified. Either provide --languages option or "
                "configure MODELTRANSLATION_LANGUAGES in settings."
            )
        return languages

    def load_translation_cache(self, language: str) -> Dict[str, str]:
        """
        Load the translation cache for the specified language.

        Args:
            language: The language code.

        Returns:
            A dictionary of cached translations.
        """
        cache_file = os.path.join(TRANSLATION_CACHE_DIR, f"{language}.json")
        if os.path.exists(cache_file):
            try:
                with open(cache_file, "r", encoding="utf-8") as file:
                    return json.load(file)
            except json.JSONDecodeError as e:
                logger.error(f"Translation cache {cache_file} is corrupted: {str(e)}")
                return {}
        return {}

    def update_po_file_from_cache(
        self, po_file_path: str, language: str, verbose: bool = True
    ) -> int:
        """
        Update a PO file with translations from the cache.

        Args:
            po_file_path: Path to the PO file.
            language: The language code.
            verbose: Whether to show detailed logs.

        Returns:
            The number of entries updated from the cache.
        """
        if verbose:
            self.stdout.write(
                f"Updating PO file {po_file_path} from cache for language {language}"
            )

        cache = self.load_translation_cache(language)
        if not cache:
            if verbose:
                self.stdout.write(f"No cache found for language {language}")
            return 0

        if verbose:
            self.stdout.write(f"Loaded {len(cache)} translations from cache")

        try:

            po = polib.pofile(po_file_path)

            updated_count = 0

            for entry in po.untranslated_entries():
                if entry.msgid in cache and cache[entry.msgid]:
                    entry.msgstr = cache[entry.msgid]
                    updated_count += 1
                    if verbose:
                        self.stdout.write(
                            f"Updated from cache: '{entry.msgid}' -> '{entry.msgstr}'"
                        )

            if updated_count > 0:
                po.save(po_file_path)
                if verbose:
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"Updated {updated_count} entries in {po_file_path} from cache"
                        )
                    )
            else:
                if verbose:
                    self.stdout.write("No entries updated from cache")

            return updated_count

        except Exception as e:
            logger.error(f"Error updating PO file from cache: {str(e)}")
            if verbose:
                self.stdout.write(
                    self.style.ERROR(f"Error updating PO file from cache: {str(e)}")
                )
            return 0

    def handle(self, **options: Any) -> None:
        """
        Execute the command.

        Args:
            **options: Command options.

        Raises:
            CommandError: If there's an error during translation.
        """

        verbose = options.get("verbose", True) and not options.get("quiet", False)

        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO if verbose else logging.WARNING)
        formatter = logging.Formatter("%(levelname)s: %(message)s")
        console_handler.setFormatter(formatter)

        root_logger = logging.getLogger("apps.internationalization")
        root_logger.setLevel(logging.INFO if verbose else logging.WARNING)

        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        root_logger.addHandler(console_handler)

        try:
            languages = self.get_languages(options)
            translation_manager = TranslationManager()

            if options["clear_cache"]:
                self.stdout.write("Clearing translation cache...")
                translation_manager.clear_translation_cache()

            self.stdout.write(
                f"Starting translation for languages: {', '.join(languages)}"
            )

            if verbose:
                self.stdout.write("Verbose mode enabled. Showing detailed logs...")

            translation_manager.verbose = verbose

            if options.get("update_from_cache", True):
                total_updated = 0
                for language in languages:
                    po_file_path = os.path.join(
                        LOCALE_DIR, f"{language}/LC_MESSAGES/django.po"
                    )
                    updated = self.update_po_file_from_cache(
                        po_file_path, language, verbose
                    )
                    total_updated += updated

                if total_updated > 0:
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"Updated a total of {total_updated} entries from cache"
                        )
                    )
                else:
                    self.stdout.write("No entries updated from cache")

            if options.get("cache_only", False):
                self.stdout.write(
                    "Cache-only mode enabled. Skipping external translation service."
                )

                try:
                    if verbose:
                        self.stdout.write("Running compilemessages...")

                    result = subprocess.run(
                        ["python", "manage.py", "compilemessages", "--exclude", "env"],
                        check=True,
                        capture_output=True,
                        text=True,
                    )

                    if verbose:
                        if result.stdout:
                            self.stdout.write(
                                f"compilemessages stdout: {result.stdout}"
                            )
                        if result.stderr:
                            self.stdout.write(
                                f"compilemessages stderr: {result.stderr}"
                            )

                    self.stdout.write(
                        self.style.SUCCESS(
                            f"Successfully updated PO files from cache for: {', '.join(languages)}"
                        )
                    )
                except subprocess.CalledProcessError as e:
                    self.stdout.write(
                        self.style.WARNING(
                            f"Warning: compilemessages failed but continuing: {e}"
                        )
                    )
                    if verbose:
                        if e.stdout:
                            self.stdout.write(f"compilemessages stdout: {e.stdout}")
                        if e.stderr:
                            self.stdout.write(f"compilemessages stderr: {e.stderr}")
            else:

                try:
                    translation_manager.translate_all_po_files(languages)
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"Successfully translated PO files for: {', '.join(languages)}"
                        )
                    )
                except TranslationError as e:
                    raise CommandError(f"Translation failed: {str(e)}")

        except Exception as e:
            logger.error(f"Command failed: {str(e)}")
            raise CommandError(f"Command failed: {str(e)}")
