"""Tests for the TranslationManager service."""

import os
import json
import shutil
import tempfile
from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.conf import settings
from apps.internationalization.services import (
    TranslationManager,
    TRANSLATION_CACHE_DIR,
    TranslationFileError,
)


class TranslationManagerTests(TestCase):
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.translation_manager = TranslationManager()
        self.test_cache_dir = os.path.join(self.temp_dir, "translation_cache")
        os.makedirs(self.test_cache_dir, exist_ok=True)
        self.original_cache_dir = TRANSLATION_CACHE_DIR
        self.patcher = patch(
            "apps.internationalization.services.TRANSLATION_CACHE_DIR",
            self.test_cache_dir,
        )
        self.patcher.start()

    def tearDown(self):
        self.patcher.stop()
        shutil.rmtree(self.temp_dir)

    def test_load_translation_cache_nonexistent_file(self):
        """Test loading translation cache when file doesn't exist."""
        cache = self.translation_manager.load_translation_cache("ar")
        self.assertEqual(cache, {})

    def test_load_translation_cache_corrupted_file(self):
        """Test loading translation cache when file is corrupted."""
        cache_file = os.path.join(self.test_cache_dir, "ar.json")
        with open(cache_file, "w") as f:
            f.write("invalid json")

        cache = self.translation_manager.load_translation_cache("ar")
        self.assertEqual(cache, {})

    def test_load_translation_cache_valid_file(self):
        """Test loading translation cache with valid file."""
        test_cache = {"hello": "مرحبا"}
        cache_file = os.path.join(self.test_cache_dir, "ar.json")
        with open(cache_file, "w", encoding="utf-8") as f:
            json.dump(test_cache, f)

        cache = self.translation_manager.load_translation_cache("ar")
        self.assertEqual(cache, test_cache)

    def test_save_translation_cache(self):
        """Test saving translation cache."""
        test_cache = {"hello": "مرحبا"}
        self.translation_manager.save_translation_cache(test_cache, "ar")

        cache_file = os.path.join(self.test_cache_dir, "ar.json")
        with open(cache_file, "r", encoding="utf-8") as f:
            saved_cache = json.load(f)

        self.assertEqual(saved_cache, test_cache)

    @patch("apps.internationalization.services.Translator")
    def test_translate_text_cached(self, mock_translator):
        """Test translating text that is already in cache."""
        cache = {"hello": "مرحبا"}
        result = self.translation_manager.translate_text("hello", "ar", cache)

        self.assertEqual(result, "مرحبا")
        mock_translator.assert_not_called()

    @patch("googletrans.Translator")
    def test_translate_text_new(self, mock_translator_class):
        """Test translating new text."""
        mock_translation = MagicMock()
        mock_translation.text = "مرحبا"
        mock_translator_instance = MagicMock()
        mock_translator_instance.translate.return_value = mock_translation
        mock_translator_class.return_value = mock_translator_instance

        self.translation_manager.translator = mock_translator_instance
        cache = {}
        result = self.translation_manager.translate_text("hello", "ar", cache)

        self.assertEqual(result, "مرحبا")
        self.assertEqual(cache["hello"], "مرحبا")
        mock_translator_instance.translate.assert_called_once_with("hello", dest="ar")

    @patch("googletrans.Translator")
    def test_translate_text_error(self, mock_translator_class):
        """Test translating text when an error occurs."""
        mock_translator_instance = MagicMock()
        mock_translator_instance.translate.side_effect = Exception("Translation error")
        mock_translator_class.return_value = mock_translator_instance

        self.translation_manager.translator = mock_translator_instance
        cache = {}
        result = self.translation_manager.translate_text("hello", "ar", cache)

        self.assertEqual(result, "hello")
        self.assertNotIn("hello", cache)

    @patch("apps.internationalization.services.polib")
    def test_translate_po_file_not_found(self, mock_polib):
        """Test translating PO file that doesn't exist."""
        mock_polib.pofile.side_effect = FileNotFoundError()

        with self.assertRaises(TranslationFileError) as context:
            self.translation_manager.translate_po_file("nonexistent.po", "ar")

        self.assertEqual(str(context.exception), "PO file not found: nonexistent.po")

    @patch("apps.internationalization.services.polib")
    @patch("apps.internationalization.services.Translator")
    def test_translate_po_file(self, mock_translator, mock_polib):
        """Test translating PO file."""

        mock_entry = MagicMock()
        mock_entry.msgid = "hello"
        mock_entry.msgstr = ""
        mock_po = MagicMock()
        mock_po.untranslated_entries.return_value = [mock_entry]
        mock_polib.pofile.return_value = mock_po

        mock_translation = MagicMock()
        mock_translation.text = "مرحبا"
        mock_translator_instance = MagicMock()
        mock_translator_instance.translate.return_value = mock_translation
        mock_translator.return_value = mock_translator_instance

        def mock_translate_side_effect(*args, **kwargs):
            mock_entry.msgstr = "مرحبا"
            return mock_translation

        mock_translator_instance.translate.side_effect = mock_translate_side_effect

        mock_entry.configure_mock(msgstr="")

        self.translation_manager.translate_po_file("test.po", "ar")

        self.assertEqual(mock_entry.msgstr, "مرحبا")
        mock_po.save.assert_called_once()

    @patch("apps.internationalization.services.subprocess.run")
    @patch("apps.internationalization.services.TranslationManager.translate_po_file")
    def test_translate_all_po_files(self, mock_translate_po_file, mock_subprocess_run):
        """Test translating all PO files."""
        languages = ["ar", "fr"]
        self.translation_manager.translate_all_po_files(languages)

        self.assertEqual(mock_translate_po_file.call_count, 2)
        self.assertEqual(mock_subprocess_run.call_count, 3)
