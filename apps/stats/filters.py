from datetime import datetime, timedelta
from django.utils import timezone
from rest_framework.exceptions import ValidationError
from dateutil.relativedelta import relativedelta
import calendar
from django.utils.translation import gettext_lazy as _


class TimeFilter:
    """
    Filter for different time periods used in stats calculations.
    Supports standard periods (this week, this month, this year) and custom date ranges.
    """

    def __init__(self, request_data=None):
        """
        Initialize the time filter with request data.

        Args:
            request_data (dict): Dictionary containing filter parameters - typically request.query_params from a GET request or request.data from a POST request
        """
        self.request_data = request_data or {}
        self.start_date = None
        self.end_date = None
        self._parse_filter()

    def _parse_filter(self):
        """Parse filter parameters from the request data."""
        period = self.request_data.get("period")

        if period:
            self._handle_period_filter(period)
        elif "start_date" in self.request_data or "end_date" in self.request_data:
            self._handle_custom_range()

    def _handle_period_filter(self, period):
        """
        Set start_date and end_date based on the specified period.

        Args:
            period (str): One of 'this_week', 'this_month', 'this_year'
        """
        now = timezone.now()

        if period == "this_week":

            self.start_date = now - timedelta(days=now.weekday())
            self.start_date = self.start_date.replace(
                hour=0, minute=0, second=0, microsecond=0
            )
            self.end_date = now

        elif period == "this_month":

            self.start_date = now.replace(
                day=1, hour=0, minute=0, second=0, microsecond=0
            )
            self.end_date = now

        elif period == "this_year":

            self.start_date = now.replace(
                month=1, day=1, hour=0, minute=0, second=0, microsecond=0
            )
            self.end_date = now

        else:
            raise ValidationError(
                f"Invalid period: {period}. Valid periods are: 'this_week', 'this_month', 'this_year'"
            )

    def _handle_custom_range(self):
        """Parse and validate custom date range from start_date and end_date parameters."""
        start_date_str = self.request_data.get("start_date")
        end_date_str = self.request_data.get("end_date")

        if start_date_str:
            try:
                self.start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
                self.start_date = timezone.make_aware(self.start_date)
            except ValueError:
                raise ValidationError(_("Invalid start_date format. Use YYYY-MM-DD"))

        if end_date_str:
            try:
                self.end_date = datetime.strptime(end_date_str, "%Y-%m-%d")

                self.end_date = self.end_date.replace(hour=23, minute=59, second=59)
                self.end_date = timezone.make_aware(self.end_date)
            except ValueError:
                raise ValidationError(_("Invalid end_date format. Use YYYY-MM-DD"))

        if self.start_date and not self.end_date:
            self.end_date = timezone.now()

        if self.start_date and self.end_date and self.start_date > self.end_date:
            raise ValidationError(_("start_date must be before end_date"))

    def get_date_filter_kwargs(self, field_name="created"):
        """
        Return filter kwargs that can be used in queryset filtering.

        Args:
            field_name (str): The name of the datetime field to filter on

        Returns:
            dict: Filter kwargs for queryset filtering
        """
        filter_kwargs = {}

        if self.start_date:
            filter_kwargs[f"{field_name}__gte"] = self.start_date

        if self.end_date:
            filter_kwargs[f"{field_name}__lte"] = self.end_date

        return filter_kwargs


class TimeSeriesFilter:
    """
    Filter for generating time series data for charts.
    Supports different time periods (months, weeks, days).
    """

    FORMAT_MONTHS = "months"
    FORMAT_WEEKS = "weeks"
    FORMAT_DAYS = "days"
    VALID_FORMATS = [FORMAT_MONTHS, FORMAT_WEEKS, FORMAT_DAYS]
    DEFAULT_PERIODS = 12

    def __init__(self, request_data=None):
        """
        Initialize the time series filter with request data.

        Args:
            request_data (dict): Dictionary containing filter parameters from request.query_params
        """
        self.request_data = request_data or {}
        self.format = self.request_data.get("format", self.FORMAT_MONTHS).lower()

        if self.format not in self.VALID_FORMATS:
            raise ValidationError(
                f"Invalid format: {self.format}. Valid formats are: {', '.join(self.VALID_FORMATS)}"
            )

        self.end_date = timezone.now()
        self.start_date = self._calculate_start_date()

    def _calculate_start_date(self):
        """Calculate the start date based on the format."""
        if self.format == self.FORMAT_MONTHS:

            current_month_start = self.end_date.replace(
                day=1, hour=0, minute=0, second=0, microsecond=0
            )
            return current_month_start - relativedelta(months=self.DEFAULT_PERIODS - 1)

        elif self.format == self.FORMAT_WEEKS:

            current_week_start = self.end_date - timedelta(days=self.end_date.weekday())
            current_week_start = current_week_start.replace(
                hour=0, minute=0, second=0, microsecond=0
            )
            return current_week_start - timedelta(weeks=self.DEFAULT_PERIODS - 1)

        elif self.format == self.FORMAT_DAYS:

            today_start = self.end_date.replace(
                hour=0, minute=0, second=0, microsecond=0
            )
            return today_start - timedelta(days=self.DEFAULT_PERIODS - 1)

        return None

    def generate_time_periods(self):
        """
        Generate time period boundaries for aggregation.

        Returns:
            tuple: (period_boundaries, labels)
                - period_boundaries: List of (start_date, end_date) tuples for each period
                - labels: List of string labels for each period
        """
        if self.format == self.FORMAT_MONTHS:
            return self._generate_month_periods()
        elif self.format == self.FORMAT_WEEKS:
            return self._generate_week_periods()
        elif self.format == self.FORMAT_DAYS:
            return self._generate_day_periods()

        return [], []

    def _generate_month_periods(self):
        """Generate periods and labels for months format."""
        periods = []
        labels = []

        current_date = self.start_date

        for _ in range(self.DEFAULT_PERIODS):

            month_start = current_date.replace(
                day=1, hour=0, minute=0, second=0, microsecond=0
            )

            if current_date.month == 12:
                next_month = current_date.replace(
                    year=current_date.year + 1, month=1, day=1
                )
            else:
                next_month = current_date.replace(month=current_date.month + 1, day=1)

            month_end = next_month - timedelta(microseconds=1)

            periods.append((month_start, month_end))
            labels.append(month_start.strftime("%m/%Y"))

            current_date = next_month

        return periods, labels

    def _generate_week_periods(self):
        """Generate periods and labels for weeks format."""
        periods = []
        labels = []

        current_date = self.start_date

        for week_num in range(self.DEFAULT_PERIODS):

            week_start = current_date

            week_end = week_start + timedelta(days=6, hours=23, minutes=59, seconds=59)

            periods.append((week_start, week_end))

            week_label = f"{week_start.strftime('%m/%d')}-{week_end.strftime('%m/%d')}"
            labels.append(week_label)

            current_date = week_start + timedelta(weeks=1)

        return periods, labels

    def _generate_day_periods(self):
        """Generate periods and labels for days format."""
        periods = []
        labels = []

        current_date = self.start_date

        for _ in range(self.DEFAULT_PERIODS):

            day_start = current_date

            day_end = day_start.replace(hour=23, minute=59, second=59)

            periods.append((day_start, day_end))
            labels.append(day_start.strftime("%m/%d"))

            current_date = day_start + timedelta(days=1)

        return periods, labels
