from rest_framework import serializers
from django.utils.translation import gettext_lazy as _


class ClubChartSerializer(serializers.Serializer):
    """
    Serializer for clubs chart data showing club counts over time periods.
    """

    format = serializers.CharField(
        help_text=_("Time format for the data points (months, weeks, days)"),
        required=False,
        allow_null=True,
    )
    labels = serializers.ListField(
        child=serializers.CharField(),
        help_text=_("Time period labels (e.g., month numbers, week ranges, dates)"),
    )
    data = serializers.ListField(
        child=serializers.IntegerField(), help_text=_("Club counts for each time period")
    )
    total_clubs = serializers.IntegerField(
        help_text=_("Total number of clubs in the time period")
    )
    start_date = serializers.DateTimeField(
        help_text=_("Start date of the chart data"), required=False, allow_null=True
    )
    end_date = serializers.DateTimeField(
        help_text=_("End date of the chart data"), required=False, allow_null=True
    )
    role = serializers.<PERSON><PERSON><PERSON><PERSON>(
        help_text=_("Filter by club manager role (admin, club_manager, or null for all)"),
        required=False,
        allow_null=True,
    )
