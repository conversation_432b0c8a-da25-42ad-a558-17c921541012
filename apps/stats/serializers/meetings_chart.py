from rest_framework import serializers
from django.utils.translation import gettext_lazy as _


class MeetingChartSerializer(serializers.Serializer):
    """
    Serializer for meeting chart data showing meeting counts over time periods.
    """

    format = serializers.CharField(
        help_text=_("Time format for the data points (months, weeks, days)"),
        required=False,
        allow_null=True,
    )
    labels = serializers.ListField(
        child=serializers.CharField(),
        help_text=_("Time period labels (e.g., month names, week numbers, dates)"),
    )
    data = serializers.ListField(
        child=serializers.IntegerField(),
        help_text=_("Meeting counts for each time period"),
    )
    total_meetings = serializers.IntegerField(
        help_text=_("Total number of meetings in the time period")
    )
    start_date = serializers.DateTimeField(
        help_text=_("Start date of the chart data"), required=False, allow_null=True
    )
    end_date = serializers.DateTimeField(
        help_text=_("End date of the chart data"), required=False, allow_null=True
    )
