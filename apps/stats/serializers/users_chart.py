from rest_framework import serializers
from django.utils.translation import gettext_lazy as _


class UserChartSerializer(serializers.Serializer):
    """
    Serializer for users chart data showing user counts over time periods.
    """

    format = serializers.CharField(
        help_text=_("Time format for the data points (months, weeks, days)"),
        required=False,
        allow_null=True,
    )
    labels = serializers.ListField(
        child=serializers.CharField(),
        help_text=_("Time period labels (e.g., month numbers, week ranges, dates)"),
    )
    data = serializers.ListField(
        child=serializers.IntegerField(), help_text=_("User counts for each time period")
    )
    total_users = serializers.IntegerField(
        help_text=_("Total number of users in the time period")
    )
    start_date = serializers.DateTimeField(
        help_text=_("Start date of the chart data"), required=False, allow_null=True
    )
    end_date = serializers.DateTimeField(
        help_text=_("End date of the chart data"), required=False, allow_null=True
    )
    role = serializers.Char<PERSON>ield(
        help_text=_("User role filter (admin, club_manager, member, or null for all)"),
        required=False,
        allow_null=True,
    )
