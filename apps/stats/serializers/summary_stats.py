from rest_framework import serializers
from django.utils.translation import gettext_lazy as _


class SummaryStatsSerializer(serializers.Serializer):
    """
    Serializer for summary statistics about users, clubs, and meetings.
    """

    total_users = serializers.IntegerField(help_text=_("Total number of users"))
    total_clubs = serializers.IntegerField(help_text=_("Total number of clubs"))
    total_meetings = serializers.IntegerField(help_text=_("Total number of club meetings"))
    total_club_managers = serializers.IntegerField(
        help_text=_("Total number of club managers")
    )
    period = serializers.CharField(
        help_text=_("Time period for the statistics"), required=False, allow_null=True
    )
    start_date = serializers.DateTimeField(
        help_text=_("Start date for custom period filter"), required=False, allow_null=True
    )
    end_date = serializers.DateTimeField(
        help_text=_("End date for custom period filter"), required=False, allow_null=True
    )
