from rest_framework import serializers
from django.utils.translation import gettext_lazy as _


class MissionProgressChartSerializer(serializers.Serializer):
    """
    Serializer for mission progress chart data showing completion rates over time periods.
    """

    format = serializers.CharField(
        help_text=_("Time format for the data points (months, weeks, days)"),
        required=False,
        allow_null=True,
    )
    labels = serializers.ListField(
        child=serializers.CharField(),
        help_text=_("Time period labels (e.g., month names, week numbers, dates)"),
    )
    data = serializers.ListField(
        child=serializers.FloatField(),
        help_text=_("Mission completion percentages for each time period"),
    )
    goal_data = serializers.ListField(
        child=serializers.FloatField(),
        help_text=_("Goal completion percentages for each time period"),
    )
    total_missions = serializers.IntegerField(
        help_text=_("Total number of missions in the time period")
    )
    completed_missions = serializers.IntegerField(
        help_text=_("Number of completed missions in the time period")
    )
    total_goals = serializers.IntegerField(
        help_text=_("Total number of goals in the time period")
    )
    completed_goals = serializers.IntegerField(
        help_text=_("Number of completed goals in the time period")
    )
    mission_completion_rate = serializers.FloatField(
        help_text=_("Overall completion rate across all missions")
    )
    goal_completion_rate = serializers.FloatField(
        help_text=_("Overall completion rate across all goals")
    )
    start_date = serializers.DateTimeField(
        help_text=_("Start date of the chart data"), required=False, allow_null=True
    )
    end_date = serializers.DateTimeField(
        help_text=_("End date of the chart data"), required=False, allow_null=True
    )
    domain = serializers.CharField(
        help_text=_("Domain ID filter"), required=False, allow_null=True, allow_blank=True
    )
