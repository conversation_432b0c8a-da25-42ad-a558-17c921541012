from rest_framework import serializers
from django.utils.translation import gettext_lazy as _


class GoalCompletionRateSerializer(serializers.Serializer):
    """
    Serializer for goal completion rate data showing completion percentages by priority and domain.
    """

    format = serializers.CharField(
        help_text=_("Time format for the data points (months, weeks, days)"),
        required=False,
        allow_null=True,
    )
    labels = serializers.ListField(
        child=serializers.CharField(),
        help_text=_("Time period labels (e.g., month names, week numbers, dates)"),
    )
    start_date = serializers.DateTimeField(
        help_text=_("Start date of the chart data"), required=False, allow_null=True
    )
    end_date = serializers.DateTimeField(
        help_text=_("End date of the chart data"), required=False, allow_null=True
    )

    high_priority_data = serializers.ListField(
        child=serializers.FloatField(),
        help_text=_("High priority goal completion percentages for each time period"),
    )
    high_priority_total = serializers.IntegerField(
        help_text=_("Total number of high priority goals in the time period")
    )
    high_priority_completed = serializers.IntegerField(
        help_text=_("Number of completed high priority goals in the time period")
    )
    high_priority_rate = serializers.FloatField(
        help_text=_("Overall completion rate across all high priority goals")
    )

    low_priority_data = serializers.ListField(
        child=serializers.FloatField(),
        help_text=_("Low priority goal completion percentages for each time period"),
    )
    low_priority_total = serializers.IntegerField(
        help_text=_("Total number of low priority goals in the time period")
    )
    low_priority_completed = serializers.IntegerField(
        help_text=_("Number of completed low priority goals in the time period")
    )
    low_priority_rate = serializers.FloatField(
        help_text=_("Overall completion rate across all low priority goals")
    )

    total_goals = serializers.IntegerField(
        help_text=_("Total number of goals in the time period")
    )
    completed_goals = serializers.IntegerField(
        help_text=_("Number of completed goals in the time period")
    )
    completion_rate = serializers.FloatField(
        help_text=_("Overall completion rate across all goals")
    )

    domain = serializers.CharField(
        help_text=_("Domain ID filter"), required=False, allow_null=True, allow_blank=True
    )
    role = serializers.CharField(
        help_text=_("User role filter (admin, club_manager, member, or null for all)"),
        required=False,
        allow_null=True,
    )
