from django.urls import path
from apps.stats.views.summary_stats import SummaryStatsAPIView
from apps.stats.views.meetings_chart import MeetingsChartView
from apps.stats.views.users_chart import UsersChartView
from apps.stats.views.clubs_chart import Clubs<PERSON>hartView
from apps.stats.views.test_view import TestAPIView
from apps.stats.views.fixed_meetings_chart import FixedMeetingsChartView
from apps.stats.views.missions_progress_chart import MissionsProgressChartView
from apps.stats.views.goal_completion_rate import GoalCompletionRateView

app_name = "stats"

urlpatterns = [
    path("summary/stats/", SummaryStatsAPIView.as_view(), name="summary-stats"),
    path(
        "charts/meetings/",
        MeetingsChartView.as_view(),
        name="charts-meetings",
    ),
    path(
        "fixed-charts/meetings/",
        FixedMeetingsChartView.as_view(),
        name="fixed-charts-meetings",
    ),
    path("charts/users/", UsersChartView.as_view(), name="charts-users"),
    path("charts/clubs/", ClubsChartView.as_view(), name="charts-clubs"),
    path(
        "charts/missions-progress/",
        MissionsProgressChartView.as_view(),
        name="charts-missions-progress",
    ),
    path(
        "charts/goal-completion-rate/",
        GoalCompletionRateView.as_view(),
        name="charts-goal-completion-rate",
    ),
    path("test/", TestAPIView.as_view(), name="test-view"),
]
