from django.db.models import (
    Count,
    Case,
    When,
    Q,
    F,
    ExpressionWrapper,
    FloatField,
    IntegerField,
)
from django.utils import timezone
from rest_framework.generics import GenericAPIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
import logging

from django.contrib.auth import get_user_model
from apps.missions.models import Goal, WeeklyMission
from apps.stats.serializers.goal_completion_rate import GoalCompletionRateSerializer
from apps.stats.filters import TimeSeriesFilter
from django.utils.translation import gettext_lazy as _

User = get_user_model()
logger = logging.getLogger(__name__)


class GoalCompletionRateView(GenericAPIView):
    """
    API view to retrieve goal completion rates by priority and domain.

    Provides data for comparing high vs low priority goal completion rates
    and analyzing completion trends across different dimensions.

    Accepts parameters:
    - format: 'months' (default), 'weeks', 'days'
    - domain: Filter by domain ID
    - role: Filter by user role ('admin', 'club_manager', 'member')
    """

    serializer_class = GoalCompletionRateSerializer
    permission_classes = [IsAuthenticated]

    @extend_schema(
        tags=["Stats"],
        summary="Get goal completion rate data",
        description="Returns goal completion rates grouped by priority (high/low) across time periods (months, weeks, or days), with optional domain and role filtering",
        parameters=[
            OpenApiParameter(
                name="format",
                type=str,
                description="Time period format: 'months' (default), 'weeks', 'days'",
                required=False,
                enum=["months", "weeks", "days"],
            ),
            OpenApiParameter(
                name="domain",
                type=int,
                description="Filter by domain ID",
                required=False,
            ),
            OpenApiParameter(
                name="role",
                type=str,
                description="Filter by user role",
                required=False,
                enum=["admin", "club_manager", "member"],
            ),
        ],
        responses={
            200: GoalCompletionRateSerializer,
            400: "Bad request, invalid parameters",
        },
        examples=[
            OpenApiExample(
                "Monthly goal completion rates by priority",
                value={
                    "format": "months",
                    "labels": [
                        "01/2024",
                        "02/2024",
                        "03/2024",
                        "04/2024",
                        "05/2024",
                        "06/2024",
                        "07/2024",
                        "08/2024",
                        "09/2024",
                        "10/2024",
                        "11/2024",
                        "12/2024",
                    ],
                    "high_priority_data": [
                        68.5,
                        72.3,
                        75.7,
                        78.1,
                        82.8,
                        85.4,
                        83.2,
                        87.6,
                        90.7,
                        89.2,
                        92.5,
                        94.3,
                    ],
                    "low_priority_data": [
                        45.2,
                        48.6,
                        52.7,
                        55.5,
                        60.8,
                        63.4,
                        61.2,
                        65.6,
                        68.7,
                        72.2,
                        75.5,
                        78.3,
                    ],
                    "high_priority_total": 850,
                    "high_priority_completed": 710,
                    "high_priority_rate": 83.5,
                    "low_priority_total": 1200,
                    "low_priority_completed": 750,
                    "low_priority_rate": 62.5,
                    "total_goals": 2050,
                    "completed_goals": 1460,
                    "completion_rate": 71.2,
                    "start_date": "2024-01-01T00:00:00Z",
                    "end_date": "2024-12-31T23:59:59Z",
                    "domain": None,
                    "role": None,
                },
                response_only=True,
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        """Get goal completion rates by priority aggregated by time periods."""

        try:
            logger.info(
                "GoalCompletionRateView accessed with params: %s", request.query_params
            )

            time_filter = TimeSeriesFilter(request.query_params)
            periods, labels = time_filter.generate_time_periods()

            domain_id = request.query_params.get("domain")
            role = request.query_params.get("role")

            if domain_id:
                try:

                    import uuid

                    if len(domain_id) > 10:
                        uuid.UUID(domain_id)
                    else:
                        int(domain_id)
                except (ValueError, TypeError):
                    return Response(
                        {
                            "error": f"Invalid domain ID: {domain_id}. Must be a valid UUID or integer."
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            start_date = time_filter.start_date
            end_date = time_filter.end_date

            logger.debug("Filtering goals between %s and %s", start_date, end_date)

            goals_queryset = Goal.objects.filter(
                created__gte=start_date, created__lte=end_date
            )

            if domain_id:

                mission_ids = WeeklyMission.objects.filter(
                    domain_id=domain_id
                ).values_list("id", flat=True)

                goals_queryset = goals_queryset.filter(
                    missions__id__in=mission_ids
                ).distinct()

                logger.debug("Applied domain filter: %s", domain_id)

            if role:
                goals_queryset = goals_queryset.filter(user__role=role)
                logger.debug("Applied role filter: %s", role)

            total_goals = goals_queryset.count()
            completed_goals = (
                goals_queryset.filter(missions__completion_date__isnull=False)
                .distinct()
                .count()
            )

            high_priority_queryset = goals_queryset.filter(priority=Goal.PRIORITY_HIGH)
            high_priority_total = high_priority_queryset.count()
            high_priority_completed = (
                high_priority_queryset.filter(missions__completion_date__isnull=False)
                .distinct()
                .count()
            )

            low_priority_queryset = goals_queryset.filter(priority=Goal.PRIORITY_LOW)
            low_priority_total = low_priority_queryset.count()
            low_priority_completed = (
                low_priority_queryset.filter(missions__completion_date__isnull=False)
                .distinct()
                .count()
            )

            overall_completion_rate = 0
            if total_goals > 0:
                overall_completion_rate = (completed_goals / total_goals) * 100

            high_priority_rate = 0
            if high_priority_total > 0:
                high_priority_rate = (
                    high_priority_completed / high_priority_total
                ) * 100

            low_priority_rate = 0
            if low_priority_total > 0:
                low_priority_rate = (low_priority_completed / low_priority_total) * 100

            high_priority_data = []
            low_priority_data = []

            for period_start, period_end in periods:

                period_goals = goals_queryset.filter(
                    created__gte=period_start, created__lte=period_end
                )

                period_high_priority = period_goals.filter(priority=Goal.PRIORITY_HIGH)
                period_high_total = period_high_priority.count()
                if period_high_total > 0:
                    period_high_completed = (
                        period_high_priority.filter(
                            missions__completion_date__isnull=False
                        )
                        .distinct()
                        .count()
                    )
                    period_high_rate = (period_high_completed / period_high_total) * 100
                else:
                    period_high_rate = 0

                high_priority_data.append(round(period_high_rate, 1))

                period_low_priority = period_goals.filter(priority=Goal.PRIORITY_LOW)
                period_low_total = period_low_priority.count()
                if period_low_total > 0:
                    period_low_completed = (
                        period_low_priority.filter(
                            missions__completion_date__isnull=False
                        )
                        .distinct()
                        .count()
                    )
                    period_low_rate = (period_low_completed / period_low_total) * 100
                else:
                    period_low_rate = 0

                low_priority_data.append(round(period_low_rate, 1))

                logger.debug(
                    "Period %s - %s: High priority: %.1f%% (%d/%d), Low priority: %.1f%% (%d/%d)",
                    period_start,
                    period_end,
                    period_high_rate,
                    period_high_completed if period_high_total > 0 else 0,
                    period_high_total,
                    period_low_rate,
                    period_low_completed if period_low_total > 0 else 0,
                    period_low_total,
                )

            data = {
                "format": time_filter.format,
                "labels": labels,
                "high_priority_data": high_priority_data,
                "low_priority_data": low_priority_data,
                "high_priority_total": high_priority_total,
                "high_priority_completed": high_priority_completed,
                "high_priority_rate": round(high_priority_rate, 1),
                "low_priority_total": low_priority_total,
                "low_priority_completed": low_priority_completed,
                "low_priority_rate": round(low_priority_rate, 1),
                "total_goals": total_goals,
                "completed_goals": completed_goals,
                "completion_rate": round(overall_completion_rate, 1),
                "start_date": start_date,
                "end_date": end_date,
                "domain": domain_id,
                "role": role,
            }

            logger.debug("Response data: %s", data)

            serializer = self.get_serializer(data=data)
            serializer.is_valid(raise_exception=True)

            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error("Error in GoalCompletionRateView: %s", str(e), exc_info=True)
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
