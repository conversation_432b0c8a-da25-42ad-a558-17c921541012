from rest_framework.generics import GenericAPIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
from django.utils.translation import gettext_lazy as _

from django.contrib.auth import get_user_model
from apps.clubs.models import Club
from apps.clubs.meetings.models import ClubMeeting
from apps.stats.serializers.summary_stats import SummaryStatsSerializer
from apps.stats.filters import TimeFilter

User = get_user_model()


class SummaryStatsAPIView(GenericAPIView):
    """
    API view to retrieve summary statistics for users, clubs, and meetings.

    Accepts period filters:
    - period: 'this_week', 'this_month', 'this_year'
    - start_date: custom start date (YYYY-MM-DD)
    - end_date: custom end date (YYYY-MM-DD)
    """

    serializer_class = SummaryStatsSerializer
    permission_classes = [IsAuthenticated]

    @extend_schema(
        tags=["Stats"],
        summary="Get summary statistics",
        description="Returns counts of users, clubs, and meetings with optional period filtering",
        parameters=[
            OpenApiParameter(
                name="period",
                type=str,
                description="Period filter: 'this_week', 'this_month', 'this_year'",
                required=False,
                enum=["this_week", "this_month", "this_year"],
            ),
            OpenApiParameter(
                name="start_date",
                type=str,
                description="Start date for custom period (YYYY-MM-DD)",
                required=False,
            ),
            OpenApiParameter(
                name="end_date",
                type=str,
                description="End date for custom period (YYYY-MM-DD)",
                required=False,
            ),
        ],
        responses={
            200: SummaryStatsSerializer,
            400: _("Bad request, invalid filter parameters"),
        },
        examples=[
            OpenApiExample(
                "All-time statistics",
                value={
                    "total_users": 1250,
                    "total_clubs": 75,
                    "total_meetings": 320,
                    "total_club_managers": 45,
                    "period": None,
                    "start_date": None,
                    "end_date": None,
                },
                response_only=True,
            ),
            OpenApiExample(
                "This month statistics",
                value={
                    "total_users": 50,
                    "total_clubs": 10,
                    "total_meetings": 45,
                    "total_club_managers": 8,
                    "period": "this_month",
                    "start_date": "2023-06-01T00:00:00Z",
                    "end_date": "2023-06-14T15:30:45Z",
                },
                response_only=True,
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        """Get summary statistics based on optional time filters."""

        time_filter = TimeFilter(request.query_params)

        user_club_filters = time_filter.get_date_filter_kwargs()

        meeting_filters = {}
        if time_filter.start_date:
            meeting_filters["start_time__gte"] = time_filter.start_date
        if time_filter.end_date:
            meeting_filters["start_time__lte"] = time_filter.end_date

        total_users = User.objects.filter(**user_club_filters).count()
        total_club_managers = User.objects.filter(
            role="club_manager", **user_club_filters
        ).count()
        total_clubs = Club.objects.filter(**user_club_filters).count()
        total_meetings = ClubMeeting.objects.filter(**meeting_filters).count()

        data = {
            "total_users": total_users,
            "total_clubs": total_clubs,
            "total_meetings": total_meetings,
            "total_club_managers": total_club_managers,
            "period": request.query_params.get("period"),
            "start_date": time_filter.start_date,
            "end_date": time_filter.end_date,
        }

        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)

        return Response(serializer.data, status=status.HTTP_200_OK)
