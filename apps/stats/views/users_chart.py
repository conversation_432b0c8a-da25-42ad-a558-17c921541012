from django.db.models import Count
from django.utils import timezone
from rest_framework.generics import GenericAPIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
import logging

from django.contrib.auth import get_user_model
from apps.stats.serializers.users_chart import UserChartSerializer
from apps.stats.filters import TimeSeriesFilter
from django.utils.translation import gettext_lazy as _

User = get_user_model()
logger = logging.getLogger(__name__)


class UsersChartView(GenericAPIView):
    """
    API view to retrieve chart data for user registrations over time periods.

    Provides data for the number of users registered per time period.

    Accepts parameters:
    - format: 'months' (default), 'weeks', 'days'
    - role: Filter by user role ('admin', 'club_manager', 'member')
    """

    serializer_class = UserChartSerializer
    permission_classes = [IsAuthenticated]

    @extend_schema(
        tags=["Stats"],
        summary="Get users chart data",
        description="Returns user registration counts grouped by time periods (months, weeks, or days), with optional role filtering",
        parameters=[
            OpenApiParameter(
                name="format",
                type=str,
                description="Time period format: 'months' (default), 'weeks', 'days'",
                required=False,
                enum=["months", "weeks", "days"],
            ),
            OpenApiParameter(
                name="role",
                type=str,
                description="Filter by user role",
                required=False,
                enum=["admin", "club_manager", "member"],
            ),
        ],
        responses={
            200: UserChartSerializer,
            400: "Bad request, invalid format parameter",
        },
        examples=[
            OpenApiExample(
                "Monthly user counts",
                value={
                    "format": "months",
                    "labels": [
                        "01/2023",
                        "02/2023",
                        "03/2023",
                        "04/2023",
                        "05/2023",
                        "06/2023",
                        "07/2023",
                        "08/2023",
                        "09/2023",
                        "10/2023",
                        "11/2023",
                        "12/2023",
                    ],
                    "data": [8, 15, 22, 18, 25, 32, 28, 42, 38, 45, 50, 65],
                    "total_users": 388,
                    "start_date": "2023-01-01T00:00:00Z",
                    "end_date": "2023-12-31T23:59:59Z",
                    "role": None,
                },
                response_only=True,
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        """Get user registration counts aggregated by time periods (months, weeks, or days)."""

        try:
            logger.info("UsersChartView accessed with params: %s", request.query_params)

            time_filter = TimeSeriesFilter(request.query_params)

            periods, labels = time_filter.generate_time_periods()

            role = request.query_params.get("role")

            start_date = time_filter.start_date
            end_date = time_filter.end_date

            logger.debug("Filtering users between %s and %s", start_date, end_date)

            users_queryset = User.objects.filter(
                created__gte=start_date, created__lte=end_date
            )

            if role:
                users_queryset = users_queryset.filter(role=role)
                logger.debug(
                    "Applied role filter: %s, count: %d", role, users_queryset.count()
                )

            user_counts = []
            for period_start, period_end in periods:
                count = users_queryset.filter(
                    created__gte=period_start, created__lte=period_end
                ).count()
                user_counts.append(count)
                logger.debug(
                    "Period %s - %s: %d users", period_start, period_end, count
                )

            data = {
                "format": time_filter.format,
                "labels": labels,
                "data": user_counts,
                "total_users": sum(user_counts),
                "start_date": start_date,
                "end_date": end_date,
                "role": role,
            }

            logger.debug("Response data: %s", data)

            serializer = self.get_serializer(data=data)
            serializer.is_valid(raise_exception=True)

            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error("Error in UsersChartView: %s", str(e), exc_info=True)
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
