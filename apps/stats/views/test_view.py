from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.utils.translation import gettext_lazy as _


class TestAPIView(APIView):
    """
    Simple test view to verify URL routing.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        """Return a simple success response to verify routing."""
        return Response({"message": _("Test view works")}, status=status.HTTP_200_OK)
