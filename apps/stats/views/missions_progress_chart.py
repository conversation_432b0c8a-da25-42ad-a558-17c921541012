from django.db.models import Count, Avg, F, Q, ExpressionWrapper, <PERSON>loat<PERSON>ield
from django.utils import timezone
from rest_framework.generics import GenericAPIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
import logging

from django.contrib.auth import get_user_model
from apps.missions.models import WeeklyMission, DailyGoal
from apps.stats.serializers.missions_progress_chart import (
    MissionProgressChartSerializer,
)
from apps.stats.filters import TimeSeriesFilter
from django.utils.translation import gettext_lazy as _

User = get_user_model()
logger = logging.getLogger(__name__)


class MissionsProgressChartView(GenericAPIView):
    """
    API view to retrieve chart data for mission completion rates over time periods.

    Provides data for the completion percentage of missions per time period
    and goal completion trends.

    Accepts parameters:
    - format: 'months' (default), 'weeks', 'days'
    - domain: Filter by domain ID
    """

    serializer_class = MissionProgressChartSerializer
    permission_classes = [IsAuthenticated]

    @extend_schema(
        tags=["Stats"],
        summary="Get missions progress chart data",
        description="Returns mission and goal completion percentages grouped by time periods (months, weeks, or days), with optional domain filtering",
        parameters=[
            OpenApiParameter(
                name="time_format",
                type=str,
                description="Time period format: 'months' (default), 'weeks', 'days'",
                required=False,
                enum=["months", "weeks", "days"],
            ),
            OpenApiParameter(
                name="domain",
                type=int,
                description="Filter by domain ID",
                required=False,
            ),
        ],
        responses={
            200: MissionProgressChartSerializer,
            400: "Bad request, invalid parameters",
        },
        examples=[
            OpenApiExample(
                "Monthly mission completion rates",
                value={
                    "format": "months",
                    "labels": [
                        "01/2023",
                        "02/2023",
                        "03/2023",
                        "04/2023",
                        "05/2023",
                        "06/2023",
                        "07/2023",
                        "08/2023",
                        "09/2023",
                        "10/2023",
                        "11/2023",
                        "12/2023",
                    ],
                    "data": [
                        45.5,
                        62.3,
                        58.7,
                        72.1,
                        65.8,
                        78.4,
                        81.2,
                        75.6,
                        83.7,
                        79.2,
                        88.5,
                        92.3,
                    ],
                    "goal_data": [
                        51.2,
                        68.5,
                        62.1,
                        75.3,
                        70.2,
                        82.6,
                        85.0,
                        78.9,
                        88.3,
                        82.5,
                        91.7,
                        95.2,
                    ],
                    "total_missions": 315,
                    "completed_missions": 218,
                    "total_goals": 945,
                    "completed_goals": 708,
                    "mission_completion_rate": 69.2,
                    "goal_completion_rate": 74.9,
                    "start_date": "2023-01-01T00:00:00Z",
                    "end_date": "2023-12-31T23:59:59Z",
                    "domain": None,
                },
                response_only=True,
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        """Get mission and goal completion rates aggregated by time periods (months, weeks, or days)."""

        try:
            logger.info(
                "MissionsProgressChartView accessed with params: %s",
                request.query_params,
            )

            # Use time_format instead of format to avoid DRF content negotiation conflicts
            query_params = request.query_params.copy()
            if 'time_format' in query_params:
                time_format_value = query_params.get('time_format')
                query_params['format'] = time_format_value
                del query_params['time_format']

            time_filter = TimeSeriesFilter(query_params)
            periods, labels = time_filter.generate_time_periods()

            domain_id = request.query_params.get("domain")
            if domain_id:
                try:

                    import uuid

                    if len(domain_id) > 10:
                        uuid.UUID(domain_id)
                    else:
                        int(domain_id)
                except (ValueError, TypeError):
                    return Response(
                        {
                            "error": f"Invalid domain ID: {domain_id}. Must be a valid UUID or integer."
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            start_date = time_filter.start_date
            end_date = time_filter.end_date

            logger.debug("Filtering missions between %s and %s", start_date, end_date)

            missions_queryset = WeeklyMission.objects.filter(
                created__gte=start_date, created__lte=end_date
            )

            if domain_id:
                missions_queryset = missions_queryset.filter(domain_id=domain_id)
                logger.debug(
                    "Applied domain filter: %s, count: %d",
                    domain_id,
                    missions_queryset.count(),
                )

            total_missions = missions_queryset.count()
            completed_missions = missions_queryset.filter(
                completion_date__isnull=False
            ).count()
            overall_mission_completion_rate = 0.0
            if total_missions > 0:
                overall_mission_completion_rate = (
                    completed_missions / total_missions
                ) * 100.0

            mission_ids = missions_queryset.values_list("id", flat=True)
            goals_queryset = DailyGoal.objects.filter(weekly_mission_id__in=mission_ids)

            total_goals = goals_queryset.count()
            completed_goals = goals_queryset.filter(
                completion_date__isnull=False
            ).count()
            overall_goal_completion_rate = 0.0
            if total_goals > 0:
                overall_goal_completion_rate = (completed_goals / total_goals) * 100.0

            mission_completion_rates = []
            goal_completion_rates = []

            for period_start, period_end in periods:

                period_missions = missions_queryset.filter(
                    created__gte=period_start, created__lte=period_end
                )
                period_mission_total = period_missions.count()

                if period_mission_total > 0:
                    period_mission_completed = period_missions.filter(
                        completion_date__isnull=False
                    ).count()
                    mission_completion_rate = (
                        period_mission_completed / period_mission_total
                    ) * 100.0
                else:
                    mission_completion_rate = 0.0

                mission_completion_rates.append(round(mission_completion_rate, 1))

                period_mission_ids = period_missions.values_list("id", flat=True)
                period_goals = goals_queryset.filter(
                    weekly_mission_id__in=period_mission_ids
                )
                period_goal_total = period_goals.count()

                if period_goal_total > 0:
                    period_goal_completed = period_goals.filter(
                        completion_date__isnull=False
                    ).count()
                    goal_completion_rate = (
                        period_goal_completed / period_goal_total
                    ) * 100.0
                else:
                    goal_completion_rate = 0.0

                goal_completion_rates.append(round(goal_completion_rate, 1))

                logger.debug(
                    "Period %s - %s: Missions %.1f%% (%d/%d), Goals %.1f%% (%d/%d)",
                    period_start,
                    period_end,
                    mission_completion_rate,
                    period_missions.filter(completion_date__isnull=False).count(),
                    period_mission_total,
                    goal_completion_rate,
                    period_goals.filter(completion_date__isnull=False).count(),
                    period_goal_total,
                )

            data = {
                "format": time_filter.format,
                "labels": labels,
                "data": mission_completion_rates,
                "goal_data": goal_completion_rates,
                "total_missions": total_missions,
                "completed_missions": completed_missions,
                "total_goals": total_goals,
                "completed_goals": completed_goals,
                "mission_completion_rate": round(overall_mission_completion_rate, 1),
                "goal_completion_rate": round(overall_goal_completion_rate, 1),
                "start_date": start_date,
                "end_date": end_date,
                "domain": domain_id,
            }

            logger.debug("Response data: %s", data)

            serializer = self.get_serializer(data=data)
            serializer.is_valid(raise_exception=True)

            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(
                "Error in MissionsProgressChartView: %s", str(e), exc_info=True
            )
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
