from django.db.models import Count
from django.utils import timezone
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
import traceback

from django.contrib.auth import get_user_model
from apps.clubs.meetings.models import ClubMeeting
from apps.stats.serializers.meetings_chart import MeetingChartSerializer
from apps.stats.filters import TimeSeriesFilter
from django.utils.translation import gettext_lazy as _

User = get_user_model()


class FixedMeetingsChartView(APIView):
    """
    API view to retrieve chart data for meetings over time periods.

    Provides data for the number of meetings per time period.

    Accepts time format parameter:
    - format: 'months' (default), 'weeks', 'days'
    """

    serializer_class = MeetingChartSerializer
    permission_classes = [IsAuthenticated]

    def get_serializer(self, *args, **kwargs):
        """Get the serializer instance to be used."""
        return self.serializer_class(*args, **kwargs)

    @extend_schema(
        tags=["Stats"],
        summary="Get meetings chart data",
        description="Returns meeting counts grouped by time periods (months, weeks, or days)",
        parameters=[
            OpenApiParameter(
                name="format",
                type=str,
                description="Time period format: 'months' (default), 'weeks', 'days'",
                required=False,
                enum=["months", "weeks", "days"],
            ),
        ],
        responses={
            200: MeetingChartSerializer,
            400: "Bad request, invalid format parameter",
        },
        examples=[
            OpenApiExample(
                "Monthly meeting counts",
                value={
                    "format": "months",
                    "labels": [
                        "01/2023",
                        "02/2023",
                        "03/2023",
                        "04/2023",
                        "05/2023",
                        "06/2023",
                        "07/2023",
                        "08/2023",
                        "09/2023",
                        "10/2023",
                        "11/2023",
                        "12/2023",
                    ],
                    "data": [5, 8, 12, 10, 15, 20, 18, 25, 22, 30, 28, 35],
                    "total_meetings": 228,
                    "start_date": "2023-01-01T00:00:00Z",
                    "end_date": "2023-12-31T23:59:59Z",
                },
                response_only=True,
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        """Get meeting counts aggregated by time periods (months, weeks, or days)."""

        try:
            print("DEBUG: Starting FixedMeetingsChartView.get()")
            print(f"DEBUG: Request parameters: {request.query_params}")

            time_filter = TimeSeriesFilter(request.query_params)

            periods, labels = time_filter.generate_time_periods()
            print(f"DEBUG: Got periods: {len(periods)} and labels: {len(labels)}")

            start_date = time_filter.start_date
            end_date = time_filter.end_date
            print(f"DEBUG: Start date: {start_date}, End date: {end_date}")

            meetings_in_range = ClubMeeting.objects.filter(
                start_time__gte=start_date, start_time__lte=end_date
            )
            print(f"DEBUG: Meetings count in range: {meetings_in_range.count()}")

            meeting_counts = []
            for period_start, period_end in periods:
                count = meetings_in_range.filter(
                    start_time__gte=period_start, start_time__lte=period_end
                ).count()
                meeting_counts.append(count)

            data = {
                "format": time_filter.format,
                "labels": labels,
                "data": meeting_counts,
                "total_meetings": sum(meeting_counts),
                "start_date": start_date,
                "end_date": end_date,
            }
            print(f"DEBUG: Data prepared: {data}")

            serializer = self.get_serializer(data=data)
            if not serializer.is_valid():
                print(f"DEBUG: Serializer validation errors: {serializer.errors}")
                return Response(
                    {
                        "error": _("Invalid data format"),
                        "details": serializer.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            print("DEBUG: Serializer validated successfully")
            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            print(f"DEBUG: Exception in FixedMeetingsChartView: {str(e)}")
            print(f"DEBUG: Traceback: {traceback.format_exc()}")
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
