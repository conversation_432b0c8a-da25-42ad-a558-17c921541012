from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status

from django.contrib.auth import get_user_model

User = get_user_model()


class TestViewTests(TestCase):
    """Test suite for the test view"""

    def setUp(self):
        """Set up test data"""
        self.admin_user = User.objects.create_user(
            email="<EMAIL>",
            password="adminpassword",
            username="admin",
            role="admin",
            is_staff=True,
            is_superuser=True,
        )

        self.client = APIClient()
        self.url = reverse("stats:test-view")
        self.direct_url = "/api/v1/stats/test/"
        print(f"DEBUG: URL from reverse is {self.url}")
        print(f"DEBUG: Direct URL is {self.direct_url}")

    def test_test_view(self):
        """Test accessing the test view"""
        self.client.force_authenticate(user=self.admin_user)

        response = self.client.get(self.url)
        print(
            f"DEBUG: Response status for test view (reversed URL): {response.status_code}"
        )
        print(f"DEBUG: Response content (reversed URL): {response.content}")

        direct_response = self.client.get(self.direct_url)
        print(
            f"DEBUG: Response status for test view (direct URL): {direct_response.status_code}"
        )
        print(f"DEBUG: Response content (direct URL): {direct_response.content}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(direct_response.status_code, status.HTTP_200_OK)
