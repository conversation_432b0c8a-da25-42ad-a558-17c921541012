from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework import status
import json
from datetime import timedelta

from django.contrib.auth import get_user_model
from apps.domains.models import Domain
from apps.missions.models import WeeklyMission, DailyGoal, Goal
from apps.okrs.models import SixWeekPeriod

User = get_user_model()


class MissionsProgressChartViewTest(TestCase):
    """Test cases for the MissionsProgressChartView."""

    def setUp(self):
        """Set up test data."""

        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpassword"
        )

        self.domain = Domain.objects.create(
            name="Test Domain",
            description="Test domain description",
            icon="test-icon.svg",
        )

        self.six_week_period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timedelta(days=42),
        )

        self.goal = Goal.objects.create(
            user=self.user,
            six_week_period=self.six_week_period,
            title="Test Goal",
            priority=Goal.PRIORITY_HIGH,
        )

        self.mission1 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.six_week_period,
            domain=self.domain,
            week_number=1,
            practice_days=[1, 3, 5],
        )
        self.mission1.goals.add(self.goal)

        self.mission2 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.six_week_period,
            domain=self.domain,
            week_number=2,
            practice_days=[2, 4, 6],
        )
        self.mission2.goals.add(self.goal)

        self.mission3 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.six_week_period,
            domain=self.domain,
            week_number=3,
            practice_days=[1, 3, 5],
            completion_date=timezone.now(),
        )
        self.mission3.goals.add(self.goal)

        self.daily_goal1 = DailyGoal.objects.create(
            weekly_mission=self.mission1,
            title="Daily Goal 1",
            priority=DailyGoal.PRIORITY_HIGH,
        )

        self.daily_goal2 = DailyGoal.objects.create(
            weekly_mission=self.mission1,
            title="Daily Goal 2",
            priority=DailyGoal.PRIORITY_LOW,
        )

        self.daily_goal3 = DailyGoal.objects.create(
            weekly_mission=self.mission2,
            title="Daily Goal 3",
            priority=DailyGoal.PRIORITY_HIGH,
            completion_date=timezone.now(),
        )

        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        self.url = reverse("stats:charts-missions-progress")

    def test_missions_progress_chart_authentication(self):
        """Test that the view requires authentication."""

        self.client.logout()

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_missions_progress_chart_success(self):
        """Test successful retrieval of missions progress chart data."""
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()
        self.assertIn("format", data)
        self.assertIn("labels", data)
        self.assertIn("data", data)
        self.assertIn("goal_data", data)
        self.assertIn("total_missions", data)
        self.assertIn("completed_missions", data)
        self.assertIn("total_goals", data)
        self.assertIn("completed_goals", data)
        self.assertIn("mission_completion_rate", data)
        self.assertIn("goal_completion_rate", data)

        self.assertEqual(data["total_missions"], 3)
        self.assertEqual(data["completed_missions"], 1)
        self.assertEqual(data["total_goals"], 3)
        self.assertEqual(data["completed_goals"], 1)

        self.assertAlmostEqual(data["mission_completion_rate"], 33.3, delta=0.1)
        self.assertAlmostEqual(data["goal_completion_rate"], 33.3, delta=0.1)

    def test_missions_progress_chart_format_filter(self):
        """Test missions progress chart with format filter."""
        for format_param in ["months", "weeks", "days"]:
            response = self.client.get(self.url, {"time_format": format_param})

            self.assertEqual(response.status_code, status.HTTP_200_OK)

            data = response.json()
            self.assertEqual(data["format"], format_param)

    def test_missions_progress_chart_domain_filter(self):
        """Test missions progress chart with domain filter."""

        domain2 = Domain.objects.create(
            name="Second Domain",
            description="Second domain description",
            icon="second-icon.svg",
        )

        mission4 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.six_week_period,
            domain=domain2,
            week_number=4,
            practice_days=[1, 3, 5],
        )

        response = self.client.get(self.url, {"domain": self.domain.id})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(data["domain"], str(self.domain.id))
        self.assertEqual(data["total_missions"], 3)

        response = self.client.get(self.url, {"domain": domain2.id})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(data["domain"], str(domain2.id))
        self.assertEqual(data["total_missions"], 1)

    def test_missions_progress_chart_invalid_domain(self):
        """Test missions progress chart with invalid domain ID."""
        response = self.client.get(self.url, {"domain": "invalid"})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("error", response.json())

    def test_missions_progress_chart_invalid_format(self):
        """Test missions progress chart with invalid format."""
        response = self.client.get(self.url, {"time_format": "invalid"})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("error", response.json())
