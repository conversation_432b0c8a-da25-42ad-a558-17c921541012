from django.test import TestCase
from django.utils import timezone
from rest_framework import status
from datetime import timedelta
import json

from django.contrib.auth import get_user_model
from apps.stats.filters import TimeSeriesFilter
from apps.stats.views.users_chart import UsersChartView
from apps.stats.serializers.users_chart import UserChartSerializer

User = get_user_model()


class UsersChartViewTests(TestCase):
    """Test suite for UsersChartView functionality"""

    def setUp(self):
        """Set up test data"""

        now = timezone.now()

        self.admin_user = User.objects.create_user(
            email="<EMAIL>",
            password="adminpassword",
            username="admin",
            fullname="Admin User",
            role="admin",
            is_staff=True,
            is_superuser=True,
        )

        User.objects.filter(pk=self.admin_user.pk).update(created=now)

        for i in range(3):
            user = User.objects.create_user(
                email=f"clubmanager{i+1}@example.com",
                password="password",
                username=f"clubmanager{i+1}",
                fullname=f"Club Manager {i+1}",
                role="club_manager",
            )

            User.objects.filter(pk=user.pk).update(created=now - timedelta(days=i + 1))

        prev_month = now - timedelta(days=35)
        for i in range(2):
            user = User.objects.create_user(
                email=f"member{i+1}@example.com",
                password="password",
                username=f"member{i+1}",
                fullname=f"Member {i+1}",
                role="member",
            )

            User.objects.filter(pk=user.pk).update(
                created=prev_month - timedelta(days=i)
            )

        two_months_ago = now - timedelta(days=65)
        old_admin = User.objects.create_user(
            email="<EMAIL>",
            password="password",
            username="oldadmin",
            fullname="Old Admin",
            role="admin",
        )

        User.objects.filter(pk=old_admin.pk).update(created=two_months_ago)

        self.time_filter = TimeSeriesFilter({"format": "months"})

    def test_time_filter_months(self):
        """Test that the TimeSeriesFilter generates correct month periods"""
        time_filter = TimeSeriesFilter({"format": "months"})
        periods, labels = time_filter.generate_time_periods()

        self.assertEqual(len(periods), 12)
        self.assertEqual(len(labels), 12)

        for label in labels:
            self.assertRegex(label, r"^\d{2}/\d{4}$")

    def test_time_filter_weeks(self):
        """Test that the TimeSeriesFilter generates correct week periods"""
        time_filter = TimeSeriesFilter({"format": "weeks"})
        periods, labels = time_filter.generate_time_periods()

        self.assertEqual(len(periods), 12)
        self.assertEqual(len(labels), 12)

        for label in labels:
            self.assertRegex(label, r"^\d{2}/\d{2}-\d{2}/\d{2}$")

    def test_time_filter_days(self):
        """Test that the TimeSeriesFilter generates correct day periods"""
        time_filter = TimeSeriesFilter({"format": "days"})
        periods, labels = time_filter.generate_time_periods()

        self.assertEqual(len(periods), 12)
        self.assertEqual(len(labels), 12)

        for label in labels:
            self.assertRegex(label, r"^\d{2}/\d{2}$")

    def test_serializer_validation(self):
        """Test that the UserChartSerializer properly validates data"""

        data = {
            "format": "months",
            "labels": [
                "01/2023",
                "02/2023",
                "03/2023",
                "04/2023",
                "05/2023",
                "06/2023",
                "07/2023",
                "08/2023",
                "09/2023",
                "10/2023",
                "11/2023",
                "12/2023",
            ],
            "data": [8, 15, 22, 18, 25, 32, 28, 42, 38, 45, 50, 65],
            "total_users": 388,
            "start_date": "2023-01-01T00:00:00Z",
            "end_date": "2023-12-31T23:59:59Z",
            "role": None,
        }

        serializer = UserChartSerializer(data=data)
        self.assertTrue(serializer.is_valid())

    def test_user_counts_by_role(self):
        """Test that the user counts by role are correct"""

        total_users = User.objects.count()
        self.assertEqual(total_users, 7)

        admin_users = User.objects.filter(role="admin").count()
        self.assertEqual(admin_users, 2)

        club_manager_users = User.objects.filter(role="club_manager").count()
        self.assertEqual(club_manager_users, 3)

        member_users = User.objects.filter(role="member").count()
        self.assertEqual(member_users, 2)
