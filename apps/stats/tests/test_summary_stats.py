from django.test import TestCase
from django.urls import reverse
from django.utils import timezone


from rest_framework.test import APIClient  # noqa
from rest_framework import status  # noqa
from datetime import timed<PERSON>ta
import json

from django.contrib.auth import get_user_model
from apps.clubs.models import Club, ClubType
from apps.clubs.meetings.models import ClubMeeting

User = get_user_model()

# Note: The linter may incorrectly flag APIClient.force_authenticate as an unknown attribute,


class SummaryStatsTests(TestCase):
    """Test suite for summary statistics API"""

    def setUp(self):
        """Set up test data"""

        self.admin_user = User.objects.create_user(
            email="<EMAIL>",
            password="adminpassword",
            username="admin",
            role="admin",
            is_staff=True,
            is_superuser=True,
        )

        self.regular_user1 = User.objects.create_user(
            email="<EMAIL>",
            password="userpassword1",
            username="regularuser1",
            role="member",
        )

        self.regular_user2 = User.objects.create_user(
            email="<EMAIL>",
            password="userpassword2",
            username="regularuser2",
            role="member",
        )

        self.club_manager_user = User.objects.create_user(
            email="<EMAIL>",
            password="managerpassword",
            username="clubmanager",
            role="club_manager",
        )

        self.club_type = ClubType.objects.create(
            name="Test Club Type",
            created_by=self.admin_user,
            visibility=ClubType.VISIBILITY_PUBLIC,
        )

        self.club1 = Club.objects.create(
            name="Test Club 1",
            type=self.club_type,
            manager=self.admin_user,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_OPEN,
        )
        self.club1.members.add(self.admin_user, self.regular_user1)

        self.club2 = Club.objects.create(
            name="Test Club 2",
            type=self.club_type,
            manager=self.club_manager_user,
            privacy=Club.PRIVACY_OPEN,
            join_permissions=Club.JOIN_PERMISSIONS_OPEN,
        )
        self.club2.members.add(
            self.regular_user1, self.regular_user2, self.club_manager_user
        )

        now = timezone.now()

        self.future_meeting = ClubMeeting.objects.create(
            club=self.club1,
            title="Future Meeting",
            description="Future meeting description",
            start_time=now + timedelta(days=2),
            end_time=now + timedelta(days=2, hours=2),
            organizer=self.admin_user,
        )

        last_week = now - timedelta(days=8)
        self.last_week_meeting = ClubMeeting.objects.create(
            club=self.club2,
            title="Last Week Meeting",
            description="Last week meeting description",
            start_time=last_week,
            end_time=last_week + timedelta(hours=1),
            organizer=self.club_manager_user,
            status=ClubMeeting.STATUS_COMPLETED,
        )

        prev_month = now - timedelta(days=40)
        self.prev_month_meeting = ClubMeeting.objects.create(
            club=self.club1,
            title="Previous Month Meeting",
            description="Previous month meeting description",
            start_time=prev_month,
            end_time=prev_month + timedelta(hours=1),
            organizer=self.admin_user,
            status=ClubMeeting.STATUS_COMPLETED,
        )

        self.client = APIClient()

        self.url = reverse("stats:summary-stats")

    def test_unauthenticated_access(self):
        """Test that unauthenticated users cannot access the stats API"""
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_all_time_stats(self):
        """Test retrieving all-time statistics"""
        self.client.force_authenticate(user=self.admin_user)  # noqa

        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()
        self.assertEqual(data["total_users"], 4)
        self.assertEqual(data["total_clubs"], 2)
        self.assertEqual(data["total_meetings"], 3)
        self.assertEqual(data["total_club_managers"], 1)

        self.assertIsNone(data["period"])
        self.assertIsNone(data["start_date"])
        self.assertIsNone(data["end_date"])

    def test_this_week_stats(self):
        """Test retrieving statistics for current week"""
        self.client.force_authenticate(user=self.admin_user)  # noqa

        response = self.client.get(self.url, {"period": "this_week"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()

        self.assertEqual(data["total_users"], 4)
        self.assertEqual(data["total_clubs"], 2)
        self.assertIn("total_club_managers", data)
        self.assertEqual(data["total_club_managers"], 1)

        self.assertIn("total_meetings", data)
        self.assertIsInstance(data["total_meetings"], int)

        self.assertEqual(data["period"], "this_week")
        self.assertIsNotNone(data["start_date"])
        self.assertIsNotNone(data["end_date"])

    def test_this_month_stats(self):
        """Test retrieving statistics for current month"""
        self.client.force_authenticate(user=self.admin_user)  # noqa

        response = self.client.get(self.url, {"period": "this_month"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()

        self.assertEqual(data["total_users"], 4)
        self.assertEqual(data["total_clubs"], 2)
        self.assertIn("total_club_managers", data)
        self.assertEqual(data["total_club_managers"], 1)

        self.assertIn("total_meetings", data)
        self.assertIsInstance(data["total_meetings"], int)

        self.assertEqual(data["period"], "this_month")
        self.assertIsNotNone(data["start_date"])
        self.assertIsNotNone(data["end_date"])

    def test_this_year_stats(self):
        """Test retrieving statistics for current year"""
        self.client.force_authenticate(user=self.admin_user)  # noqa

        response = self.client.get(self.url, {"period": "this_year"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()

        self.assertEqual(data["total_users"], 4)
        self.assertEqual(data["total_clubs"], 2)
        self.assertIn("total_club_managers", data)
        self.assertEqual(data["total_club_managers"], 1)

        self.assertIn("total_meetings", data)
        self.assertIsInstance(data["total_meetings"], int)

        self.assertGreaterEqual(data["total_meetings"], 1)

        self.assertEqual(data["period"], "this_year")
        self.assertIsNotNone(data["start_date"])
        self.assertIsNotNone(data["end_date"])

    def test_custom_date_range(self):
        """Test retrieving statistics for a custom date range"""
        self.client.force_authenticate(user=self.admin_user)  # noqa

        start_date = (timezone.now() - timedelta(days=9)).strftime("%Y-%m-%d")
        end_date = (timezone.now() - timedelta(days=7)).strftime("%Y-%m-%d")

        response = self.client.get(
            self.url, {"start_date": start_date, "end_date": end_date}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()

        self.assertEqual(data["total_users"], 0)
        self.assertEqual(data["total_clubs"], 0)
        self.assertEqual(data["total_club_managers"], 0)

        self.assertIn("total_meetings", data)
        self.assertIsInstance(data["total_meetings"], int)

        self.assertIsNone(data["period"])
        self.assertIsNotNone(data["start_date"])
        self.assertIsNotNone(data["end_date"])

    def test_invalid_period(self):
        """Test using an invalid period parameter"""
        self.client.force_authenticate(user=self.admin_user)  # noqa

        response = self.client.get(self.url, {"period": "invalid_period"})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_invalid_date_format(self):
        """Test using an invalid date format"""
        self.client.force_authenticate(user=self.admin_user)  # noqa

        response = self.client.get(self.url, {"start_date": "2023/01/01"})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        response = self.client.get(self.url, {"end_date": "01-01-2023"})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_start_date_after_end_date(self):
        """Test using a start date that's after the end date"""
        self.client.force_authenticate(user=self.admin_user)  # noqa

        start_date = (timezone.now()).strftime("%Y-%m-%d")
        end_date = (timezone.now() - timedelta(days=5)).strftime("%Y-%m-%d")

        response = self.client.get(
            self.url, {"start_date": start_date, "end_date": end_date}
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
