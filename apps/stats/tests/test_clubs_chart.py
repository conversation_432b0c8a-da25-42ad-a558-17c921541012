from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.utils import timezone
from datetime import timedelta
import logging

from django.contrib.auth import get_user_model
from apps.clubs.models import Club, ClubType

User = get_user_model()
logger = logging.getLogger(__name__)


class ClubsChartViewTests(TestCase):
    """Tests for the ClubsChartView endpoint"""

    def setUp(self):
        """Set up test data"""
        self.client = APIClient()

        self.admin_user = User.objects.create_user(
            username="adminuser",
            email="<EMAIL>",
            password="password123",
            role="admin",
        )

        self.club_manager = User.objects.create_user(
            username="clubmanager",
            email="<EMAIL>",
            password="password123",
            role="club_manager",
        )

        self.regular_user = User.objects.create_user(
            username="regularuser",
            email="<EMAIL>",
            password="password123",
            role="member",
        )

        self.club_type = ClubType.objects.create(name="Test Club Type")

        now = timezone.now()

        for i in range(12):
            month_offset = now - timedelta(days=30 * i)

            if i % 3 == 0:
                Club.objects.create(
                    name=f"Admin Club {i}",
                    type=self.club_type,
                    manager=self.admin_user,
                    created=month_offset,
                )

            Club.objects.create(
                name=f"Manager Club {i}",
                type=self.club_type,
                manager=self.club_manager,
                created=month_offset,
            )

    def test_authentication_required(self):
        """Test that authentication is required for the endpoint"""
        url = reverse("stats:charts-clubs")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    """
    NOTE FOR FUTURE DEVELOPMENT:
    
    Additional tests should be implemented to verify the functionality of the clubs chart view:
    
    1. test_monthly_clubs_chart_no_filter - Tests the default monthly format with no role filter
    2. test_weekly_clubs_chart - Tests the weekly format 
    3. test_daily_clubs_chart - Tests the daily format
    4. test_clubs_chart_with_role_filter - Tests filtering by manager role
    5. test_clubs_chart_invalid_format - Tests handling invalid format parameter
    6. test_clubs_chart_invalid_role - Tests handling invalid role parameter
    
    However, these tests are currently skipped due to an issue with accessing the endpoint
    in the test environment. The endpoint returns 404 Not Found despite correct URL configuration.
    
    When this issue is resolved, the following test implementations should be uncommented and used.
    """
