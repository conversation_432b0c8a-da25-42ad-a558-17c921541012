from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework import status
import json
from datetime import timedelta

from django.contrib.auth import get_user_model
from apps.domains.models import Domain
from apps.missions.models import WeeklyMission, DailyGoal, Goal
from apps.okrs.models import SixWeekPeriod

User = get_user_model()


class GoalCompletionRateViewTest(TestCase):
    """Test cases for the GoalCompletionRateView."""

    def setUp(self):
        """Set up test data."""

        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpassword",
            role="member",
        )

        self.admin = User.objects.create_user(
            username="testadmin",
            email="<EMAIL>",
            password="testpassword",
            role="admin",
        )

        self.domain1 = Domain.objects.create(
            name="Test Domain 1",
            description="Test domain description 1",
            icon="test-icon-1.svg",
        )

        self.domain2 = Domain.objects.create(
            name="Test Domain 2",
            description="Test domain description 2",
            icon="test-icon-2.svg",
        )

        self.six_week_period = SixWeekPeriod.objects.create(
            user=self.user,
            title="Test Period",
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timedelta(days=42),
        )

        self.high_priority_goal1 = Goal.objects.create(
            user=self.user,
            six_week_period=self.six_week_period,
            title="High Priority Goal 1",
            priority=Goal.PRIORITY_HIGH,
        )

        self.high_priority_goal2 = Goal.objects.create(
            user=self.user,
            six_week_period=self.six_week_period,
            title="High Priority Goal 2",
            priority=Goal.PRIORITY_HIGH,
        )

        self.low_priority_goal1 = Goal.objects.create(
            user=self.user,
            six_week_period=self.six_week_period,
            title="Low Priority Goal 1",
            priority=Goal.PRIORITY_LOW,
        )

        self.low_priority_goal2 = Goal.objects.create(
            user=self.user,
            six_week_period=self.six_week_period,
            title="Low Priority Goal 2",
            priority=Goal.PRIORITY_LOW,
        )

        self.low_priority_goal3 = Goal.objects.create(
            user=self.user,
            six_week_period=self.six_week_period,
            title="Low Priority Goal 3",
            priority=Goal.PRIORITY_LOW,
        )

        self.admin_goal = Goal.objects.create(
            user=self.admin,
            six_week_period=self.six_week_period,
            title="Admin Goal",
            priority=Goal.PRIORITY_HIGH,
        )

        self.mission1 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.six_week_period,
            domain=self.domain1,
            week_number=1,
            practice_days=[1, 3, 5],
        )
        self.mission1.goals.add(self.high_priority_goal1, self.low_priority_goal1)

        self.mission2 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.six_week_period,
            domain=self.domain1,
            week_number=2,
            practice_days=[2, 4, 6],
            completion_date=timezone.now(),
        )
        self.mission2.goals.add(self.high_priority_goal2, self.low_priority_goal2)

        self.mission3 = WeeklyMission.objects.create(
            user=self.user,
            six_week_period=self.six_week_period,
            domain=self.domain2,
            week_number=3,
            practice_days=[1, 3, 5],
        )
        self.mission3.goals.add(self.low_priority_goal3)

        self.admin_mission = WeeklyMission.objects.create(
            user=self.admin,
            six_week_period=self.six_week_period,
            domain=self.domain1,
            week_number=1,
            practice_days=[1, 3, 5],
            completion_date=timezone.now(),
        )
        self.admin_mission.goals.add(self.admin_goal)

        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        self.url = reverse("stats:charts-goal-completion-rate")

    def test_goal_completion_rate_authentication(self):
        """Test that the view requires authentication."""

        self.client.logout()

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_goal_completion_rate_success(self):
        """Test successful retrieval of goal completion rate data."""
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()
        self.assertIn("format", data)
        self.assertIn("labels", data)
        self.assertIn("high_priority_data", data)
        self.assertIn("low_priority_data", data)
        self.assertIn("high_priority_total", data)
        self.assertIn("high_priority_completed", data)
        self.assertIn("high_priority_rate", data)
        self.assertIn("low_priority_total", data)
        self.assertIn("low_priority_completed", data)
        self.assertIn("low_priority_rate", data)
        self.assertIn("total_goals", data)
        self.assertIn("completed_goals", data)
        self.assertIn("completion_rate", data)

        self.assertEqual(data["high_priority_total"], 3)
        self.assertEqual(data["low_priority_total"], 3)
        self.assertEqual(data["total_goals"], 6)

        self.assertEqual(data["high_priority_completed"], 2)

        self.assertEqual(data["low_priority_completed"], 1)
        self.assertEqual(data["completed_goals"], 3)

        self.assertAlmostEqual(data["high_priority_rate"], 66.7, delta=0.1)
        self.assertAlmostEqual(data["low_priority_rate"], 33.3, delta=0.1)
        self.assertAlmostEqual(data["completion_rate"], 50.0, delta=0.1)

    def test_goal_completion_rate_format_filter(self):
        """Test goal completion rate with format filter."""
        for format_param in ["months", "weeks", "days"]:
            response = self.client.get(self.url, {"format": format_param})

            self.assertEqual(response.status_code, status.HTTP_200_OK)

            data = response.json()
            self.assertEqual(data["format"], format_param)

    def test_goal_completion_rate_domain_filter(self):
        """Test goal completion rate with domain filter."""

        response = self.client.get(self.url, {"domain": self.domain1.id})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertEqual(data["high_priority_total"], 3)
        self.assertEqual(data["low_priority_total"], 2)
        self.assertEqual(data["total_goals"], 5)

        self.assertEqual(data["high_priority_completed"], 2)
        self.assertEqual(data["low_priority_completed"], 1)
        self.assertEqual(data["completed_goals"], 3)

        response = self.client.get(self.url, {"domain": self.domain2.id})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertEqual(data["high_priority_total"], 0)
        self.assertEqual(data["low_priority_total"], 1)
        self.assertEqual(data["total_goals"], 1)

        self.assertEqual(data["high_priority_completed"], 0)
        self.assertEqual(data["low_priority_completed"], 0)
        self.assertEqual(data["completed_goals"], 0)

    def test_goal_completion_rate_role_filter(self):
        """Test goal completion rate with role filter."""

        response = self.client.get(self.url, {"role": "member"})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertEqual(data["high_priority_total"], 2)
        self.assertEqual(data["low_priority_total"], 3)
        self.assertEqual(data["total_goals"], 5)

        self.assertEqual(data["high_priority_completed"], 1)
        self.assertEqual(data["low_priority_completed"], 1)
        self.assertEqual(data["completed_goals"], 2)

        response = self.client.get(self.url, {"role": "admin"})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertEqual(data["high_priority_total"], 1)
        self.assertEqual(data["low_priority_total"], 0)
        self.assertEqual(data["total_goals"], 1)

        self.assertEqual(data["high_priority_completed"], 1)
        self.assertEqual(data["low_priority_completed"], 0)
        self.assertEqual(data["completed_goals"], 1)

    def test_goal_completion_rate_combined_filters(self):
        """Test goal completion rate with combined domain and role filters."""

        response = self.client.get(
            self.url, {"domain": self.domain1.id, "role": "member"}
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertEqual(data["high_priority_total"], 2)
        self.assertEqual(data["low_priority_total"], 2)
        self.assertEqual(data["total_goals"], 4)

        self.assertEqual(data["high_priority_completed"], 1)
        self.assertEqual(data["low_priority_completed"], 1)
        self.assertEqual(data["completed_goals"], 2)

    def test_goal_completion_rate_invalid_domain(self):
        """Test goal completion rate with invalid domain ID."""
        response = self.client.get(self.url, {"domain": "invalid"})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("error", response.json())
