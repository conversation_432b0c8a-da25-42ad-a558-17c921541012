include .env

build-dev:
	@echo "Building $(TARGET)..."
	docker compose -f docker-compose.dev.yml up -d --build

build-prod:
	@echo "Building $(TARGET)..."
	docker compose -f docker-compose.prod.yml up -d --build

# API commands

it-api:
	@echo "interacting with $(TARGET) API..."
	docker exec -it $(TARGET)api bash

log-api:
	@echo "Showing $(TARGET) API logs..."
	docker logs -f $(TARGET)api

inspect-api:
	@echo "Inspecting $(TARGET) API..."
	docker inspect $(TARGET)api

down-v:
	@echo "Stopping $(TARGET)..."
	if [ "$(TARGET)" = "taj" ]; then \
		docker compose -f docker-compose.dev.yml down -v; \
	else \
		docker compose -f docker-compose.prod.yml down -v; \
	fi

make down:
	@echo "Stopping $(TARGET)..."
	if [ "$(TARGET)" = "taj" ]; then \
		docker compose -f docker-compose.dev.yml down; \
	else \
		docker compose -f docker-compose.prod.yml down; \
	fi

dump-data:
	@echo "Dumping PostgreSQL data to specified location..."
	docker exec $(TARGET)db pg_dump --clean -U postgres postgres > dumps/dump_$(shell date +'%Y%m%d').sql
	@echo "Dump completed and saved at dumps"

load-data:
	@echo "Loading PostgreSQL data from specified location..."
	docker exec -i $(TARGET)db psql -U $(shell grep DATABASE_USER .env | cut -d '=' -f2) -d $(shell grep DATABASE_NAME .env | cut -d '=' -f2) < $(path)
	@echo "Data loaded successfully"

reset-db:
	@echo "Dropping all tables and resetting the database schema..."
	docker exec -i $(TARGET)db psql -U postgres -d postgres -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;"
	@echo "Database reset completed."


# celery commands

it-celery:
	@echo "interacting with $(TARGET) Celery..."
	docker exec -it $(TARGET)celery bash

log-celery:
	@echo "Showing $(TARGET) Celery logs..."
	docker logs -f $(TARGET)celery

restart-celery:
	@echo "Restarting Celery..."
	docker restart $(TARGET)celery

# tests

test:
	@echo "Running tests..."
	docker exec $(TARGET)api python manage.py makemigrations
	docker exec $(TARGET)api python scripts/fix_migrations.py
	docker exec $(TARGET)api python manage.py migrate
	docker exec $(TARGET)api pytest -q --disable-warnings --durations=10 --durations-min=1.0

collect-only:
	@echo "Running tests..."
	docker exec $(TARGET)api pytest --collect-only

test-coverage:
	@echo "Running tests with coverage..."
	docker exec $(TARGET)api python manage.py makemigrations
	docker exec $(TARGET)api python scripts/fix_migrations.py
	docker exec $(TARGET)api python manage.py migrate
	docker exec $(TARGET)api pytest --cov=. --disable-warnings -q --durations=10 --durations-min=1.0 --cov-report html

test-file:
	@echo "Running tests..."
	docker exec $(TARGET)api python manage.py makemigrations
	docker exec $(TARGET)api python scripts/fix_migrations.py
	docker exec $(TARGET)api python manage.py migrate
	docker exec $(TARGET)api pytest $(path) --disable-warnings -q --durations=10 --durations-min=1.0 -s

# clear redis cache
clear-cache:
	@echo "Clearing cache..."
	docker exec $(TARGET)redis redis-cli flushall

# internationalization
translate:
	@echo "Making messages..."
	docker exec $(TARGET)api python manage.py translate
	
# migrations
migrate:
	@echo "Migrating..."
	docker exec $(TARGET)api python manage.py makemigrations ${app_name}
	docker exec $(TARGET)api python manage.py migrate ${app_name}

fix-migrations:
	@echo "Fixing migration issues..."
	docker exec $(TARGET)api python scripts/fix_migrations.py

start-app:
	@echo "Starting app..."
	docker exec $(TARGET)api python manage.py startapp ${app_name}