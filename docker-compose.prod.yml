version: "3.8"
name: tajapi
services:
  db:
    container_name: tajdb
    image: postgres:14.3-alpine
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      POSTGRES_USER: ${DATABASE_USER}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
      POSTGRES_DB: ${DATABASE_NAME}
    env_file:
      - .env
    networks:
      - taj
    restart: always

  redis:
    image: redis:alpine
    container_name: tajredis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/var/lib/redis
      - redis-config:/usr/local/etc/redis/redis.conf
    env_file:
      - .env
    networks:
      - taj
    restart: always

  api:
    container_name: tajapi
    build:
      context: .
      dockerfile: docker/Dockerfile
    restart: always
    ports:
      - ${API_PORT}:${API_PORT}
    volumes:
      - .:/app
      - static:/var/www/taj/static/
      - uploads:/var/www/taj/uploads/
      - /var/www/taj/uploads/:/var/www/taj/uploads/
      - /var/www/taj/static/:/var/www/taj/static/
      - ./locale:/app/locale
    environment:
      - ENV=0
    depends_on:
      - db
      - redis
    env_file:
      - .env
    networks:
      - taj

  celery:
    container_name: tajcelery
    build:
      context: .
      dockerfile: docker/Dockerfile
    restart: always
    command: celery -A config.celery worker --loglevel=INFO
    volumes:
      - .:/app
    depends_on:
      - db
    env_file:
      - .env
    networks:
      - taj

  celery-beat:
    container_name: tajcelerybeat
    build:
      context: .
      dockerfile: docker/Dockerfile
    restart: always
    command: sh /app/docker/entrypoint-beat.sh
    environment:
      - POSTGRES_USER=${DATABASE_USER}
      - POSTGRES_PASSWORD=${DATABASE_PASSWORD}
      - POSTGRES_DB=${DATABASE_NAME}
    volumes:
      - .:/app
      - ./logs:/app/logs
    depends_on:
      - db
      - api
      - redis
    env_file:
      - .env
    networks:
      - taj

volumes:
  postgres_data:
  static:
  uploads:
  redis_data:
  redis-config:

networks:
  taj:
    driver: bridge
