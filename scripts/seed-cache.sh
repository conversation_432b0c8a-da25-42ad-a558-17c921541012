#!/bin/bash

TARGET=$(grep '^TARGET=' .env | awk -F '=' '{print $2}' | tr -d '\r')

REDIS_CONTAINER="${TARGET}redis"

NUM_ENTRIES=100000

VALUE_SIZE=1000

echo "Bulk creating ${NUM_ENTRIES} cache entries in Redis..."

for i in $(seq 1 "$NUM_ENTRIES"); do
  KEY="test_key_$i"
  VALUE=$(head -c "$VALUE_SIZE" </dev/urandom | base64 | tr -dc 'A-Za-z0-9' | head -c "$VALUE_SIZE")

  docker exec "$REDIS_CONTAINER" redis-cli SET "$KEY" "$VALUE" >/dev/null
done

echo "Bulk cache creation complete."
