"""
<PERSON><PERSON>t to update .env files by removing config variables that are now in config.yml.
This script will:
1. Find all .env* files in the project
2. Keep only the secret variables in these files
3. Create backup files before making changes
"""

import os
import re
import shutil
from pathlib import Path


BASE_DIR = Path(__file__).resolve().parent.parent


SECRET_VARIABLES = [
    "SECRET_KEY",
    "DATABASE_PASSWORD",
    "POSTGRES_PASSWORD",
    "EMAIL_HOST_PASSWORD",
    "GMAIL_HOST_PASSWORD",
    "SENDGRID_API_KEY",
    "ADMIN_PASSWORD",
    "GOOGLE_CLIENT_SECRET",
    "SENTRY_DSN",
]


ENV_FILE_PATTERN = re.compile(r"\.env(\..+)?$")


def is_secret_variable(line):
    """Check if a line contains a secret variable that should be kept."""

    if not line.strip() or line.strip().startswith("#"):
        return True

    for secret in SECRET_VARIABLES:
        if line.strip().startswith(f"{secret}="):
            return True

    if "=" in line:
        var_name, var_value = line.split("=", 1)
        var_name = var_name.strip()

        secret_indicators = [
            "KEY",
            "SECRET",
            "PASSWORD",
            "TOKEN",
            "CREDENTIALS",
            "AUTH",
        ]
        if any(indicator in var_name.upper() for indicator in secret_indicators):
            return True

    return False


def update_env_file(file_path):
    """Update an .env file by removing non-secret variables."""

    backup_path = f"{file_path}.bak"
    shutil.copy2(file_path, backup_path)
    print(f"Created backup: {backup_path}")

    with open(file_path, "r") as f:
        lines = f.readlines()

    secret_lines = []
    for line in lines:
        if is_secret_variable(line):
            secret_lines.append(line)

    with open(file_path, "w") as f:
        f.write("# This file contains only secret variables\n")
        f.write("# Configuration variables have been moved to docker/config.yml\n\n")
        f.writelines(secret_lines)

    print(
        f"Updated {file_path} - removed {len(lines) - len(secret_lines)} non-secret configuration variables"
    )


def find_and_update_env_files():
    """Find all .env files in the project and update them."""
    env_files = []

    for root, dirs, files in os.walk(BASE_DIR):
        for file in files:
            if ENV_FILE_PATTERN.match(file):
                env_files.append(os.path.join(root, file))

    if env_files:
        print(f"Found {len(env_files)} .env files to update:")
        for env_file in env_files:
            print(f" - {env_file}")

        confirm = input("\nDo you want to update these files? (y/n): ").lower().strip()
        if confirm == "y":
            for env_file in env_files:
                update_env_file(env_file)
            print("\nAll .env files have been updated successfully!")
        else:
            print("\nOperation cancelled. No files were modified.")
    else:
        print("No .env files found in the project.")


if __name__ == "__main__":
    print("This script will update .env files to keep only secret variables.")
    print("Configuration variables should be moved to docker/config.yml")
    print("Backups of the original files will be created before making changes.\n")
    find_and_update_env_files()
