#!/bin/bash

set -e

FORMAT_PYTHON=true
FORMAT_HTML=true
FORMAT_SHELL=true
FORMAT_YAML=true

PYTHON_DIRS="./"
HTML_DIRS="./"
SHELL_DIRS="./"
YAML_DIRS="./"

print_header() {
  echo "🔧 $1"
}

print_success() {
  echo "✅ $1"
}

print_error() {
  echo "❌ $1"
}

print_info() {
  echo "ℹ️  $1"
}

print_warning() {
  echo "⚠️  $1"
}

detect_os() {
  if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
    if [ -f /etc/os-release ]; then
      . /etc/os-release
      DISTRO=$ID
    elif type lsb_release >/dev/null 2>&1; then
      DISTRO=$(lsb_release -si | tr '[:upper:]' '[:lower:]')
    elif [ -f /etc/lsb-release ]; then
      . /etc/lsb-release
      DISTRO=$DISTRIB_ID
    elif [ -f /etc/debian_version ]; then
      DISTRO="debian"
    elif [ -f /etc/fedora-release ]; then
      DISTRO="fedora"
    elif [ -f /etc/centos-release ]; then
      DISTRO="centos"
    else
      DISTRO="unknown"
    fi
  elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
    DISTRO="macos"
  elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" || "$OSTYPE" == "win32" ]]; then
    OS="windows"
    DISTRO="windows"
  else
    OS="unknown"
    DISTRO="unknown"
  fi

  echo "$OS:$DISTRO"
}

get_installation_instructions() {
  local tool=$1
  local os_info=$(detect_os)
  local os=$(echo "$os_info" | cut -d':' -f1)
  local distro=$(echo "$os_info" | cut -d':' -f2)

  case "$tool" in
    "black")
      case "$os" in
        "linux" | "macos")
          echo "pip install black"
          ;;
        "windows")
          echo "pip install black"
          ;;
        *)
          echo "pip install black"
          ;;
      esac
      ;;
    "prettier")
      echo "npm install -g prettier"
      ;;
    "js-beautify")
      echo "npm install -g js-beautify"
      ;;
    "shfmt")
      case "$os" in
        "linux")
          case "$distro" in
            "ubuntu" | "debian")
              echo "apt-get update && apt-get install -y shfmt"
              ;;
            "fedora")
              echo "dnf install -y shfmt"
              ;;
            "centos" | "rhel")
              echo "yum install -y shfmt"
              ;;
            "arch" | "manjaro")
              echo "pacman -S shfmt"
              ;;
            *)
              echo "go install mvdan.cc/sh/v3/cmd/shfmt@latest"
              ;;
          esac
          ;;
        "macos")
          echo "brew install shfmt"
          ;;
        "windows")
          echo "go install mvdan.cc/sh/v3/cmd/shfmt@latest"
          ;;
        *)
          echo "go install mvdan.cc/sh/v3/cmd/shfmt@latest"
          ;;
      esac
      ;;
    "yamlfmt")
      case "$os" in
        "linux" | "macos" | "windows")
          echo "pip install yamlfmt"
          ;;
        *)
          echo "pip install yamlfmt"
          ;;
      esac
      ;;
    *)
      echo "Unknown tool: $tool"
      ;;
  esac
}

is_git_repo() {
  git rev-parse --is-inside-work-tree &>/dev/null
}

get_files_respecting_gitignore() {
  local pattern="$1"

  if is_git_repo; then
    git ls-files --cached --others --exclude-standard | grep -E "$pattern"
  else
    find . -type f -name "$pattern" -not -path "*/\.*" -not -path "*/venv/*" -not -path "*/node_modules/*"
  fi
}

format_python() {
  if [ "$FORMAT_PYTHON" = true ]; then
    print_header "Formatting Python files with Black (respecting .gitignore)..."
    if ! command -v black &>/dev/null; then
      print_error "Black not found."
      print_info "Install with: $(get_installation_instructions 'black')"
      return 1
    fi

    if is_git_repo; then
      python_files=$(git ls-files --cached --others --exclude-standard | grep -E "\.py$" || echo "")

      if [ -z "$python_files" ]; then
        print_info "No Python files found to format."
        return 0
      fi

      echo "$python_files" | xargs black
      if [ $? -eq 0 ]; then
        print_success "Python files formatted successfully!"
      else
        print_error "Failed to format Python files"
        return 1
      fi
    else
      if black $PYTHON_DIRS; then
        print_success "Python files formatted successfully!"
      else
        print_error "Failed to format Python files"
        return 1
      fi
    fi
  fi
}

format_html() {
  if [ "$FORMAT_HTML" = true ]; then
    print_header "Formatting HTML files (respecting .gitignore)..."

    html_files=""
    if is_git_repo; then
      html_files=$(git ls-files --cached --others --exclude-standard | grep -E "\.html$" || echo "")

      if [ -z "$html_files" ]; then
        print_info "No HTML files found to format."
        return 0
      fi
    fi

    if command -v prettier &>/dev/null; then
      print_info "Using Prettier for HTML formatting"

      if is_git_repo; then
        echo "$html_files" | xargs prettier --write --parser html
        if [ $? -eq 0 ]; then
          print_success "HTML files formatted successfully with Prettier!"
          return 0
        else
          print_error "Failed to format HTML files with Prettier"
        fi
      else
        if prettier --write "$HTML_DIRS/**/*.html" --parser html; then
          print_success "HTML files formatted successfully with Prettier!"
          return 0
        else
          print_error "Failed to format HTML files with Prettier"
        fi
      fi

      if command -v html-beautify &>/dev/null; then
        print_info "Trying with html-beautify..."

        if is_git_repo; then
          echo "$html_files" | xargs -I{} html-beautify -r {}
          if [ $? -eq 0 ]; then
            print_success "HTML files formatted successfully with html-beautify!"
            return 0
          else
            print_error "Failed to format HTML files with html-beautify"
            return 1
          fi
        else
          if find "$HTML_DIRS" -name "*.html" -exec html-beautify -r {} \;; then
            print_success "HTML files formatted successfully with html-beautify!"
            return 0
          else
            print_error "Failed to format HTML files with html-beautify"
            return 1
          fi
        fi
      else
        print_error "Neither Prettier nor html-beautify found."
        print_info "Install Prettier with: $(get_installation_instructions 'prettier')"
        print_info "Or install html-beautify with: $(get_installation_instructions 'js-beautify')"
        return 1
      fi
    elif command -v html-beautify &>/dev/null; then
      print_info "Using html-beautify for HTML formatting"

      if is_git_repo; then
        echo "$html_files" | xargs -I{} html-beautify -r {}
        if [ $? -eq 0 ]; then
          print_success "HTML files formatted successfully!"
          return 0
        else
          print_error "Failed to format HTML files"
          return 1
        fi
      else
        if find "$HTML_DIRS" -name "*.html" -exec html-beautify -r {} \;; then
          print_success "HTML files formatted successfully!"
          return 0
        else
          print_error "Failed to format HTML files"
          return 1
        fi
      fi
    else
      print_error "No HTML formatter found."
      print_info "Install Prettier with: $(get_installation_instructions 'prettier')"
      print_info "Or install html-beautify with: $(get_installation_instructions 'js-beautify')"
      return 1
    fi
  fi
}

format_shell() {
  if [ "$FORMAT_SHELL" = true ]; then
    print_header "Formatting Shell scripts (respecting .gitignore)..."
    if ! command -v shfmt &>/dev/null; then
      print_error "shfmt not found."
      print_info "Install with: $(get_installation_instructions 'shfmt')"
      return 1
    fi

    if is_git_repo; then
      shell_files=$(git ls-files --cached --others --exclude-standard | grep -E "\.sh$" || echo "")

      if [ -z "$shell_files" ]; then
        print_info "No Shell files found to format."
        return 0
      fi

      echo "$shell_files" | xargs shfmt -w -i 2 -ci
      if [ $? -eq 0 ]; then
        print_success "Shell scripts formatted successfully!"
        return 0
      else
        print_error "Failed to format Shell scripts"
        return 1
      fi
    else
      if find "$SHELL_DIRS" -name "*.sh" -exec shfmt -w -i 2 -ci {} \;; then
        print_success "Shell scripts formatted successfully!"
        return 0
      else
        print_error "Failed to format Shell scripts"
        return 1
      fi
    fi
  fi
}

format_yaml() {
  if [ "$FORMAT_YAML" = true ]; then
    print_header "Formatting YAML files (respecting .gitignore)..."

    yaml_files=""
    if is_git_repo; then
      yaml_files=$(git ls-files --cached --others --exclude-standard | grep -E "\.(yaml|yml)$" || echo "")

      if [ -z "$yaml_files" ]; then
        print_info "No YAML files found to format."
        return 0
      fi
    fi

    if command -v prettier &>/dev/null; then
      print_info "Using Prettier for YAML formatting"

      if is_git_repo; then
        echo "$yaml_files" | xargs prettier --write --parser yaml
        if [ $? -eq 0 ]; then
          print_success "YAML files formatted successfully with Prettier!"
          return 0
        else
          print_error "Failed to format YAML files with Prettier"
        fi
      else
        if prettier --write "$YAML_DIRS/**/*.{yaml,yml}" --parser yaml; then
          print_success "YAML files formatted successfully with Prettier!"
          return 0
        else
          print_error "Failed to format YAML files with Prettier"
        fi
      fi

      if command -v yamlfmt &>/dev/null; then
        print_info "Trying with yamlfmt..."

        if is_git_repo; then
          echo "$yaml_files" | xargs yamlfmt
          if [ $? -eq 0 ]; then
            print_success "YAML files formatted successfully with yamlfmt!"
            return 0
          else
            print_error "Failed to format YAML files with yamlfmt"
            return 1
          fi
        else
          if find "$YAML_DIRS" -name "*.yaml" -o -name "*.yml" -exec yamlfmt {} \;; then
            print_success "YAML files formatted successfully with yamlfmt!"
            return 0
          else
            print_error "Failed to format YAML files with yamlfmt"
            return 1
          fi
        fi
      else
        print_error "Neither Prettier nor yamlfmt found."
        print_info "Install Prettier with: $(get_installation_instructions 'prettier')"
        print_info "Or install yamlfmt with: $(get_installation_instructions 'yamlfmt')"
        return 1
      fi
    elif command -v yamlfmt &>/dev/null; then
      print_info "Using yamlfmt for YAML formatting"

      if is_git_repo; then
        echo "$yaml_files" | xargs yamlfmt
        if [ $? -eq 0 ]; then
          print_success "YAML files formatted successfully!"
          return 0
        else
          print_error "Failed to format YAML files"
          return 1
        fi
      else
        if find "$YAML_DIRS" -name "*.yaml" -o -name "*.yml" -exec yamlfmt {} \;; then
          print_success "YAML files formatted successfully!"
          return 0
        else
          print_error "Failed to format YAML files"
          return 1
        fi
      fi
    else
      print_error "No YAML formatter found."
      print_info "Install Prettier with: $(get_installation_instructions 'prettier')"
      print_info "Or install yamlfmt with: $(get_installation_instructions 'yamlfmt')"
      return 1
    fi
  fi
}

main() {
  print_header "Starting code formatting..."

  os_info=$(detect_os)
  os=$(echo "$os_info" | cut -d':' -f1)
  distro=$(echo "$os_info" | cut -d':' -f2)
  print_info "Detected system: $os ($distro)"

  if is_git_repo; then
    print_info "Git repository detected. Respecting .gitignore patterns."
  else
    print_info "Not a git repository. Using standard file exclusion patterns."
  fi

  format_python
  # format_html # TODO: fix html formatting
  format_shell
  format_yaml

  print_success "All code formatting complete!"
}

main "$@"
