import os
import re
import tokenize
import io
import argparse
from typing import List, Tu<PERSON>, Dict, Callable
import sys


def is_linting_comment(comment: str) -> bool:
    """Check if the comment is a linting comment that should be preserved."""
    linting_patterns = [
        r"#\s*type:\s*ignore",
        r"#\s*noqa",
        r"#\s*fmt:\s*(on|off)",
        r"#\s*pylint:",
        r"#\s*mypy:",
        r"#\s*ruff:",
        r"#\s*isort:",
        r"#\s*flake8:",
        r"#\s*pyright:",
    ]
    return any(
        re.search(pattern, comment, re.IGNORECASE) for pattern in linting_patterns
    )


def is_critical_comment(comment: str) -> bool:
    """Check if the comment is a critical warning to developers."""
    critical_patterns = [
        r"#\s*CRITICAL",
        r"#\s*WARNING",
        r"#\s*IMPORTANT",
        r"#\s*NOTE",
        r"#\s*FIXME",
        r"#\s*TODO",
        r"#\s*HACK",
        r"#\s*XXX",
        r"#\s*BUG",
        r"#\s*SECURITY",
    ]
    return any(
        re.search(pattern, comment, re.IGNORECASE) for pattern in critical_patterns
    )


def clean_comments_from_python_file(
    filepath: str, dry_run: bool = False
) -> Tuple[int, int]:
    """
    Remove comments from a Python file while preserving docstrings, linting comments, and critical warnings.

    Args:
        filepath: Path to the Python file
        dry_run: If True, don't modify the file, just count comments

    Returns:
        Tuple of (removed_comments, preserved_comments)
    """
    try:
        with open(filepath, "rb") as f:
            tokens = list(tokenize.tokenize(f.readline))

        removed_comments = 0
        preserved_comments = 0
        result = []

        for token in tokens:
            if token.type == tokenize.COMMENT:
                comment = token.string
                if is_linting_comment(comment) or is_critical_comment(comment):
                    result.append((token.type, token.string))
                    preserved_comments += 1
                else:
                    removed_comments += 1
            else:
                result.append((token.type, token.string))

        if not dry_run and removed_comments > 0:
            untokenized = tokenize.untokenize(result)
            with open(filepath, "wb") as f:
                f.write(untokenized)

        return removed_comments, preserved_comments
    except (tokenize.TokenError, IndentationError, SyntaxError) as e:
        print(f"Error processing {filepath}: {e}")
        return 0, 0


def clean_comments_from_shell_file(
    filepath: str, dry_run: bool = False
) -> Tuple[int, int]:
    """
    Remove comments from a shell script file while preserving critical warnings.

    Args:
        filepath: Path to the shell script file
        dry_run: If True, don't modify the file, just count comments

    Returns:
        Tuple of (removed_comments, preserved_comments)
    """
    with open(filepath, "r", encoding="utf-8") as f:
        lines = f.readlines()

    new_lines = []
    removed_comments = 0
    preserved_comments = 0

    for line in lines:
        if line.strip().startswith("#!"):
            new_lines.append(line)
            continue

        comment_match = re.match(r"^(\s*)#(.*)$", line)
        if comment_match:
            if is_critical_comment("#" + comment_match.group(2)):
                new_lines.append(line)
                preserved_comments += 1
            else:
                removed_comments += 1
                continue

        comment_idx = line.find("#")
        if comment_idx >= 0:
            in_string = False
            escape_char = False
            string_char = None

            for i in range(comment_idx):
                if line[i] in ('"', "'"):
                    if not escape_char:
                        if not in_string:
                            in_string = True
                            string_char = line[i]
                        elif line[i] == string_char:
                            in_string = False
                elif line[i] == "\\":
                    escape_char = not escape_char
                else:
                    escape_char = False

            if not in_string:
                comment = line[comment_idx:]
                if is_critical_comment(comment):
                    new_lines.append(line)
                    preserved_comments += 1
                else:
                    new_lines.append(line[:comment_idx] + "\n")
                    removed_comments += 1
            else:
                new_lines.append(line)
        else:
            new_lines.append(line)

    if not dry_run and removed_comments > 0:
        with open(filepath, "w", encoding="utf-8") as f:
            f.writelines(new_lines)

    return removed_comments, preserved_comments


def clean_comments_from_yaml_file(
    filepath: str, dry_run: bool = False
) -> Tuple[int, int]:
    """
    Remove comments from a YAML file while preserving critical warnings.

    Args:
        filepath: Path to the YAML file
        dry_run: If True, don't modify the file, just count comments

    Returns:
        Tuple of (removed_comments, preserved_comments)
    """
    with open(filepath, "r", encoding="utf-8") as f:
        lines = f.readlines()

    new_lines = []
    removed_comments = 0
    preserved_comments = 0

    for line in lines:
        comment_match = re.match(r"^(\s*)#(.*)$", line)
        if comment_match:
            if is_critical_comment("#" + comment_match.group(2)):
                new_lines.append(line)
                preserved_comments += 1
            else:
                removed_comments += 1
                continue

        parts = re.split(r"(?<![\\])#", line)
        if len(parts) > 1:
            comment = "#" + "#".join(parts[1:])
            if is_critical_comment(comment):
                new_lines.append(line)
                preserved_comments += 1
            else:
                new_lines.append(parts[0] + "\n")
                removed_comments += 1
        else:
            new_lines.append(line)

    if not dry_run and removed_comments > 0:
        with open(filepath, "w", encoding="utf-8") as f:
            f.writelines(new_lines)

    return removed_comments, preserved_comments


def clean_comments_from_file(filepath: str, dry_run: bool = False) -> Tuple[int, int]:
    """
    Remove comments from a file based on its file extension.

    Args:
        filepath: Path to the file
        dry_run: If True, don't modify the file, just count comments

    Returns:
        Tuple of (removed_comments, preserved_comments)
    """
    _, ext = os.path.splitext(filepath)
    ext = ext.lower()

    if ext == ".py":
        return clean_comments_from_python_file(filepath, dry_run)
    elif ext == ".sh":
        return clean_comments_from_shell_file(filepath, dry_run)
    elif ext in (".yaml", ".yml"):
        return clean_comments_from_yaml_file(filepath, dry_run)
    else:
        return 0, 0


def process_directory(
    directory: str, exclude_dirs: List[str], dry_run: bool = False
) -> Tuple[int, int, int]:
    """
    Process all supported files in the given directory and its subdirectories.

    Args:
        directory: Directory to process
        exclude_dirs: List of directory names to exclude
        dry_run: If True, don't modify any files

    Returns:
        Tuple of (files_processed, total_removed, total_preserved)
    """
    files_processed = 0
    total_removed = 0
    total_preserved = 0
    errors = 0

    supported_extensions = (".py", ".sh", ".yaml", ".yml")

    for root, dirs, files in os.walk(directory):
        dirs[:] = [d for d in dirs if d not in exclude_dirs and not d.startswith(".")]

        for file in files:
            _, ext = os.path.splitext(file)
            if ext.lower() in supported_extensions:
                filepath = os.path.join(root, file)
                try:
                    removed, preserved = clean_comments_from_file(filepath, dry_run)

                    if removed > 0 or preserved > 0:
                        files_processed += 1
                        total_removed += removed
                        total_preserved += preserved

                        if dry_run:
                            status = f"Would remove {removed} comments, preserve {preserved} comments"
                        else:
                            status = f"Removed {removed} comments, preserved {preserved} comments"

                        print(f"{filepath}: {status}")
                except Exception as e:
                    errors += 1
                    print(f"Error processing {filepath}: {e}")

    if errors > 0:
        print(f"\nEncountered errors in {errors} files. See above for details.")

    return files_processed, total_removed, total_preserved


def main():
    parser = argparse.ArgumentParser(
        description="Clean comments from Python, Shell, and YAML files while preserving docstrings, linting comments, and critical warnings"
    )
    parser.add_argument(
        "path",
        nargs="?",
        default=".",
        help="Path to process (default: current directory)",
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Do not modify files, just show what would be done",
    )
    parser.add_argument(
        "--exclude",
        nargs="+",
        default=["venv", "env", ".git", "__pycache__", "migrations"],
        help="Directories to exclude (default: venv env .git __pycache__ migrations)",
    )
    parser.add_argument(
        "--extensions",
        nargs="+",
        default=[".py", ".sh", ".yaml", ".yml"],
        help="File extensions to process (default: .py .sh .yaml .yml)",
    )
    parser.add_argument(
        "--verbose",
        "-v",
        action="store_true",
        help="Print more detailed information",
    )

    args = parser.parse_args()

    print(
        f"Processing files with extensions {', '.join(args.extensions)} in {os.path.abspath(args.path)}"
    )
    if args.dry_run:
        print("DRY RUN MODE: No files will be modified")

    try:
        files_processed, total_removed, total_preserved = process_directory(
            args.path, args.exclude, args.dry_run
        )

        print("\nSummary:")
        print(f"Files processed: {files_processed}")
        print(f"Comments removed: {total_removed}")
        print(f"Comments preserved: {total_preserved}")

        if files_processed == 0:
            print(
                "\nNo files were processed. Check if the path and extensions are correct."
            )
        elif args.dry_run and total_removed > 0:
            print("\nRun without --dry-run to apply changes")

    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        return 130
    except Exception as e:
        print(f"\nError: {e}", file=sys.stderr)
        if args.verbose:
            import traceback

            traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
