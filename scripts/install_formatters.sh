#!/bin/bash

set -e

print_header() {
  echo "🔧 $1"
}

print_success() {
  echo "✅ $1"
}

print_error() {
  echo "❌ $1"
}

print_info() {
  echo "ℹ️  $1"
}

detect_os() {
  if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
    if [ -f /etc/os-release ]; then
      . /etc/os-release
      DISTRO=$ID
    elif type lsb_release >/dev/null 2>&1; then
      DISTRO=$(lsb_release -si | tr '[:upper:]' '[:lower:]')
    elif [ -f /etc/lsb-release ]; then
      . /etc/lsb-release
      DISTRO=$DISTRIB_ID
    elif [ -f /etc/debian_version ]; then
      DISTRO="debian"
    elif [ -f /etc/fedora-release ]; then
      DISTRO="fedora"
    elif [ -f /etc/centos-release ]; then
      DISTRO="centos"
    else
      DISTRO="unknown"
    fi
  elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
    DISTRO="macos"
  elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" || "$OSTYPE" == "win32" ]]; then
    OS="windows"
    DISTRO="windows"
  else
    OS="unknown"
    DISTRO="unknown"
  fi

  echo "$OS:$DISTRO"
}

confirm() {
  read -r -p "$1 [y/N] " response
  case "$response" in
    [yY][eE][sS] | [yY])
      return 0
      ;;
    *)
      return 1
      ;;
  esac
}

get_python_cmd() {
  if command -v python3 &>/dev/null; then
    echo "python3"
  elif command -v python &>/dev/null; then
    echo "python"
  else
    echo ""
  fi
}

get_pip_cmd() {
  if command -v pip3 &>/dev/null; then
    echo "pip3"
  elif command -v pip &>/dev/null; then
    echo "pip"
  else
    echo ""
  fi
}

check_sudo_needed() {
  local cmd=$1
  case "$OS" in
    "linux")
      case "$cmd" in
        "apt-get" | "apt" | "dnf" | "yum" | "pacman")
          echo "sudo"
          ;;
        *)
          echo ""
          ;;
      esac
      ;;
    *)
      echo ""
      ;;
  esac
}

install_python_formatters() {
  print_header "Installing Python formatters (Black)..."

  local pip_cmd=$(get_pip_cmd)

  if [ -n "$pip_cmd" ]; then
    if [ "$OS" = "linux" ] && [ "$DISTRO" = "ubuntu" -o "$DISTRO" = "debian" ] && command -v apt-get &>/dev/null; then
      print_info "Ubuntu/Debian detected. Checking if python3-black is available in repositories..."
      if apt-cache show python3-black &>/dev/null; then
        if confirm "Would you like to install black via apt?"; then
          sudo apt-get update && sudo apt-get install -y python3-black
          print_success "Black installed successfully via apt!"
          return 0
        fi
      fi
    fi

    $pip_cmd install black
    print_success "Black installed successfully via pip!"
  else
    print_error "pip not found. Please install Python and pip first."

    case "$OS" in
      "linux")
        case "$DISTRO" in
          "ubuntu" | "debian")
            print_info "Try: sudo apt-get update && sudo apt-get install -y python3-pip"
            ;;
          "fedora")
            print_info "Try: sudo dnf install -y python3-pip"
            ;;
          "centos" | "rhel")
            print_info "Try: sudo yum install -y python3-pip"
            ;;
          "arch" | "manjaro")
            print_info "Try: sudo pacman -S python-pip"
            ;;
        esac
        ;;
      "macos")
        print_info "Try: brew install python"
        ;;
      "windows")
        print_info "Download Python from https://www.python.org/downloads/windows/"
        ;;
    esac
    return 1
  fi
}

install_html_formatters() {
  print_header "Installing HTML formatters..."

  if command -v npm &>/dev/null; then
    if confirm "Would you like to install Prettier for HTML formatting?"; then
      npm install -g prettier
      print_success "Prettier installed successfully!"
    fi

    if confirm "Would you like to install js-beautify (html-beautify) for HTML formatting?"; then
      npm install -g js-beautify
      print_success "js-beautify installed successfully!"
    fi
  else
    print_error "npm not found. Please install Node.js and npm first to use HTML formatters."

    case "$OS" in
      "linux")
        case "$DISTRO" in
          "ubuntu" | "debian")
            print_info "Try: sudo apt-get update && sudo apt-get install -y nodejs npm"
            ;;
          "fedora")
            print_info "Try: sudo dnf install -y nodejs npm"
            ;;
          "centos" | "rhel")
            print_info "Try: sudo yum install -y nodejs npm"
            ;;
          "arch" | "manjaro")
            print_info "Try: sudo pacman -S nodejs npm"
            ;;
        esac
        ;;
      "macos")
        print_info "Try: brew install node"
        ;;
      "windows")
        print_info "Download Node.js from https://nodejs.org/en/download/"
        ;;
    esac
    return 1
  fi
}

install_shell_formatters() {
  print_header "Installing Shell formatters (shfmt)..."

  case "$OS" in
    "linux")
      case "$DISTRO" in
        "ubuntu" | "debian")
          if command -v apt-get &>/dev/null; then
            if confirm "Would you like to install shfmt via apt?"; then
              sudo apt-get update && sudo apt-get install -y shfmt
              if [ $? -eq 0 ]; then
                print_success "shfmt installed successfully using apt!"
                return 0
              fi
            fi
          fi
          ;;
        "fedora")
          if command -v dnf &>/dev/null; then
            if confirm "Would you like to install shfmt via dnf?"; then
              sudo dnf install -y shfmt
              if [ $? -eq 0 ]; then
                print_success "shfmt installed successfully using dnf!"
                return 0
              fi
            fi
          fi
          ;;
        "centos" | "rhel")
          if command -v yum &>/dev/null; then
            if confirm "Would you like to install shfmt via yum?"; then
              sudo yum install -y shfmt
              if [ $? -eq 0 ]; then
                print_success "shfmt installed successfully using yum!"
                return 0
              fi
            fi
          fi
          ;;
        "arch" | "manjaro")
          if command -v pacman &>/dev/null; then
            if confirm "Would you like to install shfmt via pacman?"; then
              sudo pacman -S shfmt
              if [ $? -eq 0 ]; then
                print_success "shfmt installed successfully using pacman!"
                return 0
              fi
            fi
          fi
          ;;
      esac
      ;;
    "macos")
      if command -v brew &>/dev/null; then
        if confirm "Would you like to install shfmt using Homebrew?"; then
          brew install shfmt
          if [ $? -eq 0 ]; then
            print_success "shfmt installed successfully using Homebrew!"
            return 0
          fi
        fi
      else
        print_info "Homebrew not found. Consider installing it for easy package management:"
        print_info "/bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
      fi
      ;;
  esac

  if command -v go &>/dev/null; then
    if confirm "Would you like to install shfmt using Go?"; then
      go install mvdan.cc/sh/v3/cmd/shfmt@latest
      print_success "shfmt installed successfully using Go! Make sure your Go bin directory is in your PATH."

      go_bin=$(go env GOPATH)/bin
      if [[ ":$PATH:" != *":$go_bin:"* ]]; then
        print_info "You may need to add Go bin directory to your PATH:"
        case "$OS" in
          "linux" | "macos")
            print_info "echo 'export PATH=\$PATH:$go_bin' >> ~/.bashrc && source ~/.bashrc"
            ;;
          "windows")
            print_info "Add $go_bin to your PATH environment variable in Windows settings"
            ;;
        esac
      fi
      return 0
    fi
  else
    print_error "Go not found. Please install Go first to use shfmt."

    case "$OS" in
      "linux")
        case "$DISTRO" in
          "ubuntu" | "debian")
            print_info "Try: sudo apt-get update && sudo apt-get install -y golang-go"
            ;;
          "fedora")
            print_info "Try: sudo dnf install -y golang"
            ;;
          "centos" | "rhel")
            print_info "Try: sudo yum install -y golang"
            ;;
          "arch" | "manjaro")
            print_info "Try: sudo pacman -S go"
            ;;
        esac
        ;;
      "macos")
        print_info "Try: brew install go"
        ;;
      "windows")
        print_info "Download Go from https://golang.org/dl/"
        ;;
    esac
    return 1
  fi

  print_error "Could not install shfmt. Please install manually."
  return 1
}

install_yaml_formatters() {
  print_header "Installing YAML formatters..."

  local pip_cmd=$(get_pip_cmd)
  local success=false

  if [ -n "$pip_cmd" ]; then
    if confirm "Would you like to install yamlfmt for YAML formatting?"; then
      $pip_cmd install yamlfmt
      print_success "yamlfmt installed successfully!"
      success=true
    fi
  else
    print_error "pip not found. Please install Python and pip first to use yamlfmt."

    case "$OS" in
      "linux")
        case "$DISTRO" in
          "ubuntu" | "debian")
            print_info "Try: sudo apt-get update && sudo apt-get install -y python3-pip"
            ;;
          "fedora")
            print_info "Try: sudo dnf install -y python3-pip"
            ;;
          "centos" | "rhel")
            print_info "Try: sudo yum install -y python3-pip"
            ;;
          "arch" | "manjaro")
            print_info "Try: sudo pacman -S python-pip"
            ;;
        esac
        ;;
      "macos")
        print_info "Try: brew install python"
        ;;
      "windows")
        print_info "Download Python from https://www.python.org/downloads/windows/"
        ;;
    esac
  fi

  if command -v npm &>/dev/null; then
    if confirm "Would you like to install Prettier for YAML formatting?"; then
      npm install -g prettier
      print_success "Prettier installed successfully!"
      success=true
    fi
  else
    print_info "npm not found. Prettier won't be installed."

    case "$OS" in
      "linux")
        case "$DISTRO" in
          "ubuntu" | "debian")
            print_info "Try: sudo apt-get update && sudo apt-get install -y nodejs npm"
            ;;
          "fedora")
            print_info "Try: sudo dnf install -y nodejs npm"
            ;;
          "centos" | "rhel")
            print_info "Try: sudo yum install -y nodejs npm"
            ;;
          "arch" | "manjaro")
            print_info "Try: sudo pacman -S nodejs npm"
            ;;
        esac
        ;;
      "macos")
        print_info "Try: brew install node"
        ;;
      "windows")
        print_info "Download Node.js from https://nodejs.org/en/download/"
        ;;
    esac
  fi

  if ! $success; then
    return 1
  fi
}

main() {
  print_header "Starting formatter installation..."

  os_info=$(detect_os)
  OS=$(echo "$os_info" | cut -d':' -f1)
  DISTRO=$(echo "$os_info" | cut -d':' -f2)
  print_info "Detected system: $OS ($DISTRO)"

  echo "This script will help you install code formatters for various languages."
  echo "You can choose which formatters to install."
  echo ""

  if confirm "Would you like to install Python formatters (Black)?"; then
    install_python_formatters
  fi

  if confirm "Would you like to install HTML formatters (Prettier and/or html-beautify)?"; then
    install_html_formatters
  fi

  if confirm "Would you like to install Shell formatters (shfmt)?"; then
    install_shell_formatters
  fi

  if confirm "Would you like to install YAML formatters (yamlfmt and/or Prettier)?"; then
    install_yaml_formatters
  fi

  print_success "Installation complete!"
  echo ""
  echo "You can now use ./scripts/format_code.sh to format your code."
}

main "$@"
