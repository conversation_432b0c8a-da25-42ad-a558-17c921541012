#!/bin/bash

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
VENV_DIR="$SCRIPT_DIR/venv"

echo -e "${YELLOW}Setting up Python virtual environment for scripts...${NC}"

if ! command -v python3 &>/dev/null; then
	echo -e "${RED}Python 3 not found. Please install Python 3.9 or higher.${NC}"
	exit 1
fi

PYTHON_VERSION=$(python3 --version | cut -d " " -f 2)
echo -e "Found Python version: ${GREEN}$PYTHON_VERSION${NC}"

if [ ! -d "$VENV_DIR" ]; then
	echo -e "Creating virtual environment in ${GREEN}$VENV_DIR${NC}"
	python3 -m venv "$VENV_DIR"

	if [ $? -ne 0 ]; then
		echo -e "${RED}Failed to create virtual environment.${NC}"
		exit 1
	fi
else
	echo -e "${YELLOW}Virtual environment already exists at $VENV_DIR${NC}"
fi

echo -e "Activating virtual environment..."
source "$VENV_DIR/bin/activate"

if [ $? -ne 0 ]; then
	echo -e "${RED}Failed to activate virtual environment.${NC}"
	exit 1
fi

echo -e "Upgrading pip..."
pip install --upgrade pip

echo -e "Installing dependencies from ${GREEN}$SCRIPT_DIR/requirements.txt${NC}"
pip install -r "$SCRIPT_DIR/requirements.txt"

if [ $? -ne 0 ]; then
	echo -e "${RED}Failed to install requirements.${NC}"
	exit 1
fi

read -p "Do you want to install development dependencies? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
	echo -e "Installing development dependencies..."
	pip install black isort mypy flake8 pytest pytest-cov

	if [ $? -ne 0 ]; then
		echo -e "${RED}Failed to install development dependencies.${NC}"
		exit 1
	fi
fi

if [ -f "$SCRIPT_DIR/translation/requirements.txt" ]; then
	echo -e "Installing translation module dependencies..."
	pip install -r "$SCRIPT_DIR/translation/requirements.txt"
fi

echo -e "${GREEN}Virtual environment setup complete!${NC}"
echo
echo -e "To activate the virtual environment, run:"
echo -e "${YELLOW}source $VENV_DIR/bin/activate${NC}"
echo
echo -e "To deactivate the virtual environment, run:"
echo -e "${YELLOW}deactivate${NC}"
