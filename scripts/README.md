# Project Scripts Collection

A comprehensive suite of utility scripts designed to maintain code quality, enforce best practices, and streamline development workflows for Django/React applications.

## Overview

This collection provides a modular and configurable approach to various development tasks:

- **Code Cleaning & Formatting** - Maintain consistent code style and remove unnecessary comments
- **Structure Validation** - Ensure project organization follows defined conventions
- **Production Configuration Checks** - Verify Django settings are properly configured for production
- **Docker Utilities** - Simplify Docker-related operations
- **Git Workflow Tools** - Streamline commit processes with built-in checks
- **Virtual Environment Management** - Handle Python virtual environments consistently
- **Translation Utilities** - Automate translation of Django localization files using AI

## Common Design Patterns

All scripts in this collection follow common patterns:

1. **Modular Organization** - Each utility lives in its own directory with dedicated configuration
2. **YAML Configuration** - All scripts use `config.yml` files for customization
3. **Centralized Logging** - All activities are logged using the shared `logger.py` system
4. **Consistent CLI Interface** - Common command-line arguments across all utilities
5. **Integration Capabilities** - Scripts can be run individually or as part of larger workflows

## Available Scripts

### Clean Comments

Removes unnecessary comments while preserving important documentation and directives.

```bash
python clean_comments/clean_comments.py --directory path/to/dir --recursive
```

[Documentation](./clean_comments/README.md)

### Code Formatter

Ensures consistent code formatting across multiple languages.

```bash
./format_code/code_formatter.sh --verbose
```

[Documentation](./format_code/README.md)

### Structure Validation

Verifies project structure complies with defined conventions.

```bash
python structure_validation/validate_structure.py --verbose
```

[Documentation](./structure_validation/README.md)

### Production Configuration Check

Validates Django settings for security and production readiness.

```bash
python prod_config_check/check_prod_config.py --summary
```

[Documentation](./prod_config_check/README.md)

### Docker Utilities

Simplifies Docker operations for development and deployment.

```bash
python docker/docker.py build
```

### Git Commit Workflow

Automates checks before commit and provides standardized commit messages.

```bash
python commit/commit.py
```

### Formatter Installation

Installs and configures all required code formatters.

```bash
python install_formatters/install_formatters.py
```

### Virtual Environment Management

Creates and configures Python virtual environments.

```bash
python venv/venv.py create
# or use the shell script
./venv.sh
```

### Translation Utilities

Automates the translation of Django localization (.po) files using Ollama's AI models, with efficient caching.

```bash
# Translate a specific file
python -m translation.translate_file --file path/to/locale/ar/LC_MESSAGES/django.po

# Translate all files for a language
python -m translation.translate_all --language ar
```

[Documentation](./translation/README.md)

## Integrated Workflow

The `run_all_checks.py` script combines multiple validators and formatters:

```bash
python run_all_checks.py --all  # Run all checks
python run_all_checks.py --format-only  # Just format code
python run_all_checks.py --validate-only  # Just run validations
```

## Logging System

All scripts utilize the centralized logging system defined in `logger.py`:

- Logs are stored in the `logs/` directory with timestamped filenames
- Default log level is INFO, but can be increased with --verbose
- Each script produces its own log file

## Common Command Line Arguments

Most scripts support these common arguments:

| Argument | Description |
|----------|-------------|
| `--verbose`, `-v` | Enable detailed output |
| `--config`, `-c` | Specify custom configuration file |
| `--dry-run`, `-n` | Run without making changes |

## Configuration

Each script has its own `config.yml` file with appropriate defaults. You can override these with custom configuration files.

## CI/CD Integration

These scripts are designed to integrate with CI/CD workflows:

1. Add them to your CI pipeline for validation
2. Use in pre-commit hooks to enforce standards
3. Include in deployment scripts to verify production readiness

## Extending the System

To add new scripts to the collection:

1. Create a new directory under `scripts/`
2. Include a `config.yml` for configuration
3. Add appropriate Python/shell scripts
4. Use the shared logging system
5. Create a comprehensive README.md

## Troubleshooting

If you encounter issues:

1. Check the log files in `logs/` for detailed error messages
2. Verify your configuration files for errors
3. Run scripts with `--verbose` flag for additional debugging information

# Development Scripts

This directory contains automated scripts for maintaining code quality and consistency in the project.

## 🚀 Quick Setup

1. **Install Git Hooks** (Recommended):
   ```bash
   python3 scripts/setup_git_hooks.py
   ```

2. **Check Dependencies**:
   ```bash
   python3 scripts/setup_git_hooks.py --check-deps
   ```

## 📁 Scripts Overview

### Code Formatting (`format_code/`)

**Python Formatter** (`code_formatter.py`):
- Replaces the old bash script with a robust Python implementation
- Formats Python (black, isort), JavaScript/TypeScript (prettier), HTML, CSS, YAML
- Optimized for git commit workflows with timeouts and batch processing
- Configuration via `config.yml`

**Usage:**
```bash
# Format staged files (default)
python3 scripts/format_code/code_formatter.py --staged

# Format specific files
python3 scripts/format_code/code_formatter.py --files "file1.py,file2.js"

# Dry run (check only)
python3 scripts/format_code/code_formatter.py --dry-run --verbose
```

### Comment Cleaning (`clean_comments/`)

**Comment Cleaner** (`clean_comments.py`):
- Removes unnecessary comments while preserving important ones (TODO, FIXME, etc.)
- Supports Python, JavaScript/TypeScript, HTML, CSS, Shell scripts
- Integrated with git commit workflow

**Usage:**
```bash
# Clean comments in staged files
python3 scripts/clean_comments/clean_comments.py

# Clean specific files
python3 scripts/clean_comments/clean_comments.py --files file1.py file2.js
```

### Git Commit Integration (`commit/`)

**Enhanced Commit Script** (`commit.py`):
- Fast and reliable git commits with automatic formatting and cleaning
- Always completes within 10 seconds
- Treats formatting failures as warnings, never blocks commits
- Uses the new Python formatter

**Usage:**
```bash
# Commit with auto-formatting and cleaning
python3 scripts/commit/commit.py -m "Your commit message"

# Skip validation (fastest)
python3 scripts/commit/commit.py -m "Quick fix" --skip-validation

# Stage all files and commit
python3 scripts/commit/commit.py -m "Update features" --stage-all
```

### Git Hooks (`git_hooks/`)

**Pre-commit Hook** (`pre_commit_hook.py`):
- Automatically runs on every commit
- Formats code and cleans comments
- Re-stages modified files
- Never blocks commits (treats failures as warnings)

## ⚙️ Configuration

### Formatter Configuration (`format_code/config.yml`)

```yaml
# Language formatting
format_python: true
format_js: true
format_tsx: true
format_html: true
format_css: true

# Performance settings
timeout_per_file: 10
max_file_size_mb: 5

# Prettier configuration
prettier_config: ".prettierrc"
```

### Comment Cleaner Configuration (`clean_comments/config.yml`)

```yaml
include_extensions:
  - py
  - js
  - ts
  - tsx
  - html
  - css
```

## 🔧 Installation & Dependencies

### Required Dependencies:
- Python 3.7+
- Git

### Optional Dependencies (for full functionality):
```bash
# Python formatting
pip install black isort

# JavaScript/TypeScript/CSS formatting
npm install -g prettier

# Shell script formatting
brew install shfmt  # macOS
apt install shfmt   # Ubuntu
```

### Setup Git Hooks:
```bash
# Install hooks
python3 scripts/setup_git_hooks.py

# Check what dependencies are available
python3 scripts/setup_git_hooks.py --check-deps

# Uninstall hooks
python3 scripts/setup_git_hooks.py --uninstall
```

## 🚦 Workflow Integration

### Automatic (Recommended):
When git hooks are installed, formatting and comment cleaning happen automatically on every commit.

### Manual:
```bash
# Format code before committing
python3 scripts/format_code/code_formatter.py --staged

# Clean comments
python3 scripts/clean_comments/clean_comments.py

# Commit with integrated tools
python3 scripts/commit/commit.py -m "Your message"
```

### CI/CD Integration:
```bash
# Check code formatting in CI
python3 scripts/format_code/code_formatter.py --staged --dry-run

# Validate structure (separate tool)
python3 reviewer/cli.py --check-production-cleanliness
```

## 🎯 Key Features

### Speed & Reliability:
- All scripts optimized for commit workflows
- Timeouts prevent hanging
- Batch processing for efficiency
- Never blocks commits

### Security:
- Safe comment pattern matching
- File size limits
- Proper error handling
- Validation of shell scripts

### Maintainability:
- Clean Python code following Django best practices
- Comprehensive logging
- Configurable via YAML
- Modular design

## 🔍 Troubleshooting

### Git Hooks Not Working:
```bash
# Check hook installation
ls -la .git/hooks/

# Re-install hooks
python3 scripts/setup_git_hooks.py --force

# Test manually
python3 scripts/git_hooks/pre_commit_hook.py
```

### Formatting Issues:
```bash
# Check available formatters
python3 scripts/setup_git_hooks.py --check-deps

# Run with verbose output
python3 scripts/format_code/code_formatter.py --staged --verbose

# Test dry run
python3 scripts/format_code/code_formatter.py --dry-run
```

### Performance Issues:
- Large files (>5MB) are automatically skipped
- Batch processing limits prevent timeouts
- Configure timeouts in `config.yml`

## 📊 Statistics

The scripts provide detailed statistics about:
- Files processed
- Comments cleaned
- Formatting changes
- Execution time
- Errors encountered

All operations are logged for debugging and optimization. 