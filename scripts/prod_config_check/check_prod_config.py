"""
Production Configuration Checker

This script checks Django settings to ensure proper configuration for production environments.
It verifies security settings, performance optimizations, and infrastructure configurations
before deployment.
"""

import os
import sys
import re
import yaml
import importlib.util
import argparse
from typing import Dict, List, Set, Any, Optional, Tuple
from pathlib import Path


sys.path.append(str(Path(__file__).parent.parent))
try:
    from logger import get_logger
except ImportError:

    class FallbackLogger:
        def __init__(self, script_name, verbose=False):
            self.script_name = script_name
            self.verbose = verbose
            self.stats = {}

        def start_script(self):
            pass

        def end_script(self):
            return 0

        def info(self, message):
            if self.verbose:
                print(message)

        def debug(self, message):
            pass

        def warning(self, message):
            if self.verbose:
                print(f"WARNING: {message}")

        def error(self, message):
            print(f"ERROR: {message}")

        def critical(self, message):
            print(f"CRITICAL: {message}")

        def log_file_action(self, filepath, action, **stats):
            if self.verbose:
                print(f"{action.capitalize()}: {filepath}")
            for key, value in stats.items():
                if key not in self.stats:
                    self.stats[key] = 0
                self.stats[key] += value

        def print_summary(self):
            summary_lines = ["", "Summary:"]
            for key, value in self.stats.items():
                pretty_key = " ".join(word.capitalize() for word in key.split("_"))
                summary_lines.append(f"{pretty_key}: {value}")
            print("\n".join(summary_lines))
            return "\n".join(summary_lines)

    def get_logger(script_name, verbose=False):
        return FallbackLogger(script_name, verbose)


class ProductionConfigChecker:
    """Checks Django settings for production readiness."""

    def __init__(self, config_path: Optional[str] = None, verbose: bool = False):
        """
        Initialize the ProductionConfigChecker with a configuration.

        Args:
            config_path: Path to the configuration file
            verbose: Whether to show verbose output
        """
        self.logger = get_logger("prod_config_check", verbose)
        self.logger.start_script()

        self.config = self._load_config(config_path)
        self.errors = []
        self.warnings = []
        self.django_settings = {}

    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """
        Load configuration from a YAML file.

        Args:
            config_path: Path to the configuration file

        Returns:
            Dictionary containing configuration values
        """
        default_config = {
            "security_checks": {
                "debug_must_be_false": True,
                "secret_key_must_be_set": True,
                "allowed_hosts_must_be_set": True,
                "csrf_cookie_secure": True,
                "secure_ssl_redirect": True,
                "session_cookie_secure": True,
                "secure_hsts_seconds": True,
                "secure_hsts_include_subdomains": True,
                "secure_content_type_nosniff": True,
            },
            "performance_checks": {
                "cache_configured": True,
                "database_conn_max_age": True,
                "static_root_set": True,
                "media_root_set": True,
                "whitenoise_middleware_used": True,
            },
            "middleware_checks": {
                "security_middleware": True,
                "cache_middleware": True,
                "cors_middleware": True,
            },
            "minimum_values": {
                "database_conn_max_age": 60,
                "secure_hsts_seconds": 31536000,
            },
            "insecure_settings": [
                "RECAPTCHA_PRIVATE_KEY",
                "EMAIL_HOST_PASSWORD",
                "SENDGRID_API_KEY",
            ],
            "excluded_checks": [],
            "settings_file": "api/config/settings/prod.py",
        }

        if config_path and os.path.isfile(config_path):
            try:
                with open(config_path, "r", encoding="utf-8") as f:
                    user_config = yaml.safe_load(f)
                    if user_config:

                        for section, values in user_config.items():
                            if section in default_config and isinstance(values, dict):
                                default_config[section].update(values)
                            else:
                                default_config[section] = values
            except Exception as e:
                self.logger.error(f"Error loading config file: {e}")

        return default_config

    def load_django_settings(self) -> bool:
        """
        Load Django settings from the settings file.

        Returns:
            True if settings were loaded successfully, False otherwise
        """
        settings_file = self.config.get("settings_file")
        if not settings_file or not os.path.isfile(settings_file):
            self.errors.append(f"Settings file not found: {settings_file}")
            return False

        try:
            spec = importlib.util.spec_from_file_location("settings", settings_file)
            if spec and spec.loader:
                settings_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(settings_module)

                self.django_settings = {
                    name: getattr(settings_module, name)
                    for name in dir(settings_module)
                    if name.isupper()
                }
                return True
            else:
                self.errors.append(f"Failed to load module from {settings_file}")
                return False
        except Exception as e:
            self.errors.append(f"Error loading Django settings: {e}")
            return False

    def check_security_settings(self) -> None:
        """Check security-related settings."""
        security_checks = self.config.get("security_checks", {})
        excluded_checks = set(self.config.get("excluded_checks", []))

        if (
            security_checks.get("debug_must_be_false")
            and "debug_must_be_false" not in excluded_checks
        ):
            debug = self.django_settings.get("DEBUG")
            if debug is not True and debug is not False:
                self.warnings.append("DEBUG setting not found or not a boolean")
            elif debug:
                self.errors.append("DEBUG is set to True in production settings")

        if (
            security_checks.get("secret_key_must_be_set")
            and "secret_key_must_be_set" not in excluded_checks
        ):
            secret_key = self.django_settings.get("SECRET_KEY")
            if not secret_key:
                self.errors.append("SECRET_KEY is not set")
            elif (
                "SECRET_KEY" in str(secret_key) or secret_key == "your-secret-key-here"
            ):
                self.errors.append(
                    "SECRET_KEY appears to be a default or placeholder value"
                )

        if (
            security_checks.get("allowed_hosts_must_be_set")
            and "allowed_hosts_must_be_set" not in excluded_checks
        ):
            allowed_hosts = self.django_settings.get("ALLOWED_HOSTS")
            if not allowed_hosts:
                self.errors.append("ALLOWED_HOSTS is empty")
            elif "*" in allowed_hosts:
                self.warnings.append(
                    "ALLOWED_HOSTS contains a wildcard (*), which is not recommended for production"
                )

        if (
            security_checks.get("csrf_cookie_secure")
            and "csrf_cookie_secure" not in excluded_checks
        ):
            csrf_cookie_secure = self.django_settings.get("CSRF_COOKIE_SECURE")
            if csrf_cookie_secure is not True:
                self.errors.append("CSRF_COOKIE_SECURE is not set to True")

        if (
            security_checks.get("secure_ssl_redirect")
            and "secure_ssl_redirect" not in excluded_checks
        ):
            secure_ssl_redirect = self.django_settings.get("SECURE_SSL_REDIRECT")
            if secure_ssl_redirect is not True:
                self.errors.append("SECURE_SSL_REDIRECT is not set to True")

        if (
            security_checks.get("session_cookie_secure")
            and "session_cookie_secure" not in excluded_checks
        ):
            session_cookie_secure = self.django_settings.get("SESSION_COOKIE_SECURE")
            if session_cookie_secure is not True:
                self.errors.append("SESSION_COOKIE_SECURE is not set to True")

        if (
            security_checks.get("secure_hsts_seconds")
            and "secure_hsts_seconds" not in excluded_checks
        ):
            secure_hsts_seconds = self.django_settings.get("SECURE_HSTS_SECONDS")
            min_value = self.config.get("minimum_values", {}).get(
                "secure_hsts_seconds", 31536000
            )
            if not secure_hsts_seconds:
                self.errors.append("SECURE_HSTS_SECONDS is not set")
            elif secure_hsts_seconds < min_value:
                self.warnings.append(
                    f"SECURE_HSTS_SECONDS is set to {secure_hsts_seconds}, which is less than the recommended {min_value}"
                )

        if (
            security_checks.get("secure_hsts_include_subdomains")
            and "secure_hsts_include_subdomains" not in excluded_checks
        ):
            secure_hsts_include_subdomains = self.django_settings.get(
                "SECURE_HSTS_INCLUDE_SUBDOMAINS"
            )
            if secure_hsts_include_subdomains is not True:
                self.errors.append("SECURE_HSTS_INCLUDE_SUBDOMAINS is not set to True")

        if (
            security_checks.get("secure_content_type_nosniff")
            and "secure_content_type_nosniff" not in excluded_checks
        ):
            secure_content_type_nosniff = self.django_settings.get(
                "SECURE_CONTENT_TYPE_NOSNIFF"
            )
            if secure_content_type_nosniff is not True:
                self.errors.append("SECURE_CONTENT_TYPE_NOSNIFF is not set to True")

    def check_performance_settings(self) -> None:
        """Check performance-related settings."""
        performance_checks = self.config.get("performance_checks", {})
        excluded_checks = set(self.config.get("excluded_checks", []))

        if (
            performance_checks.get("cache_configured")
            and "cache_configured" not in excluded_checks
        ):
            caches = self.django_settings.get("CACHES")
            if (
                not caches
                or caches.get("default", {}).get("BACKEND")
                == "django.core.cache.backends.locmem.LocMemCache"
            ):
                self.warnings.append(
                    "CACHES is using the default local memory cache, which is not suitable for production"
                )

        if (
            performance_checks.get("database_conn_max_age")
            and "database_conn_max_age" not in excluded_checks
        ):
            databases = self.django_settings.get("DATABASES", {})
            default_db = databases.get("default", {})
            conn_max_age = default_db.get("CONN_MAX_AGE")
            min_value = self.config.get("minimum_values", {}).get(
                "database_conn_max_age", 60
            )

            if conn_max_age is None:
                self.warnings.append(
                    "DATABASES['default']['CONN_MAX_AGE'] is not set, which means connections are closed after each request"
                )
            elif conn_max_age < min_value:
                self.warnings.append(
                    f"DATABASES['default']['CONN_MAX_AGE'] is set to {conn_max_age}, which is less than the recommended {min_value}"
                )

        if (
            performance_checks.get("static_root_set")
            and "static_root_set" not in excluded_checks
        ):
            static_root = self.django_settings.get("STATIC_ROOT")
            if not static_root:
                self.errors.append("STATIC_ROOT is not set")

        if (
            performance_checks.get("media_root_set")
            and "media_root_set" not in excluded_checks
        ):
            media_root = self.django_settings.get("MEDIA_ROOT")
            if not media_root:
                self.errors.append("MEDIA_ROOT is not set")

        if (
            performance_checks.get("whitenoise_middleware_used")
            and "whitenoise_middleware_used" not in excluded_checks
        ):
            middleware = self.django_settings.get("MIDDLEWARE", [])
            has_whitenoise = any("whitenoise" in str(m).lower() for m in middleware)
            if not has_whitenoise:
                self.warnings.append(
                    "WhiteNoise middleware is not used, which is recommended for serving static files in production"
                )

    def check_middleware_settings(self) -> None:
        """Check middleware-related settings."""
        middleware_checks = self.config.get("middleware_checks", {})
        excluded_checks = set(self.config.get("excluded_checks", []))

        middleware = self.django_settings.get("MIDDLEWARE", [])

        if (
            middleware_checks.get("security_middleware")
            and "security_middleware" not in excluded_checks
        ):
            has_security_middleware = (
                "django.middleware.security.SecurityMiddleware" in middleware
            )
            if not has_security_middleware:
                self.errors.append("SecurityMiddleware is not included in MIDDLEWARE")

        if (
            middleware_checks.get("cache_middleware")
            and "cache_middleware" not in excluded_checks
        ):
            has_cache_middleware = any("cache" in str(m).lower() for m in middleware)
            if not has_cache_middleware:
                self.warnings.append("No cache middleware found in MIDDLEWARE")

        if (
            middleware_checks.get("cors_middleware")
            and "cors_middleware" not in excluded_checks
        ):
            has_cors_middleware = any("cors" in str(m).lower() for m in middleware)
            if not has_cors_middleware:
                self.warnings.append("No CORS middleware found in MIDDLEWARE")

    def check_insecure_settings(self) -> None:
        """Check for insecure settings like hardcoded credentials."""
        insecure_settings = self.config.get("insecure_settings", [])

        for setting_name in insecure_settings:
            setting_value = self.django_settings.get(setting_name)
            if (
                setting_value
                and isinstance(setting_value, str)
                and not setting_value.startswith("${")
                and not setting_value.startswith("@ENV")
            ):
                self.errors.append(
                    f"{setting_name} appears to be hardcoded in settings rather than loaded from environment variables"
                )

    def check_all(self) -> bool:
        """
        Run all configuration checks.

        Returns:
            True if all checks passed (no errors), False otherwise
        """
        self.logger.info("Starting production configuration checks")

        self.logger.debug(
            f"Loading Django settings from {self.config.get('settings_file')}"
        )
        if not self.load_django_settings():
            self.logger.error(
                "Failed to load Django settings, cannot continue with checks"
            )
            return False

        self.logger.debug("Running security checks")
        self.check_security_settings()

        self.logger.debug("Running performance checks")
        self.check_performance_settings()

        self.logger.debug("Running middleware checks")
        self.check_middleware_settings()

        self.logger.debug("Checking for insecure settings")
        self.check_insecure_settings()

        if self.errors:
            for error in self.errors:
                self.logger.error(f"Config Error: {error}")

        if self.warnings:
            for warning in self.warnings:
                self.logger.warning(f"Config Warning: {warning}")

        self.logger.stats.update(
            {
                "errors": len(self.errors),
                "warnings": len(self.warnings),
                "settings_checked": len(self.django_settings),
                "valid": len(self.errors) == 0,
            }
        )

        return len(self.errors) == 0

    def get_summary(self) -> str:
        """
        Get a summary of validation results.

        Returns:
            A string containing the summary of validation results
        """
        result = []

        if not self.errors and not self.warnings:
            result.append("✅ Production configuration check passed with no issues!")
        elif not self.errors:
            result.append(
                f"✅ Production configuration check passed with {len(self.warnings)} warnings"
            )
        else:
            result.append(
                f"❌ Production configuration check failed with {len(self.errors)} errors and {len(self.warnings)} warnings"
            )

        if self.errors:
            result.append("\nErrors:")
            for i, error in enumerate(self.errors, 1):
                result.append(f"{i}. {error}")

        if self.warnings:
            result.append("\nWarnings:")
            for i, warning in enumerate(self.warnings, 1):
                result.append(f"{i}. {warning}")

        settings_file = self.config.get("settings_file", "")
        if settings_file:
            result.append(f"\nSettings file checked: {settings_file}")
            result.append(f"Settings entries found: {len(self.django_settings)}")

        return "\n".join(result)


def main():
    """Main function to parse arguments and run the checker."""
    parser = argparse.ArgumentParser(
        description="Check Django production configuration"
    )
    parser.add_argument("--config", "-c", help="Path to configuration file")
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Show verbose output"
    )
    parser.add_argument(
        "--summary", "-s", action="store_true", help="Show summary of check results"
    )

    args = parser.parse_args()

    if not args.config:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        default_config = os.path.join(script_dir, "config.yml")
        if os.path.exists(default_config):
            args.config = default_config

    checker = ProductionConfigChecker(args.config, args.verbose)
    success = checker.check_all()

    if args.summary or not args.verbose:
        checker.logger.print_summary()

    execution_time = checker.logger.end_script()
    checker.logger.debug(f"Total execution time: {execution_time:.2f} seconds")

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
