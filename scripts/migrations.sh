#!/bin/bash

echo "Preparing to delete files and database..."

for app_dir in apps/**/; do
  if [ -d "$app_dir"migrations/ ]; then
    echo "Deleting migrations for app: $app_dir"
    rm -rf "$app_dir"migrations/ 2>/dev/null || echo "Unable to delete migrations for: $app_dir"
  fi
done

for pycache_dir in apps/**/__pycache__/; do
  if [ -d "$pycache_dir" ]; then
    echo "Removing __pycache__ from: $pycache_dir"
    rm -rf "$pycache_dir" 2>/dev/null || echo "Error removing __pycache__ from: $pycache_dir"
  fi
done

if [ -f "../db.sqlite3" ]; then
  echo "Deleting db.sqlite3"
  rm -f "../db.sqlite3" 2>/dev/null || echo "Error deleting db.sqlite3"
fi

echo "Removing builtin migrations..."
rm -rf ../env/lib/*/site-packages/django/contrib/admin/migrations/ 2>/dev/null || echo "Error removing admin migrations"
rm -rf ../env/lib/*/site-packages/rest_framework/authtoken/migrations 2>/dev/null || echo "Error removing authtoken migrations"
rm -rf ../env/lib/*/site-packages/rest_framework_simplejwt/token_blacklist/migrations 2>/dev/null || echo "Error removing token_blacklist migrations"
rm -rf ../env/lib/python3.11/site-packages/social_django/migrations 2>/dev/null || echo "Error removing social_django migrations"

echo "Cleanup complete."

echo "Creating database..."
python manage.py makemigrations &&
  python manage.py migrate &&
  echo "Creating migrations for apps..."

python manage.py makemigrations _user && python manage.py migrate _user &&
  python manage.py makemigrations admin && python manage.py migrate admin &&
  python manage.py makemigrations authtoken && python manage.py migrate authtoken &&
  python manage.py makemigrations token_blacklist && python manage.py migrate token_blacklist &&
  python manage.py makemigrations social_django && python manage.py migrate social_django &&
  python manage.py makemigrations && python manage.py migrate
