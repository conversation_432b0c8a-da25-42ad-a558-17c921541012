"""
<PERSON><PERSON><PERSON> to fix migration issues by checking if columns exist and faking migrations if needed.
This is particularly useful for third-party apps where migrations might be out of sync.
"""

import os
import sys
import importlib
import subprocess
from pathlib import Path


BASE_DIR = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(BASE_DIR))


possible_settings_modules = [
    "config.settings.testing",
    "config.settings.base",
    "config.settings",
    "taj.settings",
    "taj.settings.base",
    "settings",
    "settings.base",
]


settings_module = os.environ.get("DJANGO_SETTINGS_MODULE")


if not settings_module:
    for module_path in possible_settings_modules:
        try:
            importlib.import_module(module_path)
            settings_module = module_path
            os.environ["DJANGO_SETTINGS_MODULE"] = settings_module
            print(f"Found settings module: {settings_module}")
            break
        except ImportError:
            continue


if not settings_module:
    print(
        "Could not find settings module in standard locations. Searching for settings files..."
    )

    import glob

    settings_files = glob.glob(f"{BASE_DIR}/**/settings*.py", recursive=True)

    for settings_file in settings_files:
        rel_path = os.path.relpath(settings_file, BASE_DIR)
        if rel_path.endswith(".py"):
            rel_path = rel_path[:-3]
        module_path = rel_path.replace(os.path.sep, ".")

        try:
            importlib.import_module(module_path)
            settings_module = module_path
            os.environ["DJANGO_SETTINGS_MODULE"] = settings_module
            print(f"Found settings module: {settings_module}")
            break
        except ImportError:
            continue


if not settings_module:
    print("Error: Could not find Django settings module.")
    print("Please set DJANGO_SETTINGS_MODULE environment variable manually.")
    sys.exit(1)


try:
    import django

    django.setup()
    print(f"Django setup successful with settings module: {settings_module}")
except Exception as e:
    print(f"Error setting up Django: {e}")
    sys.exit(1)


from django.db import connection
from django.db.migrations.recorder import MigrationRecorder


MIGRATIONS_TO_CHECK = [
    {
        "app": "oauth2_provider",
        "name": "0007_application_post_logout_redirect_uris",
        "table": "oauth2_provider_application",
        "column": "post_logout_redirect_uris",
    },
]


def check_and_fix_migrations():
    """Check if columns exist and fake migrations if needed."""
    for migration in MIGRATIONS_TO_CHECK:
        app = migration["app"]
        name = migration["name"]
        table = migration["table"]
        column = migration["column"]

        print(f"Checking migration {app}.{name} for column {table}.{column}...")

        with connection.cursor() as cursor:
            cursor.execute(
                f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_name='{table}' 
                    AND column_name='{column}'
                );
            """
            )
            result = cursor.fetchone()
            column_exists = result[0] if result is not None else False

        migration_record = MigrationRecorder.Migration.objects.filter(
            app=app, name=name
        ).first()
        migration_applied = migration_record is not None

        if column_exists and not migration_applied:
            print(
                f"Column {column} exists but migration {app}.{name} not applied. Faking migration..."
            )
            subprocess.call(["python", "manage.py", "migrate", app, name, "--fake"])
            print(f"Migration {app}.{name} faked successfully")
        elif migration_applied and not column_exists:
            print(
                f"Warning: Migration {app}.{name} is applied but column {column} does not exist!"
            )
            print(f"This might require manual intervention.")
        elif column_exists and migration_applied:
            print(f"Migration {app}.{name} already correctly applied")
        else:
            print(
                f"Column {column} does not exist and will be created by migration {app}.{name}"
            )


if __name__ == "__main__":
    check_and_fix_migrations()
