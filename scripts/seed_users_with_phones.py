from django.contrib.auth import get_user_model
from django.db import transaction
import random
import sys

User = get_user_model()


COUNTRY_PHONE_PREFIXES = {
    "United States": "+1",
    "United Kingdom": "+44",
    "France": "+33",
    "Germany": "+49",
    "Spain": "+34",
    "Italy": "+39",
    "Canada": "+1",
    "Australia": "+61",
    "Japan": "+81",
    "China": "+86",
    "India": "+91",
    "Brazil": "+55",
    "Russia": "+7",
    "Mexico": "+52",
    "South Korea": "+82",
    "Netherlands": "+31",
    "Sweden": "+46",
    "Norway": "+47",
    "Denmark": "+45",
    "Finland": "+358",
    "Switzerland": "+41",
    "Belgium": "+32",
    "Austria": "+43",
    "Portugal": "+351",
    "Greece": "+30",
    "Ireland": "+353",
    "New Zealand": "+64",
    "Singapore": "+65",
    "Israel": "+972",
    "South Africa": "+27",
}


def generate_phone_number(country=None):
    """Generate a random phone number with country code."""
    if not country:
        country = random.choice(list(COUNTRY_PHONE_PREFIXES.keys()))

    prefix = COUNTRY_PHONE_PREFIXES[country]

    if prefix == "+1":
        area_code = random.randint(200, 999)
        exchange = random.randint(200, 999)
        number = random.randint(1000, 9999)
        phone_number = f"{prefix}{area_code}{exchange}{number}"
    else:
        length = random.randint(8, 11)
        digits = "".join([str(random.randint(0, 9)) for _ in range(length)])
        phone_number = f"{prefix}{digits}"

    return phone_number, country


def run(*args):
    """
    Seed users with phone numbers from different countries.

    Usage:
        python manage.py runscript seed_users_with_phones [--script-args all]

    Args:
        all: Update all users, even those who already have phone numbers
    """
    update_all = "all" in args

    if update_all:
        users = User.objects.all()
        print(f"Updating all {users.count()} users with phone numbers")
    else:
        users = User.objects.filter(phone_number__isnull=True) | User.objects.filter(
            phone_number=""
        )
        print(f"Updating {users.count()} users without phone numbers")

    if not users.exists():
        print("No users to update. Exiting.")
        return

    countries = list(COUNTRY_PHONE_PREFIXES.keys())
    country_cycle = countries * (users.count() // len(countries) + 1)

    updated_count = 0
    country_counts = {}

    with transaction.atomic():
        for i, user in enumerate(users):
            country = country_cycle[i % len(country_cycle)]
            phone_number, _ = generate_phone_number(country)

            user.phone_number = phone_number
            if hasattr(user, "country"):
                user.country = country
            user.save()

            country_counts[country] = country_counts.get(country, 0) + 1
            updated_count += 1

            if updated_count % 10 == 0:
                print(f"Updated {updated_count} users so far...")

    print(f"Successfully updated {updated_count} users with phone numbers")

    print("\nCountry distribution:")
    for country, count in sorted(
        country_counts.items(), key=lambda x: x[1], reverse=True
    ):
        print(f"  {country}: {count} users")
