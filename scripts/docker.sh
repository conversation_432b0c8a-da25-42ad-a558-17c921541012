#!/bin/bash

function launch() {
  docker compose -f docker-compose.yml --env-file .env up -d --build
}

function launch-no-cache() {
  docker-compose --env-file .env --build --no-cache
  docker-compose --env-file .env up -d
}

function delete_all_containers() {
  docker rm -v -f $(docker ps -qa)
}

function shutdown() {
  docker compose --env-file .env down -v --remove-orphans
}

function prune_volumes() {
  docker volume prune -f
}

function prune_networks() {
  docker network prune -f
}

function prune_images() {
  docker image prune -a -f
}

function view_logs() {
  docker-compose logs -f
}

function exec_bash() {
  docker exec -it "$1" bash
}

function exec_command() {
  docker exec taj-api_api "$1"
}

function test() {
  docker exec taj-api_api pytest
}

function log() {
  docker logs -f taj-api_api
}

function translate() {
  docker exec taj-api_api python manage.py makemessages -l ar
  docker exec taj-api_api python manage.py compilemessages
  python ./locale/auto_translator.py
}

case "$1" in
  --launch)
    launch
    ;;
  --launch-no-cache)
    launch-no-cache
    ;;
  --delete-all-containers)
    delete_all_containers
    ;;
  --shutdown)
    shutdown
    ;;
  --prune-volumes)
    prune_volumes
    ;;
  --prune-networks)
    prune_networks
    ;;
  --prune-images)
    prune_images
    ;;
  --view-logs)
    view_logs
    ;;
  --test)
    test
    ;;
  --log)
    log
    ;;
  --translate)
    translate
    ;;
  --exec-command)
    if [ -z "$2" ]; then
      echo "Please specify a command."
      exit 1
    fi
    exec_command "$2"
    ;;
  --exec-bash)
    if [ -z "$2" ]; then
      echo "Please specify a container name or ID."
      exit 1
    fi
    exec_bash "$2"
    ;;
  *)
    echo "Usage: $0 {--launch|--delete-all-containers|--shutdown|--prune-volumes|--prune-networks|--prune-images|--view-logs|--exec-bash <container>}"
    exit 1
    ;;
esac
